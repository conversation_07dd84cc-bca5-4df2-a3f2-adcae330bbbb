@echo off
echo ========================================
echo OpenClover 覆盖率测试脚本
echo 解决JaCoCo覆盖率显示0%的问题
echo ========================================

echo.
echo 步骤1：清理项目
mvn clean

echo.
echo 步骤2：编译项目
mvn compile test-compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码语法
    pause
    exit /b 1
)

echo.
echo 步骤3：设置Clover并运行测试
echo 运行ActivityServiceTest...
mvn clover:setup test -Dtest=ActivityServiceTest -Dspring.profiles.active=wurth -DfailIfNoTests=false

echo.
echo 步骤4：生成Clover覆盖率报告
mvn clover:aggregate clover:clover

echo.
echo 步骤5：检查报告生成结果
if exist "target\site\clover\index.html" (
    echo ✅ Clover覆盖率报告生成成功！
    echo 报告位置: target\site\clover\index.html
    
    echo.
    echo 步骤6：打开覆盖率报告
    start "" "target\site\clover\index.html"
    
    echo.
    echo 📊 Clover覆盖率分析指导：
    echo 1. 红色：未覆盖的代码行
    echo 2. 绿色：已覆盖的代码行  
    echo 3. 黄色：部分覆盖的分支
    echo 4. 点击类名查看详细覆盖情况
    echo 5. Clover对Spring Boot和Dubbo有更好的支持
    
) else (
    echo ❌ Clover覆盖率报告生成失败
    echo.
    echo 故障排除建议：
    echo 1. 检查测试是否成功执行
    echo 2. 验证Clover插件配置是否正确
    echo 3. 查看Maven输出日志中的错误信息
    echo.
    echo 如果仍然有问题，可以尝试JaCoCo修复方案：
    echo 运行: test-coverage-jacoco-fixed.bat
)

echo.
echo 步骤7：显示覆盖率摘要
if exist "target\site\clover\clover.xml" (
    echo 从XML报告中提取覆盖率数据...
    findstr /C:"elements=" "target\site\clover\clover.xml" 2>nul
    findstr /C:"coveredelements=" "target\site\clover\clover.xml" 2>nul
)

echo.
echo ========================================
echo OpenClover覆盖率测试完成
echo ========================================
pause
