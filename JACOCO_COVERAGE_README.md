# JaCoCo 测试覆盖率使用指南

本项目已配置JaCoCo测试覆盖率插件，可以生成详细的测试覆盖率报告。

## 快速开始

### 方法一：使用脚本（推荐）

**Windows用户：**
```bash
# 双击运行或在命令行执行
jacoco-test.bat
```

**Linux/macOS用户：**
```bash
# 给脚本执行权限
chmod +x jacoco-test.sh
# 运行脚本
./jacoco-test.sh
```

### 方法二：使用Maven命令

```bash
# 推荐方式：运行特定测试类（避免Spring配置问题）
mvn clean test -Dtest=StringUtilsTest jacoco:report

# 或者运行所有测试（可能遇到Spring配置问题）
mvn clean test jacoco:report

# 单独生成报告（如果测试已运行）
mvn jacoco:report

# 查看报告
# 打开 target/site/jacoco/index.html
```

### 注意事项

- **推荐使用特定测试**：由于项目依赖复杂的Spring配置，建议先运行简单的单元测试如 `StringUtilsTest`
- **完整测试**：如需运行所有测试，请确保相关的配置文件和依赖服务已正确配置
- **报告查看**：生成的HTML报告会自动在浏览器中打开

## 覆盖率报告说明

### 报告位置
- HTML报告：`target/site/jacoco/index.html`
- XML报告：`target/site/jacoco/jacoco.xml`
- CSV报告：`target/site/jacoco/jacoco.csv`

### 覆盖率指标说明

1. **Instructions (指令覆盖率)**
   - 最小的代码单元，Java字节码指令
   - 绿色：已覆盖，红色：未覆盖

2. **Branches (分支覆盖率)**
   - if/else、switch、循环等分支语句
   - 绿色：所有分支都被执行，黄色：部分分支被执行，红色：未执行

3. **Lines (行覆盖率)**
   - 源代码行的覆盖情况
   - 绿色：已覆盖，红色：未覆盖

4. **Methods (方法覆盖率)**
   - 方法级别的覆盖率
   - 显示哪些方法被调用过

5. **Classes (类覆盖率)**
   - 类级别的覆盖率
   - 显示哪些类被使用过

### 颜色说明
- **绿色**：完全覆盖
- **黄色**：部分覆盖
- **红色**：未覆盖

## 配置说明

### JaCoCo插件配置
项目中的JaCoCo配置位于`pom.xml`中，主要配置包括：

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
</plugin>
```

### 排除规则
以下类型的文件被排除在覆盖率统计之外：
- 配置类 (`**/config/**/*`)
- 启动类 (`CosfoMallApplication.class`)
- 模型类 (`**/model/**/*`, `**/dto/**/*`, `**/vo/**/*`)
- 常量类 (`**/constant/**/*`, `**/constants/**/*`)
- 枚举类 (`**/enums/**/*`)
- 异常类 (`**/exception/**/*`)
- Mapper接口 (`**/mapper/**/*`)

### 覆盖率阈值
当前设置的最低覆盖率阈值为30%，可以在`pom.xml`中调整：

```xml
<limit>
    <counter>LINE</counter>
    <value>COVEREDRATIO</value>
    <minimum>0.30</minimum>
</limit>
```

## 常用命令

### 只运行测试
```bash
mvn test -Dspring.profiles.active=dev
```

### 只生成报告（需要先运行测试）
```bash
mvn jacoco:report
```

### 检查覆盖率是否达标
```bash
mvn jacoco:check
```

### 运行特定测试类
```bash
mvn test -Dtest=StringUtilsTest -Dspring.profiles.active=dev
```

### 运行特定测试方法
```bash
mvn test -Dtest=StringUtilsTest#testIsEmpty -Dspring.profiles.active=dev
```

## 集成到CI/CD

### GitHub Actions示例
```yaml
- name: Run tests with coverage
  run: mvn test -Dspring.profiles.active=dev

- name: Generate coverage report
  run: mvn jacoco:report

- name: Upload coverage to Codecov
  uses: codecov/codecov-action@v3
  with:
    file: target/site/jacoco/jacoco.xml
```

### Jenkins示例
```groovy
stage('Test Coverage') {
    steps {
        sh 'mvn test -Dspring.profiles.active=dev'
        sh 'mvn jacoco:report'
        publishHTML([
            allowMissing: false,
            alwaysLinkToLastBuild: true,
            keepAll: true,
            reportDir: 'target/site/jacoco',
            reportFiles: 'index.html',
            reportName: 'JaCoCo Coverage Report'
        ])
    }
}
```

## 最佳实践

1. **定期运行覆盖率检查**
   - 在每次提交前运行测试覆盖率检查
   - 设置合理的覆盖率阈值

2. **关注关键业务逻辑**
   - 优先为核心业务逻辑编写测试
   - 确保重要的service和controller有足够的测试覆盖

3. **分析未覆盖代码**
   - 定期查看红色（未覆盖）的代码
   - 判断是否需要补充测试用例

4. **排除不必要的类**
   - 合理配置排除规则
   - 避免为配置类、模型类等编写无意义的测试

## 故障排查

### 常见问题

1. **测试运行失败**
   - 检查是否使用了正确的profile：`-Dspring.profiles.active=dev`
   - 确保数据库连接配置正确

2. **报告生成失败**
   - 确保测试先执行成功
   - 检查target目录是否有写权限

3. **覆盖率为0**
   - 检查include/exclude配置是否正确
   - 确保测试实际执行了业务代码

4. **浏览器无法打开报告**
   - 手动导航到`target/site/jacoco/index.html`
   - 使用文件协议打开：`file:///path/to/project/target/site/jacoco/index.html`

### 日志查看
```bash
# 查看详细的测试执行日志
mvn test -Dspring.profiles.active=dev -X

# 查看JaCoCo插件执行日志
mvn jacoco:report -X
```

## 示例测试用例

项目中已包含示例测试用例：
- `src/test/java/com/cosfo/mall/CosfoMallApplicationTests.java` - 应用启动测试
- `src/test/java/com/cosfo/mall/common/utils/StringUtilsTest.java` - 工具类测试示例

可以参考这些示例编写更多的测试用例。
