@echo off
chcp 65001 >nul
echo 开始执行测试覆盖率分析...

REM 清理之前的构建
echo 清理之前的构建...
call mvn clean

REM 运行测试并生成覆盖率报告
echo 运行测试并生成覆盖率报告...
call mvn test -Dspring.profiles.active=dev

REM 检查测试是否成功
if %errorlevel% equ 0 (
    echo 测试执行成功！
    
    REM 生成覆盖率报告
    echo 生成覆盖率报告...
    call mvn jacoco:report
    
    REM 检查报告是否生成成功
    if exist "target\site\jacoco\index.html" (
        echo 覆盖率报告生成成功！
        echo 报告位置: target\site\jacoco\index.html
        
        REM 打开浏览器查看报告
        start target\site\jacoco\index.html
    ) else (
        echo 覆盖率报告生成失败！
        pause
        exit /b 1
    )
) else (
    echo 测试执行失败！
    pause
    exit /b 1
)

echo 测试覆盖率分析完成！
pause
