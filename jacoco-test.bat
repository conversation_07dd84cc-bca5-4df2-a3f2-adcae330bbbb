@echo off
echo Starting JaCoCo test coverage analysis...

REM Clean previous build
echo Cleaning previous build...
call mvn clean

REM Run tests and generate coverage report
echo Running tests and generating coverage report...
echo Note: Running tests that do not depend on Spring context to avoid configuration issues
call mvn test -Dtest=StringUtilsTest

REM Check if tests were successful
if %errorlevel% equ 0 (
    echo Tests executed successfully!

    REM Generate coverage report
    echo Generating coverage report...
    call mvn jacoco:report

    REM Check if report was generated successfully
    if exist "target\site\jacoco\index.html" (
        echo Coverage report generated successfully!
        echo Report location: target\site\jacoco\index.html

        REM Open browser to view report
        start target\site\jacoco\index.html
    ) else (
        echo Coverage report generation failed!
        pause
        exit /b 1
    )
) else (
    echo Test execution failed!
    pause
    exit /b 1
)

echo Test coverage analysis completed!
echo For all tests use: mvn clean test jacoco:report
echo For specific test use: mvn clean test -Dtest=TestClassName jacoco:report
pause
