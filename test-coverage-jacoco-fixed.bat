@echo off
echo ========================================
echo JaCoCo 修复版覆盖率测试脚本
echo 解决Spring Boot + Dubbo覆盖率问题
echo ========================================

echo.
echo 步骤1：启用修复后的JaCoCo配置
echo 请手动编辑pom.xml：
echo 1. 注释掉OpenClover插件配置
echo 2. 取消注释JaCoCo插件配置
echo 3. 保存文件后按任意键继续...
pause

echo.
echo 步骤2：清理项目
mvn clean

echo.
echo 步骤3：编译项目
mvn compile test-compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码语法
    pause
    exit /b 1
)

echo.
echo 步骤4：运行JaCoCo覆盖率分析
echo 使用修复后的配置运行ActivityServiceTest...
mvn jacoco:prepare-agent test jacoco:report -Dtest=ActivityServiceTest -Dspring.profiles.active=wurth -DfailIfNoTests=false -Djacoco.agent.append=false

echo.
echo 步骤5：检查JaCoCo数据文件
if exist "target\jacoco.exec" (
    echo ✅ JaCoCo数据文件已生成
    dir "target\jacoco.exec"
    
    rem 检查文件大小
    for %%A in ("target\jacoco.exec") do (
        if %%~zA gtr 0 (
            echo ✅ JaCoCo数据文件大小: %%~zA 字节 ^(非空^)
        ) else (
            echo ❌ JaCoCo数据文件为空，覆盖率收集失败
        )
    )
) else (
    echo ❌ JaCoCo数据文件未生成
    echo 可能的原因：
    echo 1. JaCoCo代理未正确附加
    echo 2. 测试执行失败
    echo 3. Spring Boot上下文启动问题
)

echo.
echo 步骤6：检查覆盖率报告
if exist "target\site\jacoco\index.html" (
    echo ✅ JaCoCo覆盖率报告生成成功！
    echo 报告位置: target\site\jacoco\index.html
    
    echo.
    echo 步骤7：打开覆盖率报告
    start "" "target\site\jacoco\index.html"
    
    echo.
    echo 📊 JaCoCo覆盖率分析指导：
    echo 1. 检查ActivityServiceImpl类的覆盖率
    echo 2. 如果仍显示0%%，说明需要使用OpenClover
    echo 3. 红色：未覆盖的代码行
    echo 4. 绿色：已覆盖的代码行
    echo 5. 黄色：部分覆盖的分支
    
) else (
    echo ❌ JaCoCo覆盖率报告生成失败
    echo.
    echo 推荐解决方案：
    echo 1. 使用OpenClover替代JaCoCo
    echo 2. 运行: test-coverage-clover.bat
    echo 3. OpenClover对Spring Boot项目有更好的支持
)

echo.
echo ========================================
echo JaCoCo修复版测试完成
echo ========================================
pause
