#!/bin/bash

echo "========================================"
echo "JaCoCo 测试覆盖率报告生成工具"
echo "========================================"

echo "1. 清理项目..."
mvn clean

echo "2. 运行测试并生成覆盖率数据..."
echo "   注意：运行不依赖Spring上下文的测试以避免配置问题"
mvn test -Dtest=StringUtilsTest

# 检查测试是否成功
if [ $? -eq 0 ]; then
    echo "测试执行成功！"
    
    echo "3. 生成覆盖率报告..."
    mvn jacoco:report
    
    # 检查报告是否生成成功
    if [ -f "target/site/jacoco/index.html" ]; then
        echo "覆盖率报告生成成功！"
        echo "报告位置: target/site/jacoco/index.html"
        
        # 尝试打开浏览器查看报告
        if command -v xdg-open > /dev/null; then
            xdg-open target/site/jacoco/index.html
        elif command -v open > /dev/null; then
            open target/site/jacoco/index.html
        else
            echo "请手动打开浏览器查看报告: target/site/jacoco/index.html"
        fi
    else
        echo "覆盖率报告生成失败！"
        exit 1
    fi
else
    echo "测试执行失败！"
    exit 1
fi

echo "========================================"
echo "完成！"
echo "如需运行所有测试，请使用: mvn clean test jacoco:report"
echo "如需只运行特定测试，请使用: mvn clean test -Dtest=TestClassName jacoco:report"
echo "========================================"
