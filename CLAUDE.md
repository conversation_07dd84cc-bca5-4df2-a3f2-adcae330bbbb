# COSFO_MALL.md

本文档为 Claude Code (claude.ai/code) 在处理此COSFO商城系统代码库时提供指导。

## 项目概览

这是一个基于 Spring Boot 2.3.7 的SaaS商城管理系统，专为多租户电商业务设计，采用微服务架构，包含商品管理、订单管理、支付管理、库存管理、营销管理、商户管理等核心功能。项目支持多租户架构、分布式缓存、消息驱动的业务处理和微信生态集成。

## 技术栈

- **框架**: Spring Boot 2.3.7, MyBatis Plus 3.5.1, Dubbo 2.7.15
- **数据库**: MySQL 8.0, Redis (Redisson 3.16.4), 动态数据源支持
- **消息队列**: RocketMQ
- **配置中心**: Nacos
- **连接池**: Druid 1.2.9, HikariCP
- **工具**: FastJSON 1.2.83, Lombok, Hutool 5.7.22, MapStruct 1.5.3, PageHelper 1.4.1
- **缓存**: Caffeine 2.7.0, 本地缓存 + Redis分布式缓存
- **安全**: Spring Security, JWT, Apache Shiro 1.7.1
- **其他**: SchedulerX2定时任务, 七牛云存储, 汇付支付

## 核心架构

### 分层架构
```
src/main/java/com/cosfo/mall/
├── CosfoMallApplication.java        # 应用启动类
├── bill/                           # 账单管理模块
│   ├── controller/                 # 账单控制器
│   ├── service/                    # 账单服务
│   ├── mapper/                     # 数据访问层
│   └── model/                      # 账单模型
├── cache/                          # 缓存模块
│   ├── LocalCache.java             # 本地缓存注解
│   ├── LocalCacheProcessor.java    # 本地缓存处理器
│   └── LocalResponseLogAspect.java # 响应日志切面
├── common/                         # 公共模块
│   ├── config/                     # 配置类
│   │   ├── MybatisPlusConfig.java  # MyBatis Plus配置
│   │   ├── ChageeConfig.java       # 霸王茶姬配置
│   │   └── TenantProperties.java   # 租户配置
│   ├── constant/                   # 常量定义
│   ├── exception/                  # 异常处理
│   ├── mq/                         # 消息队列
│   ├── redission/                  # Redisson配置
│   ├── task/                       # 定时任务
│   └── utils/                      # 工具类
├── facade/                         # 门面层
│   ├── MarketFacade.java           # 商品门面
│   ├── PriceFacade.java            # 价格门面
│   ├── StockFacade.java            # 库存门面
│   ├── usercenter/                 # 用户中心门面
│   └── payment/                    # 支付门面
├── market/                         # 商品市场模块
│   ├── controller/                 # 商品控制器
│   ├── service/                    # 商品服务
│   ├── home/                       # 首页相关
│   └── model/                      # 商品模型
├── order/                          # 订单管理模块
│   ├── controller/                 # 订单控制器
│   ├── service/                    # 订单服务
│   ├── factory/                    # 订单工厂
│   └── executor/                   # 订单执行器
├── payment/                        # 支付管理模块
│   ├── controller/                 # 支付控制器
│   ├── service/                    # 支付服务
│   ├── strategy/                   # 支付策略
│   └── template/                   # 支付模板
├── merchant/                       # 商户管理模块
├── stock/                          # 库存管理模块
├── marketing/                      # 营销管理模块
├── tenant/                         # 租户管理模块
├── wechat/                         # 微信集成模块
├── system/                         # 系统管理模块
├── supplier/                       # 供应商管理模块
├── warehouse/                      # 仓库管理模块
└── openapi/                        # 开放API模块
```

### 关键模块
- **商品管理模块**: 支持商品CRUD、分类管理、价格管理、库存管理
- **订单管理模块**: 支持订单创建、支付、发货、售后全流程管理
- **支付管理模块**: 支持微信支付、汇付支付等多种支付方式
- **库存管理模块**: 支持实时库存、库存预占、库存释放
- **营销管理模块**: 支持优惠券、促销活动、会员营销
- **租户管理模块**: 支持多租户数据隔离、租户配置管理
- **微信集成模块**: 支持微信小程序、公众号、支付等功能

## 构建和测试命令

### Maven构建
注意，如果用户terminal使用的JDK版本不是1.8的话，可以尝试使用`sdk use java 8.0.432-zulu`类似的命令来切换到1.8版本。否则会编译异常

```bash
# 完整构建
mvn clean package

# 跳过测试构建
mvn clean package -DskipTests

# 运行测试 (使用dev配置文件)
mvn test -Dspring.profiles.active=dev

# 运行单个测试类
mvn test -Dtest=OrderServiceTest -Dspring.profiles.active=dev

# 运行单个测试方法
mvn test -Dtest=PaymentServiceTest#testPay -Dspring.profiles.active=dev
```

### Spring Boot运行
```bash
# 开发环境运行
mvn spring-boot:run -Dspring.profiles.active=dev

# 开发环境2运行
mvn spring-boot:run -Dspring.profiles.active=dev2

# 开发环境3运行
mvn spring-boot:run -Dspring.profiles.active=dev3

# 开发环境4运行
mvn spring-boot:run -Dspring.profiles.active=dev4

# 本地环境运行
mvn spring-boot:run -Dspring.profiles.active=local

# 生产环境运行
java -jar target/cosfo-mall-0.0.1-SNAPSHOT.jar --spring.profiles.active=pro
```

## 配置文件

- `src/main/resources/application.yml` - 主配置文件
- `src/main/resources/application-dev.yml` - 开发环境
- `src/main/resources/application-dev2.yml` - 开发环境2
- `src/main/resources/application-dev3.yml` - 开发环境3
- `src/main/resources/application-dev4.yml` - 开发环境4
- `src/main/resources/application-local.yml` - 本地环境
- `src/main/resources/application-pro.yml` - 生产环境
- `src/main/resources/application-qa.yml` - 测试环境
- `src/main/resources/logback-spring.xml` - 日志配置

## 开发规范

### 代码风格
- 使用Lombok减少样板代码
- 遵循阿里巴巴Java开发规范
- 统一使用FastJSON进行JSON序列化
- **日志规范**: 必须使用SLF4J日志框架（log.info(), log.error()等），禁止使用System.out.println()
- 使用MapStruct进行实体映射转换
- 使用MyBatis Plus进行数据库操作

### 测试要求
- 单元测试必须使用`-Dspring.profiles.active=dev`参数
- 测试类命名: *Test.java
- 测试方法命名: test*()
- 测试类需要使用`@SpringBootTest`注解

### 数据库规范
- 支持多数据源配置，主库(master)和离线库(offline)
- 使用HikariCP连接池，配置合理的连接池参数
- 使用`@DS`注解指定数据源
- 支持多租户数据隔离，使用tenant_id字段

## 常见任务

### 新增商品管理接口
1. 在`market/controller`包下创建或扩展控制器
2. 在`market/service`包下创建业务服务
3. 在`market/mapper`包下实现数据访问层
4. 在`facade`包下创建门面服务调用外部系统

### 新增订单处理功能
1. 在`order/service`中添加订单业务逻辑
2. 在`order/controller`中添加REST接口
3. 在订单相关的数据访问层添加数据操作方法
4. 配置相关的消息队列处理

### 支付功能集成
1. 在`payment/strategy`中实现支付策略
2. 在`payment/service`中添加支付业务逻辑
3. 使用支付模板模式处理不同支付渠道
4. 实现支付回调和状态同步

### 库存管理功能
1. 在`stock/service`中实现库存业务逻辑
2. 支持库存增减、批量查询
3. 使用Redisson分布式锁保证库存操作的原子性
4. 实现库存变更记录和监控

### 消息队列处理
1. 在`common/mq`包下创建消息消费者
2. 实现业务消息监听和处理
3. 配置消息消费和异常处理
4. 使用顺序消息保证数据一致性

## 依赖管理

项目使用大量内部二方库，主要依赖：
- `xianmu-common`: 仙木公共组件 1.1.15
- `xianmu-dubbo-support`: Dubbo支持组件 1.0.14
- `xianmu-log-support`: 日志支持组件 1.0.14
- `xianmu-rocketmq-support`: RocketMQ支持组件 1.2.0
- `xianmu-task-support`: 任务支持组件 1.0.5
- 各业务客户端: order-center-client, usercenter-client, item-center-client等

## 核心业务概念

### 商品类型
- **普通商品**: 基础商品类型
- **组合商品**: 由多个子商品组成的商品包
- **自营商品**: 平台自营的商品
- **代理商品**: 代理销售的商品

### 订单状态
- **下单中**: 订单创建中
- **待支付**: 等待用户支付
- **已支付**: 支付完成
- **已发货**: 商品已发货
- **已完成**: 订单完成
- **已取消**: 订单取消

### 支付方式
- **微信支付**: 支持小程序、H5、扫码支付
- **汇付支付**: 企业级支付解决方案
- **账户余额**: 用户账户余额支付

### 租户管理
- **多租户支持**: 支持多个租户独立管理商城
- **租户配置**: 每个租户可以有独立的配置
- **数据隔离**: 租户间数据完全隔离

## 部署说明

1. 配置Nacos连接信息和动态配置
2. 配置Redis、MySQL连接信息
3. 配置RocketMQ连接信息
4. 配置SchedulerX2定时任务
5. 使用`mvn clean package -DskipTests`打包
6. 使用`java -jar target/cosfo-mall-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev`运行

## 故障排查

- 查看应用日志文件 (通过logback-spring.xml配置的位置)
- 检查Nacos配置是否正确加载
- 验证各微服务客户端连接状态
- 检查RocketMQ消息消费情况
- 验证数据库连接池状态 (HikariCP监控)
- 检查Redis连接和缓存状态
- 验证定时任务执行状态

## 关键文件参考
- **应用入口**: `src/main/java/com/cosfo/mall/CosfoMallApplication.java`
- **订单控制器**: `src/main/java/com/cosfo/mall/order/controller/OrderController.java`
- **支付服务**: `src/main/java/com/cosfo/mall/payment/service/impl/PaymentServiceImpl.java`
- **商品首页控制器**: `src/main/java/com/cosfo/mall/market/home/<USER>/HomeController.java`
- **租户服务**: `src/main/java/com/cosfo/mall/tenant/service/impl/TenantServiceImpl.java`
- **MyBatis配置**: `src/main/java/com/cosfo/mall/common/config/MybatisPlusConfig.java`

## 注意事项
- 项目使用 FastJSON 而非默认的 Jackson 进行 JSON 序列化
- 支持多环境配置，默认使用dev环境
- 使用 MapStruct 进行实体映射，提高性能
- 支持多数据源，注意使用`@DS`注解指定数据源
- **重要**: 支付回调处理需要保证幂等性
- **重要**: 库存操作需要考虑并发控制，使用Redisson分布式锁
- **重要**: 订单状态变更使用状态机模式，保证状态流转正确性
- **重要**: 多租户数据隔离通过MyBatis Plus拦截器实现
- **重要**: 定时任务使用SchedulerX2，注意任务执行状态监控
- **重要**: 消息队列消费需要保证幂等性和异常处理

## 单元测试
- 单元测试用例入参涉及到租户Id（tenantId)请默认为"10000"
- 单元测试的@ActiveProfiles注解默认指定配置文件为"wurth"，比如@ActiveProfiles("wurth")
- 假如单元测试涉及这个命令相关的-Dspring.profiles.active=qa，请改成-Dspring.profiles.active=wurth
- 不要随意更改pom文件已有的二方包的版本号等信息，只要正常引入你自己所需包即可