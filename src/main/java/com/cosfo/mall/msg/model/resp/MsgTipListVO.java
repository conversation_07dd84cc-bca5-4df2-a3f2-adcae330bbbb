package com.cosfo.mall.msg.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息
 */
@Data
public class MsgTipListVO {
    /**
     * 公告id
     */
    private Long contentId;
    /**
     * 发布时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushTime;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容前n个字
     */
    private String content;
    /**
     * 消息类型,0=公告消息
     */
    private Integer contentType;
    /**
     * 读取状态0=未读,1=已读
     */
    private Integer readStatus;
}
