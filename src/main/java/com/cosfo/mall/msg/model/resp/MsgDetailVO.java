package com.cosfo.mall.msg.model.resp;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MsgDetailVO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 上一个 没有就是第一个
     */
    private Long beforeId;
    /**
     * 下一个 没有就是最后一个
     */
    private Long afterId;
    /**
     * 标题
     */
    private String title;
    /**
     * 公告内容
     */
    private String content;
    /**
     * 消息类型,0=公告消息
     */
    private Integer contentType;
    /**
     * 发布时间
     */
    private LocalDateTime pushTime;
    /**
     * 是否开启点赞 0=关闭；1开启
     */
    private Integer supportSwitch;

    /**
     * 阅读数
     */
    private Integer readAmount;

    /**
     * 点赞数
     */
    private Integer supportAmount;

    /**
     * 当前用户 是否已经点赞过  0=未点赞     1=已经点赞
     */
    private Integer supportStatus;
}
