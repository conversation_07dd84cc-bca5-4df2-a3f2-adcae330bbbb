package com.cosfo.mall.msg.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.msg.model.req.MessageTemplateQueryDTO;
import com.cosfo.mall.msg.service.MsgTemplateService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@RestController
@RequestMapping("message/template")
public class MessageTemplateController extends BaseController {
    @Resource
    private MsgTemplateService msgTemplateService;
    /**
     * 获取三方模版id
     */
    @PostMapping("getThirdTemplate")
    public CommonResult<String> getThirdTemplate(@RequestBody MessageTemplateQueryDTO dto) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return CommonResult.ok(msgTemplateService.getThirdTemplate(dto,contextInfoDTO));
    }
}
