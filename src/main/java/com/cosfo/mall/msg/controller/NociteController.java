package com.cosfo.mall.msg.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.PageResultDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.msg.model.req.MsgQueryDTO;
import com.cosfo.mall.msg.model.req.MsgReadLogDTO;
import com.cosfo.mall.msg.model.resp.MsgDetailVO;
import com.cosfo.mall.msg.model.resp.MsgTipListVO;
import com.cosfo.mall.msg.service.NoticeService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
/**
 * 消息
 */
@RestController
@RequestMapping("/msg")
public class NociteController extends BaseController {
    @Resource
    private NoticeService noticeService;
    /**
     * 点赞/取消点赞 公告
     */
    @PostMapping("support")
    public CommonResult<Void> support(@RequestBody MsgReadLogDTO req) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        noticeService.supportNotic(req,contextInfoDTO);
        return CommonResult.ok();
    }
    /**
     * 读取公告
     */
    @PostMapping("read")
    public CommonResult<MsgDetailVO> read(@RequestBody MsgReadLogDTO req) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return CommonResult.ok(noticeService.readNotic(req,contextInfoDTO));
    }
    /**
     * 公告列表
     */
    @PostMapping("page")
    public ResultDTO page(@RequestBody MsgQueryDTO dto) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        PageInfo<MsgTipListVO> vos = noticeService.pageNotice(dto, contextInfoDTO);
        return PageResultDTO.success(vos);
    }

    /**
     * 查询未读公告数
     */
    @PostMapping("unReadAmount")
    public CommonResult<Integer> unReadAmount() {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return CommonResult.ok(noticeService.noticUnReadAmount(contextInfoDTO));
    }
}
