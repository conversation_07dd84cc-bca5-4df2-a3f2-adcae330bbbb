package com.cosfo.mall.msg.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.msg.model.req.MsgQueryDTO;
import com.cosfo.mall.msg.model.req.MsgReadLogDTO;
import com.cosfo.mall.msg.model.resp.MsgDetailVO;
import com.cosfo.mall.msg.model.resp.MsgTipListVO;
import com.github.pagehelper.PageInfo;

public interface NoticeService {
    /**
     * 点赞或者取消点赞
     * @param req
     */
    void supportNotic(MsgReadLogDTO req, LoginContextInfoDTO contextInfoDTO);

    /**
     * 读取公告
     * @param req
     * @return
     */
    MsgDetailVO readNotic(MsgReadLogDTO req, LoginContextInfoDTO contextInfoDTO);


    /**
     * 公告列表
     * @param req
     * @return
     */
    PageInfo<MsgTipListVO> pageNotice(MsgQueryDTO req, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询未读公告数
     * @param contextInfoDTO
     * @return
     */
    Integer noticUnReadAmount(LoginContextInfoDTO contextInfoDTO);
}
