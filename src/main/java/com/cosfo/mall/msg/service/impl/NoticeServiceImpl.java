package com.cosfo.mall.msg.service.impl;

import com.cosfo.mall.facade.MessageServiceFacade;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.msg.model.req.MsgQueryDTO;
import com.cosfo.mall.msg.model.req.MsgReadLogDTO;
import com.cosfo.mall.msg.model.resp.MsgDetailVO;
import com.cosfo.mall.msg.model.resp.MsgTipListVO;
import com.cosfo.mall.msg.service.NoticeService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NoticeServiceImpl implements NoticeService {

    @Autowired
    private MessageServiceFacade messageServiceFacade;
    @Override
    public void supportNotic(MsgReadLogDTO dto, LoginContextInfoDTO contextInfoDTO) {
        messageServiceFacade.supportNotic(dto,contextInfoDTO);
    }

    @Override
    public MsgDetailVO readNotic(MsgReadLogDTO dto,LoginContextInfoDTO contextInfoDTO) {
       return messageServiceFacade.readNotic(dto,contextInfoDTO);
    }

    @Override
    public PageInfo<MsgTipListVO> pageNotice(MsgQueryDTO dto, LoginContextInfoDTO contextInfoDTO) {
        return messageServiceFacade.pageNotice(dto,contextInfoDTO);
    }

    @Override
    public Integer noticUnReadAmount(LoginContextInfoDTO contextInfoDTO) {
        return messageServiceFacade.noticUnReadAmount(contextInfoDTO);
    }
}
