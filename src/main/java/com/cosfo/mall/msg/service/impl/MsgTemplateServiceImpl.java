package com.cosfo.mall.msg.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.mall.facade.OmsServiceFacade;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.msg.model.req.MessageTemplateQueryDTO;
import com.cosfo.mall.msg.model.resp.MsgSceneTenantVO;
import com.cosfo.mall.msg.service.MsgTemplateService;
import com.cosfo.oms.client.common.MsgSceneTenantMappingEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class MsgTemplateServiceImpl implements MsgTemplateService {

    @Autowired
    private OmsServiceFacade omsServiceFacade;

    @Override
    public String getThirdTemplate(MessageTemplateQueryDTO dto, LoginContextInfoDTO contextInfoDTO) {
        dto.setTemplateType(ObjectUtil.isEmpty(dto.getTemplateType())? MsgSceneTenantMappingEnum.TemplateType.WECHAT.getValue():dto.getTemplateType());
        dto.setSceneId(ObjectUtil.isEmpty(dto.getSceneId())?1L:dto.getSceneId());
        List<MsgSceneTenantVO> msgSceneTenantVOS = omsServiceFacade.listSceneTenantByTenantId(dto, contextInfoDTO.getTenantId());
        if(!CollectionUtil.isEmpty(msgSceneTenantVOS)){
            Optional<MsgSceneTenantVO> any = msgSceneTenantVOS.stream().findAny();
            if(any.isPresent()){
                return any.get().getThirdTemplateId();
            }
        }
        return null;
    }
}
