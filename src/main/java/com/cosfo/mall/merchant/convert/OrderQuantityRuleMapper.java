package com.cosfo.mall.merchant.convert;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.merchant.model.dto.OrderQuantityRuleJsonDTO;
import com.cosfo.mall.merchant.model.po.MerchantOrderQuantityRule;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderQuantityRuleMapper {

    OrderQuantityRuleMapper INSTANCE = Mappers.getMapper(OrderQuantityRuleMapper.class);


    /**
     * ruleList to voList
     * @param ruleList
     * @return
     */
    List<OrderQuantityRuleVO> toRuleVOList(List<MerchantOrderQuantityRule> ruleList);




    /**
     * rule to vo
     * @param rule
     * @return
     */
    OrderQuantityRuleVO ruleToVo(MerchantOrderQuantityRule rule);


    /**
     * 处理vo json
     * @param ruleVO
     */
    @AfterMapping
    default void handleRuleJson(@MappingTarget OrderQuantityRuleVO ruleVO) {
        String rule = ruleVO.getRule();
        OrderQuantityRuleJsonDTO json = JSON.parseObject(rule, OrderQuantityRuleJsonDTO.class);
        ruleVO.setOperator(json.getOp());
        ruleVO.setRuleDetailList(json.getRule());
    }

}
