package com.cosfo.mall.merchant.convert;

import com.cosfo.mall.merchant.model.dto.DeliveryRuleInfoDTO;
import com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.mall.merchant.model.vo.DeliveryFeeSnapshotVO;
import com.cosfo.mall.order.model.po.OrderDeliveryFeeSnapshot;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: monna.chen
 * @Date: 2023/7/18 15:50
 * @Description:
 */
@Mapper
public interface MerchantDeliveryConvert {

    MerchantDeliveryConvert INSTANCE = Mappers.getMapper(MerchantDeliveryConvert.class);

    @Mapping(source = "deliveryFee", target = "orderDeliveryFee")
    @Mapping(source = "ruleList", target = "ruleInfo")
    @Mapping(source = "hitRuleList", target = "hitRuleFee")
    @Mapping(target = "orderInfo",expression = "java(com.alibaba.fastjson.JSON.toJSONString(vo.getOrderInfo()))")
    OrderDeliveryFeeSnapshot convert2Entity(DeliveryFeeSnapshotVO vo);


    @Mapping(source = "id",target = "ruleId")
    @Mapping(source = "type",target = "warehouseType")
    @Mapping(source = "freeDeliveryType",target = "deliveryType")
    @Mapping(source = "hitAreas",target = "hitAreaList")
    DeliveryRuleInfoDTO convert2Dto(MerchantDeliveryFeeRule feeRule);
}
