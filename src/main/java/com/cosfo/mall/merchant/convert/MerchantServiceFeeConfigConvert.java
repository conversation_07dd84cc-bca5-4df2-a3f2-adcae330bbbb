package com.cosfo.mall.merchant.convert;

import com.cosfo.mall.merchant.model.dto.MerchantServiceFeeConfigDTO;
import com.cosfo.mall.merchant.model.vo.MerchantServiceFeeConfigVO;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-08-29
 **/
public class MerchantServiceFeeConfigConvert {

    public static MerchantServiceFeeConfigVO convert(MerchantServiceFeeConfigDTO dto) {
        MerchantServiceFeeConfigVO vo = new MerchantServiceFeeConfigVO();
        vo.setWechatFeeRate(dto.getWechatFeeRate());
        vo.setAlipayFeeRate(dto.getAlipayFeeRate());
        return vo;
    }
}
