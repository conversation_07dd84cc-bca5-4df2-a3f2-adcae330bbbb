package com.cosfo.mall.merchant.convert;

import com.cosfo.mall.merchant.model.po.MerchantContact;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-29
 * @Description:
 */
@Mapper
public interface MerchantContactMapperConvert {

    MerchantContactMapperConvert INSTANCE = Mappers.getMapper(MerchantContactMapperConvert.class);

    /**
     * resp转contact
     * @param merchantContactResultResp
     * @return
     */
    MerchantContact respToContact(MerchantContactResultResp merchantContactResultResp);

    /**
     * respList转contactList
     * @param merchantContacts
     * @return
     */
    List<MerchantContact> respListToContactList(List<MerchantContactResultResp> merchantContacts);
}
