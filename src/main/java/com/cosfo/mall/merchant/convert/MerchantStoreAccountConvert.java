package com.cosfo.mall.merchant.convert;

import com.cosfo.mall.common.constants.MerchantStoreAccountSortLevelEnum;
import com.cosfo.mall.common.context.MerchantStoreStatusEnum;
import com.cosfo.mall.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.po.MerchantStore;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantStoreAccountConvert {

    MerchantStoreAccountConvert INSTANCE = Mappers.getMapper(MerchantStoreAccountConvert.class);

    PageInfo<MerchantStoreAccountDTO> respToDTOPage(PageInfo<MerchantStoreAccountPageResp> respPageInfo, @Context Map<Long, MerchantStore> storeMap, @Context Map<Long, MerchantAddress> addressMap, @Context Long currentLoginAccount);

    MerchantStoreAccountDTO respToDTO(MerchantStoreAccountPageResp resp, @Context Map<Long, MerchantStore> storeMap, @Context Map<Long, MerchantAddress> addressMap, @Context Long currentLoginAccount);

    @AfterMapping
    default void respAfterMap(@MappingTarget MerchantStoreAccountDTO merchantStoreAccountDTO, @Context Map<Long, MerchantStore> storeMap, @Context Map<Long, MerchantAddress> addressMap, @Context Long currentLoginAccount) {

        MerchantStore merchantStore = storeMap.get(merchantStoreAccountDTO.getStoreId());
        if (merchantStore != null) {
            merchantStoreAccountDTO.setStoreName(merchantStore.getStoreName());
            merchantStoreAccountDTO.setStoreId(merchantStore.getId());
            merchantStoreAccountDTO.setStoreStatus(merchantStore.getStatus());
            merchantStoreAccountDTO.setStoreAuditRemark(merchantStore.getAuditRemark());
            merchantStoreAccountDTO.setSortLevel(Objects.equals(merchantStoreAccountDTO.getId(), currentLoginAccount) ? MerchantStoreAccountSortLevelEnum.CURRENT_LOGIN_ACCOUNT.getLevel() : Objects.equals(merchantStore.getStatus(), MerchantStoreStatusEnum.AUDIT_SUCCESS.getStatus()) ? MerchantStoreAccountSortLevelEnum.USABLE_LOGIN_ACCOUNTS.getLevel() : MerchantStoreAccountSortLevelEnum.OTHER.getLevel());

        }

        MerchantAddress merchantAddress = addressMap.get(merchantStoreAccountDTO.getStoreId());
        if (merchantAddress != null) {
            merchantStoreAccountDTO.setProvince(merchantAddress.getProvince());
            merchantStoreAccountDTO.setCity(merchantAddress.getCity());
            merchantStoreAccountDTO.setArea(merchantAddress.getArea());
            merchantStoreAccountDTO.setAddress(merchantAddress.getAddress());
            merchantStoreAccountDTO.setPoiNote(merchantAddress.getPoiNote());
            merchantStoreAccountDTO.setHouseNumber(merchantAddress.getHouseNumber());
        }


    }
}
