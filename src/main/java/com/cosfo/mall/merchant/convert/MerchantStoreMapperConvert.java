package com.cosfo.mall.merchant.convert;

import com.cosfo.mall.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.mall.merchant.model.po.MerchantStore;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-05-29
 * @Description:
 */
@Mapper
public interface MerchantStoreMapperConvert {

    MerchantStoreMapperConvert INSTANCE = Mappers.getMapper(MerchantStoreMapperConvert.class);

    /**
     * dto转为req
     * @param storeDTO
     * @return
     */
    MerchantStoreDomainCommandReq dtoToCommandReq(MerchantStoreDTO storeDTO);

    /**
     * dto转为storeCommandReq
     * @param storeDTO
     * @return
     */
    MerchantStoreCommandReq dtoToStoreCommandReq(MerchantStoreDTO storeDTO);

    /**
     * rpc返回数据转换为实体类
     * @param merchantStoreResultResp
     * @return
     */
    MerchantStore RespToStore(MerchantStoreResultResp merchantStoreResultResp);
}
