package com.cosfo.mall.merchant.convert;

import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-05-29
 * @Description:
 */
@Mapper
public interface MerchantAddressMapperConvert {

    MerchantAddressMapperConvert INSTANCE = Mappers.getMapper(MerchantAddressMapperConvert.class);

    /**
     * resp转为address
     * @param resp
     * @return
     */
    MerchantAddress respToAddress(MerchantAddressResultResp resp);


    MerchantAddressDTO address2Dto(MerchantAddress merchantAddress);
}
