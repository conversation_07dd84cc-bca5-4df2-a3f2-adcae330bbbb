package com.cosfo.mall.merchant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.merchant.mapper.MerchantStoreBalanceChangeRecordMapper;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryConditionDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.repository.MerchantStoreBalanceChangeRecordRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 门店余额变动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class MerchantStoreBalanceChangeRecordRepositoryImpl extends ServiceImpl<MerchantStoreBalanceChangeRecordMapper, MerchantStoreBalanceChangeRecord> implements MerchantStoreBalanceChangeRecordRepository {

    @Resource
    private MerchantStoreBalanceChangeRecordMapper merchantStoreBalanceChangeRecordMapper;

    @Override
    public Page<MerchantStoreBalanceChangeRecord> pageListByCondition(MerchantStoreBalanceChangeQueryDTO queryDTO) {
        LambdaQueryWrapper<MerchantStoreBalanceChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantStoreBalanceChangeRecord::getTenantId, queryDTO.getTenantId());
        queryWrapper.eq(MerchantStoreBalanceChangeRecord::getStoreId, queryDTO.getStoreId());
        queryWrapper.eq(MerchantStoreBalanceChangeRecord::getAccountType, queryDTO.getAccountType());
        queryWrapper.eq(Objects.nonNull(queryDTO.getType()), MerchantStoreBalanceChangeRecord::getType, queryDTO.getType());
        queryWrapper.between(StringUtils.isNotBlank(queryDTO.getTradeStartTime()) && StringUtils.isNotBlank(queryDTO.getTradeEndTime()), MerchantStoreBalanceChangeRecord::getCreateTime, queryDTO.getTradeStartTime(), queryDTO.getTradeEndTime());
        queryWrapper.orderByDesc(MerchantStoreBalanceChangeRecord::getId);
        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), queryWrapper);
    }

    @Override
    public List<MerchantStoreBalanceChangeRecord> queryByCondition(MerchantStoreBalanceChangeQueryConditionDTO conditionDTO) {
        LambdaQueryWrapper<MerchantStoreBalanceChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(conditionDTO.getTenantId()), MerchantStoreBalanceChangeRecord::getTenantId, conditionDTO.getTenantId());
        queryWrapper.eq(Objects.nonNull(conditionDTO.getOrderNo()), MerchantStoreBalanceChangeRecord::getAssociatedOrderNo, conditionDTO.getOrderNo());
        queryWrapper.eq(Objects.nonNull(conditionDTO.getType()), MerchantStoreBalanceChangeRecord::getType, conditionDTO.getType());
        return list(queryWrapper);
    }

    @Override
    public MerchantStoreBalanceChangeRecord queryByUniqueKey(String afterSaleOrderNo, Long tenantId, Long storeId) {
        LambdaQueryWrapper<MerchantStoreBalanceChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), MerchantStoreBalanceChangeRecord::getTenantId, tenantId);
        queryWrapper.eq(Objects.nonNull(storeId), MerchantStoreBalanceChangeRecord::getStoreId, storeId);
        queryWrapper.eq(MerchantStoreBalanceChangeRecord::getAssociatedOrderNo, afterSaleOrderNo);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    public List<MerchantStoreBalanceChangeRecord> queryByAssociatedOrderNos(List<String> associatedOrderNos) {
        if (associatedOrderNos.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantStoreBalanceChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MerchantStoreBalanceChangeRecord::getAssociatedOrderNo, associatedOrderNos);
        return list(queryWrapper);
    }
}
