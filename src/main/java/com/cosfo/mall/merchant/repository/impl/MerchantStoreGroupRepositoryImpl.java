//package com.cosfo.mall.merchant.repository.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.cosfo.mall.common.constants.MerchantStoreGroupTypeEnum;
//import com.cosfo.mall.merchant.mapper.MerchantStoreGroupMapper;
//import com.cosfo.mall.merchant.model.po.MerchantStoreGroup;
//import com.cosfo.mall.merchant.repository.MerchantStoreGroupRepository;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <p>
// * 门店分组 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2022-11-14
// * 请通过用户中心rpc调用
// */
//@Deprecated
//@Service
//public class MerchantStoreGroupRepositoryImpl extends ServiceImpl<MerchantStoreGroupMapper, MerchantStoreGroup> implements MerchantStoreGroupRepository {
////    @Resource
////    private MerchantStoreGroupMapper merchantStoreGroupMapper;
//
////    @Override
////    public MerchantStoreGroup queryDefaultGroup(Long tenantId) {
////        LambdaQueryWrapper<MerchantStoreGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
////        lambdaQueryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
////        lambdaQueryWrapper.eq(MerchantStoreGroup::getType, MerchantStoreGroupTypeEnum.DEFAULT.getType());
////        return merchantStoreGroupMapper.selectOne(lambdaQueryWrapper);
////    }
//}
