//package com.cosfo.mall.merchant.repository;
//
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.cosfo.mall.merchant.model.po.MerchantStoreGroup;
//
//
///**
// * <p>
// * 门店分组 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2022-11-14
// */
//public interface MerchantStoreGroupRepository extends IService<MerchantStoreGroup> {
//
//
////    /**
////     * 查询默认分组
////     * @param tenantId
////     * @return
////     */
////    MerchantStoreGroup queryDefaultGroup(Long tenantId);
//}
