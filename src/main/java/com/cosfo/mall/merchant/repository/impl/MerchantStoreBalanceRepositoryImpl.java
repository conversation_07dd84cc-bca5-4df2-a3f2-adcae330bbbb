package com.cosfo.mall.merchant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.merchant.mapper.MerchantStoreBalanceMapper;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.repository.MerchantStoreBalanceRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 门店余额表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class MerchantStoreBalanceRepositoryImpl extends ServiceImpl<MerchantStoreBalanceMapper, MerchantStoreBalance> implements MerchantStoreBalanceRepository {

    @Resource
    private MerchantStoreBalanceMapper merchantStoreBalanceMapper;

    @Override
    public int decreaseBalance(Long id, BigDecimal changeBalance) {
        return merchantStoreBalanceMapper.decreaseBalance(id, changeBalance);
    }

    @Override
    public int freezeBalance(Long id, BigDecimal changeBalance) {
        return merchantStoreBalanceMapper.freezeBalance(id, changeBalance);
    }

    @Override
    public int decreaseFreezeBalance(Long id, BigDecimal changeBalance) {
        return merchantStoreBalanceMapper.decreaseFreezeBalance(id, changeBalance);
    }

    @Override
    public int increaseBalance(Long id, BigDecimal changeBalance) {
        return merchantStoreBalanceMapper.increaseBalance(id, changeBalance);
    }

    @Override
    public MerchantStoreBalance queryCashAccountByStoreId(Long tenantId, Long storeId) {
        LambdaQueryWrapper<MerchantStoreBalance> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreBalance::getTenantId, tenantId);
        query.eq(MerchantStoreBalance::getStoreId, storeId);
        query.eq(MerchantStoreBalance::getAccountType, MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType());
        return merchantStoreBalanceMapper.selectOne(query);
    }

    @Override
    public List<MerchantStoreBalance> queryNonCashAccountByStoreId(Long tenantId, Long storeId) {
        LambdaQueryWrapper<MerchantStoreBalance> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreBalance::getTenantId, tenantId);
        query.eq(MerchantStoreBalance::getStoreId, storeId);
        query.eq(MerchantStoreBalance::getAccountType, MerchantStoreBalanceEnums.AccountTypeEnum.NON_CASH.getType());
        query.orderByAsc(MerchantStoreBalance::getId);
        return merchantStoreBalanceMapper.selectList(query);
    }

    @Override
    public List<MerchantStoreBalance> queryByStoreId(Long tenantId, Long storeId) {
        LambdaQueryWrapper<MerchantStoreBalance> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreBalance::getTenantId, tenantId);
        query.eq(MerchantStoreBalance::getStoreId, storeId);
        return merchantStoreBalanceMapper.selectList(query);
    }

    @Override
    public MerchantStoreBalance queryByStoreAndFundAccountId(Long tenantId, Long storeId, Long fundAccountId) {
        LambdaQueryWrapper<MerchantStoreBalance> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreBalance::getTenantId, tenantId);
        query.eq(MerchantStoreBalance::getStoreId, storeId);
        if (fundAccountId == null) {
            query.isNull(MerchantStoreBalance::getFundAccountId);
        } else {
            query.eq(MerchantStoreBalance::getFundAccountId, fundAccountId);
        }
        return merchantStoreBalanceMapper.selectOne(query);
    }

    @Override
    public List<MerchantStoreBalance> queryNonCashAccountByStoreIdForUpdate(Long tenantId, Long storeId) {
        return merchantStoreBalanceMapper.queryNonCashAccountByStoreIdForUpdate(tenantId, storeId);
    }
}
