package com.cosfo.mall.merchant.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryConditionDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;

import java.util.List;

/**
 * <p>
 * 门店余额变动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface MerchantStoreBalanceChangeRecordRepository extends IService<MerchantStoreBalanceChangeRecord> {

    /**
     * 根据条件查询
     * @param queryDTO
     * @return
     */
    Page<MerchantStoreBalanceChangeRecord> pageListByCondition(MerchantStoreBalanceChangeQueryDTO queryDTO);

    /**
     * 根据条件查询
     *
     * @param conditionDTO
     * @return
     */
    List<MerchantStoreBalanceChangeRecord> queryByCondition(MerchantStoreBalanceChangeQueryConditionDTO conditionDTO);

    /**
     * 根据业务幂等ID查询
     *
     * @param afterSaleOrderNo
     * @param tenantId
     * @param storeId
     * @return
     */
    MerchantStoreBalanceChangeRecord queryByUniqueKey(String afterSaleOrderNo, Long tenantId, Long storeId);

    /**
     * 根据关联订单号查询
     *
     * @param associatedOrderNos
     * @return
     */
    List<MerchantStoreBalanceChangeRecord> queryByAssociatedOrderNos(List<String> associatedOrderNos);
}
