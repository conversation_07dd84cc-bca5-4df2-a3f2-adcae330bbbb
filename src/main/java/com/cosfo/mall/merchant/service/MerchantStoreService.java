package com.cosfo.mall.merchant.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.*;
import com.cosfo.mall.merchant.model.po.MerchantStore;
import com.cosfo.mall.merchant.model.vo.LoginVO;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/21 15:20
 */
public interface MerchantStoreService {

    /**
     * 登录
     * @param loginDto
     * @return
     */
//    ResultDTO login(LoginDTO loginDto);

    /**
     * 登录
     * @param loginDto
     * @return
     */
    ResultDTO newLogin(LoginDTO loginDto);

    /**
     * 发送验证码
     * @param phone
     * @param tenantId
     * @return
     */
    ResultDTO sendCode(String phone, Long tenantId);

    /**
     * 校验验证码
     * @param phone
     * @param code
     * @return
     */
    Boolean examineCode(String phone, String code);

    /**
     * 提交用户信息
     * @param merchantStoreDTO
     * @return
     */
    ResultDTO submitStoreInfo(MerchantStoreDTO merchantStoreDTO);

    /**
     * 更新用户信息
     * @param merchantStoreDTO
     * @return
     */
    ResultDTO resubmitStoreInfo(MerchantStoreDTO merchantStoreDTO);

    /**
     * 校验用户是否已注册
     *
     * @param merchantStoreDTO  用户门店信息
     * @return
     */
    ResultDTO<Boolean> checkRegistered(MerchantStoreDTO merchantStoreDTO);

    /**
     * 校验用户是否已注册
     *
     * @param merchantStoreDTO 用户门店信息
     * @param querySessionKey  是否查询sessionKey信息
     * @return
     */
    ResultDTO<Boolean> checkRegistered(MerchantStoreDTO merchantStoreDTO, Boolean querySessionKey);

    /**
     * 查询门店信息
     *
     * @param storeId
     * @return
     */
    MerchantStoreDTO queryStoreDtoInfo(Long storeId);

    /**
     * 查询门店信息
     *
     * @param storeId
     * @return
     */
    MerchantStore queryStoreInfo(Long storeId);

    /**
     * 批量查询门店信息
     *
     * @param storeIds
     * @return
     */
    Map<Long, MerchantStore> queryStoreInfoMap(Set<Long> storeIds);

    /**
     * 获取用户账期相关信息
     *
     * @param contextInfoDTO
     * @return
     */
    ResultDTO getBillInfo(LoginContextInfoDTO contextInfoDTO);


    // TODO:[zhoujiachen]
    Boolean newLoginOut(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 切换门店
     * @param id
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<LoginSuccessReturnDTO> newChangeStore(Long id, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 验证码登录
     * @param smsLoginDTO
     * @return
     */
    ResultDTO<LoginVO> smsCodeLogin(SmsLoginDTO smsLoginDTO);


    /**
     * 用户名密码
     * @param smsLoginDTO
     * @return
     */
    ResultDTO<LoginVO> usernamePasswordLogin(UsernameLoginDTO usernameLoginDTO);

    /**
     * 新登录方法
     *
     * @param appId
     * @param weChatCode
     * @param phone
     * @param H5Request
     * @param h5TenantId H5从token中解析出的租户Id
     * @param loginVO
     * @return 返回登录信息
     */
    LoginVO loginV2(String appId, String weChatCode, String phone, Boolean H5Request, Long h5TenantId, LoginVO loginVO);

    /**
     * 返回门店下单有效期，是临期才返回
     * @param storeId
     * @return
     */
    String getPlaceOrderPermissionExpiryTime( Long storeId);


    boolean verifyUsernameAndPhoneMatch(UsernameQueryDTO usernameQueryDTO);

    ResultDTO<Void> examineCodeAndUpdatePassword(SendCodeDTO sendCodeDTO);
}
