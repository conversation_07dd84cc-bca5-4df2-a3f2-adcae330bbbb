package com.cosfo.mall.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.DefaultFlagEnum;
import com.cosfo.mall.common.constants.MerchantAddressStatusEnum;
import com.cosfo.mall.common.model.dto.CommonLocationCityDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.service.AreaService;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantAddressFacade;
import com.cosfo.mall.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.po.MerchantContact;
import com.cosfo.mall.merchant.model.vo.MerchantAddressVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.merchant.service.MerchantContactService;
import com.google.common.collect.Lists;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@Service
public class MerchantAddressServiceImpl implements MerchantAddressService {
//    @Resource
//    private MerchantAddressMapper merchantAddressMapper;
//    @Resource
//    private MerchantContactMapper merchantContactMapper;
    @Resource
    private MerchantContactService merchantContactService;
    @Resource
    private AreaService areaService;
    @Resource
    private UserCenterMerchantAddressFacade userCenterMerchantAddressFacade;

    @Override
    public ResultDTO getAddressInfo(LoginContextInfoDTO loginContextInfoDTO) {
        // 获取商户信息
//        MerchantAddress merchantAddress = merchantAddressMapper.selectByStoreId(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId());
        MerchantAddress merchantAddress = selectByStoreId(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId());
        List<MerchantContact> merchantContacts = merchantContactService.queryMerchantContact(loginContextInfoDTO.getTenantId(), merchantAddress.getId());
        List<MerchantContact> contacts = merchantContacts.stream().map(merchantContact -> {
            MerchantAddressVO merchantAddressVO = new MerchantAddressVO();
            BeanUtils.copyProperties(merchantContact, merchantAddressVO);
            return merchantContact;
        }).collect(Collectors.toList());

        MerchantAddressVO merchantAddressVO = new MerchantAddressVO();
        BeanUtils.copyProperties(merchantAddress, merchantAddressVO);
        merchantAddressVO.setContacts(contacts);
        return ResultDTO.success(merchantAddressVO);
    }

    @Override
    public MerchantAddress queryDefaultAddress(Long storeId, Long tenantId) {
        AssertCheckParams.notNull(storeId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "门店Id不能为空");
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
//        MerchantAddress merchantAddress = merchantAddressMapper.selectByStoreId(storeId, tenantId);
        MerchantAddress merchantAddress = selectByStoreId(storeId, tenantId);
        return merchantAddress;
    }

    @Override
    public MerchantAddressDTO queryDefaultAddressDTO(Long storeId, Long tenantId) {
        MerchantAddress merchantAddress = queryDefaultAddress(storeId, tenantId);
        MerchantAddressDTO merchantAddressDTO = new MerchantAddressDTO();
        BeanUtils.copyProperties(merchantAddress, merchantAddressDTO);
        CommonLocationCityDTO commonLocationCityDTO = areaService.selectByCityName(merchantAddress.getCity());
        if (Objects.isNull(commonLocationCityDTO)) {
            throw new BizException("您的下单地址暂时不支持配送，请更换地址");
        }
        merchantAddressDTO.setCityId(commonLocationCityDTO.getId());
        return merchantAddressDTO;
    }

    @Override
    public MerchantAddressVO queryMerchantAddress(Long storeId, Long tenantId, Long merchantAddressId, Long merchantContactId) {
        AssertCheckParams.notNull(merchantAddressId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "地址Id不能为空");
        AssertCheckParams.notNull(merchantContactId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "联系人Id不能为空");
        MerchantAddress merchantAddress = selectByPrimaryKey(merchantAddressId, tenantId);
        MerchantContact merchantContact = merchantContactService.selectByIdAndTenantId(tenantId, merchantContactId);
        AssertCheckParams.notNull(merchantAddress, ResultDTOEnum.PARAMETER_MISSING.getCode(), "地址信息不能为空");
        AssertCheckParams.notNull(merchantContact, ResultDTOEnum.PARAMETER_MISSING.getCode(), "联系人信息不能为空");
        MerchantAddressVO merchantAddressVO = new MerchantAddressVO();
        BeanUtils.copyProperties(merchantAddress, merchantAddressVO);
        if (!StringUtils.isEmpty(merchantAddress.getHouseNumber())) {
            merchantAddressVO.setAddress(merchantAddress.getAddress().contains(merchantAddress.getHouseNumber()) ? merchantAddress.getAddress() : merchantAddress.getAddress() + merchantAddress.getHouseNumber());
        }

        merchantAddressVO.setPhone(merchantContact.getPhone());
        merchantAddressVO.setName(merchantContact.getName());
        return merchantAddressVO;
    }

    @Override
    public ResultDTO updateAddress(MerchantAddressDTO addressDTO) {
        if (!StringUtils.isAddress(addressDTO.getAddress()) || !StringUtils.isAddress(addressDTO.getProvince()) || !StringUtils.isAddress(addressDTO.getCity())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_ADDRESS_ERROR);
        }
        if (!StringUtils.isEmpty(addressDTO.getHouseNumber()) && !StringUtils.isHouseNumber(addressDTO.getHouseNumber())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_HOUSE_NUMBER_ERROR);
        }
        LoginContextInfoDTO loginContextInfoDTO = ThreadTokenHolder.getLoginContextInfoDTO();
        Long tenantId = Optional.ofNullable(loginContextInfoDTO).map(LoginContextInfoDTO::getTenantId).orElse(null);
        if (Objects.isNull(tenantId)) {
            return ResultDTO.fail(ResultDTOEnum.TENANT_NOT_FOUND);
        }
        Long id = addressDTO.getId();
        MerchantAddress address = selectByPrimaryKey(id, tenantId);
        if (Objects.isNull(address)) {
            return ResultDTO.fail(ResultDTOEnum.ADDRESS_INFO_NOT_FOUND);
        }
        MerchantAddressCommandReq addressCommandReq = new MerchantAddressCommandReq();
        addressCommandReq.setId(address.getId());
        addressCommandReq.setStoreId(address.getStoreId());
        addressCommandReq.setTenantId(address.getTenantId());
        addressCommandReq.setProvince(addressDTO.getProvince());
        addressCommandReq.setCity(addressDTO.getCity());
        addressCommandReq.setArea(addressDTO.getArea());
        addressCommandReq.setAddress(addressDTO.getAddress());
        addressCommandReq.setHouseNumber(addressDTO.getHouseNumber());
        addressCommandReq.setPoiNote(addressDTO.getPoiNote());
//        merchantAddressMapper.updateByPrimaryKey(address);
        Boolean update = userCenterMerchantAddressFacade.update(addressCommandReq);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }
        return ResultDTO.success();
    }

    @Override
    public MerchantAddress selectByPrimaryKey(Long id, Long tenantId) {
        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
        merchantAddressQueryReq.setId(id);
        merchantAddressQueryReq.setTenantId(tenantId);
        merchantAddressQueryReq.setDefaultFlag(DefaultFlagEnum.IS_DEFAULT.getCode());
        merchantAddressQueryReq.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(merchantAddressQueryReq);
        if (CollectionUtil.isNotEmpty(merchantAddressList)) {
            MerchantAddressResultResp merchantAddressResultResp = merchantAddressList.get(NumberConstant.ZERO);
            return MerchantAddressMapperConvert.INSTANCE.respToAddress(merchantAddressResultResp);
        }
        throw new BizException("门店地址信息不存在");
    }

    @Override
    public MerchantAddress selectByStoreId(Long storeId, Long tenantId) {
        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
        merchantAddressQueryReq.setStoreId(storeId);
        merchantAddressQueryReq.setTenantId(tenantId);
        merchantAddressQueryReq.setDefaultFlag(DefaultFlagEnum.IS_DEFAULT.getCode());
        merchantAddressQueryReq.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(merchantAddressQueryReq);
        if (CollectionUtil.isNotEmpty(merchantAddressList)) {
            MerchantAddressResultResp merchantAddressResultResp = merchantAddressList.get(NumberConstant.ZERO);
            return MerchantAddressMapperConvert.INSTANCE.respToAddress(merchantAddressResultResp);
        }
        throw new BizException("门店地址信息不存在");
    }

    @Override
    public Map<Long, MerchantAddress> batchQueryByStoreIds(Set<Long> storeIds, Long tenantId) {
        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
        merchantAddressQueryReq.setStoreIdList(Lists.newArrayList(storeIds));
        merchantAddressQueryReq.setTenantId(tenantId);
        merchantAddressQueryReq.setDefaultFlag(DefaultFlagEnum.IS_DEFAULT.getCode());
        merchantAddressQueryReq.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(merchantAddressQueryReq);
        if (CollectionUtils.isEmpty(merchantAddressList)) {
            return Collections.emptyMap();
        }
        return merchantAddressList.stream().collect(Collectors.toMap(MerchantAddressResultResp::getStoreId, MerchantAddressMapperConvert.INSTANCE::respToAddress));
    }
}
