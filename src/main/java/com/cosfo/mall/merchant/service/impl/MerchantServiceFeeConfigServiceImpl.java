package com.cosfo.mall.merchant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.context.MerchantServiceFeeConfigEnum;
import com.cosfo.mall.merchant.model.dto.MerchantServiceFeeConfigDTO;
import com.cosfo.mall.merchant.model.po.MerchantServiceFeeConfig;
import com.cosfo.mall.merchant.repository.MerchantServiceFeeConfigRepository;
import com.cosfo.mall.merchant.service.MerchantServiceFeeConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2025-08-29
 **/
@Service
public class MerchantServiceFeeConfigServiceImpl implements MerchantServiceFeeConfigService {

    @Resource
    private MerchantServiceFeeConfigRepository merchantServiceFeeConfigRepository;

    @Override
    public MerchantServiceFeeConfigDTO queryByTenantId(Long tenantId) {
        LambdaQueryWrapper<MerchantServiceFeeConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantServiceFeeConfig::getTenantId, tenantId);
        List<MerchantServiceFeeConfig> configList = merchantServiceFeeConfigRepository.list(queryWrapper);
        if (!CollectionUtils.isEmpty(configList)) {
            Map<String, BigDecimal> payTypeFeeRateMap = configList.stream()
                    .filter(el -> Objects.equals(el.getStatus(), MerchantServiceFeeConfigEnum.StatusEnum.ENABLE.getStatus()))
                    .collect(Collectors.toMap(MerchantServiceFeeConfig::getPayType, MerchantServiceFeeConfig::getFeeRate));
            if (!CollectionUtils.isEmpty(payTypeFeeRateMap)) {
                MerchantServiceFeeConfigDTO dto = new MerchantServiceFeeConfigDTO();
                dto.setWechatFeeRate(payTypeFeeRateMap.get(MerchantServiceFeeConfigEnum.PayType.WECHAT.getType()));
                dto.setAlipayFeeRate(payTypeFeeRateMap.get(MerchantServiceFeeConfigEnum.PayType.ALIPAY.getType()));
                dto.setStatus(configList.get(0).getStatus());
                return dto;
            }
        }
        return new MerchantServiceFeeConfigDTO();
    }
}
