package com.cosfo.mall.merchant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.merchant.service.MerchantDeliveryFeeService;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/29 9:54
 */
@Slf4j
@Service
public class MerchantDeliveryFeeServiceImpl implements MerchantDeliveryFeeService {

    /**
     * 构造查询运费的参数
     *
     * @param orderDTO
     * @param orderItemDTOS
     * @param orderAddressDTO
     * @param mulOrderTotalPrice
     * @return
     */
    @Override
    public DeliveryTotalReq buildDeliveryParam(OrderResp orderDTO, List<OrderItemAndSnapshotResp> orderItemDTOS, OrderAddressResp orderAddressDTO, BigDecimal mulOrderTotalPrice) {
        DeliveryTotalReq deliveryTotalDTO = new DeliveryTotalReq();
        deliveryTotalDTO.setTenantId(orderDTO.getTenantId());
        deliveryTotalDTO.setStoreId(orderDTO.getStoreId());
        deliveryTotalDTO.setSupplierTenantId(orderDTO.getSupplierTenantId());
        deliveryTotalDTO.setDeliveryTime(orderDTO.getDeliveryTime() == null ? null : orderDTO.getDeliveryTime().toLocalDate());

        List<com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderItemInfoDTO> orderItemInfoDTOList = new ArrayList<>();
        BigDecimal totalPrice = BigDecimal.ZERO;
        Integer totalAmount = NumberConstant.ZERO;
        for (OrderItemAndSnapshotResp orderItemDTO : orderItemDTOS) {
            com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderItemInfoDTO itemInfoDTO = new com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderItemInfoDTO();
            itemInfoDTO.setItemId(orderItemDTO.getItemId());
            itemInfoDTO.setGoodsType(orderItemDTO.getGoodsType());
            itemInfoDTO.setItemCount(orderItemDTO.getAmount());
            itemInfoDTO.setItemTotalPrice(orderItemDTO.getTotalPrice());
            itemInfoDTO.setSkuId(orderItemDTO.getSkuId());
            itemInfoDTO.setSupplierSkuId(orderItemDTO.getSupplierSkuId());
            itemInfoDTO.setWeight(orderItemDTO.getWeight());
            itemInfoDTO.setTotalWeight(Optional.ofNullable(orderItemDTO.getWeight()).map(e -> NumberUtil.mul(e, orderItemDTO.getAmount())).orElse(null));
            orderItemInfoDTOList.add(itemInfoDTO);

            totalPrice = totalPrice.add(itemInfoDTO.getItemTotalPrice());
            totalAmount = totalAmount + itemInfoDTO.getItemCount();
        }
        deliveryTotalDTO.setOrderItemInfoDTOList(orderItemInfoDTOList);

        com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderInfoDTO orderInfoDTO = new com.cosfo.ordercenter.client.resp.delivery.DeliveryOrderInfoDTO();
        orderInfoDTO.setOrderTotalPrice(totalPrice);
        orderInfoDTO.setOrderTotalCount(totalAmount);
        orderInfoDTO.setWarehouseType(orderDTO.getWarehouseType());
        orderInfoDTO.setWarehouseNo(orderDTO.getWarehouseNo() == null ? null : NumberUtils.toLong(orderDTO.getWarehouseNo()));
        orderInfoDTO.setStoreProvince(orderAddressDTO.getProvince());
        orderInfoDTO.setStoreCity(orderAddressDTO.getCity());
        orderInfoDTO.setStoreArea(orderAddressDTO.getArea());
        orderInfoDTO.setStorePoi(orderAddressDTO.getPoiNote());
        orderInfoDTO.setFulfillmentType(orderDTO.getFulfillmentType());
        orderInfoDTO.setMulOrderTotalPrice(mulOrderTotalPrice);
        orderInfoDTO.setStorePoi (orderAddressDTO.getPoiNote());
        deliveryTotalDTO.setOrderInfoDTO(orderInfoDTO);

        return deliveryTotalDTO;
    }


}
