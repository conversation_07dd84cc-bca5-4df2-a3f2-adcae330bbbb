package com.cosfo.mall.merchant.service;

import com.cosfo.mall.merchant.model.dto.*;
import com.cosfo.mall.merchant.model.po.MerchantStoreAccount;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 描述: 商户账号服务类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
public interface MerchantStoreAccountService {
    /**
     * 查询账户信息
     *
     * @param accountId
     * @param tenantId
     * @return
     */
    MerchantStoreAccountVO queryAccountInfo(Long accountId, Long tenantId);

    CommonResult<MerchantStoreAccountPreLoginDTO> newListBelongStoreAccounts(MerchantStoreAccountQueryDTO merchantStoreAccountQueryDto);

    /**
     * 获取账号切换列表
     *
     * @param pageQueryDTO
     * @return
     */
    PageInfo<MerchantStoreAccountDTO> listStoreAccount(MerchantStoreAccountPageQueryDTO pageQueryDTO);

    /**
     * 刷新登录时间和openId
     *
     * @param id
     * @param openId
     */
    void refreshLoginTimeAndOpenId(Long id, String openId);

    /**
     * 查询门店信息
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<List<MerchantStoreAccountDTO>> listAll(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询当前登录信息
     * @param loginContextInfoDto
     * @return
     */
    CommonResult<LoginSuccessReturnDTO> queryLoginAccountInfo(LoginContextInfoDTO loginContextInfoDto);

    /**
     * 插入账号
     * @param merchantStoreAccountDto
     * @param loginContextInfoDto
     * @return
     */
    CommonResult insertAccount(MerchantStoreAccountDTO merchantStoreAccountDto, LoginContextInfoDTO loginContextInfoDto);

    /**
     * 删除账号
     * @param id
     * @return
     */
    CommonResult deleteAccount(Long id);

    /**
     * 更新
     * @param merchantStoreAccountDto
     * @param loginContextInfoDto
     * @return
     */
    CommonResult updateAccount(MerchantStoreAccountDTO merchantStoreAccountDto, LoginContextInfoDTO loginContextInfoDto);

    /**
     * 清空账号缓存信息
     * @param id
     */
    void removeUserCache(Long id);

    /**
     * 根据条件查询账号信息
     * @param query
     * @return
     */
    MerchantStoreAccount selectOne(MerchantStoreAccount query);

    /**
     * 检查门店公众号是否已授权
     *
     * @param loginContextInfoDTO
     * @return
     */
    Boolean checkOaOpenIdIsExist(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 用户授权
     *
     * @return
     */
    Boolean authorization(String code, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 从登录账号列表中选择一个去默认登录
     * @param tenantId
     * @param phone
     */
    MerchantStoreAccountLoginDTO selectOneLoginFromBelongStoreAccountsList(Long tenantId, String phone);
}
