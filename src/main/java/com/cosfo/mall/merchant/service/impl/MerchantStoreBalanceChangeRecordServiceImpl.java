package com.cosfo.mall.merchant.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceChangeRecordTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.*;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.model.vo.MerchantStoreBalanceChangeRecordVO;
import com.cosfo.mall.merchant.repository.MerchantStoreBalanceChangeRecordRepository;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceChangeRecordService;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.tenant.model.po.TenantFundAccount;
import com.cosfo.mall.tenant.service.TenantFundAccountService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 18:25
 */
@Slf4j
@Service
public class MerchantStoreBalanceChangeRecordServiceImpl implements MerchantStoreBalanceChangeRecordService {

    @Resource
    private MerchantStoreBalanceChangeRecordRepository merchantStoreBalanceChangeRecordRepository;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @Resource
    private TenantFundAccountService tenantFundAccountService;

    @Override
    public PageInfo<MerchantStoreBalanceChangeRecordVO> pageQueryChangeRecord(MerchantStoreBalanceChangeQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        dealQueryCondition(queryDTO, loginContextInfoDTO);
        Page<MerchantStoreBalanceChangeRecord> page = merchantStoreBalanceChangeRecordRepository.pageListByCondition(queryDTO);
        List<MerchantStoreBalanceChangeRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageInfo<>();
        }

        // 查询交易的订单和售后信息
        Map<String, Long> orderMaps = queryTradeOrderInfo(records);
        Map<String, Long> orderAfterSaleMaps = queryTradeOrderAfterSaleInfo(records);

        // 如果是非现金账户 查询账户名称
        Map<Long, String> nonCashAccountNameMap = queryNonCashAccountName(records);

        return PageInfoConverter.toPageInfo(page, (MerchantStoreBalanceChangeRecord record) -> {
            MerchantStoreBalanceChangeRecordVO data = new MerchantStoreBalanceChangeRecordVO();
            data.setChangeBalance(record.getChangeBalance());
            data.setType(record.getType());
            data.setCreateTime(record.getCreateTime());
            data.setAssociatedOrderId(orderMaps.get(record.getAssociatedOrderNo()));
            data.setAssociatedOrderAfterSaleItemId(orderAfterSaleMaps.get(record.getAssociatedOrderNo()));
            data.setAccountName(nonCashAccountNameMap.get(record.getFundAccountId()));
            if (Objects.equals(record.getAccountType(), MerchantStoreBalanceEnums.AccountTypeEnum.NON_CASH.getType())) {
                data.setRemark(record.getRemark());
            }
            return data;
        });
    }

    private Map<Long, String> queryNonCashAccountName(List<MerchantStoreBalanceChangeRecord> records) {
        List<Long> accountIds = records.stream().map(MerchantStoreBalanceChangeRecord::getFundAccountId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<TenantFundAccount> tenantFundAccounts = tenantFundAccountService.queryByIds(accountIds);
        return tenantFundAccounts.stream().collect(Collectors.toMap(TenantFundAccount::getId, TenantFundAccount::getAccountName));
    }

    /**
     * 查询余额变动的交易订单信息
     * @param records
     * @return
     */
    private Map<String, Long> queryTradeOrderInfo(List<MerchantStoreBalanceChangeRecord> records) {
        List<String> orderNos = records.stream().filter(el -> StringUtils.startsWithIgnoreCase(el.getAssociatedOrderNo(), Global.NORMAL_ORDER_CODE)).map(MerchantStoreBalanceChangeRecord::getAssociatedOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyMap();
        }
//        List<Order> orders =  orderService.queryByOrderNos(orderNos);
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        Map<String, Long> orderInfoMap = orderList.stream().collect(Collectors.toMap(OrderResp::getOrderNo, OrderResp::getId));
        return CollectionUtils.isEmpty(orderList) ? Collections.emptyMap() : orderInfoMap;
    }

    /**
     * 查询余额变动的交易售后订单信息
     * @param records
     * @return
     */
    private Map<String, Long> queryTradeOrderAfterSaleInfo(List<MerchantStoreBalanceChangeRecord> records) {
        List<String> orderAfterSaleNos = records.stream().filter(el -> StringUtils.startsWithIgnoreCase(el.getAssociatedOrderNo(), Global.NORMAL_ORDER_AFTER_SALE_CODE)).map(MerchantStoreBalanceChangeRecord::getAssociatedOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderAfterSaleNos)) {
            return Collections.emptyMap();
        }
//        List<OrderAfterSaleDTO> orderAfterSales = orderAfterSaleService.queryByOrderAfterSaleNos(orderAfterSaleNos);
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(orderAfterSaleNos));
        Map<String, Long> orderAfterSaleInfoMap = afterSaleDTOList.stream().collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, OrderAfterSaleResp::getOrderItemId));
        return CollectionUtils.isEmpty(afterSaleDTOList) ? Collections.emptyMap() : orderAfterSaleInfoMap;
    }


    private void dealQueryCondition(MerchantStoreBalanceChangeQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(queryDTO.getTradeYear(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "查询交易年份不能为空");
        AssertCheckParams.notNull(queryDTO.getTradeMonth(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "查询交易月份不能为空");
        String monthFirstDate = TimeUtils.getMonthFirstDate(queryDTO.getTradeYear(), queryDTO.getTradeMonth());
        String nextMouthFirstDate = TimeUtils.getMonthFirstDate(queryDTO.getTradeYear(), queryDTO.getTradeMonth() + 1);

        queryDTO.setTradeStartTime(monthFirstDate);
        queryDTO.setTradeEndTime(nextMouthFirstDate);
        queryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        queryDTO.setStoreId(loginContextInfoDTO.getStoreId());

        if (queryDTO.getAccountType() == null) {
            //默认非现金账户
            queryDTO.setAccountType(MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType());
        }
    }

    @Override
    public void generateBalanceChangeRecordByOrder(String orderNo, BigDecimal changeBalance, Integer type, MerchantStoreBalance balance) {
        MerchantStoreBalanceChangeRecord record = new MerchantStoreBalanceChangeRecord();
        record.setTenantId(balance.getTenantId());
        record.setStoreId(balance.getStoreId());
        record.setChangeBalance(changeBalance.negate());
        record.setAfterChangeBalance(balance.getBalance().subtract(changeBalance));
        record.setAssociatedOrderNo(orderNo);
        record.setType(type);
        record.setAccountType(balance.getAccountType());
        record.setFundAccountId(balance.getFundAccountId());
        merchantStoreBalanceChangeRecordRepository.save(record);
        log.info("生成变动记录成功, 变动记录id: {}, 订单号: {}, 金额:{}", record.getId(), orderNo, changeBalance.negate());
    }

    @Override
    public void batchInsertChangeRecord(List<MerchantStoreBalanceChangeRecord> records) {
        merchantStoreBalanceChangeRecordRepository.saveBatch(records);
        log.info("生成变动记录成功:{}", records);
    }

}
