package com.cosfo.mall.merchant.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.model.vo.MerchantStoreBalanceChangeRecordVO;
import com.cosfo.mall.order.model.po.Order;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc 门店余额变动记录服务层
 * <AUTHOR>
 * @date 2023/3/15 18:22
 */
public interface MerchantStoreBalanceChangeRecordService {

    /**
     * 查询余额变动记录服务层
     * @param queryDTO
     * @param loginContextInfoDTO
     * @return
     */
    PageInfo<MerchantStoreBalanceChangeRecordVO> pageQueryChangeRecord(MerchantStoreBalanceChangeQueryDTO queryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 根据订单记录余额变动
     * @param orderNo
     * @param balance
     */
    void generateBalanceChangeRecordByOrder(String orderNo, BigDecimal changeBalance, Integer type, MerchantStoreBalance balance);

    /**
     * 批量插入变更记录
     * @param records
     */
    void batchInsertChangeRecord(List<MerchantStoreBalanceChangeRecord> records);
    
}
