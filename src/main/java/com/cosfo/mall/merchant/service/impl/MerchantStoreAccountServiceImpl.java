package com.cosfo.mall.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.MerchantAccountTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreStatusEnum;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.common.utils.JwtUtils;
import com.cosfo.mall.common.utils.RedisUtils;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.auth.AuthUserQueryFacade;
import com.cosfo.mall.facade.converter.MerchantStoreAccountConverter;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.merchant.convert.MerchantStoreAccountConvert;
import com.cosfo.mall.merchant.model.dto.*;
import com.cosfo.mall.merchant.model.po.Merchant;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.po.MerchantStore;
import com.cosfo.mall.merchant.model.po.MerchantStoreAccount;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.mall.tenant.model.po.TenantCommonConfig;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
@Service
@Slf4j
public class MerchantStoreAccountServiceImpl implements MerchantStoreAccountService {
    @Lazy
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private AuthUserQueryFacade authUserQueryFacade;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public MerchantStoreAccountVO queryAccountInfo(Long accountId, Long tenantId) {
        AssertCheckParams.notNull(accountId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "账户Id不能为空");
//        MerchantStoreAccount merchantStoreAccount = merchantStoreAccountMapper.selectById(accountId, tenantId);
        MerchantStoreAccountResultResp merchantStoreAccount = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountId);
        MerchantStoreAccountVO merchantStoreAccountVO = new MerchantStoreAccountVO();
        BeanUtils.copyProperties(merchantStoreAccount, merchantStoreAccountVO);
        return merchantStoreAccountVO;
    }


    @Override
    public CommonResult<MerchantStoreAccountPreLoginDTO> newListBelongStoreAccounts(MerchantStoreAccountQueryDTO merchantStoreAccountQueryDto) {
        Long tenantId = merchantStoreAccountQueryDto.getTenantId();
        String phone = merchantStoreAccountQueryDto.getPhone();
        AssertCheckParams.notNull(tenantId, ResultStatusEnum.SERVER_ERROR.getStatus(), "租户id不能为空");
        MerchantStoreAccountPreLoginDTO merchantStoreAccountPreLoginDTO = new MerchantStoreAccountPreLoginDTO();
        if (StringUtils.isEmpty(phone)) {
            return CommonResult.ok(merchantStoreAccountPreLoginDTO);
        }
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        merchantStoreAccountQueryReq.setPhone(phone);
        merchantStoreAccountQueryReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getFlag());
        List<MerchantStoreAccountResultResp> merchantStoreAccounts = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        if (CollectionUtils.isEmpty(merchantStoreAccounts)) {
            return CommonResult.ok(merchantStoreAccountPreLoginDTO);
        }

        List<MerchantStoreAccountDTO> merchantStoreAccountDtoList = new ArrayList<>();

        Map<Long, AuthUserResp> authUserRespMap = getAuthUserRespMap(tenantId, phone);

        for (MerchantStoreAccountResultResp merchantStoreAccount : merchantStoreAccounts) {
            MerchantStoreAccountDTO merchantStoreAccountDTO = new MerchantStoreAccountDTO();
            // 查询账号所属门店信息及地址信息
            MerchantStore store = merchantStoreService.queryStoreInfo(merchantStoreAccount.getStoreId());
            if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.CLOSE.getStatus())) {
                continue;
            }
            MerchantAddress address = merchantAddressService.selectByStoreId(store.getId(), tenantId);
            if (Objects.nonNull(address)) {
                merchantStoreAccountDTO.setProvince(address.getProvince());
                merchantStoreAccountDTO.setCity(address.getCity());
                merchantStoreAccountDTO.setArea(address.getArea());
                merchantStoreAccountDTO.setAddress(address.getAddress());
                merchantStoreAccountDTO.setPoiNote(address.getPoiNote());
                merchantStoreAccountDTO.setHouseNumber(address.getHouseNumber());
            }

            merchantStoreAccountDTO.setId(merchantStoreAccount.getId());
            merchantStoreAccountDTO.setStoreId(merchantStoreAccount.getStoreId());
            merchantStoreAccountDTO.setType(merchantStoreAccount.getType());
            merchantStoreAccountDTO.setStoreName(store.getStoreName());
            merchantStoreAccountDTO.setStoreId(store.getId());
            merchantStoreAccountDTO.setStoreStatus(store.getStatus());
            merchantStoreAccountDTO.setStoreAuditRemark(store.getAuditRemark());

            AuthUserResp authUserResp = authUserRespMap.get(merchantStoreAccount.getId());
            if (authUserResp != null && authUserResp.getLastLoginTime() != null) {
                merchantStoreAccountDTO.setLastLoginTime(authUserResp.getLastLoginTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            merchantStoreAccountDTO.setSortLevel(Objects.equals(merchantStoreAccountDTO.getId(), merchantStoreAccountQueryDto.getCurrentLoginAccountId()) ? MerchantStoreAccountSortLevelEnum.CURRENT_LOGIN_ACCOUNT.getLevel() : Objects.equals(store.getStatus(), MerchantStoreStatusEnum.AUDIT_SUCCESS.getStatus()) ? MerchantStoreAccountSortLevelEnum.USABLE_LOGIN_ACCOUNTS.getLevel() : MerchantStoreAccountSortLevelEnum.OTHER.getLevel());
            merchantStoreAccountDtoList.add(merchantStoreAccountDTO);
        }

        if (CollectionUtils.isEmpty(merchantStoreAccountDtoList)) {
            return CommonResult.ok(merchantStoreAccountPreLoginDTO);
        }

        // 计算默认登录门店
        List<MerchantStoreAccountDTO> auditSuccessStores = merchantStoreAccountDtoList.stream().
                filter(el -> Objects.equals(el.getStoreStatus(), MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus())).
                collect(Collectors.toList());

        // 只有一个审核通过的
        if (!CollectionUtils.isEmpty(auditSuccessStores) && auditSuccessStores.size() == NumberConstant.ONE) {
            merchantStoreAccountPreLoginDTO.setDefaultLoginStoreId(auditSuccessStores.get(NumberConstant.ZERO).getStoreId());
            merchantStoreAccountPreLoginDTO.setAccountList(merchantStoreAccountDtoList);
            return CommonResult.ok(merchantStoreAccountPreLoginDTO);
        }

        List<MerchantStoreAccountDTO> lastLoginStoreOrderlyList = auditSuccessStores.stream().filter(el -> Objects.nonNull(el.getLastLoginTime()))
                .sorted(Comparator.comparing(MerchantStoreAccountDTO::getLastLoginTime).reversed())
                .collect(Collectors.toList());

        //有多个审核通过的筛选审核成功的门店按上次登录时间倒序排
        boolean selectLogin = isSelectLogin(phone);
        Long defaultLoginStoreId = null;
        if (!CollectionUtils.isEmpty(lastLoginStoreOrderlyList) && !selectLogin) {
            defaultLoginStoreId = lastLoginStoreOrderlyList.get(NumberConstant.ZERO).getStoreId();
        }
        merchantStoreAccountPreLoginDTO.setDefaultLoginStoreId(defaultLoginStoreId);
        //账号排序
        List<MerchantStoreAccountDTO> sortAccountList = merchantStoreAccountDtoList.stream().sorted(Comparator.comparing(MerchantStoreAccountDTO::getSortLevel).thenComparing(MerchantStoreAccountDTO::getId)).collect(Collectors.toList());
        merchantStoreAccountPreLoginDTO.setAccountList(sortAccountList);
        return CommonResult.ok(merchantStoreAccountPreLoginDTO);
    }

    /**
     * 是否是选择账户去登录
     * @param phone
     * @return
     */
    private boolean isSelectLogin(String phone) {
        String cacheKey = RedisKeyEnum.C00013.join(phone);
        boolean selectLogin = Objects.nonNull(redisTemplate.opsForValue().get(cacheKey));
        return selectLogin;
    }

    @Override
    public PageInfo<MerchantStoreAccountDTO> listStoreAccount(MerchantStoreAccountPageQueryDTO pageQueryDTO) {
        Long tenantId = pageQueryDTO.getTenantId();
        String phone = pageQueryDTO.getPhone();
        if (tenantId == null || StringUtils.isEmpty(phone)) {
            log.error("pageQuery={}", pageQueryDTO);
            throw new BizException("用户信息为空");
        }
        MerchantStoreAccountPageReq merchantStoreAccountPageReq = new MerchantStoreAccountPageReq();
        merchantStoreAccountPageReq.setStoreName(pageQueryDTO.getStoreName());
        merchantStoreAccountPageReq.setTenantId(tenantId);
        merchantStoreAccountPageReq.setPhone(phone);
        merchantStoreAccountPageReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getFlag());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(pageQueryDTO.getPageIndex());
        pageQueryReq.setPageSize(pageQueryDTO.getPageSize());
        PageInfo<MerchantStoreAccountPageResp> merchantStorePage = userCenterMerchantStoreAccountFacade.getMerchantStorePage(merchantStoreAccountPageReq, pageQueryReq);
        if (merchantStorePage == null || CollectionUtils.isEmpty(merchantStorePage.getList())) {
            return PageInfo.emptyPageInfo();
        }
        Set<Long> storeIds = merchantStorePage.getList().stream().map(MerchantStoreAccountPageResp::getStoreId).collect(Collectors.toSet());

        Map<Long, MerchantStore> storeMap = merchantStoreService.queryStoreInfoMap(storeIds);
        Map<Long, MerchantAddress> addressMap = merchantAddressService.batchQueryByStoreIds(storeIds, tenantId);
        return MerchantStoreAccountConvert.INSTANCE.respToDTOPage(merchantStorePage, storeMap, addressMap, pageQueryDTO.getCurrentLoginAccountId());
    }

    @Override
    public void refreshLoginTimeAndOpenId(Long id, String openId) {
        MerchantStoreAccountCommandReq update = new MerchantStoreAccountCommandReq();
        update.setId(id);
        update.setLastLoginTime(LocalDateTime.now());
        update.setOpenId(openId);
//        merchantStoreAccountMapper.updateByPrimaryKeySelective(update);
        Boolean result = userCenterMerchantStoreAccountFacade.update(update);
        if (!Boolean.TRUE.equals(result)) {
            throw new BizException("操作失败");
        }
    }

    @Override
    public CommonResult<List<MerchantStoreAccountDTO>> listAll(LoginContextInfoDTO loginContextInfoDTO) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(loginContextInfoDTO.getTenantId());
        merchantStoreAccountQueryReq.setStoreId(loginContextInfoDTO.getStoreId());
        merchantStoreAccountQueryReq.setDeleteFlag(com.cosfo.mall.common.context.MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
        List<MerchantStoreAccountResultResp> respList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        List<MerchantStoreAccountDTO> merchantStoreAccountDtoList = MerchantStoreAccountConverter.INSTANCE.respListToDtoList(respList);
//        List<MerchantStoreAccountDTO> merchantStoreAccountDtoList = merchantStoreAccountMapper.selectByStoreId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId());
        return CommonResult.ok(merchantStoreAccountDtoList.stream().sorted(Comparator.comparing(MerchantStoreAccountDTO::getType).thenComparing(MerchantStoreAccountDTO::getId)).collect(Collectors.toList()));
    }

    @Override
    public CommonResult<LoginSuccessReturnDTO> queryLoginAccountInfo(LoginContextInfoDTO loginContextInfoDto) {
        MerchantStoreAccountResultResp account = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(loginContextInfoDto.getAccountId());
        LoginSuccessReturnDTO loginSuccessReturnDto = new LoginSuccessReturnDTO();
        Merchant merchant = userCenterMerchantInfoFacade.getMerchantByTenantId(loginContextInfoDto.getTenantId());
        MerchantStore store = merchantStoreService.queryStoreInfo(loginContextInfoDto.getStoreId());

        loginSuccessReturnDto.setStoreId(store.getId());
        loginSuccessReturnDto.setStoreName(store.getStoreName());
        loginSuccessReturnDto.setPhone(account.getPhone());
        loginSuccessReturnDto.setLogoImage(merchant.getLogoImage());
        loginSuccessReturnDto.setBillSwitch(store.getBillSwitch());
        loginSuccessReturnDto.setBackgroundImage(merchant.getBackgroundImage());
        loginSuccessReturnDto.setAccountId(account.getId());
        loginSuccessReturnDto.setAccountName(account.getAccountName());
        loginSuccessReturnDto.setOnlinePayment(store.getOnlinePayment());
        loginSuccessReturnDto.setType(account.getType());
        loginSuccessReturnDto.setBalanceAuthority(store.getBalanceAuthority());
        loginSuccessReturnDto.setBalance(BigDecimal.ZERO);
        loginSuccessReturnDto.setStoreNo(store.getStoreNo());
        loginSuccessReturnDto.setUniqueNo(store.getUniqueNo());
        loginSuccessReturnDto.setInvoiceAddress(store.getInvoiceAddress());

        //poi
        MerchantAddress address = merchantAddressService.selectByStoreId(store.getId(), account.getTenantId());
        loginSuccessReturnDto.setPoiNote(Objects.isNull(address) ? Constants.EMPTY_STRING : address.getPoiNote());

        return CommonResult.ok(loginSuccessReturnDto);
    }

    @Override
    public CommonResult insertAccount(MerchantStoreAccountDTO merchantStoreAccountDto, LoginContextInfoDTO loginContextInfoDto) {
        String accountName = merchantStoreAccountDto.getAccountName();
        String phone = merchantStoreAccountDto.getPhone();
        AssertCheckParams.notNull(phone, ResultStatusEnum.SERVER_ERROR.getStatus(), "手机号码不能为空");
        AssertCheckParams.notNull(accountName, ResultStatusEnum.SERVER_ERROR.getStatus(), "账号名称不能为空");
        MerchantStoreAccountCommandReq insertAccount = new MerchantStoreAccountCommandReq();
        insertAccount.setTenantId(loginContextInfoDto.getTenantId());
        insertAccount.setStoreId(loginContextInfoDto.getStoreId());
        insertAccount.setAccountName(accountName);
        insertAccount.setPhone(phone);
        insertAccount.setType(MerchantAccountTypeEnum.CLERK.getType());
        insertAccount.setRegisterTime(LocalDateTime.now());
        insertAccount.setAuditTime(LocalDateTime.now());
        insertAccount.setStatus((MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus()));
        Long id = userCenterMerchantStoreAccountFacade.create(insertAccount);
        if (NumberConstant.ZERO > id) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "操作失败");
        }

//        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
//        merchantStoreAccountQueryReq.setTenantId(loginContextInfoDto.getTenantId());
//        merchantStoreAccountQueryReq.setStoreId(loginContextInfoDto.getStoreId());
//        List<MerchantStoreAccountResultResp> merchantStoreAccountDtos = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
////        List<MerchantStoreAccountDTO> merchantStoreAccountDtos = merchantStoreAccountMapper.selectByStoreId(loginContextInfoDto.getTenantId(), loginContextInfoDto.getStoreId());
//        if (!CollectionUtils.isEmpty(merchantStoreAccountDtos) && merchantStoreAccountDtos.size() > NumberConstant.TEN) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "最多有一个店长十个店员");
//        }
//
//        MerchantStoreAccount query = new MerchantStoreAccount();
//        query.setStoreId(loginContextInfoDto.getStoreId());
//        query.setPhone(phone);
//        MerchantStoreAccount account = selectOne(query);
//        if (Objects.nonNull(account)) {
//            if (Objects.equals(account.getDeleteFlag(), MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus())) {
//                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该手机号已经是当前门店的员工，请勿重复提交");
//            }
//            // 更新该账号为正常使用
//            MerchantStoreAccountCommandReq update = new MerchantStoreAccountCommandReq();
//            update.setId(account.getId());
//            update.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
//            update.setAccountName(merchantStoreAccountDto.getAccountName());
////            merchantStoreAccountMapper.updateByPrimaryKeySelective(update);
//            userCenterMerchantStoreAccountFacade.update(update);
//            return CommonResult.ok();
//        }
//
//        MerchantStoreAccountCommandReq insertAccount = new MerchantStoreAccountCommandReq();
//        insertAccount.setTenantId(loginContextInfoDto.getTenantId());
//        insertAccount.setStoreId(loginContextInfoDto.getStoreId());
//        insertAccount.setAccountName(accountName);
//        insertAccount.setPhone(phone);
//        insertAccount.setType(MerchantAccountTypeEnum.CLERK.getType());
//        insertAccount.setRegisterTime(LocalDateTime.now());
//        insertAccount.setAuditTime(LocalDateTime.now());
//        insertAccount.setStatus((MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus()));
////        merchantStoreAccountMapper.insert(insertAccount);
//        userCenterMerchantStoreAccountFacade.create(insertAccount);

        log.info("门店：{}添加了账号：{}", loginContextInfoDto.getStoreName(), accountName);
        return CommonResult.ok();
    }

    @Override
    public CommonResult deleteAccount(Long id) {
        MerchantStoreAccountResultResp merchantStoreAccount = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(id);
//        MerchantStoreAccount merchantStoreAccount = MerchantStoreAccountConverter.INSTANCE.respToAccount(resp);
//        MerchantStoreAccount merchantStoreAccount = merchantStoreAccountMapper.selectByPrimaryKey(id);
        if (Objects.isNull(merchantStoreAccount)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到该账号");
        }

        // 清空openId
//        merchantStoreAccount.setOpenId(null);
//        merchantStoreAccount.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.DELETED.getStatus());
//        merchantStoreAccountMapper.updateByPrimaryKey(merchantStoreAccount);
        MerchantStoreAccountCommandReq merchantStoreAccountCommandReq = new MerchantStoreAccountCommandReq();
        merchantStoreAccountCommandReq.setId(id);
        Boolean remove = userCenterMerchantStoreAccountFacade.remove(merchantStoreAccountCommandReq);
        if (!Boolean.TRUE.equals(remove)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "操作失败");
        }
        // T出门店
        removeUserCache(id);

        log.info("账号：{}已被店长删除", merchantStoreAccount.getAccountName());
        return CommonResult.ok();
    }

    @Override
    public CommonResult updateAccount(MerchantStoreAccountDTO merchantStoreAccountDto, LoginContextInfoDTO loginContextInfoDto) {
//        MerchantStoreAccountResultResp resp = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(merchantStoreAccountDto.getId());
//        MerchantStoreAccount merchantStoreAccount = MerchantStoreAccountConverter.INSTANCE.respToAccount(resp);
////        MerchantStoreAccount merchantStoreAccount = merchantStoreAccountMapper.selectByPrimaryKey(merchantStoreAccountDto.getId());
//        AssertCheckParams.notNull(merchantStoreAccount, ResultStatusEnum.SERVER_ERROR.getStatus(), "未查询到该账号");

        MerchantStoreAccountCommandReq update = new MerchantStoreAccountCommandReq();
        update.setId(merchantStoreAccountDto.getId());
        update.setType(merchantStoreAccountDto.getType());
        update.setAccountName(merchantStoreAccountDto.getAccountName());
//        update.setLoginAccountId(loginContextInfoDto.getAccountId());
//        merchantStoreAccountMapper.updateByPrimaryKeySelective(update);
        Boolean result = userCenterMerchantStoreAccountFacade.update(update);
        if (!Boolean.TRUE.equals(result)) {
            throw new BizException("操作失败");
        }

//
//        // 转交店长
//        if (Objects.equals(merchantStoreAccountDto.getType(), MerchantAccountTypeEnum.MANAGER.getType())) {
//            update.setId(loginContextInfoDto.getAccountId());
//            update.setType(MerchantAccountTypeEnum.CLERK.getType());
////            merchantStoreAccountMapper.updateByPrimaryKeySelective(update);
//            userCenterMerchantStoreAccountFacade.update(update);
//        }
        return CommonResult.ok();
    }

    @Override
    public void removeUserCache(Long id) {
        String accessTokenKey = JwtUtils.TOKEN_PREFIX + id;
        redisUtils.delete(accessTokenKey);
    }

    @Override
    public MerchantStoreAccount selectOne(MerchantStoreAccount query) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(query.getTenantId());
        merchantStoreAccountQueryReq.setPhone(query.getPhone());
        merchantStoreAccountQueryReq.setStatus(query.getStatus());
        merchantStoreAccountQueryReq.setDeleteFlag(query.getDeleteFlag());
        merchantStoreAccountQueryReq.setStoreId(query.getStoreId());
        List<MerchantStoreAccountResultResp> merchantStoreList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        if (CollectionUtil.isNotEmpty(merchantStoreList) && merchantStoreList.size() == NumberConstant.ONE) {
            MerchantStoreAccountResultResp resp = merchantStoreList.get(NumberConstant.ZERO);
            return MerchantStoreAccountConverter.INSTANCE.respToAccount(resp);
        }
        return null;
    }

    @Override
    public MerchantStoreAccountLoginDTO selectOneLoginFromBelongStoreAccountsList(Long tenantId, String phone) {
        AssertCheckParams.notNull(tenantId, ResultStatusEnum.SERVER_ERROR.getStatus(), "租户id不能为空");
        AssertCheckParams.isTrue(!StringUtils.isEmpty(phone), ResultStatusEnum.SERVER_ERROR.getStatus(), "phone不能为空");

        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        merchantStoreAccountQueryReq.setPhone(phone);
        merchantStoreAccountQueryReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getFlag());
        List<MerchantStoreAccountResultResp> merchantStoreAccounts = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        if (CollectionUtils.isEmpty(merchantStoreAccounts)) {
            return null;
        }

        Set<Long> storeIds = merchantStoreAccounts.stream().map(MerchantStoreAccountResultResp::getStoreId).collect(Collectors.toSet());
        Map<Long, MerchantStore> merchantStoreMap = merchantStoreService.queryStoreInfoMap(storeIds);

        Map<Long, AuthUserResp> authUserRespMap = getAuthUserRespMap(tenantId, phone);

        List<MerchantStoreAccountLoginDTO> merchantStoreAccountDtoList = Lists.newArrayList();
        for (MerchantStoreAccountResultResp merchantStoreAccount : merchantStoreAccounts) {
            MerchantStoreAccountLoginDTO merchantStoreAccountLoginDTO = new MerchantStoreAccountLoginDTO();
            MerchantStore store = merchantStoreMap.get(merchantStoreAccount.getStoreId());
            if (Objects.isNull(store)) {
                continue;
            }
            if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.CLOSE.getStatus())) {
                continue;
            }

            merchantStoreAccountLoginDTO.setId(merchantStoreAccount.getId());
            merchantStoreAccountLoginDTO.setStoreId(merchantStoreAccount.getStoreId());
            merchantStoreAccountLoginDTO.setType(merchantStoreAccount.getType());
            merchantStoreAccountLoginDTO.setStoreName(store.getStoreName());
            merchantStoreAccountLoginDTO.setAccountName(merchantStoreAccount.getAccountName());
            merchantStoreAccountLoginDTO.setStoreId(store.getId());
            merchantStoreAccountLoginDTO.setStoreStatus(store.getStatus());
            merchantStoreAccountLoginDTO.setStoreAuditRemark(store.getAuditRemark());
            merchantStoreAccountLoginDTO.setTenantId(merchantStoreAccount.getTenantId());
            merchantStoreAccountLoginDTO.setPhone(merchantStoreAccount.getPhone());
            AuthUserResp authUserResp = authUserRespMap.get(merchantStoreAccount.getId());
            if (authUserResp != null && authUserResp.getLastLoginTime() != null) {
                merchantStoreAccountLoginDTO.setLastLoginTime(authUserResp.getLastLoginTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            merchantStoreAccountDtoList.add(merchantStoreAccountLoginDTO);
        }

        // 审核成功门店
        List<MerchantStoreAccountLoginDTO> auditSuccessStores = merchantStoreAccountDtoList.stream().
                filter(el -> Objects.equals(el.getStoreStatus(), MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus())).
                collect(Collectors.toList());

        if (CollectionUtils.isEmpty(auditSuccessStores)) {
            return null;
        }

        // 只有一个审核通过的
        if (!CollectionUtils.isEmpty(auditSuccessStores) && auditSuccessStores.size() == NumberConstant.ONE) {
            return auditSuccessStores.get(NumberConstant.ZERO);
        }

        List<MerchantStoreAccountLoginDTO> lastLoginStoreOrderlyList = auditSuccessStores.stream().filter(el -> Objects.nonNull(el.getLastLoginTime()))
                .sorted(Comparator.comparing(MerchantStoreAccountLoginDTO::getLastLoginTime).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lastLoginStoreOrderlyList)) {
            return auditSuccessStores.get(NumberConstant.ZERO);
        }

        return lastLoginStoreOrderlyList.get(NumberConstant.ZERO);
    }

    private Map<Long, AuthUserResp> getAuthUserRespMap(Long tenantId, String phone) {
        List<AuthUserResp> authUserResps = authUserQueryFacade.queryAuthUserList(tenantId, phone);
        Map<Long, AuthUserResp> authUserRespMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(authUserResps)) {
            authUserRespMap = authUserResps.stream().collect(Collectors.toMap(AuthUserResp::getBizUserId, authUserResp -> authUserResp));
        }
        return authUserRespMap;
    }
}
