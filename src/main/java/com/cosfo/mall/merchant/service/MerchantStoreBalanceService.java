package com.cosfo.mall.merchant.service;

import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc 门店余额服务层
 * <AUTHOR>
 * @date 2023/3/17 14:01
 */
public interface MerchantStoreBalanceService {

    /**
     * 查询现金账户账户
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    MerchantStoreBalance queryCashAccountByStoreId(Long tenantId, Long storeId);

    /**
     * 查询非现金账户账户
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryNonCashAccountByStoreId(Long tenantId, Long storeId);

    /**
     * 查询非现金账户账户（加锁）
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryNonCashAccountByStoreIdForUpdate(Long tenantId, Long storeId);


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    MerchantStoreBalance queryById(Long id);

    /**
     * 变更余额
     * @param id
     * @param changeBalance
     * @return
     */
    int decreaseBalance(Long id, BigDecimal changeBalance);

    /**
     * 冻结金额
     *
     * @param id
     * @param changeBalance
     * @return
     */
    int freezeBalance(Long id, BigDecimal changeBalance);

    /**
     * 增加余额
     * @param id
     * @param changeBalance
     * @return
     */
    int increaseBalance(Long id, BigDecimal changeBalance);

    /**
     * 扣减冻结金额
     *
     * @param id
     * @param changeBalance
     * @return
     */
    int decreaseFreezeBalance(Long id, BigDecimal changeBalance);

    /**
     * 根据门店id查询
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryByStoreId(Long tenantId, Long storeId);
}
