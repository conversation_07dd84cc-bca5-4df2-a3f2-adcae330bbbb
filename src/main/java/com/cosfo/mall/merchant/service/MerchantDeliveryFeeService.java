package com.cosfo.mall.merchant.service;

import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 运费业务层
 * @date 2022/7/29 9:53
 */
public interface MerchantDeliveryFeeService {


    DeliveryTotalReq buildDeliveryParam(OrderResp orderDTO, List<OrderItemAndSnapshotResp> orderItemDTOS, OrderAddressResp orderAddressDTO, BigDecimal mulOrderTotalPrice);


}
