package com.cosfo.mall.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.context.ContactDefaultEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantContactFacade;
import com.cosfo.mall.merchant.convert.MerchantContactMapperConvert;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantContactDTO;
import com.cosfo.mall.merchant.model.po.MerchantContact;
import com.cosfo.mall.merchant.service.MerchantContactService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 17:22
 */
@Slf4j
@Service
public class MerchantContactServiceImpl implements MerchantContactService {

//    @Resource
//    private MerchantContactMapper merchantContactMapper;

    @Resource
    private UserCenterMerchantContactFacade userCenterMerchantContactFacade;

    @Override
    public ResultDTO updateContact(MerchantContactDTO contactDTO) {
        AssertCheckParams.notNull(contactDTO.getName(), ResultDTOEnum.PARAMETER_MISSING.getCode(),"联系人名称不能为空");
        AssertCheckParams.notNull(contactDTO.getPhone(), ResultDTOEnum.PARAMETER_MISSING.getCode(),"联系人号码不能为空");

        // TODO: 2023/6/9
        MerchantContactCommandReq updateContact = new MerchantContactCommandReq();
        BeanUtils.copyProperties(contactDTO, updateContact);
        Boolean update = userCenterMerchantContactFacade.update(updateContact);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }

//        Long id = contactDTO.getId();
//        MerchantContact contact = selectByPrimaryKey(id);
//        if (Objects.isNull(contact)) {
//            return ResultDTO.fail(ResultDTOEnum.CONTACT_NOT_FOUND);
//        }
//
////        MerchantContact updateContact = new MerchantContact();
//        MerchantContactCommandReq updateContact = new MerchantContactCommandReq();
//        if (Objects.nonNull(contactDTO.getDefaultFlag())) {
//            //需要将原来的默认地址设置为非默认
//            MerchantContact defaultContact = queryDefaultContact(contact.getTenantId(), contact.getAddressId());
////            MerchantContact defaultContact = merchantContactMapper.queryDefaultContact(contact.getTenantId(), contact.getAddressId());
//            if (Objects.nonNull(defaultContact)) {
//                updateContact.setId(defaultContact.getId());
//                updateContact.setDefaultFlag(ContactDefaultEnum.NOT_DEFAULT.getType());
////                merchantContactMapper.updateByPrimaryKeySelective(updateContact);
//                userCenterMerchantContactFacade.update(updateContact);
//            }
//        }
//
//        BeanUtils.copyProperties(contactDTO, updateContact);
//        userCenterMerchantContactFacade.update(updateContact);
////        merchantContactMapper.updateByPrimaryKeySelective(updateContact);
        return ResultDTO.success();
    }

    @Override
    public ResultDTO addContact(MerchantContactDTO contactDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.notNull(contactDTO.getName(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "收获人名称不能为空");
        AssertCheckParams.notNull(contactDTO.getPhone(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "收获人手机号不能为空");
//        MerchantContact merchantContact = new MerchantContact();
//        merchantContact.setTenantId(loginContextInfoDTO.getTenantId());
//        merchantContact.setAddressId(contactDTO.getAddressId());
//        merchantContact.setName(contactDTO.getName());
//        merchantContact.setPhone(contactDTO.getPhone());
//        merchantContact.setDefaultFlag(ContactDefaultEnum.NOT_DEFAULT.getType());
//        merchantContactMapper.insert(merchantContact);
        MerchantContactCommandReq merchantContactCommandReq = new MerchantContactCommandReq();
        merchantContactCommandReq.setTenantId(loginContextInfoDTO.getTenantId());
        merchantContactCommandReq.setAddressId(contactDTO.getAddressId());
        merchantContactCommandReq.setName(contactDTO.getName());
        merchantContactCommandReq.setPhone(contactDTO.getPhone());
        merchantContactCommandReq.setDefaultFlag(ContactDefaultEnum.NOT_DEFAULT.getType());
        Long id = userCenterMerchantContactFacade.create(merchantContactCommandReq);
        if (NumberConstant.ZERO > id) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }
        return ResultDTO.success();
    }

    @Override
    public ResultDTO deleteContact(LoginContextInfoDTO contextInfoDTO, Long contactId) {
        Long tenantId = contextInfoDTO.getTenantId();
        Long storeId = contextInfoDTO.getStoreId();
        MerchantContact contact = selectByIdAndTenantId(tenantId, contactId);
        if (Objects.isNull(contact)) {
            return ResultDTO.fail(ResultDTOEnum.CONTACT_NOT_FOUND);
        }
        Long addressId = contact.getAddressId();
        List<MerchantContact> contactList = queryMerchantContact(tenantId, addressId);
        if (CollectionUtils.isEmpty(contactList)) {
            return ResultDTO.fail(ResultDTOEnum.CONTACT_NOT_FOUND);
        }
        if (contactList.size() == NumberConstant.ONE) {
            return ResultDTO.fail(ResultDTOEnum.CONTACT_AT_LEAST_ONE);
        }
//        merchantContactMapper.deleteByPrimaryKey(contactId);
        MerchantContactCommandReq merchantContactCommandReq = new MerchantContactCommandReq();
        merchantContactCommandReq.setId(contactId);
        Boolean remove = userCenterMerchantContactFacade.remove(merchantContactCommandReq);
        if (!Boolean.TRUE.equals(remove)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }
        log.info("门店：{}的联系人id：{}删除成功", storeId, contactId);
        return ResultDTO.success();
    }

//    @Override
//    public MerchantContact selectByPrimaryKey(Long id) {
//        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
//        merchantContactQueryReq.setId(id);
//        merchantContactQueryReq.setTenantId(id);
//        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
//        if (CollectionUtil.isNotEmpty(merchantContacts)) {
//            MerchantContactResultResp merchantContactResultResp = merchantContacts.get(NumberConstant.ZERO);
//            return MerchantContactMapperConvert.INSTANCE.respToContact(merchantContactResultResp);
//        }
//        return null;
//    }

    @Override
    public MerchantContact selectByIdAndTenantId(Long tenantId, Long merchantContactId) {
        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
        merchantContactQueryReq.setId(merchantContactId);
        merchantContactQueryReq.setTenantId(tenantId);
        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
        if (CollectionUtil.isNotEmpty(merchantContacts)) {
            MerchantContactResultResp merchantContactResultResp = merchantContacts.get(NumberConstant.ZERO);
            return MerchantContactMapperConvert.INSTANCE.respToContact(merchantContactResultResp);
        }
        return null;
    }

    @Override
    public List<MerchantContact> queryMerchantContact(Long tenantId, Long addressId) {
        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
        merchantContactQueryReq.setTenantId(tenantId);
        merchantContactQueryReq.setAddressId(addressId);
        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
        return MerchantContactMapperConvert.INSTANCE.respListToContactList(merchantContacts);
    }

    @Override
    public List<MerchantContact> queryMerchantContactByStoreId(Long tenantId, Long storeId) {
        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
        merchantContactQueryReq.setTenantId(tenantId);
        merchantContactQueryReq.setStoreId(storeId);
        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
        return MerchantContactMapperConvert.INSTANCE.respListToContactList(merchantContacts);
    }
}
