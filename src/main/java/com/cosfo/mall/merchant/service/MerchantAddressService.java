package com.cosfo.mall.merchant.service;


import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.vo.MerchantAddressVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
public interface MerchantAddressService {
    /**
     * 查询商户地址信息
     */
    ResultDTO getAddressInfo(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询绑定默认地址
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    MerchantAddress queryDefaultAddress(Long storeId, Long tenantId);

    /**
     * 查询绑定默认地址 返回MerchantAddressDTO
     * @param storeId
     * @param tenantId
     * @return
     */
    MerchantAddressDTO queryDefaultAddressDTO(Long storeId, Long tenantId);

    /**
     * 查询下单地址信息
     *
     * @param storeId
     * @param tenantId
     * @param merchantAddressId
     * @param merchantContactId
     * @return
     */
    MerchantAddressVO queryMerchantAddress(Long storeId, Long tenantId, Long merchantAddressId, Long merchantContactId);

    /**
     * 更新地址
     * @param addressDTO
     * @return
     */
    ResultDTO updateAddress(MerchantAddressDTO addressDTO);

    /**
     * 根据id查询地址信息
     * @param id
     * @return
     */
    MerchantAddress selectByPrimaryKey(Long id, Long tenantId);

    /**
     * 查询店铺绑定收货地址
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    MerchantAddress selectByStoreId(Long storeId, Long tenantId);

    /**
     * 批量获取门店绑定收货地址
     *
     * @param storeIds
     * @param tenantId
     * @return
     */
    Map<Long, MerchantAddress> batchQueryByStoreIds(Set<Long> storeIds, Long tenantId);


    /**
     * 查询商户地址信息
     */
    List<MerchantAddress> selectListByStoreId(Long storeId, Long tenantId);

}
