package com.cosfo.mall.merchant.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantContactDTO;
import com.cosfo.mall.merchant.model.po.MerchantContact;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 17:18
 */
public interface MerchantContactService {

    /**
     * 更新联系人
     * @param contactDTO
     * @return
     */
    ResultDTO updateContact(MerchantContactDTO contactDTO);

    /**
     * 新建联系人
     * @param contactDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO addContact(MerchantContactDTO contactDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 删除联系人
     * @param contextInfoDTO
     * @param contactId
     * @return
     */
    ResultDTO deleteContact(LoginContextInfoDTO contextInfoDTO, Long contactId);

//    /**
//     * 根据id查询
//     * @param id
//     * @return
//     */
//    MerchantContact selectByPrimaryKey(Long id);
    /**
     * 根据id查询
     * @param tenantId
     * @param merchantContactId
     * @return
     */
    MerchantContact selectByIdAndTenantId(Long tenantId, Long merchantContactId);

    /**
     * 查询联系人列表
     * @param tenantId
     * @param addressId
     * @return
     */
    List<MerchantContact> queryMerchantContact(Long tenantId, Long addressId);
}
