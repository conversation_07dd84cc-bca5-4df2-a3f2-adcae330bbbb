package com.cosfo.mall.merchant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.bill.model.dto.FinancialBillDTO;
import com.cosfo.mall.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.mall.bill.model.po.FinancialBill;
import com.cosfo.mall.bill.service.FinancialBillService;
import com.cosfo.mall.common.config.AuthInfoConfig;
import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.ContactDefaultEnum;
import com.cosfo.mall.common.context.MerchantAccountTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreStatusEnum;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.sms.SmsSender;
import com.cosfo.mall.common.sms.model.Sms;
import com.cosfo.mall.common.sms.model.SmsSenderFactory;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.common.utils.JwtUtils;
import com.cosfo.mall.common.utils.RedisUtils;
import com.cosfo.mall.common.utils.SpringContextUtil;
import com.cosfo.mall.facade.SmsFacade;
import com.cosfo.mall.facade.auth.AuthClientLoginFacade;
import com.cosfo.mall.facade.auth.AuthUserQueryFacade;
import com.cosfo.mall.facade.auth.AuthUserUpdateFacade;
import com.cosfo.mall.facade.converter.MerchantStoreAccountConverter;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.mall.merchant.convert.MerchantStoreMapperConvert;
import com.cosfo.mall.merchant.model.dto.*;
import com.cosfo.mall.merchant.model.po.*;
import com.cosfo.mall.merchant.model.vo.LoginVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.merchant.service.MerchantContactService;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.system.model.po.SystemParameters;
import com.cosfo.mall.system.service.SystemParameterService;
import com.cosfo.mall.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.mall.tenant.model.po.TenantCommonConfig;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.wechat.model.vo.SessionKeyVo;
import com.cosfo.mall.wechat.service.WeChatAuthService;
import com.cosfo.mall.wechat.service.WeixinService;
import com.cosfo.message.client.enums.SMSTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.input.user.AuthUserPasswordUpdateInput;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.i18n.exception.I18nBizException;
import net.xianmu.usercenter.client.merchant.req.*;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 门店服务层
 * @date 2022/5/21 15:20
 */
@Service
@Slf4j
public class MerchantStoreServiceImpl implements MerchantStoreService {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SmsSenderFactory smsSenderFactory;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private MerchantContactService merchantContactService;
    @Resource
    private WeixinService weixinService;
    @Resource
    private FinancialBillService financialBillService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private SystemParameterService systemParameterService;
    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private AuthClientLoginFacade authClientLoginFacade;
    @Resource
    private WeChatAuthService weChatAuthService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private AuthInfoConfig authInfoConfig;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private AuthUserQueryFacade authUserQueryFacade;
    @Resource
    private AuthUserUpdateFacade authUserUpdateFacade;
    @Resource
    private SmsFacade smsFacade;

//    @Override
//    public ResultDTO login(LoginDTO loginDTO) {
//        String openId = loginDTO.getOpenId();
//        Long storeId = loginDTO.getStoreId();
//        String phone = loginDTO.getPhone();
//
//        // 查询账户信息
//        MerchantStoreAccount query = new MerchantStoreAccount();
//        query.setPhone(phone);
//        query.setStoreId(storeId);
//        query.setStatus(MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus());
//        query.setDeleteFlag(com.cosfo.mall.common.context.MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
//
//        MerchantStoreAccount account = merchantStoreAccountService.selectOne(query);
//        if (Objects.isNull(account)) {
//            return ResultDTO.fail(ResultDTOEnum.ACCOUNT_NOT_FOUND);
//        }
//
//        // 处理对应门店信息
//        MerchantStore store = queryStoreInfo(storeId);
//
//        if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.AUDITING.getStatus())) {
//            return ResultDTO.fail(ResultDTOEnum.STORE_AUDITING, store);
//        }
//        if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.AUDIT_REFUSE.getStatus())) {
//            //门店信息、审核失败原因
//            return ResultDTO.fail(ResultDTOEnum.STORE_REFUSE, store);
//        }
//        if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.CLOSE.getStatus())) {
//            return ResultDTO.fail(ResultDTOEnum.STORE_HAS_CLOSED, store);
//        }
//
//        // 刷新登录时间
//        merchantStoreAccountService.refreshLoginTimeAndOpenId(account.getId(), openId);
//
//        // T掉当前account的登录信息
//        merchantStoreAccountService.removeUserCache(account.getId());
//
//        // 缓存用户信息
//        account.setOpenId(openId);
//        String accessToken = generateUserCache(account, store);
//
//        // 返回门店信息
//        LoginSuccessReturnDTO loginSuccessReturnDto = assemblyStoreInfo(accessToken, store, account);
//
//        log.info("账号：{}登录了门店：{}", account.getAccountName(), store.getStoreName());
//        return ResultDTO.success(loginSuccessReturnDto);
//    }

    @Override
    public ResultDTO newLogin(LoginDTO loginDto) {
        String openId = loginDto.getOpenId();
        Long storeId = loginDto.getStoreId();
        String phone = loginDto.getPhone();
        // 查询账户信息
        MerchantStoreAccount query = new MerchantStoreAccount();
        query.setPhone(phone);
        query.setStoreId(storeId);
        query.setStatus(MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus());
        query.setDeleteFlag(com.cosfo.mall.common.context.MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());

        MerchantStoreAccount account = merchantStoreAccountService.selectOne(query);
        if (Objects.isNull(account)) {
            return ResultDTO.fail(ResultDTOEnum.ACCOUNT_NOT_FOUND);
        }

        // 处理对应门店信息
        MerchantStore store = queryStoreInfo(storeId);

        if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.AUDITING.getStatus())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_AUDITING, store);
        }
        if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.AUDIT_REFUSE.getStatus())) {
            //门店信息、审核失败原因
            return ResultDTO.fail(ResultDTOEnum.STORE_REFUSE, store);
        }
        if (Objects.equals(store.getStatus(), MerchantStoreStatusEnum.CLOSE.getStatus())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_HAS_CLOSED, store);
        }

        String token = getTokenByAuthLogin(openId, phone, account, store);
        // 移除首次可选择标识
        redisTemplate.delete(RedisKeyEnum.C00013.join(phone));

        // 返回门店信息
        LoginSuccessReturnDTO loginSuccessReturnDto = assemblyStoreInfo(token, store, account);

        log.info("账号：{}登录了门店：{}", account.getAccountName(), store.getStoreName());

        return ResultDTO.success(loginSuccessReturnDto);
    }

    /**
     * 通过auth登录接口获取token信息
     * @param openId
     * @param phone
     * @param account
     * @param store
     * @return
     */
    private String getTokenByAuthLogin(String openId, String phone, MerchantStoreAccount account, MerchantStore store) {
        Long expireTime = authInfoConfig.getExpireTime();

        AuthLoginDto authLoginDto = authClientLoginFacade.authClientLogin(account.getTenantId(), openId, phone, account.getId(), expireTime);
        merchantStoreAccountService.removeUserCache(account.getId());
        // 缓存用户信息
        account.setOpenId(openId);
        String token = authLoginDto.getToken();
        generateUserCache(account, store, token, expireTime);
        return token;
    }

//    private String generateUserCache(MerchantStoreAccount account, MerchantStore store) {
//        String token = JwtUtils.generateToken(account.getTenantId(), account.getStoreId(), account.getId(), JwtUtils.EXPIRATION_TIME);
//
//        // 用户信息进行缓存
//        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//        loginContextInfoDTO.setTenantId(account.getTenantId());
//        loginContextInfoDTO.setStoreId(account.getStoreId());
//        loginContextInfoDTO.setAccountId(account.getId());
//        loginContextInfoDTO.setStoreName(store.getStoreName());
//        loginContextInfoDTO.setJwtToken(token);
//        loginContextInfoDTO.setOpenId(account.getOpenId());
//        loginContextInfoDTO.setPhone(account.getPhone());
//        loginContextInfoDTO.setAccountName(account.getAccountName());
//        // 简化token长度
//        String userCacheKey = JwtUtils.TOKEN_PREFIX + account.getId();
//        String loginCache = JSONObject.toJSONString(loginContextInfoDTO);
//        redisUtils.set(userCacheKey, loginCache, JwtUtils.EXPIRATION_TIME);
//        return token;
//    }

    private String generateUserCache(MerchantStoreAccount account, MerchantStore store, String token, Long expireTime) {
        // 用户信息进行缓存
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(account.getTenantId());
        loginContextInfoDTO.setStoreId(account.getStoreId());
        loginContextInfoDTO.setAccountId(account.getId());
        loginContextInfoDTO.setStoreName(store.getStoreName());
        loginContextInfoDTO.setJwtToken(token);
        loginContextInfoDTO.setOpenId(account.getOpenId());
        loginContextInfoDTO.setPhone(account.getPhone());
        loginContextInfoDTO.setAccountName(account.getAccountName());
        // 简化token长度
        String userCacheKey = JwtUtils.TOKEN_PREFIX + account.getId();
        String loginCache = JSONObject.toJSONString(loginContextInfoDTO);
        redisUtils.set(userCacheKey, loginCache, TimeUnit.HOURS.toMillis(expireTime));
        return token;
    }

    /**
     * 装配返回的门店信息
     *
     * @param accessToken
     * @param store
     * @param account
     * @return
     */
    private LoginSuccessReturnDTO assemblyStoreInfo(String accessToken, MerchantStore store, MerchantStoreAccount account) {
        LoginSuccessReturnDTO loginSuccessReturnDto = new LoginSuccessReturnDTO();
        Merchant merchant = userCenterMerchantInfoFacade.getMerchantByTenantId(store.getTenantId());
        loginSuccessReturnDto.setStoreId(store.getId());
        loginSuccessReturnDto.setToken(accessToken);
        loginSuccessReturnDto.setStoreName(store.getStoreName());
        loginSuccessReturnDto.setPhone(account.getPhone());
        loginSuccessReturnDto.setLogoImage(merchant.getLogoImage());
        loginSuccessReturnDto.setBackgroundImage(merchant.getBackgroundImage());
        loginSuccessReturnDto.setBillSwitch(store.getBillSwitch());
        loginSuccessReturnDto.setOnlinePayment(store.getOnlinePayment());
        loginSuccessReturnDto.setAccountId(account.getId());
        loginSuccessReturnDto.setAccountName(account.getAccountName());
        loginSuccessReturnDto.setType(account.getType());
        loginSuccessReturnDto.setStoreNo(store.getStoreNo());
        TenantCommonConfig config = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(store.getTenantId(), TenantCommonConfigKeyEnum.CONSULTATION_ENTRANCE.getConfigKey());
        loginSuccessReturnDto.setConsultationEntranceSwitch(transferConsultationEntrance(config));
        //poi
        MerchantAddress address = merchantAddressService.selectByStoreId(store.getId(), account.getTenantId());
        loginSuccessReturnDto.setPoiNote(Objects.isNull(address) ? Constants.EMPTY_STRING : address.getPoiNote());

        // 是否存在账单
        List<FinancialBillDTO> financialBillDTOS = financialBillService.queryFinancialBill(store.getId(), store.getTenantId());
        loginSuccessReturnDto.setHavingBill(!CollectionUtils.isEmpty(financialBillDTOS));
        // 查询账期规则
        FinancialBillRuleDTO financialBillRuleDTO = financialBillService.queryBillRule(store.getTenantId());
        if (!Objects.isNull(financialBillRuleDTO)) {
            loginSuccessReturnDto.setBillType(financialBillRuleDTO.getType());
            loginSuccessReturnDto.setDay(financialBillRuleDTO.getDay());
        }
        return loginSuccessReturnDto;
    }

    private Integer transferConsultationEntrance(TenantCommonConfig config) {
        Integer consultationEntranceSwitch;
        try {
            String consultationEntrance = Optional.ofNullable(config).map(TenantCommonConfig::getConfigValue).orElse(TenantCommonConfigKeyEnum.CONSULTATION_ENTRANCE.getDefaultValue());
            consultationEntranceSwitch = Integer.parseInt(consultationEntrance);
        } catch (Exception e) {
            log.info("config数据异常,config:{}", JSON.toJSONString(config));
            consultationEntranceSwitch = Integer.parseInt(TenantCommonConfigKeyEnum.CONSULTATION_ENTRANCE.getDefaultValue());
        }
        return consultationEntranceSwitch;
    }

    @Override
    public ResultDTO sendCode(String phone, Long tenantId) {
        // 无注册权限的租户，前置校验手机号
        if (!canRegistry(tenantId)) {
            // 通过手机号查询用户信息是否已经存在
            MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
            merchantStoreAccountQueryReq.setTenantId(tenantId);
            merchantStoreAccountQueryReq.setPhone(phone);
            merchantStoreAccountQueryReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getFlag());
            List<MerchantStoreAccountResultResp> merchantStoreAccounts = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
//            List<MerchantStoreAccount> merchantStoreAccounts = merchantStoreAccountMapper.selectByPhoneAndTenantId(tenantId, phone);
            long success = merchantStoreAccounts.stream().filter(el -> Objects.equals(el.getStatus(), MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus())).count();
            long count = merchantStoreAccounts.stream().filter(el -> Objects.equals(el.getStatus(), MerchantStoreAccountStatusEnum.AUDITING.getStatus())).count();
            if (success == NumberConstant.ZERO && count > NumberConstant.ZERO) {
                return ResultDTO.fail(ResultDTOEnum.PHONE_ALL_WAIT_AUDIT_EXIST);
            }
            if (success == NumberConstant.ZERO) {
                return ResultDTO.fail(ResultDTOEnum.PHONE_NOT_EXIST);
            }
        }

        RLock lock = redissonClient.getLock(RedisKeyEnum.C00014.join(phone));
        if (!lock.tryLock()) {
            throw new I18nBizException("{0}正在发送验证码，请稍后再试", phone);
        }

        try {
            String cacheKey = RedisKeyEnum.C00015.join(phone);
            if (Objects.nonNull(redisTemplate.opsForValue().get(cacheKey)) && !grayReleaseConfig.getReturnCodePhoneSet().contains(phone)) {
                throw new I18nBizException("{0}近一分钟已发送验证码，请稍后再试", phone);
            }

            String code = SmsSender.buildRandom(6) + Constants.EMPTY_STRING;

            // 发送验证码
            smsFacade.sendSms (phone,Arrays.asList(code));
            // 缓存
            String key = Constants.CODE_PREFIX + phone;
            redisUtils.set(key, code, Constants.EXPIRATION_TIME);
            // 设置发送标记
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstant.ONE), grayReleaseConfig.getSmsSendIntervalTime(), TimeUnit.MILLISECONDS);
            log.info("phone：{} code：{}", phone, code);
            if (!SpringContextUtil.isPro() && grayReleaseConfig.getReturnCodePhoneSet().contains(phone)) {
                //测试环境返回code
                HashMap<String, String> result = new HashMap<>(NumberConstant.ONE);
                result.put(Constants.CODE, code);
                return ResultDTO.success(result);
            }
        } finally {
            lock.unlock();
        }

        return ResultDTO.success();
    }

    @Override
    public Boolean examineCode(String phone, String examineCode) {
        if (Objects.isNull(phone) || Objects.isNull(examineCode)) {
            throw new ParamsException("校验验证码参数缺失");
        }
        SystemParameters testPhone = systemParameterService.selectByKey(SysParamKeyEnum.PRODUCT_TEST_PHONE.getKey());
        SystemParameters testCode = systemParameterService.selectByKey(SysParamKeyEnum.PRODUCT_TEST_CODE.getKey());
        if (Objects.equals(testPhone.getParamValue(), phone) && Objects.equals(testCode.getParamValue(), examineCode)) {
            return Boolean.TRUE;
        }

        // 无缓存
        String key = Constants.CODE_PREFIX + phone;
        Object codeObj = redisUtils.get(key);
        if (Objects.isNull(codeObj)) {
            return Boolean.FALSE;
        }
        // 验证码错误
        if (!Objects.equals(codeObj.toString(), examineCode)) {
            return Boolean.FALSE;
        }

        // 绑定门店信息
        return Boolean.TRUE;
    }

    /**
     * 校验该租户是否开启注册权限
     *
     * @return true有权限，false无权限
     */
    private boolean canRegistry(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return Boolean.TRUE;
        }

        TenantCommonConfig registryConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigKeyEnum.REGISTRY_SWITCH.getConfigKey());
        String registrySwitch = Optional.ofNullable(registryConfig).map(TenantCommonConfig::getConfigValue).orElse(TenantConfigKeyEnum.REGISTRY_SWITCH.getDefaultValue());
        return CommonSwitchEnum.OPEN.getCode().equals(registrySwitch);
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultDTO submitStoreInfo(MerchantStoreDTO storeDTO) {
        // 校验手机号不能为空
        String phone = storeDTO.getPhone();
        AssertCheckParams.notNull(phone, ResultDTOEnum.PARAMETER_MISSING.getCode(), "手机号不能为空");
        String verificationCode = storeDTO.getVerificationCode();
        if(verificationCode != null && !examineCode(phone, verificationCode)) {
            log.error("验证码校验不通过: phone :{}, verificationCode:{}", phone, verificationCode);
            return ResultDTO.fail(ResultDTOEnum.EXAMINE_CODE_FAIL);
        }

        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantStoreMapperConvert.INSTANCE.dtoToStoreCommandReq(storeDTO);
        merchantStoreCommandReq.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
        // 门店信息入库
        merchantStoreCommandReq.setRegisterTime(LocalDateTime.now());
        merchantStoreCommandReq.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
        merchantStoreCommandReq.setBillSwitch(BillSwitchEnum.SHUTDOWN.getCode());
        merchantStoreCommandReq.setOnlinePayment(OnlinePaymentEnum.OPEN.getCode());
        RegionalOrganizationResultResp regionalOrganizationResultResp = userCenterMerchantStoreFacade.saasGetRegionalByTenantId(storeDTO.getTenantId());
        if (Objects.isNull(regionalOrganizationResultResp)) {
            throw new BizException("租户默认区域组织不存在");
        }
        if (!canRegistry(storeDTO.getTenantId())) {
            return ResultDTO.fail(ResultDTOEnum.SERVER_ERROR.getCode(), ResultDTOEnum.REGISTRY_CLOSE.getMessage());
        }
        merchantStoreCommandReq.setRegionalId(regionalOrganizationResultResp.getId());
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);
        // 门店账号信息入库
        MerchantStoreAccount storeAccount = new MerchantStoreAccount();
        storeAccount.setTenantId(storeDTO.getTenantId());
        storeAccount.setAccountName(storeDTO.getStoreName());
        storeAccount.setPhone(storeDTO.getPhone());
        storeAccount.setUsername(storeDTO.getUsername());
        storeAccount.setType(MerchantAccountTypeEnum.MANAGER.getType());
        storeAccount.setRegisterTime(LocalDateTime.now());
        storeAccount.setOpenId(storeDTO.getOpenId());
        storeAccount.setUnionId(storeDTO.getUnionId());
        storeAccount.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
        MerchantStoreAccountCommandReq merchantStoreAccountCommandReq = MerchantStoreAccountConverter.INSTANCE.accountToCommangReq(storeAccount);
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = Collections.singletonList(merchantStoreAccountCommandReq);

        // 门店地址信息入库
        MerchantAddressCommandReq merchantAddressCommandReq = new MerchantAddressCommandReq();
        merchantAddressCommandReq.setTenantId(storeDTO.getTenantId());
        merchantAddressCommandReq.setProvince(storeDTO.getProvince());
        merchantAddressCommandReq.setCity(storeDTO.getCity());
        merchantAddressCommandReq.setArea(storeDTO.getArea());
        merchantAddressCommandReq.setAddress(storeDTO.getAddress());
        merchantAddressCommandReq.setHouseNumber(storeDTO.getHouseNumber());
        merchantAddressCommandReq.setPoiNote(storeDTO.getPoiNote());
        merchantAddressCommandReq.setDefaultFlag(MerchantAddressDefaultTypeEnum.DEFAULT.getCode());
        merchantAddressCommandReq.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        MerchantContactCommandReq contact = new MerchantContactCommandReq();
        contact.setTenantId(storeDTO.getTenantId());
        contact.setName(storeDTO.getStoreName());
        contact.setPhone(storeDTO.getPhone());
        contact.setDefaultFlag(ContactDefaultEnum.DEFAULT.getType());
        merchantAddressCommandReq.setMerchantContactList(Collections.singletonList(contact));
        merchantStoreDomainCommandReq.setMerchantAddressList(Collections.singletonList(merchantAddressCommandReq));
        merchantStoreDomainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);
        Long id = userCenterMerchantStoreFacade.createMerchantStoreInfo(merchantStoreDomainCommandReq);
        if (NumberConstant.ZERO > id) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }
        log.info("门店：{}在租户：{}下进行了注册", storeDTO.getStoreName(), storeDTO.getTenantId());
        return ResultDTO.success();
//        // 校验门店名称
//        ResultDTOEnum resultDTOEnum = checkStoreInfo(storeDTO);
//        if (!Objects.equals(resultDTOEnum, ResultDTOEnum.SUCCESS)) {
//            return ResultDTO.fail(resultDTOEnum);
//        }
//
//        MerchantStore merchantStore = merchantStoreMapper.selectByName(storeDTO.getTenantId(), storeDTO.getStoreName());
//        if (Objects.nonNull(merchantStore)) {
//            return ResultDTO.fail(ResultDTOEnum.STORE_NAME_REPEAT);
//        }
//
//        Long tenantId = storeDTO.getTenantId();
//        TenantResultResp tenantResultResp = userCenterTenantFacade.getTenantById(tenantId);
//        Tenant tenant = TenantInfoMapper.INSTANCE.respToTenant(tenantResultResp);
////        Tenant tenant = tenantMapper.selectByPrimaryKey(tenantId);
//        if (Objects.isNull(tenant)) {
//            return ResultDTO.fail(ResultDTOEnum.TENANT_NOT_FOUND);
//        }
//
//        if (!canRegistry(tenantId)) {
//            return ResultDTO.fail(ResultDTOEnum.REGISTRY_CLOSE);
//        }
//
//        // 查看租户维度下门店个数
//        Long storeNum = merchantStoreMapper.countStoreNum(tenantId);
//        // 查询租户下的默认分组
//        MerchantStoreGroup merchantStoreGroup = merchantStoreGroupRepository.queryDefaultGroup(tenantId);
//
//        // 门店信息入库
//        MerchantStore store = new MerchantStore();
//        store.setTenantId(storeDTO.getTenantId());
//        store.setStoreName(storeDTO.getStoreName());
//        store.setRegisterTime(LocalDateTime.now());
//        store.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
//        store.setBillSwitch(BillSwitchEnum.SHUTDOWN.getCode());
//        store.setOnlinePayment(OnlinePaymentEnum.OPEN.getCode());
//        store.setStoreNo(String.valueOf(storeNum + 1));
//
//        merchantStoreMapper.insert(store);
//
//        // 插入默认分组
//        MerchantStoreGroupMapping groupMapping = new MerchantStoreGroupMapping();
//        groupMapping.setTenantId(tenantId);
//        groupMapping.setGroupId(merchantStoreGroup.getId());
//        groupMapping.setStoreId(store.getId());
//        merchantStoreGroupMappingRepository.save(groupMapping);
//
//        // 门店账号信息入库
//        MerchantStoreAccount storeAccount = new MerchantStoreAccount();
//        storeAccount.setTenantId(storeDTO.getTenantId());
//        storeAccount.setStoreId(store.getId());
//        storeAccount.setAccountName(storeDTO.getStoreName());
//        storeAccount.setPhone(storeDTO.getPhone());
//        storeAccount.setType(MerchantAccountTypeEnum.MANAGER.getType());
//        storeAccount.setRegisterTime(LocalDateTime.now());
//        storeAccount.setOpenId(storeDTO.getOpenId());
//        storeAccount.setUnionId(storeDTO.getUnionId());
//        storeAccount.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
//        merchantStoreAccountMapper.insert(storeAccount);
//
//        // 门店地址信息入库
//        MerchantAddress address = new MerchantAddress();
//        address.setTenantId(storeDTO.getTenantId());
//        address.setStoreId(store.getId());
//        address.setProvince(storeDTO.getProvince());
//        address.setCity(storeDTO.getCity());
//        address.setArea(storeDTO.getArea());
//        address.setAddress(storeDTO.getAddress());
//        address.setHouseNumber(storeDTO.getHouseNumber());
//        address.setPoiNote(storeDTO.getPoiNote());
//        merchantAddressMapper.insert(address);
//
//        // 联系人信息入库
//        MerchantContact contact = new MerchantContact();
//        contact.setTenantId(storeDTO.getTenantId());
//        contact.setAddressId(address.getId());
//        contact.setName(storeDTO.getStoreName());
//        contact.setPhone(storeDTO.getPhone());
//        contact.setDefaultFlag(ContactDefaultEnum.DEFAULT.getType());
//        merchantContactMapper.insert(contact);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultDTO resubmitStoreInfo(MerchantStoreDTO storeDTO) {
        Long tenantId = storeDTO.getTenantId();
        String storeName = storeDTO.getStoreName();

        if (!canRegistry(tenantId)) {
            return ResultDTO.fail(ResultDTOEnum.REGISTRY_CLOSE);
        }

        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        // 门店地址信息入库
        Long storeId = storeDTO.getId();
        MerchantStore store = queryStoreInfo(storeId);
        if (Objects.isNull(store)) {
            return ResultDTO.fail("门店信息不存在");
        }
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantStoreMapperConvert.INSTANCE.dtoToStoreCommandReq(storeDTO);
        merchantStoreCommandReq.setId(storeId);
        merchantStoreCommandReq.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);
        MerchantAddress address = merchantAddressService.selectByStoreId(storeId, tenantId);
        MerchantAddressCommandReq merchantAddressCommandReq = new MerchantAddressCommandReq();
        merchantAddressCommandReq.setId(address.getId());
        merchantAddressCommandReq.setTenantId(storeDTO.getTenantId());
        merchantAddressCommandReq.setStoreId(storeDTO.getId());
        merchantAddressCommandReq.setProvince(storeDTO.getProvince());
        merchantAddressCommandReq.setCity(storeDTO.getCity());
        merchantAddressCommandReq.setArea(storeDTO.getArea());
        merchantAddressCommandReq.setAddress(storeDTO.getAddress());
        merchantAddressCommandReq.setHouseNumber(storeDTO.getHouseNumber());
        merchantAddressCommandReq.setPoiNote(storeDTO.getPoiNote());
        merchantAddressCommandReq.setDefaultFlag(DefaultFlagEnum.IS_DEFAULT.getCode());

        //门店名称发生变化 变更对应的账号、联系人
        MerchantStoreAccount queryAccount = new MerchantStoreAccount();
        queryAccount.setStoreId(storeId);
        MerchantStoreAccount account = merchantStoreAccountService.selectOne(queryAccount);
        merchantStoreDomainCommandReq.setStoreId(storeId);
        MerchantStoreAccountCommandReq updateAccount = new MerchantStoreAccountCommandReq();
        updateAccount.setId(account.getId());
        updateAccount.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
        if (!Objects.equals(storeName, store.getStoreName())) {
            //账号更新
            updateAccount.setAccountName(storeName);

            //联系人更新
            List<MerchantContact> merchantContacts = merchantContactService.queryMerchantContact(tenantId, address.getId());
            List<MerchantContactCommandReq> merchantContactList = merchantContacts.stream().map(merchantContact -> {
                MerchantContactCommandReq contact = new MerchantContactCommandReq();
                contact.setId(merchantContact.getId());
                contact.setName(storeDTO.getStoreName());
                return contact;
            }).collect(Collectors.toList());
            merchantAddressCommandReq.setMerchantContactList(merchantContactList);
        }
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = Collections.singletonList(updateAccount);

        merchantStoreDomainCommandReq.setMerchantAddressList(Collections.singletonList(merchantAddressCommandReq));
        merchantStoreDomainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);
        Boolean update = userCenterMerchantStoreFacade.updateMerchantStoreInfo(merchantStoreDomainCommandReq);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }

//        // 校验门店名称
//        ResultDTOEnum resultDTOEnum = checkStoreInfo(storeDTO);
//        if (!Objects.equals(resultDTOEnum, ResultDTOEnum.SUCCESS)) {
//            return ResultDTO.fail(resultDTOEnum);
//        }
//
//        Long storeId = storeDTO.getId();
//        MerchantStore store = queryStoreInfo(storeId);
//        if (Objects.isNull(store)) {
//            return ResultDTO.fail(ResultDTOEnum.STORE_INFO_NOT_FOUND);
//        }
//
//        if (!Objects.equals(store.getStoreName(), storeDTO.getStoreName())) {
//            MerchantStore merchantStore = merchantStoreMapper.selectByName(storeDTO.getTenantId(), storeDTO.getStoreName());
//            if (Objects.nonNull(merchantStore)) {
//                return ResultDTO.fail(ResultDTOEnum.STORE_NAME_REPEAT);
//            }
//        }
//
//
//        //地址变更
////        MerchantAddress address = merchantAddressMapper.selectByStoreId(storeId, tenantId);
//        MerchantAddress address = merchantAddressService.selectByStoreId(storeId, tenantId);
//        MerchantAddress updateAddress = new MerchantAddress();
//        updateAddress.setId(address.getId());
//        updateAddress.setProvince(storeDTO.getProvince());
//        updateAddress.setCity(storeDTO.getCity());
//        updateAddress.setArea(storeDTO.getArea());
//        updateAddress.setAddress(storeDTO.getAddress());
//        updateAddress.setHouseNumber(storeDTO.getHouseNumber());
//        updateAddress.setPoiNote(storeDTO.getPoiNote());
//        merchantAddressMapper.updateByPrimaryKeySelective(updateAddress);
//
//        //门店名称发生变化 变更对应的账号、联系人
//        MerchantStoreAccount queryAccount = new MerchantStoreAccount();
//        queryAccount.setStoreId(storeId);
//        MerchantStoreAccount account = merchantStoreAccountMapper.selectOne(queryAccount);
//        if (!Objects.equals(storeName, store.getStoreName())) {
//            MerchantStore updateStore = new MerchantStore();
//            updateStore.setId(storeId);
//            updateStore.setStoreName(storeName);
//            updateStore.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
//            merchantStoreMapper.updateByPrimaryKeySelective(updateStore);
//
//            //账号更新
//            MerchantStoreAccount updateAccount = new MerchantStoreAccount();
//            updateAccount.setId(account.getId());
//            updateAccount.setAccountName(storeName);
//            updateAccount.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
//            merchantStoreAccountMapper.updateByPrimaryKeySelective(updateAccount);
//
//            //联系人更新
//            MerchantContact contact = merchantContactMapper.selectByAddressId(address.getId(), tenantId);
//            MerchantContact updateContact = new MerchantContact();
//            updateContact.setId(contact.getId());
//            updateContact.setName(storeName);
//            merchantContactMapper.updateByPrimaryKeySelective(updateContact);
//        } else {
//            // 门店状态改成待审核
//            MerchantStore updateStore = new MerchantStore();
//            updateStore.setId(storeId);
//            updateStore.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
//            merchantStoreMapper.updateByPrimaryKeySelective(updateStore);
//
//            MerchantStoreAccount updateAccount = new MerchantStoreAccount();
//            updateAccount.setId(account.getId());
//            updateAccount.setStatus(MerchantStoreStatusEnum.AUDITING.getStatus());
//            merchantStoreAccountMapper.updateByPrimaryKeySelective(updateAccount);
//        }

        return ResultDTO.success();
    }


    @Override
    public ResultDTO<Boolean> checkRegistered(MerchantStoreDTO merchantStoreDTO) {
        return checkRegistered(merchantStoreDTO, Boolean.TRUE);
    }

    @Override
    public ResultDTO<Boolean> checkRegistered(MerchantStoreDTO merchantStoreDTO, Boolean querySessionKey) {
        AssertCheckParams.notNull(merchantStoreDTO.getTenantId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        AssertCheckParams.notNull(merchantStoreDTO.getPhone(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "手机号不能为空");

        querySessionKey(merchantStoreDTO, querySessionKey);

        // 通过手机号查询用户信息是否已经存在
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(merchantStoreDTO.getTenantId());
        merchantStoreAccountQueryReq.setPhone(merchantStoreDTO.getPhone());
        merchantStoreAccountQueryReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getFlag());
        List<MerchantStoreAccountResultResp> merchantStoreAccounts = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);
        // 未开启注册功能，无审核通过的用户信息，就抛出异常中断
        if (!canRegistry(merchantStoreDTO.getTenantId())) {
            long success = merchantStoreAccounts.stream().filter(el -> Objects.equals(el.getStatus(), MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus())).count();
            long count = merchantStoreAccounts.stream().filter(el -> Objects.equals(el.getStatus(), MerchantStoreAccountStatusEnum.AUDITING.getStatus())).count();
            if (success == NumberConstant.ZERO && count > NumberConstant.ZERO) {
                return ResultDTO.fail(ResultDTOEnum.PHONE_ALL_WAIT_AUDIT_EXIST);
            }
            if (success == NumberConstant.ZERO) {
                return ResultDTO.fail(ResultDTOEnum.PHONE_NOT_EXIST);
            }
        }

        long count = merchantStoreAccounts.stream().filter(el -> !Objects.equals(el.getStatus(), MerchantStoreAccountStatusEnum.CLOSED.getStatus())).count();
        if (count == NumberConstant.ZERO) {
            // 未注册去填写门店信息
            return ResultDTO.success(false);
        }

        // 放行登录
        return ResultDTO.success(true);
    }

    private void querySessionKey(MerchantStoreDTO merchantStoreDTO, Boolean querySessionKey) {
        if (!querySessionKey) {
            return;
        }
        if (!merchantStoreDTO.getH5Request()) {
            AssertCheckParams.notNull(merchantStoreDTO.getAppId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "appId不能为空");
            AssertCheckParams.notNull(merchantStoreDTO.getCode(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "code不能为空");
            // 获取openId
            try {
                SessionKeyVo sessionKeyVo = weChatAuthService.getMiniProgramSessionKey(merchantStoreDTO.getAppId(), merchantStoreDTO.getCode());

                merchantStoreDTO.setOpenId(sessionKeyVo.getOpenId());
            } catch (Exception e) {
                throw new DefaultServiceException("获取微信唯一标识OpenId失败");
            }
        }
    }


    @Override
    public Boolean newLoginOut(LoginContextInfoDTO loginContextInfoDTO) {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        authClientLoginFacade.logout(loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getJwtToken());
        merchantStoreAccountService.removeUserCache(loginContextInfoDTO.getAccountId());
        return true;
    }

    @Override
    public MerchantStoreDTO queryStoreDtoInfo(Long storeId) {
        MerchantStore merchantStore = queryStoreInfo(storeId);
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(merchantStore, merchantStoreDTO);
        return merchantStoreDTO;
    }

    @Override
    public MerchantStore queryStoreInfo(Long storeId) {
        AssertCheckParams.notNull(storeId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "门店Id不能为空");
        MerchantStoreResultResp merchantStoreResultResp = userCenterMerchantStoreFacade.getMerchantStoreById(storeId);
        MerchantStore merchantStore = MerchantStoreMapperConvert.INSTANCE.RespToStore(merchantStoreResultResp);
//        MerchantStore merchantStore = merchantStoreMapper.selectByPrimaryKey(storeId);
        return merchantStore;
    }

    @Override
    public Map<Long, MerchantStore> queryStoreInfoMap(Set<Long> storeIds) {
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(Lists.newArrayList(storeIds));
        return merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, MerchantStoreMapperConvert.INSTANCE::RespToStore, (v1, v2) -> v1));
    }

    @Override
    public ResultDTO getBillInfo(LoginContextInfoDTO contextInfoDTO) {
        // 返回门店信息
//        Merchant merchant = merchantMapper.selectByTenantId(contextInfoDTO.getTenantId());
        Merchant merchant = userCenterMerchantInfoFacade.getMerchantByTenantId(contextInfoDTO.getTenantId());
//        MerchantStore store = merchantStoreMapper.selectByPrimaryKey(contextInfoDTO.getStoreId());
        MerchantStore store = queryStoreInfo(contextInfoDTO.getStoreId());
        MerchantStoreDTO storeDTO = new MerchantStoreDTO();
        storeDTO.setStoreName(store.getStoreName());
        storeDTO.setLogoImage(merchant.getLogoImage());
        storeDTO.setBackgroundImage(merchant.getBackgroundImage());
        storeDTO.setBillSwitch(store.getBillSwitch());
        storeDTO.setOnlinePayment(store.getOnlinePayment());

        // 是否存在账单
        List<FinancialBill> financialBills = financialBillService.queryNeedPayBill(contextInfoDTO.getStoreId(), contextInfoDTO.getTenantId());

        storeDTO.setHavingBill(!(CollectionUtils.isEmpty(financialBills) && BillSwitchEnum.SHUTDOWN.getCode().equals(store.getBillSwitch())));
        // 查询账期规则
        FinancialBillRuleDTO financialBillRuleDTO = financialBillService.queryBillRule(store.getTenantId());
        if (!Objects.isNull(financialBillRuleDTO)) {
            storeDTO.setBillType(financialBillRuleDTO.getType());
            storeDTO.setDay(financialBillRuleDTO.getDay());
        }

        return ResultDTO.success(storeDTO);
    }


    @Override
    public ResultDTO<LoginSuccessReturnDTO> newChangeStore(Long id, LoginContextInfoDTO loginContextInfoDTO) {
        newLoginOut(loginContextInfoDTO);
        // 调用登录接口
        LoginDTO loginDto = new LoginDTO();
        loginDto.setStoreId(id);
        loginDto.setOpenId(loginContextInfoDTO.getOpenId());
        loginDto.setPhone(loginContextInfoDTO.getPhone());
        loginDto.setTenantId(loginContextInfoDTO.getTenantId());
        return newLogin(loginDto);
    }

    @Override
    public ResultDTO<LoginVO> smsCodeLogin(SmsLoginDTO smsLoginDTO) {
        if (!smsLoginDTO.getH5Request()) {
            AssertCheckParams.notNull(smsLoginDTO.getWeChatCode(), ResultStatusEnum.SERVER_ERROR.getStatus(), "小程序登录weChatCode不能为空");
            AssertCheckParams.notNull(smsLoginDTO.getAppId(), ResultStatusEnum.SERVER_ERROR.getStatus(), "小程序登录appId不能为空");
        }

        LoginVO loginVO = LoginVO.builder().phone(smsLoginDTO.getPhone()).build();
        // 校验验证码
        Boolean success = examineCode(smsLoginDTO.getPhone(), smsLoginDTO.getCode());
        if (!success) {
            return ResultDTO.fail(ResultDTOEnum.EXAMINE_CODE_FAIL);
        }

        loginVO = loginV2(smsLoginDTO.getAppId(), smsLoginDTO.getWeChatCode(), smsLoginDTO.getPhone(), smsLoginDTO.getH5Request(), smsLoginDTO.getTenantId(), loginVO);
        return ResultDTO.success(loginVO);
    }

    @Override
    public ResultDTO<LoginVO> usernamePasswordLogin(UsernameLoginDTO usernameLoginDTO) {
        if (!usernameLoginDTO.getH5Request()) {
            AssertCheckParams.notNull(usernameLoginDTO.getWeChatCode(), ResultStatusEnum.SERVER_ERROR.getStatus(), "小程序登录weChatCode不能为空");
            AssertCheckParams.notNull(usernameLoginDTO.getAppId(), ResultStatusEnum.SERVER_ERROR.getStatus(), "小程序登录appId不能为空");
        }

        // 校验验用户密码
        String username = usernameLoginDTO.getUsername();
        boolean success = authUserQueryFacade.checkUsernamePassword(username, usernameLoginDTO.getPassword());
        if (!success) {
            log.info("登录失败!usernameLoginDTO:{}", JSON.toJSONString(usernameLoginDTO));
            return ResultDTO.fail(ResultDTOEnum.USERNAME_PASSWORD_FAIL);
        }

        // 获取用户手机号
        AuthUserBase authUserBase = authUserQueryFacade.selectByUsername(username);
        // 手机号不存在时直接抛出异常
        if(authUserBase == null || StringUtils.isBlank(authUserBase.getPhone())) {
            log.info("登录失败!用户基本信息异常:authUserBase:{}", JSON.toJSONString(authUserBase));
            return ResultDTO.fail(ResultDTOEnum.USERNAME_PASSWORD_FAIL);
        }

        // 复用原本的mall的交互流程
        String phone = authUserBase.getPhone();
        LoginVO loginVO = LoginVO.builder().phone(phone).build();
        loginVO = loginV2(usernameLoginDTO.getAppId(), usernameLoginDTO.getWeChatCode(), phone, usernameLoginDTO.getH5Request(), usernameLoginDTO.getTenantId(), loginVO);
        return ResultDTO.success(loginVO);
    }

    /**
     * 新登录方法
     *
     * @param appId
     * @param weChatCode
     * @param phone
     * @param H5Request
     * @param h5TenantId h5解析出的租户Id
     * @param loginVO
     * @return 返回登录信息
     */
    @Override
    public LoginVO loginV2(String appId, String weChatCode, String phone, Boolean H5Request, Long h5TenantId, LoginVO loginVO) {
        // H5手机号验证码登录,不获取openId信息
        Long tenantId = h5TenantId;
        if (!H5Request) {
            // 获取openId信息
            if (!fillSessionKeyForLoginVO(appId, weChatCode, loginVO)) {
                throw new ProviderException("获取用户openId信息异常，请稍后再试");
            }
            tenantId = loginVO.getTenantId();
        }
        // 手机号是否注册
        MerchantStoreDTO merchantStoreDTO = MerchantStoreDTO.builder().appId(appId).code(weChatCode)
                .tenantId(tenantId).phone(phone).H5Request(H5Request).build();
        ResultDTO<Boolean> registered = checkRegistered(merchantStoreDTO, Boolean.FALSE);
        loginVO.setRegistered(registered.getData());
        if (registered.isFail() || Boolean.FALSE.equals(registered.getData())) {
            return loginVO;
        }

        // 选择门店登录
        selectAccountLogin(tenantId, phone, loginVO);
        return loginVO;
    }

    /**
     * 选择账户登录
     *
     * @param tenantId
     * @param phone
     * @param loginVO
     * @return false:无账号可登录;true：登陆成功
     */
    private boolean selectAccountLogin(Long tenantId, String phone, LoginVO loginVO) {
        // 选择账户进行登录
        MerchantStoreAccountLoginDTO merchantStoreAccountLoginDTO = merchantStoreAccountService.selectOneLoginFromBelongStoreAccountsList(tenantId, phone);
        if (Objects.isNull(merchantStoreAccountLoginDTO)) {
            return false;
        }
        MerchantStore store = queryStoreInfo(merchantStoreAccountLoginDTO.getStoreId());
        if (Objects.isNull(store)) {
            return false;
        }
        String token = getTokenByAuthLogin(loginVO.getOpenId(), phone, merchantStoreAccountLoginDTO, store);
        // 选择的账户没有登陆过,则标识用户进入账号列表自行选择门店进入
        if (Objects.isNull(merchantStoreAccountLoginDTO.getLastLoginTime())) {
            redisTemplate.opsForValue().set(RedisKeyEnum.C00013.join(phone), String.valueOf(NumberConstant.ONE), NumberConstant.HUNDRED, TimeUnit.DAYS);
        }
        loginVO.setToken(token);
        return true;
    }

    /**
     * 查询openId等信息进行填充
     * @param appId
     * @param code
     * @param smsLoginVO
     * @return 填充失败返回false
     */
    private boolean fillSessionKeyForLoginVO(String appId, String code, LoginVO smsLoginVO) {
        try {
            // 获取用户openId
            SessionKeyVo sessionKeyVo = weChatAuthService.getMiniProgramSessionKey(appId, code);
            smsLoginVO.setOpenId(sessionKeyVo.getOpenId());
            smsLoginVO.setSessionKey(sessionKeyVo.getSessionKey());
            smsLoginVO.setTenantId(sessionKeyVo.getTenantId());
            smsLoginVO.setTenantName(sessionKeyVo.getTenantName());
            smsLoginVO.setUnionId(sessionKeyVo.getUnionId());
        } catch (Exception e) {
            log.error("获取openId异常,", e);
            return false;
        }
        return true;
    }

    public String getPlaceOrderPermissionExpiryTime(Long storeId) {
        MerchantStoreResultResp store = userCenterMerchantStoreFacade.getMerchantStoreById (storeId);
        if(ObjectUtil.isNotNull (store)){
            TenantCommonConfig registryConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(store.getTenantId (), TenantConfigKeyEnum.PLACE_ORDER_PERMISSION_EXPIRY_TIME.getConfigKey());
            String registrySwitch = Optional.ofNullable(registryConfig).map(TenantCommonConfig::getConfigValue).orElse(TenantConfigKeyEnum.REGISTRY_SWITCH.getDefaultValue());
            Long day = Long.valueOf (registrySwitch);
            LocalDateTime placeOrderPermissionExpiryTime = store.getPlaceOrderPermissionExpiryTime ();
            if(ObjectUtil.isNotNull (placeOrderPermissionExpiryTime) && LocalDateTime.now ().plusDays (day).isAfter (placeOrderPermissionExpiryTime)){
                return LocalDateTimeUtil.format (placeOrderPermissionExpiryTime, "yyyy-MM-dd HH:mm:ss");
            }
        }
        return "";
    }

    @Override
    public boolean verifyUsernameAndPhoneMatch(UsernameQueryDTO usernameQueryDTO) {
        AssertCheckParams.notNull(usernameQueryDTO.getUsername(), ResultStatusEnum.SERVER_ERROR.getStatus(), "用户名不能为空");
        AssertCheckParams.notNull(usernameQueryDTO.getPhone(), ResultStatusEnum.SERVER_ERROR.getStatus(), "手机号不能为空");
        String phone = usernameQueryDTO.getPhone();
        String username = usernameQueryDTO.getUsername();
        AuthUserBase authUserBase = authUserQueryFacade.selectByPhone(phone);
        if(authUserBase == null) {
            throw new BizException("手机号对应的用户信息不存在!");
        }
        return username.equals(authUserBase.getUsername());
    }

    @Override
    public ResultDTO<Void> examineCodeAndUpdatePassword(SendCodeDTO sendCodeDTO) {

        // 校验验证码
        Boolean success = this.examineCode(sendCodeDTO.getPhone(), sendCodeDTO.getCode());
        if (!success) {
            log.info("验证码校验不通过");
            return ResultDTO.fail(ResultDTOEnum.EXAMINE_CODE_FAIL);
        }
        log.info("验证码校验通过,开始初始化密码.sendCodeDTO:{}", JSON.toJSONString(sendCodeDTO));

        // 校验用户基本信息
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(sendCodeDTO.getTenantId());
        merchantStoreAccountQueryReq.setPhone(sendCodeDTO.getPhone());
        merchantStoreAccountQueryReq.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getFlag());
        List<MerchantStoreAccountResultResp> merchantStoreList = userCenterMerchantStoreAccountFacade.getMerchantStoreList(merchantStoreAccountQueryReq);

        if(CollUtil.isEmpty(merchantStoreList)) {
            log.info("当前手机号下无状态正常的账户! sendCodeDTO:{}", JSON.toJSONString(sendCodeDTO));
            return ResultDTO.fail(ResultDTOEnum.ACCOUNT_NOT_FOUND_FAIL);
        }

        // 修改密码
        MerchantStoreAccountResultResp merchantStoreAccountResultResp = merchantStoreList.get(0);
        AuthUserPasswordUpdateInput updateDTO = AuthUserPasswordUpdateInput.builder()
                .bizUserId(merchantStoreAccountResultResp.getId())
                .tenantId(sendCodeDTO.getTenantId())
                .origin(SystemOriginEnum.COSFO_MALL.getType())
                .password(sendCodeDTO.getLoginPassword())
                .build();
        authUserUpdateFacade.updateAuthUserPassword(updateDTO);
        return ResultDTO.success();
    }
}
