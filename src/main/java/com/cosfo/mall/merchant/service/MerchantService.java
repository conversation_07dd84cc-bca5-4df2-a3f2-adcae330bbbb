package com.cosfo.mall.merchant.service;

import com.cosfo.mall.common.result.ResultDTO;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/25 15:54
 */
public interface MerchantService {

    /**
     * 根据tenantId查询
     * @param tenantId
     * @return
     */
    ResultDTO selectByTenantId(Long tenantId);

    /**
     * 查询 租户是否开启进销存 开关
     * @param tenantId
     * @return
     */
    Boolean getStoreInventorySwitch(Long tenantId);
}
