package com.cosfo.mall.merchant.service.impl;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.mall.merchant.model.po.Merchant;
import com.cosfo.mall.merchant.service.MerchantService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/25 15:54
 */
@Service
public class MerchantServiceImpl implements MerchantService {

    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    @Override
    public ResultDTO selectByTenantId(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        Merchant merchant = userCenterMerchantInfoFacade.getMerchantByTenantId(tenantId);
        return ResultDTO.success(merchant);
    }

    @Override
    public Boolean getStoreInventorySwitch(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        return userCenterTenantFacade.getStoreInventorySwitch(tenantId);
    }
}
