package com.cosfo.mall.merchant.service.impl;

import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.repository.MerchantStoreBalanceRepository;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @desc 门店余额服务层
 * <AUTHOR>
 * @date 2023/3/17 14:01
 */
@Service
public class MerchantStoreBalanceServiceImpl implements MerchantStoreBalanceService {

    @Resource
    private MerchantStoreBalanceRepository merchantStoreBalanceRepository;

    @Override
    public MerchantStoreBalance queryCashAccountByStoreId(Long tenantId, Long storeId) {
        return merchantStoreBalanceRepository.queryCashAccountByStoreId(tenantId, storeId);
    }

    @Override
    public List<MerchantStoreBalance> queryNonCashAccountByStoreId(Long tenantId, Long storeId) {
        return merchantStoreBalanceRepository.queryNonCashAccountByStoreId(tenantId, storeId);
    }

    @Override
    public List<MerchantStoreBalance> queryNonCashAccountByStoreIdForUpdate(Long tenantId, Long storeId) {
        return merchantStoreBalanceRepository.queryNonCashAccountByStoreIdForUpdate(tenantId, storeId);
    }

    @Override
    public MerchantStoreBalance queryById(Long id) {
        return merchantStoreBalanceRepository.getBaseMapper().selectById(id);
    }

    @Override
    public int decreaseBalance(Long id, BigDecimal changeBalance) {
        AssertCheckParams.notNull(id, ErrorCodeEnum.PARAMETER_ERROR.getStatus(), ErrorCodeEnum.PARAMETER_ERROR.getMessage());
        AssertCheckParams.notNull(changeBalance, ErrorCodeEnum.PARAMETER_ERROR.getStatus(), ErrorCodeEnum.PARAMETER_ERROR.getMessage());
        return merchantStoreBalanceRepository.decreaseBalance(id, changeBalance);
    }

    @Override
    public int freezeBalance(Long id, BigDecimal changeBalance) {
        return merchantStoreBalanceRepository.freezeBalance(id, changeBalance);
    }

    @Override
    public int increaseBalance(Long id, BigDecimal changeBalance) {
        AssertCheckParams.notNull(id, ErrorCodeEnum.PARAMETER_ERROR.getStatus(), ErrorCodeEnum.PARAMETER_ERROR.getMessage());
        AssertCheckParams.notNull(changeBalance, ErrorCodeEnum.PARAMETER_ERROR.getStatus(), ErrorCodeEnum.PARAMETER_ERROR.getMessage());
        return merchantStoreBalanceRepository.increaseBalance(id, changeBalance);
    }

    @Override
    public int decreaseFreezeBalance(Long id, BigDecimal changeBalance) {
        AssertCheckParams.notNull(id, ErrorCodeEnum.PARAMETER_ERROR.getStatus(), ErrorCodeEnum.PARAMETER_ERROR.getMessage());
        AssertCheckParams.notNull(changeBalance, ErrorCodeEnum.PARAMETER_ERROR.getStatus(), ErrorCodeEnum.PARAMETER_ERROR.getMessage());
        return merchantStoreBalanceRepository.decreaseFreezeBalance(id, changeBalance);
    }

    @Override
    public List<MerchantStoreBalance> queryByStoreId(Long tenantId, Long storeId) {
        return merchantStoreBalanceRepository.queryByStoreId(tenantId, storeId);
    }
}
