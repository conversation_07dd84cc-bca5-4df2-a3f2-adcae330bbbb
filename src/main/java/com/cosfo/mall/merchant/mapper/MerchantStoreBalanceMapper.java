package com.cosfo.mall.merchant.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 门店余额表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Mapper
public interface MerchantStoreBalanceMapper extends BaseMapper<MerchantStoreBalance> {

    /**
     * 变更余额
     * @param id
     * @param changeBalance
     * @return
     */
    int decreaseBalance(@Param("id") Long id, @Param("changeBalance") BigDecimal changeBalance);

    /**
     * 增加余额
     * @param id
     * @param changeBalance
     * @return
     */
    int increaseBalance(@Param("id")Long id, @Param("changeBalance") BigDecimal changeBalance);

    /**
     * 冻结金额
     *
     * @param id
     * @param changeBalance
     * @return
     */
    int freezeBalance(@Param("id") Long id, @Param("changeBalance") BigDecimal changeBalance);

    /**
     * 扣除冻结金额
     *
     * @param id
     * @param changeBalance
     * @return
     */
    int decreaseFreezeBalance(@Param("id") Long id, @Param("changeBalance") BigDecimal changeBalance);

    /**
     * 查询现金账户(加锁)
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryNonCashAccountByStoreIdForUpdate(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId);
}
