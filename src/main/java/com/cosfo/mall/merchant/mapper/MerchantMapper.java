//package com.cosfo.mall.merchant.mapper;
//
//import com.cosfo.mall.merchant.model.po.Merchant;
//import org.apache.ibatis.annotations.Mapper;
//import org.springframework.stereotype.Repository;
//
//@Deprecated
//@Mapper
//public interface MerchantMapper {
////    int deleteByPrimaryKey(Long id);
////
////    int insert(Merchant record);
////
////    int insertSelective(Merchant record);
////
////    Merchant selectByPrimaryKey(Long id);
////
////    int updateByPrimaryKeySelective(Merchant record);
////
////    int updateByPrimaryKey(Merchant record);
////
////    /**
////     * 根据租户id查询
////     * @param tenantId
////     * @return
////     */
////    Merchant selectByTenantId(Long tenantId);
//}
