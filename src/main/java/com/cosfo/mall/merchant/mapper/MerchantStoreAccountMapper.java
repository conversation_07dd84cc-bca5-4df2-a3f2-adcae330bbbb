//package com.cosfo.mall.merchant.mapper;
//
//import com.cosfo.mall.merchant.model.dto.MerchantStoreAccountDTO;
//import com.cosfo.mall.merchant.model.po.Merchant;
//import com.cosfo.mall.merchant.model.po.MerchantStoreAccount;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.security.core.parameters.P;
//import org.springframework.stereotype.Repository;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import java.util.List;
//
///**
// * @discription 门店账户持久层
// * <AUTHOR>
// * @date 2022/5/21 11:30
// */
//@Deprecated
//@Mapper
//public interface MerchantStoreAccountMapper {
//
////    /**
////     * 删除
////     * @param id
////     * @return
////     */
////    int deleteByPrimaryKey(Long id);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insert(MerchantStoreAccount record);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insertSelective(MerchantStoreAccount record);
////
////    /**
////     * 查询
////     * @param id
////     * @return
////     */
////    MerchantStoreAccount selectByPrimaryKey(Long id);
////
////    /****
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKeySelective(MerchantStoreAccount record);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKey(MerchantStoreAccount record);
////
////    /**
////     * 查询商城账户信息
////     *
////     * @param accountId
////     * @param tenantId
////     * @return
////     */
////    MerchantStoreAccount selectById(@Param("accountId") Long accountId,@Param("tenantId")Long tenantId);
////
////    /**
////     * 查询
////     * @param query
////     * @return
////     */
////    MerchantStoreAccount selectOne(MerchantStoreAccount query);
////
////    /**
////     * 根据手机号和租户Id查询
////     *
////     * @param tenantId
////     * @param phone
////     * @return
////     */
////    List<MerchantStoreAccount> selectByPhoneAndTenantId(@Param("tenantId") Long tenantId, @Param("phone") String phone);
////
////    /**
////     * 查询店长信息
////     * @param storeId
////     * @return
////     */
////    MerchantStoreAccount queryManagerInfo(Long storeId);
////
////
////    /**
////     * 根据门店id查询账号
////     * @param tenantId
////     * @param storeId
////     * @return
////     */
////    List<MerchantStoreAccountDTO> selectByStoreId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId);
//}
