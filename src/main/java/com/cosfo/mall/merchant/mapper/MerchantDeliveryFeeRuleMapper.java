package com.cosfo.mall.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.merchant.model.dto.MerchantDeliveryFeeRuleQueryConditionDTO;
import com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantDeliveryFeeRuleMapper extends BaseMapper<MerchantDeliveryFeeRule> {

    /**
     * 查询
     * @param query
     * @return
     */
    MerchantDeliveryFeeRule selectOne(MerchantDeliveryFeeRuleQueryConditionDTO query);
}
