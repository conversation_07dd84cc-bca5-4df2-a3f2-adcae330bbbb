//package com.cosfo.mall.merchant.mapper;
//
//import com.cosfo.mall.merchant.model.po.MerchantContact;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * @desc 门店地址持久层
// * <AUTHOR>
// * @date 2022/5/22
// */
//@Deprecated
//@Mapper
//public interface MerchantContactMapper {
//
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insert(MerchantContact record);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insertSelective(MerchantContact record);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKeySelective(MerchantContact record);
////
////    /**
////     * 查询商户绑定默认收货人信息
////     *
////     * @param tenantId
////     * @param addressId
////     * @return
////     */
////    MerchantContact queryDefaultContact(@Param("tenantId") Long tenantId, @Param("addressId") Long addressId);
////
////    /**
////     * 根据Id查询联系人
////     *
////     * @param tenantId
////     * @param merchantContactId
////     * @return
////     */
////    MerchantContact selectById(@Param("tenantId") Long tenantId,@Param("merchantContactId") Long merchantContactId);
////
////    /**
////     * 查询联系人列表
////     *
////     * @param tenantId
////     * @param addressId
////     * @return
////     */
////    List<MerchantContact> queryMerchantContact(@Param("tenantId") Long tenantId, @Param("addressId") Long addressId);
////
////    /**
////     * 根据地址id查询
////     * @param addressId
////     * @param tenantId
////     * @return
////     */
////    MerchantContact selectByAddressId(@Param("addressId") Long addressId, @Param("tenantId") Long tenantId);
////
////    /**
////     * 根据id查询
////     * @param id
////     * @return
////     */
////    MerchantContact selectByPrimaryKey(Long id);
////
////    /**
////     * 根据id删除
////     * @param id
////     */
////    void deleteByPrimaryKey(Long id);
//}
