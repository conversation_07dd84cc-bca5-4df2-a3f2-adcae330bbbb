//package com.cosfo.mall.merchant.mapper;
//
//import com.cosfo.mall.merchant.model.po.MerchantAddress;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.stereotype.Repository;
//
///**
// * @desc 门店地址持久层
// * <AUTHOR>
// * @date 2022/5/22
// */
//@Deprecated
//@Mapper
//public interface MerchantAddressMapper {
//
////    /**
////     * 删除
////     * @param id
////     * @return
////     */
////    int deleteByPrimaryKey(Long id);
////
////    /**
////     * 新增
////     * @param record
////     * @return
////     */
////    int insert(MerchantAddress record);
////
////    /**
////     * 新增
////     * @param record
////     * @return
////     */
////    int insertSelective(MerchantAddress record);
////
////    /**
////     * 查询
////     * @param id
////     * @return
////     */
////    MerchantAddress selectByPrimaryKey(Long id);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKeySelective(MerchantAddress record);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKey(MerchantAddress record);
////
////    /**
////     * 查询店铺绑定收货地址
////     *
////     * @param storeId
////     * @param tenantId
////     * @return
////     */
////    MerchantAddress selectByStoreId(@Param("storeId") Long storeId,@Param("tenantId") Long tenantId);
//}
