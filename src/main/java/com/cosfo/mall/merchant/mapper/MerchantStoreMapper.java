//package com.cosfo.mall.merchant.mapper;
//
//import com.cosfo.mall.merchant.model.po.MerchantStore;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.stereotype.Repository;
//
///**
// * @discription 门店持久层
// * <AUTHOR>
// * @date 2022/5/21 11:30
// */
//@Deprecated
//@Mapper
//public interface MerchantStoreMapper {
//
////    /**
////     * 删除
////     * @param id
////     * @return
////     */
////    int deleteByPrimaryKey(Long id);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insert(MerchantStore record);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insertSelective(MerchantStore record);
////
////    /**
////     * 根据主键查询
////     * @param id
////     * @return
////     */
////    MerchantStore selectByPrimaryKey(Long id);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKeySelective(MerchantStore record);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKey(MerchantStore record);
////
////    /**
////     * 查看品牌维度下的门店个数
////     * @param tenantId
////     * @return
////     */
////    Long countStoreNum(Long tenantId);
////
////    /**
////     * 根据门店名查询
////     * @param tenantId
////     * @param storeName
////     * @return
////     */
////    MerchantStore selectByName(@Param("tenantId") Long tenantId, @Param("storeName") String storeName);
//}
