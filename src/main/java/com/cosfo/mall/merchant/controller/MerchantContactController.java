package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantContactDTO;
import com.cosfo.mall.merchant.model.vo.MerchantContactVO;
import com.cosfo.mall.merchant.service.MerchantContactService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 17:16
 */
@RestController
@RequestMapping("/merchant/contact")
public class MerchantContactController extends BaseController {

    @Resource
    private MerchantContactService merchantContactService;

    /**
     * 更新联系人
     * @param contactVO
     * @return
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultDTO updateContact(@RequestBody MerchantContactVO contactVO) {
        MerchantContactDTO contactDTO = new MerchantContactDTO();
        BeanUtils.copyProperties(contactVO, contactDTO);
        return merchantContactService.updateContact(contactDTO);
    }

    /**
     * 新建联系人
     * @param contactVO
     * @return
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResultDTO addContact(@RequestBody MerchantContactVO contactVO) {
        MerchantContactDTO contactDTO = new MerchantContactDTO();
        BeanUtils.copyProperties(contactVO, contactDTO);
        return merchantContactService.addContact(contactDTO, getRequestContextInfoDTO());
    }

    /**
     * 删除联系人
     * @param id
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public ResultDTO deleteContact(Long id) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return merchantContactService.deleteContact(contextInfoDTO, id);
    }
}
