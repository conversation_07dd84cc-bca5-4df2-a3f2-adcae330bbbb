package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.dto.MerchantContactDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.vo.MerchantAddressVO;
import com.cosfo.mall.merchant.model.vo.MerchantContactVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 地址管理
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@RestController
@RequestMapping("/merchant/address")
public class MerchantAddressController extends BaseController {
    @Resource
    private MerchantAddressService merchantAddressService;

    @RequestMapping(value = "get", method = RequestMethod.GET)
    public ResultDTO getAddressInfo(){
        return merchantAddressService.getAddressInfo(getRequestContextInfoDTO());
    }


    /**
     * 获取当前登录人的地址列表
     * @return
     */
    @RequestMapping(value = "/query/current-user/list", method = RequestMethod.GET)
    public ResultDTO<List<MerchantAddress>> selectListByStoreId(){
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        Long tenantId = loginContextInfoDTO.getTenantId();
        Long storeId = loginContextInfoDTO.getStoreId();
        return ResultDTO.success(merchantAddressService.selectListByStoreId(storeId, tenantId));
    }


    /**
     * 修改地址
     * @return
     */
    @RequestMapping(value = "/updateAddress", method = RequestMethod.POST)
    public ResultDTO updateAddress(@RequestBody MerchantAddressVO addressVO){
        MerchantAddressDTO addressDTO = new MerchantAddressDTO();
        BeanUtils.copyProperties(addressVO, addressDTO);
        return merchantAddressService.updateAddress(addressDTO);
    }
}
