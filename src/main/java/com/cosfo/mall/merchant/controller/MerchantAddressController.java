package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.dto.MerchantContactDTO;
import com.cosfo.mall.merchant.model.vo.MerchantAddressVO;
import com.cosfo.mall.merchant.model.vo.MerchantContactVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@RestController
@RequestMapping("/merchant/address")
public class MerchantAddressController extends BaseController {
    @Resource
    private MerchantAddressService merchantAddressService;

    @RequestMapping(value = "get", method = RequestMethod.GET)
    public ResultDTO getAddressInfo(){
        return merchantAddressService.getAddressInfo(getRequestContextInfoDTO());
    }

    @RequestMapping(value = "/updateAddress", method = RequestMethod.POST)
    public ResultDTO updateAddress(@RequestBody MerchantAddressVO addressVO){
        MerchantAddressDTO addressDTO = new MerchantAddressDTO();
        BeanUtils.copyProperties(addressVO, addressDTO);
        return merchantAddressService.updateAddress(addressDTO);
    }
}
