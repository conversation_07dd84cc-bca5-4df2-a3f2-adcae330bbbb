package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryDTO;
import com.cosfo.mall.merchant.model.vo.MerchantStoreBalanceChangeRecordVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/14 13:53
 */
@RestController
@RequestMapping("/merchant-store-balance")
public class MerchantStoreBalanceController extends BaseController {

    /**
     * 门店余额变动记录
     *
     * @param queryDTO
     * @return
     */
    @RequestMapping(value = "/query/change-record", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreBalanceChangeRecordVO>> pageQueryChangeRecord(@RequestBody MerchantStoreBalanceChangeQueryDTO queryDTO) {
        return CommonResult.ok();
    }
}
