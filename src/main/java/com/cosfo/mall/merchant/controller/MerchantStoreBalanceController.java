package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreAccountPreLoginDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreAccountQueryDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreBalanceChangeQueryDTO;
import com.cosfo.mall.merchant.model.po.MerchantStore;
import com.cosfo.mall.merchant.model.vo.MerchantStoreBalanceChangeRecordVO;
import com.cosfo.mall.merchant.model.vo.MerchantStoreBalanceOverviewVO;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceChangeRecordService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/14 13:53
 */
@RestController
@RequestMapping("/merchant-store-balance")
public class MerchantStoreBalanceController extends BaseController {

    @Resource
    private MerchantStoreBalanceChangeRecordService merchantStoreBalanceChangeRecordService;

    /**
     * 门店余额变动记录
     * @param queryDTO
     * @return
     */
    @RequestMapping(value = "/query/change-record",method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreBalanceChangeRecordVO>> pageQueryChangeRecord(@RequestBody MerchantStoreBalanceChangeQueryDTO queryDTO) {
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        PageInfo<MerchantStoreBalanceChangeRecordVO> pageInfo = merchantStoreBalanceChangeRecordService.pageQueryChangeRecord(queryDTO, loginContextInfoDTO);
        return CommonResult.ok(pageInfo);
    }
}
