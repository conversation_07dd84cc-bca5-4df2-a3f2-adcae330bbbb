package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.convert.MerchantServiceFeeConfigConvert;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantServiceFeeConfigDTO;
import com.cosfo.mall.merchant.model.vo.MerchantServiceFeeConfigVO;
import com.cosfo.mall.merchant.service.MerchantServiceFeeConfigService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-08-29
 **/
@RestController
@RequestMapping("/merchant/service-fee-config")
public class MerchantServiceFeeConfigController {

    @Resource
    private MerchantServiceFeeConfigService merchantServiceFeeConfigService;

    /**
     * 查询租户的门店手续费配置
     *
     * @return
     */
    @PostMapping("/query")
    public CommonResult<MerchantServiceFeeConfigVO> query() {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        MerchantServiceFeeConfigDTO dto = merchantServiceFeeConfigService.queryByTenantId(loginContextInfoDTO.getTenantId());
        return CommonResult.ok(MerchantServiceFeeConfigConvert.convert(dto));
    }
}
