package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAccountDTO;
import com.cosfo.mall.merchant.model.po.Merchant;
import com.cosfo.mall.merchant.model.vo.MerchantAccountVO;
import com.cosfo.mall.merchant.service.MerchantService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 商城管理
 * <AUTHOR>
 * @description
 * @date 2022/5/25 11:34
 */
@RestController
@RequestMapping("/merchant")
public class MerchantController extends BaseController {

    @Resource
    private MerchantService merchantService;

    @RequestMapping(value = "/logo",method = RequestMethod.GET)
    public ResultDTO<Merchant> getLogo(Long tenantId) {
        return merchantService.selectByTenantId(tenantId);
    }


    /**
     * 查询是否开启门店进销存开关
     * true = 开启，false = 关闭
     * @return
     */
    @RequestMapping(value = "/storeInventorySwitch",method = RequestMethod.POST)
    public CommonResult<Boolean> getStoreInventorySwitch() {
        return CommonResult.ok (merchantService.getStoreInventorySwitch(getRequestContextInfoDTO().getTenantId()));
    }

}
