package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.config.SystemTenantProperties;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.po.Merchant;
import com.cosfo.mall.merchant.service.MerchantService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 商城管理
 * <AUTHOR>
 * @description
 * @date 2022/5/25 11:34
 */
@RestController
@RequestMapping("/merchant")
public class MerchantController extends BaseController {

    @Resource
    private SystemTenantProperties systemTenantProperties;

    @Resource
    private MerchantService merchantService;

    @RequestMapping(value = "/logo",method = RequestMethod.GET)
    public ResultDTO<Merchant> getLogo() {
        return merchantService.selectByTenantId(systemTenantProperties.getWurthTenantId());
    }
}
