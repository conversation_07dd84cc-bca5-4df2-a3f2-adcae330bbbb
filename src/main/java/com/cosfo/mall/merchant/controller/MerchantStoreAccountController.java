package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.merchant.model.dto.*;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 账号管理
 *
 * <AUTHOR>
 * @date 2022/11/29 11:27
 */
@RestController
@Slf4j
@RequestMapping("/merchant-store-account")
public class MerchantStoreAccountController extends BaseController {

    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    /**
     * 根据手机号查询所属的账号
     *
     * @param merchantStoreAccountQueryDto
     * @return
     */
    @RequestMapping(value = "/query/list-belong-store-accounts", method = RequestMethod.POST)
    public CommonResult<MerchantStoreAccountPreLoginDTO> listBelongStoreAccounts(@RequestBody MerchantStoreAccountQueryDTO merchantStoreAccountQueryDto) {
        return merchantStoreAccountService.newListBelongStoreAccounts(merchantStoreAccountQueryDto);
    }

    /**
     * 获取当前用户门店切换列表
     *
     * @param pageQueryDTO
     * @return
     */
    @RequestMapping(value = "/query/list-store-account", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreAccountDTO>> listStoreAccount(@RequestBody @Valid MerchantStoreAccountPageQueryDTO pageQueryDTO) {
        return CommonResult.ok(merchantStoreAccountService.listStoreAccount(pageQueryDTO));
    }

    /**
     * 查询账号列表
     *
     * @return
     */
    @RequestMapping(value = "/query/list-all", method = RequestMethod.POST)
    public CommonResult<List<MerchantStoreAccountDTO>> listAll() {
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        return merchantStoreAccountService.listAll(loginContextInfoDTO);
    }

    /**
     * 保存账号
     *
     * @param merchantStoreAccountDto
     * @return
     */
    @RequestMapping(value = "/upsert/insert-account", method = RequestMethod.POST)
    public CommonResult insertAccount(@RequestBody MerchantStoreAccountDTO merchantStoreAccountDto) {
        LoginContextInfoDTO loginContextInfoDto = getRequestContextInfoDTO();
        return merchantStoreAccountService.insertAccount(merchantStoreAccountDto, loginContextInfoDto);
    }

    /**
     * 删除账号
     *
     * @param merchantStoreAccountVO
     * @return
     */
    @RequestMapping(value = "/upsert/delete-account", method = RequestMethod.POST)
    public CommonResult deleteAccount(@RequestBody MerchantStoreAccountVO merchantStoreAccountVO) {
        return merchantStoreAccountService.deleteAccount(merchantStoreAccountVO.getId());
    }

    /**
     * 修改账号
     *
     * @param merchantStoreAccountDto
     * @return
     */
    @RequestMapping(value = "/upsert/update-account", method = RequestMethod.POST)
    public CommonResult updateAccount(@RequestBody MerchantStoreAccountDTO merchantStoreAccountDto) {
        LoginContextInfoDTO loginContextInfoDto = getRequestContextInfoDTO();
        return merchantStoreAccountService.updateAccount(merchantStoreAccountDto, loginContextInfoDto);
    }

    /**
     * 查询当前登录账号信息
     *
     * @return
     */
    @RequestMapping(value = "/query/login-account-info", method = RequestMethod.POST)
    public CommonResult<LoginSuccessReturnDTO> queryLoginAccountInfo() {
        LoginContextInfoDTO loginContextInfoDto = getRequestContextInfoDTO();
        return merchantStoreAccountService.queryLoginAccountInfo(loginContextInfoDto);
    }
}
