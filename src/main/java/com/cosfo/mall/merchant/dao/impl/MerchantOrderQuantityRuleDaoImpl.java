package com.cosfo.mall.merchant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.merchant.dao.MerchantOrderQuantityRuleDao;
import com.cosfo.mall.merchant.mapper.MerchantOrderQuantityRuleMapper;
import com.cosfo.mall.merchant.model.po.MerchantOrderQuantityRule;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 起订量规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Service
public class MerchantOrderQuantityRuleDaoImpl extends ServiceImpl<MerchantOrderQuantityRuleMapper, MerchantOrderQuantityRule> implements MerchantOrderQuantityRuleDao {

    @Override
    public List<MerchantOrderQuantityRule> listOrderRule(Long tenantId) {
        LambdaQueryWrapper<MerchantOrderQuantityRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantOrderQuantityRule::getTenantId, tenantId);
        return list(queryWrapper);
    }
}
