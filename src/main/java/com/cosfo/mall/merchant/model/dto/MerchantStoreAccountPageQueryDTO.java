package com.cosfo.mall.merchant.model.dto;

import com.cosfo.mall.common.model.dto.PageQueryDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantStoreAccountPageQueryDTO extends PageQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 当前登录账号id
     */
    private Long currentLoginAccountId;

    /**
     * 店铺名称，搜索用
     */
    private String storeName;

}
