package com.cosfo.mall.merchant.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/22 15:38
 */
@Data
public class MerchantStoreVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 验证码 (这个字段存在二义性，不推荐继续使用)，具体可见：MerchantStoreDTO 定义
     */
    private String code;

    /**
     * 手机号
     */
    private String phone;

    /**
     * openId
     */
    private String openId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * poi
     */
    private String poiNote;

    /**
     * 登录账号名称
     */
    private String username;


    /**
     * 验证码
     */
    private String verificationCode;

}
