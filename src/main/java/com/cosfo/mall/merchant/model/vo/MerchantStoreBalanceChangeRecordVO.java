package com.cosfo.mall.merchant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店余额变动表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
public class MerchantStoreBalanceChangeRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 变动余额
     */
    private BigDecimal changeBalance;

    /**
     * 关联订单单号
     */
    private Long associatedOrderId;
    /**
     * 关联售后的订单明细id
     */
    private Long associatedOrderAfterSaleItemId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 0、预付 1、消费 2、消费退款
     */
    private Integer type;

}
