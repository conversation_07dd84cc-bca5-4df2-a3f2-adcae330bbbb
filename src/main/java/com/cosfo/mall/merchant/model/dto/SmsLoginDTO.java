package com.cosfo.mall.merchant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: fansongsong
 * @Date: 2024-01-17
 * @Description:
 */
@Data
public class SmsLoginDTO {

    /**
     * 手机号
     */
    @NotNull(message = "phone不能为空")
    private String phone;

    /**
     * 验证码
     */
    @NotNull(message = "code不能为空")
    private String code;

    /**
     * appId
     */
    private String appId;

    /**
     * 微信code
     */
    private String weChatCode;

    /**
     * 租户ID
     * 1、H5登录从token里解析出来
     * 2、小程序登录不会传
     */
    private Long tenantId;

    /**
     * 是否H5请求
     */
    private Boolean H5Request = Boolean.FALSE;
}
