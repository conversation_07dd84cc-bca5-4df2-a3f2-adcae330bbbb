package com.cosfo.mall.merchant.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 门店登录成功返回数据 VO
 * <AUTHOR>
 * @date 2022/11/29 13:55
 */
@Data
public class LoginSuccessReturnDTO {
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * token
     */
    private String token;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * logo
     */
    private String logoImage;
    /**
     * bgm
     */
    private String backgroundImage;
    /**
     * 账期权限 1开启0关闭
     */
    private Integer billSwitch;
    /**
     * 在线支付能力 1开启0关闭
     */
    private Integer onlinePayment;
    /**
     * 是否存在账单
     */
    private Boolean havingBill;
    /**
     * 账单类型
     */
    private Integer billType;
    /**
     * 第多少天
     */
    private Integer day;
    /**
     * poi
     */
    private String poiNote;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 汇付支付权限 1开启0关闭
     */
    private Integer huifu_switch;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 客服咨询入口开关 0:关闭 1开启
     */
    private Integer consultationEntranceSwitch;

    /**
     * 注册功能 0:关闭 1开启
     */
    private Integer registrySwitch;

    /**
     * 用户可用的支付渠道
     */
    private PayChannel payChannel;

    /**
     * 统一编号
     */
    private String uniqueNo;
    /**
     * 发票地址
     */
    private String invoiceAddress;

    /**
     * 用户可用的支付渠道
     */
    @Data
    public static class PayChannel {

        /**
         * 小程序微信直连开关 0、关闭 1、开启
         */
        private Integer wechatDirectSwitch;

        /**
         * 账期开关 0、关闭 1开启
         */
        private Integer billSwitch;

        /**
         * 小程序微信间连开关 0、关闭 1、开启
         */
        private Integer wechatIndirectSwitch;

        /**
         * H5支付宝间连开关 0、关闭 1、开启
         */
        private Integer aliIndirectSwitch;

        /**
         * H5微信间连开关 0、关闭 1、开启
         */
        private Integer h5WechatIndirectSwitch;

        /**
         * H5微信间连分账开关 0、关闭 1、开启
         */
        private Integer h5WechatIndirectSharingSwitch;

        /**
         * 小程序微信间联-汇付插件开关 0、关闭 1、开启
         */
        private Integer wechatIndirectPluginSwitch;

        /**
         * 小程序微信支付开关 0、关闭 1开启
         */
        private Integer appletWechatPaySwitch;

        /**
         * 余额权限 0、关闭 1、开启
         */
        private Integer balanceSwitch;

        /**
         * 线下支付权限1=开启;0=关闭
         */
        private Integer offlinePayFlag;
    }

}
