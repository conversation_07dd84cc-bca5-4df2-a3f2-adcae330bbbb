package com.cosfo.mall.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderQuantityRuleDTO implements Serializable {
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0,全商城,其他值表示具体仓
     */
    private Integer ruleTarget;

    /**
     * 0,无仓1三方仓 2自营仓
     */
    private Integer warehouseType;

    /**
     * 规则json
     */
    private String rule;

    /**
     * 规则详情
     */
    private List<OrderQuantityRuleDetailDTO> ruleDetailList;

    /**
     * 操作符 and or
     */
    private String operator;
}
