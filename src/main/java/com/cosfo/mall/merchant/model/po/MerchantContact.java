package com.cosfo.mall.merchant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * merchant_contact
 * <AUTHOR>
@Data
public class MerchantContact implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 联系人名称
     */
    private String name;

    /**
     * 联系人手机号
     */
    private String phone;

    /**
     * 是否是默认地址：0、否 1、是
     */
    private Integer defaultFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}