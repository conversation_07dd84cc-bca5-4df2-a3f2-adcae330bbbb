package com.cosfo.mall.merchant.model.dto;

import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.merchant.validgroup.FollowTypeGroup;
import com.cosfo.mall.merchant.validgroup.QueryRuleGroup;
import com.cosfo.mall.merchant.validgroup.StepTypeGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/7/13 16:53
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderInfoDTO implements Serializable {
    private static final long serialVersionUID = 7385721249403216279L;

    /**
     * 订单总金额
     */
    @NotNull(message = "订单金额不可为空", groups = StepTypeGroup.class)
    private BigDecimal orderTotalPrice;

    /**
     * 订单总件数
     */
    @NotNull(message = "订单件数不可为空", groups = StepTypeGroup.class)
    private Integer orderTotalCount;

    /**
     * 门店地址-省
     */
    @NotNull(message = "门店地址-省不可为空", groups = {FollowTypeGroup.class, StepTypeGroup.class})
    private String storeProvince;

    /**
     * 门店地址-市
     */
    @NotNull(message = "门店地址-市不可为空", groups = {FollowTypeGroup.class, StepTypeGroup.class})
    private String storeCity;

    /**
     * 门店地址-区
     */
    @NotNull(message = "门店地址-区不可为空", groups = {FollowTypeGroup.class, StepTypeGroup.class})
    private String storeArea;

    /**
     * 门店地址-门牌地址
     */
    private String storeAddress;


    /**
     * 门店地址-门牌地址
     */
    private String storePoi;

    /**
     * 仓库类型
     */
    @NotNull(message = "仓库类型不可为空", groups = QueryRuleGroup.class)
    private Integer warehouseType;
    private OrderEnums.WarehouseTypeEnum warehouseTypeEnum;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;
}
