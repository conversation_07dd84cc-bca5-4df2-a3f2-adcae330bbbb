package com.cosfo.mall.merchant.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
@Data
public class MerchantStoreAccountVO {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 所属门店名称
     */
    private String storeName;

    /**
     * 所属门店审核备注
     */
    private String storeAuditRemark;

    /**
     * 所属门店状态
     */
    private String storeStatus;

    /**
     * 所属门店省
     */
    private String province;

    /**
     * 所属门店城市
     */
    private String city;

    /**
     * 所属门店区域
     */
    private String area;

    /**
     * 所属门店详细地址
     */
    private String address;

    /**
     * 所属门店门牌号
     */
    private String houseNumber;

    /**
     * 公众号openId
     */
    private String oaOpenId;
}


