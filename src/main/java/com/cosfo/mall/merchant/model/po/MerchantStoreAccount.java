package com.cosfo.mall.merchant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * merchant_store_account
 * <AUTHOR>
@Data
public class MerchantStoreAccount implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 上次登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 0 已删除 1 正常使用
     */
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;

    /**
     * 登录账号名称
     */
    private String username;
}
