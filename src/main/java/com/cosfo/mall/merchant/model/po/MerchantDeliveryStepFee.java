package com.cosfo.mall.merchant.model.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 阶梯运费(MerchantDeliveryStepFee)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:41:43
 */
@Data
@TableName("merchant_delivery_step_fee")
public class MerchantDeliveryStepFee implements Serializable {
    private static final long serialVersionUID = 119720805987218687L;
    /**
     * primary key
     */     
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户ID
     */     
    private Long tenantId;
    /**
     * 运费规则ID
     */     
    private Long ruleId;
    /**
     * 门槛类型  0-金额 1-数量
     */     
    private Integer feeRule;
    /**
     * 阶梯门槛。即满xx元/件
     */     
    private BigDecimal stepThreshold;
    /**
     * 运费
     */     
    private BigDecimal deliveryFee;
    /**
     * create time
     */     
    private LocalDateTime createTime;
    /**
     * update time
     */     
    private LocalDateTime updateTime;

}

