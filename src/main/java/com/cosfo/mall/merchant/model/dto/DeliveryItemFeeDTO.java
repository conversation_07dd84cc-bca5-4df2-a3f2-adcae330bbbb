package com.cosfo.mall.merchant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/7/14 18:47
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryItemFeeDTO implements Serializable {
    private static final long serialVersionUID = 1291860909922419002L;

    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 最终运费
     */
    private BigDecimal deliveryFee;
}
