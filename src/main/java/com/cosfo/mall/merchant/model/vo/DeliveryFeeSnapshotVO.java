package com.cosfo.mall.merchant.model.vo;

import com.cosfo.mall.merchant.model.dto.DeliveryItemFeeDTO;
import com.cosfo.mall.merchant.model.dto.DeliveryRuleInfoDTO;
import com.cosfo.mall.merchant.model.dto.DeliveryTotalDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/7/17 18:42
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryFeeSnapshotVO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 订单运费
     */
    private BigDecimal deliveryFee;

    /**
     * 订单信息
     */
    private DeliveryTotalDTO orderInfo;

    /**
     * 规则列表
     */
    private List<DeliveryRuleInfoDTO> ruleList;

    /**
     * 命中规则的运费
     */
    private List<DeliveryItemFeeDTO> hitRuleList;

    /**
     * 备注
     */
    private String remark;

}
