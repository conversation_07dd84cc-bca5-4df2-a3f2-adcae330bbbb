package com.cosfo.mall.merchant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/22 15:41
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MerchantStoreDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 验证码 / 微信code
     */
    private String code;

    /**
     * 手机号
     */
    private String phone;

    /**
     * openId
     */
    private String openId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * token
     */
    private String token;

    /**
     * 门店logo
     */
    private String logoImage;

    /**
     * 背景图
     */
    private String backgroundImage;

    /**
     * appId
     */
    private String appId;
    /**
     * 账期权限 1开启0关闭
     */
    private Integer billSwitch;
    /**
     * 余额权限0、关闭 1、开启
     */
    private Integer balanceAuthority;
    /**
     * 在线支付能力 1开启0关闭
     */
    private Integer onlinePayment;
    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;

    /**
     * 非现金支付权限 0、关闭 1开启
     */
    private Integer nonCashAuthority;
    /**
     * 是否存在账单
     */
    private Boolean havingBill;
    /**
     * 账单类型
     */
    private Integer billType;
    /**
     * 第多少天
     */
    private Integer day;

    /**
     * poi
     */
    private String poiNote;

    /**
     * 是否H5请求
     */
    private Boolean H5Request = Boolean.FALSE;

    /**
     * 登录账号名称
     */
    private String username;

    /**
     * 验证码
     */
    private String verificationCode;


}
