package com.cosfo.mall.merchant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/7/14 16:14
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryStepFeeDTO implements Serializable {
    private static final long serialVersionUID = 4999435675990126831L;

    /**
     * 阶梯门槛。即满xx元/件
     */
    private BigDecimal stepThreshold;
    /**
     * 运费
     */
    private BigDecimal deliveryFee;
}
