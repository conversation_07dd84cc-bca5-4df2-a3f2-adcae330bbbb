package com.cosfo.mall.merchant.model.dto;

import com.cosfo.mall.merchant.validgroup.FollowTypeGroup;
import com.cosfo.mall.merchant.validgroup.StepTypeGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/7/13 16:54
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryOrderItemInfoDTO implements Serializable {
    private static final long serialVersionUID = -1924271927384279542L;

    /**
     * item_id
     */
    @NotNull(message = "订单商品不可为空", groups = StepTypeGroup.class)
    private Long itemId;

    /**
     * 商品数量
     */
    @NotNull(message = "订单商品数量不可为空", groups = FollowTypeGroup.class)
    private Integer itemCount;

    /**
     * 单品总价
     */
    @NotNull(message = "订单商品总价不可为空", groups = FollowTypeGroup.class)
    private BigDecimal itemTotalPrice;

    @NotNull(message = "skuId不可为空", groups = FollowTypeGroup.class)
    private Long skuId;

    @NotNull(message = "供应商skuId不可为空", groups = FollowTypeGroup.class)
    private Long supplierSkuId;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 货品重量
     */
    private BigDecimal weight;
    /**
     * 货品总重量
     */
    private BigDecimal totalWeight;
}
