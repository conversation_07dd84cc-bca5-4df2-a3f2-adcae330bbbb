package com.cosfo.mall.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店余额变动表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantStoreBalanceChangeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 变动余额
     */
    private BigDecimal changeBalance;

    /**
     * 变动过后的余额
     */
    private BigDecimal afterChangeBalance;

    /**
     * 关联订单单号/售后单号
     */
    private String associatedOrderNo;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 凭证
     */
    private String proof;

    /**
     * 备注
     */
    private String remark;

    /**
     * 账户类型 0=现金余额，1=非现金账户
     *
     * @see com.cosfo.mall.common.context.MerchantStoreBalanceEnums.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;


    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 余额变动类型 0、预付 1、消费 2、消费退款 3、冻结
     */
    private Integer type;


}
