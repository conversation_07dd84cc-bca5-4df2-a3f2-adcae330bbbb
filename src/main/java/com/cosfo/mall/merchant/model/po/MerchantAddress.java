package com.cosfo.mall.merchant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * merchant_address
 * <AUTHOR>
@Data
public class MerchantAddress implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * poi
     */
    private String poiNote;

    private static final long serialVersionUID = 1L;
}
