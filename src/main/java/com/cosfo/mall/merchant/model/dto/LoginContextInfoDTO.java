package com.cosfo.mall.merchant.model.dto;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Data
public class LoginContextInfoDTO implements UserDetails {
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 店铺Id
     */
    private Long storeId;
    /**
     * 账户Id
     */
    private Long accountId;
    /**
     * 店铺名
     */
    private String storeName;
    /**
     * jwtToken
     */
    private String jwtToken;
    /**
     * openId
     */
    private String openId;

    private String phone;

    private String accountName;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return false;
    }

    @Override
    public boolean isAccountNonLocked() {
        return false;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return false;
    }

    @Override
    public boolean isEnabled() {
        return false;
    }
}
