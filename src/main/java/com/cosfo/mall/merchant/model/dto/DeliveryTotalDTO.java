package com.cosfo.mall.merchant.model.dto;

import com.cosfo.mall.merchant.validgroup.DailyTypeGroup;
import com.cosfo.mall.merchant.validgroup.QueryRuleGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/7/13 16:47
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTotalDTO implements Serializable {
    private static final long serialVersionUID = 3713465035592352940L;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不可为空", groups = QueryRuleGroup.class)
    private Long tenantId;

    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不可为空", groups = DailyTypeGroup.class)
    private Long storeId;

    /**
     * 供应商租户
     */
    @NotNull(message = "供应商租户ID不可为空", groups = DailyTypeGroup.class)
    private Long supplierTenantId;

    /**
     * 配送时间
     */
    @NotNull(message = "配送时间不可为空", groups = DailyTypeGroup.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryTime;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "订单信息不可为空", groups = QueryRuleGroup.class)
    private DeliveryOrderInfoDTO orderInfoDTO;

    /**
     * 订单商品信息
     */
    @Valid
    @NotNull(message = "订单商品信息不可为空", groups = QueryRuleGroup.class)
    private List<DeliveryOrderItemInfoDTO> orderItemInfoDTOList;

    /**
     * 相关订单信息
     */
    private DeliveryRelatedOrderInfoDTO relatedOrderInfoDTO;
}
