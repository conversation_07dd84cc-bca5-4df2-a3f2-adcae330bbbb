package com.cosfo.mall.merchant.model.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cosfo.mall.merchant.model.fieldhandle.SetLongHandler;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 门店运费规则表(MerchantDeliveryFeeRule)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 11:54:22
 */
@Data
@TableName(value = "merchant_delivery_fee_rule",autoResultMap = true)
public class MerchantDeliveryFeeRule implements Serializable {
    private static final long serialVersionUID = -11903759875648620L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 0、无仓 1、三方仓 2、自营仓
     */
    private Integer type;
    /**
     * 运费/ 加价运费 / 实时上浮百分比
     */
    private BigDecimal deliveryFee;
    /**
     * 免运费金额
     */
    private BigDecimal freeDeliveryPrice;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 0,每单1,每日2,基于仓运费报价
     */
    private Integer ruleType;
    /**
     * 0,固定 1实时加价 2实时上浮
     */
    private Integer priceType;
    /**
     * 实时加价运费，实时上浮百分比
     */
    private BigDecimal relateNumber;
    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;
    /**
     * 免运费规则，0金额，1数量
     */
    private Integer freeDeliveryType;
    /**
     * 免运费数量
     */
    private Integer freeDeliveryQuantity;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 选中商品ID列表
     */
    @TableField(typeHandler = SetLongHandler.class)
    private Set<Long> hitItemIds;
    /**
     * 选中配送区域名称列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<List<String>> hitAreas;

}

