package com.cosfo.mall.merchant.model.dto;

import com.cosfo.mall.order.model.dto.OrderDTO;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/29 10:45
 */
@Data
@Builder
public class MerchantDeliveryDTO {

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 实付金额
     */
    private BigDecimal totalPrice;

    /**
     * 下单数量
     */
    private Integer itemQuantity;

    /**
     * 0 自营 1 三方
     */
    private Integer warehouseType;

    /**
     * 供应商租户id
     */
    private Long supplierTenantId;

    /**
     * 订单项信息
     */
    private OrderDTO orderDTO;
}
