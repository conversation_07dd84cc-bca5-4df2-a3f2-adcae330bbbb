package com.cosfo.mall.merchant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/9 18:10
 * @Description: 关联订单的相关信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryRelatedOrderInfoDTO {

    /**
     * 参与计算运费的所有订单号。
     * 1.每日运费下单时,在计算运费时订单号未生成。所以日志打印时是历史订单，DB中是历史订单ID+本次订单ID
     * 2.退款时记录的是当日历史所有订单，包含本次退款订单ID
     * 3.其它情况为空
     */
    private List<Long> orderIds;

    /**
     * 售后单ID
     */
    private List<Long> afterSaleIds;

    /**
     * 当日所有三方仓订单已付运费（M）
     */
    private BigDecimal paidDeliveryFee;

    /**
     * 退款后的当日应收运费 （N）
     */
    private BigDecimal receivableDeliveryFee;
}
