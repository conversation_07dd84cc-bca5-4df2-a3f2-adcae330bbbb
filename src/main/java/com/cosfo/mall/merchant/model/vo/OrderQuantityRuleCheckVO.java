package com.cosfo.mall.merchant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderQuantityRuleCheckVO implements Serializable {

    /**
     * 0 自营仓
     */
    private Integer warehouseType;

    /**
     * 当0 且 target = 0 时是全商城
     */
    private Long target;

    /**
     * 对应购物车仓名
     */
    private String targetName;


    private String msg;

    private Integer quantity;

    private BigDecimal amount;

    private String op;

    private Boolean rulePass;


    public OrderQuantityRuleCheckVO() {
        this.quantity = 0;
        this.amount = BigDecimal.ZERO;
        this.rulePass = true;
    }
}
