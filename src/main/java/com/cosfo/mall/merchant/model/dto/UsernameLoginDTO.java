package com.cosfo.mall.merchant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: fansongsong
 * @Date: 2024-01-17
 * @Description:
 */
@Data
public class UsernameLoginDTO {

    /**
     * 用户名
     */
    @NotNull(message = "username不能为空")
    private String username;

    /**
     * 密码
     */
    @NotNull(message = "密码不能为空")
    private String password;

    /**
     * appId
     */
    private String appId;

    /**
     * 微信code
     */
    private String weChatCode;

    /**
     * 租户ID
     */
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;

    /**
     * 是否H5请求
     */
    private Boolean H5Request = Boolean.FALSE;
}
