package com.cosfo.mall.merchant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/1
 */
@Data
public class SendCodeDTO {


    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 手机号
     */
    @NotNull(message = "phone不能为空")
    private String phone;
    /**
     * 验证码
     */
    @NotNull(message = "code不能为空")
    private String code;

    /**
     * 登陆密码
     */
    @NotNull(message = "密码不能为空")
    private String loginPassword;
}
