package com.cosfo.mall.merchant.model.dto;

import com.cosfo.mall.common.constants.MerchantAddressDefaultTypeEnum;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.context.MerchantDeliveryFeeRuleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @author: monna.chen
 * @Date: 2023/7/14 14:41
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryRuleInfoDTO implements Serializable {
    private static final long serialVersionUID = -7220677295940011470L;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 仓库类型
     *
     * @see OrderEnums.WarehouseTypeEnum
     */
    private Integer warehouseType;

    /**
     * 规则类型
     *
     * @see MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum
     */
    private Integer ruleType;

    /**
     * 是否为默认规则
     *
     * @see MerchantAddressDefaultTypeEnum
     */
    private Integer defaultType;

    /**
     * 门槛类型  0-金额 1-数量
     *
     * @see MerchantDeliveryFeeRuleEnum.DeliveryThresholdType
     */
    private Integer deliveryType;

    /**
     * 阶梯价
     */
    private List<DeliveryStepFeeDTO> stepFeeDescList;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 命中商品ID
     */
    private Set<Long> hitItemIds;

    /**
     * 命中区域列表
     */
    private Set<List<String>> hitAreaList;

    /**
     * 订单仓库编号。自营仓这个字段为空时表示当前规则未命中
     */
    private Long warehouseNo;

    /**
     * 三方随仓相关属性
     */
    /**
     * 加价类型
     *
     * @see MerchantDeliveryFeeRuleEnum.MerchatDeliveryFeeRulePriceTypeEnum
     */
    private Integer priceType;

    /**
     * 加价金额
     */
    private BigDecimal relateNumber;

}
