package com.cosfo.mall.merchant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店手续费配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Getter
@Setter
@TableName("merchant_service_fee_config")
public class MerchantServiceFeeConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 支付方式：wechat、alipay
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 服务费率，如0.6%
     */
    @TableField("fee_rate")
    private BigDecimal feeRate;

    /**
     * 状态：1-启用，0-关闭
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;


}
