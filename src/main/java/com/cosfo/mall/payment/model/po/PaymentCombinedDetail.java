package com.cosfo.mall.payment.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description: ${description}
 * @author: George
 * @date: 2025-04-21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentCombinedDetail {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店账户id
     */
    private Long accountId;

    /**
     * 商户/服务商appid
     */
    private String spAppid;

    /**
     * 商户/服务商id
     */
    private String spMchid;

    /**
     * 子商户appid
     */
    private String supAppid;

    /**
     * 子商户id
     */
    private String subMchid;

    /**
     * 主支付单id
     */
    private Long masterPaymentId;

    /**
     * 主支付单号
     */
    private String masterPaymentNo;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 支付金额
     */
    private BigDecimal totalPrice;

    /**
     * 状态：0、待支付（对应汇付P） 1、支付成功(对应汇付S) 2、支付失败(对应汇付F) 3、取消支付 4、处理中 5、重复支付成功幂等状态 6、冻结中
     */
    private Integer status;

    /**
     * 微信预支付id/汇付支付信息
     */
    private String prepayId;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 交易类型，枚举值：JSAPI：微信原生支付 汇付字段:T_JSAPI: 微信公众号支付、T_MINIAPP: 微信小程序支付、A_NATIVE: 支付宝正扫、ZERO_PRICE: 无需支付、OFFLINE:线下支付、NON_CASH：非现金支付
     */
    private String tradeType;

    /**
     * 交易状态，枚举值：
     * SUCCESS：支付成功
     * REFUND：转入退款
     * NOTPAY：未支付
     * CLOSED：已关闭
     * REVOKED：已撤销（付款码支付）
     * USERPAYING：用户支付中（付款码支付）
     * PAYERROR：支付失败(其他原因，如银行返回失败)
     */
    private String tradeState;

    /**
     * 交易状态描述
     */
    private String tradeStateDesc;

    /**
     * 银行类型，采用字符串类型的银行标识
     */
    private String bankType;

    /**
     * 用户在商户/服务商appid下的唯一标识
     */
    private String spOpenid;

    /**
     * 用户在子商户appid下的唯一标识
     */
    private String subOpenid;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 支付成功时间
     */
    private LocalDateTime successTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 支付类型：0微信支付，1汇付支付
     */
    private Integer onlinePayChannel;

    /**
     * 支付手续费率
     */
    private BigDecimal feeRate;

    /**
     * 支付通道id
     */
    private Long paymentChannelId;

    /**
     * 手续费
     */
    private BigDecimal feeAmount;

    /**
     * 商户号
     */
    private String merchantNo;
}