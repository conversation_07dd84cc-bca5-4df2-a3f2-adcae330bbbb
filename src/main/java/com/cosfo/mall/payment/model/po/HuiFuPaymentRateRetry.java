package com.cosfo.mall.payment.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @Date: 2023-04-04
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("huifu_payment_rate_retry")
public class HuiFuPaymentRateRetry implements Serializable {

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付单id
     */
    @TableField("payment_id")
    private Long paymentId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 重试次数
     */
    @TableField("retry_num")
    private Integer retryNum;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

}
