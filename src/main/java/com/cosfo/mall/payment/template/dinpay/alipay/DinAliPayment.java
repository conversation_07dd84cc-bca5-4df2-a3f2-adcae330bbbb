package com.cosfo.mall.payment.template.dinpay.alipay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.template.dinpay.DinPaymentTemplate;
import com.cosfo.mall.payment.template.huifu.HuiFuPaymentTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentMethodEnums;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-09-01
 **/
@Service
@Slf4j
public class DinAliPayment extends DinPaymentTemplate {

    @Resource
    private TenantService tenantService;

    @Override
    protected String getPaymentMethod() {
        return PaymentMethodEnums.ALIPAY.getCode();
    }

    protected void preProcess(PaymentRequest paymentRequest) {
        super.preProcess(paymentRequest);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        // 根据请求类型确定要检查的开关
        Integer targetSwitch = tenantAuthConnectionDTO.getAliIndirectSwitch();
        if (Objects.equals(targetSwitch, PaymentEnum.Switch.CLOSE.getType())) {
            throw new BizException("您暂无支付宝支付权限");
        }
    }
}
