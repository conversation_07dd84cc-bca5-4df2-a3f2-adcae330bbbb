package com.cosfo.mall.payment.template.dinpay.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.template.dinpay.DinPaymentTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantService;
import net.summerfarm.payment.routing.common.enums.PaymentMethodEnums;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2025-08-19
 **/
@Service
public class DinWechatPayment extends DinPaymentTemplate {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private TenantService tenantService;

    @Override
    protected String getPaymentMethod() {
        return PaymentMethodEnums.WECHAT.getCode();
    }

    @Override
    protected void preProcess(PaymentRequest paymentRequest) {
        super.preProcess(paymentRequest);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        // 根据请求类型确定要检查的开关
        Integer targetSwitch = paymentRequest.getH5Request() ?
                tenantAuthConnectionDTO.getH5WechatIndirectSwitch() :
                tenantAuthConnectionDTO.getWechatIndirectSwitch();
        if (Objects.equals(targetSwitch, PaymentEnum.Switch.CLOSE.getType())) {
            throw new BizException("您暂无微信支付权限");
        }
//        if (StringUtils.isEmpty(tenantAuthConnectionDTO.getDinMerchantNo()) ||
//                StringUtils.isEmpty(tenantAuthConnectionDTO.getDinPublicKey()) ||
//                StringUtils.isEmpty(tenantAuthConnectionDTO.getDinPrivateKey()) ||
//                StringUtils.isEmpty(tenantAuthConnectionDTO.getDinSecret())) {
//            throw new BizException("汇付商户信息未配置，请联系管理员");
//        }
    }


    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        Long paymentId = request.getPaymentId();
        Payment payment = new Payment();
        payment.setId(paymentId);
        payment.setPrepayId(result.getPrepayId());
        paymentMapper.updateByPrimaryKeySelective(payment);

        super.onSuccess(request, result);
    }

    @Override
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        super.assemblyPaymentResult(request, result);
        String prepayId = result.getPrepayId();
        JSONObject jsonObject = JSON.parseObject(prepayId);
        result.setTimeStamp(jsonObject.getString("timeStamp"));
        result.setNonceStr(jsonObject.getString("nonceStr"));
        result.setPackageStr(jsonObject.getString("package"));
        result.setSignType(jsonObject.getString("signType"));
        result.setPaySign(jsonObject.getString("paySign"));
        result.setOaAppId(jsonObject.getString("appId"));
        return result;
    }
}
