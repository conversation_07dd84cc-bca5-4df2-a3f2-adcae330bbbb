package com.cosfo.mall.payment.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.payment.model.po.HuiFuRefund;

/**
 * @description: 汇付退款记录表服务类
 * @author: <PERSON>
 * @date: 2023-12-13
 **/
public interface HuiFuRefundRepository extends IService<HuiFuRefund> {


    /**
     * 根据退款id查询最新的退款记录
     *
     * @param refundId
     * @return
     */
    HuiFuRefund queryLastByRefundId(Long refundId);
}
