package com.cosfo.mall;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
//@MapperScan(basePackages = {"com.cosfo.mall.market.mapper", "com.cosfo.mall.order.mapper",
//        "com.cosfo.mall.trolley.mapper", "com.cosfo.mall.tenant.mapper", "com.cosfo.mall.product.mapper",
//        "com.cosfo.mall.merchant.mapper", "com.cosfo.mall.system.mapper", "com.cosfo.mall.stock.mapper",
//        "com.cosfo.mall.common.sms.mapper", "com.cosfo.mall.payment.mapper","com.cosfo.mall.wechat.mapper",
//        "com.cosfo.mall.supplier.mapper", "com.cosfo.mall.bill.mapper","com.cosfo.mall.common.mapper", "com.cosfo.aftersale.mapper"
//})
@MapperScan(basePackages = {"com.cosfo.mall.**", "com.cosfo.aftersale.**", "com.cosfo.storeinventory.**", "net.summerfarm.payment.*"}, annotationClass = Mapper.class)
@EnableAsync
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@SpringBootApplication(scanBasePackages = {"com.cosfo", "net.xianmu.authentication", "net.summerfarm.payment"})
@DubboComponentScan(basePackages = "com.cosfo.mall.**.provider")
@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class CosfoMallApplication implements WebMvcConfigurer, InitializingBean {

    @Autowired
    private MybatisPlusProperties mybatisPlusProperties;

    public static void main(String[] args) {
        SpringApplication.run(CosfoMallApplication.class, args);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("打印mybatis-plus配置项：{}", mybatisPlusProperties);
    }
}
