package com.cosfo.mall.bill.controller;

import com.cosfo.mall.bill.convert.BillProfitSharingOrderConvert;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-01-10
 **/
@RestController
@RequestMapping("/bill-profit-sharing")
@Slf4j
public class BillProfitSharingController {

    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    /**
     * 保存分账规则快照
     *
     * @param orderId
     * @return
     */
    @PostMapping("/save-snapshot/{orderId}")
    public CommonResult saveRuleSnapshot(@PathVariable Long orderId) {
        profitSharingBusinessService.saveOrderProfitSharingRule(orderId);
        return CommonResult.ok();
    }

    /**
     * 计算分账规则快照
     *
     * @param profitSharingNo
     * @return
     */
    @PostMapping("/calculate/{profitSharingNo}")
    public CommonResult calculate(@PathVariable String profitSharingNo) {
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderService.queryByProfitSharingNo(profitSharingNo);
        profitSharingBusinessService.calculateProfitSharing(BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder));
        return CommonResult.ok();
    }

    /**
     * 实际分账
     *
     * @param profitSharingNo
     * @return
     */
    @PostMapping("/do-profit-sharing/{profitSharingNo}")
    public CommonResult doProfitSharing(@PathVariable String profitSharingNo) {
        profitSharingBusinessService.doProfitSharing(profitSharingNo);
        return CommonResult.ok();
    }

    @PostMapping("/profit-sharing-flow/{orderId}")
    public CommonResult profitSharingFlow(@PathVariable Long orderId) {
        try {
            profitSharingBusinessService.profitSharingFlow(orderId);
        } catch (Exception e) {
            log.error("处理订单:{}分账失败", orderId);
        }
        return CommonResult.ok();
    }

    @PostMapping("/profit-sharing-flow/by-no/{profitSharingNo}")
    public CommonResult profitSharingFlowByNo(@PathVariable String profitSharingNo) {
        try {
            profitSharingBusinessService.profitSharingFlowByNo(profitSharingNo);
        } catch (Exception e) {
            log.error("处理分账单:{}分账失败", profitSharingNo);
        }
        return CommonResult.ok();
    }

    @PostMapping("/update/snapshots")
    public CommonResult updateSnapshots(@RequestBody BillProfitSharingSnapshot billProfitSharingSnapshot) {
        try {
            profitSharingBusinessService.updateSnapshots(billProfitSharingSnapshot);
        } catch (Exception e) {
            log.error("更新分账快照失败");
        }
        return CommonResult.ok();
    }

}
