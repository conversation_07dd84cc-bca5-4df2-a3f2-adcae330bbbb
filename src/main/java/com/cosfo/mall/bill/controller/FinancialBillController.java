package com.cosfo.mall.bill.controller;

import com.cosfo.mall.bill.model.dto.BillCredentialsDTO;
import com.cosfo.mall.bill.model.vo.FinancialBillVO;
import com.cosfo.mall.bill.service.FinancialBillService;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@RestController
@RequestMapping("/bill")
public class FinancialBillController extends BaseController {
    @Resource
    private FinancialBillService financialBillService;

    /**
     * 应付订单分页查询
     *
     * @param pageIndex
     * @param pageSize
     * @param status
     * @return
     */
    @GetMapping("/list/{pageIndex}/{pageSize}")
    public ResultDTO<PageInfo<FinancialBillVO>> list(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, Integer status, String year) {
        return financialBillService.list(pageSize, pageIndex, status, year, getRequestContextInfoDTO());
    }

    /**
     * 查询年度总金额
     *
     * @param status
     * @param year
     * @return
     */
    @GetMapping("/get/year/receivable/price")
    public ResultDTO<BigDecimal> getYearReceivablePrice(Integer status, String year){
        return financialBillService.getYearReceivablePrice(status,year, getRequestContextInfoDTO());
    }

    /**
     * 获取账单详情
     *
     * @param billId
     * @return
     */
    @GetMapping("/detail")
    public ResultDTO<FinancialBillVO> detail(Long billId){
        return financialBillService.detail(billId, getRequestContextInfoDTO());
    }

    @PostMapping("/upload/credentials")
    public ResultDTO uploadCredentials(@RequestBody BillCredentialsDTO billCredentialsDTO){
        return financialBillService.uploadCredentials(billCredentialsDTO, getRequestContextInfoDTO());
    }
}
