package com.cosfo.mall.bill.controller;

import com.cosfo.mall.bill.model.bo.RefundCalculateResultBO;
import com.cosfo.mall.bill.service.BillProfitSharingRefundService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-07-30
 **/
@RestController
@RequestMapping("/bill-profit-sharing-refund")
public class BillProfitSharingRefundController {

    @Resource
    private BillProfitSharingRefundService billProfitSharingRefundService;

    /**
     * 实际退款分账任务
     */
    @PostMapping("/do-refund-sharing/{id}")
    public CommonResult doRefundSharing(@PathVariable Long id) {
        billProfitSharingRefundService.doRefundSharing(id);
        return CommonResult.ok();
    }

    /**
     * 计算退款分账接口
     */
    @PostMapping("/calculate/{id}")
    public CommonResult<RefundCalculateResultBO> calculateRefundSharing(@PathVariable Long id) {
        RefundCalculateResultBO refundCalculateResultBO = billProfitSharingRefundService.calculateRefundSharing(id);
        return CommonResult.ok(refundCalculateResultBO);
    }
}
