package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.BillProfitSharing;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述: 微信分账记录
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Mapper
public interface BillProfitSharingMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(BillProfitSharing record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(BillProfitSharing record);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    BillProfitSharing selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BillProfitSharing record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(BillProfitSharing record);

    /**
     * 查询订单分账记录
     *
     * @param tenantId
     * @param orderId
     * @param businessType
     * @return
     */
    List<BillProfitSharing> querySuccessByTenantIdAndOrderId(@Param("tenantId") Long tenantId,
                                                             @Param("orderId") Long orderId,
                                                             @Param("businessType") Integer businessType);


    /**
     * 批量更新
     *
     * @param update
     * @param sharingIds
     */
    void batchUpdateBillProFitSharing(@Param("sharingUpdate") BillProfitSharing update, @Param("ids") List<Long> sharingIds);

    /**
     * 根据请求编号查询交易流水记录
     *
     * @param tenantId
     * @param outTradeNo
     * @param businessType
     * @return
     */
    List<BillProfitSharing> queryByTenantIdAndOutTradeNo(@Param("tenantId") Long tenantId,
                                                         @Param("outTradeNo") String outTradeNo,
                                                         @Param("businessType") Integer businessType);

    /**
     * 批量保存
     *
     * @param billProfitSharings
     */
    void saveBatch(@Param("billProfitSharings") List<BillProfitSharing> billProfitSharings);
}

