package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.FinancialBillCredentials;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface FinancialBillCredentialsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinancialBillCredentials record);

    int insertSelective(FinancialBillCredentials record);

    FinancialBillCredentials selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinancialBillCredentials record);

    int updateByPrimaryKey(FinancialBillCredentials record);

    /**
     * 查询账单凭证信息
     *
     * @param tenantId
     * @Param billId
     * @param operatorType 0收款人 1付款方
     * @return
     */
    FinancialBillCredentials selectByBillId(@Param("tenantId") Long tenantId,
                                            @Param("storeId") Long storeId,
                                            @Param("billId") Long billId,
                                            @Param("operatorType") Integer operatorType);
}