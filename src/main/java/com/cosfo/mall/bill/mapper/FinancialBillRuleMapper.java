package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.FinancialBillRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface FinancialBillRuleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinancialBillRule record);

    int insertSelective(FinancialBillRule record);

    FinancialBillRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinancialBillRule record);

    int updateByPrimaryKey(FinancialBillRule record);

    /**
     * 查询品牌方账单规则
     *
     * @param tenantId
     * @return
     */
    FinancialBillRule queryTenantBillRule(@Param("tenantId") Long tenantId);


}