package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Mapper
public interface BillProfitSharingSnapshotMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(BillProfitSharingSnapshot record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(BillProfitSharingSnapshot record);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    BillProfitSharingSnapshot selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BillProfitSharingSnapshot record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(BillProfitSharingSnapshot record);

    /**
     * 批量新增
     *
     * @param billProfitSharingSnapshots
     * @return
     */
    int batchInsert(@Param("billProfitSharingSnapshots") List<BillProfitSharingSnapshot> billProfitSharingSnapshots);

    /**
     * 查询订单分账规则
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<BillProfitSharingSnapshot> queryByTenantIdAndOrderId(@Param("tenantId") Long tenantId,
                                                              @Param("orderId") Long orderId);

    /**
     * 查询订单分账规则
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<BillProfitSharingSnapshot> queryOrderSharingSnapshots(@Param("tenantId") Long tenantId,
                                                               @Param("orderId") Long orderId,
                                                               @Param("profitSharingNo") String profitSharingNo
    );

    /**
     * 查询订单分账规则
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<BillProfitSharingSnapshot> queryByTenantIdAndOrderIds(@Param("tenantId") Long tenantId,
                                                              @Param("orderIds") Collection<Long> orderIds);


    /**
     * 根据分账单号查询
     *
     * @param tenantId
     * @param profitSharingNo
     * @return
     */
    List<BillProfitSharingSnapshot> queryByProfitSharingNo(@Param("tenantId") Long tenantId, @Param("profitSharingNo") String profitSharingNo);

    void resetPriceByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);

    /**
     * 根据订单id判断是否存在分账规则
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    boolean existsByOrderIds(@Param("tenantId") Long tenantId, @Param("orderIds") List<Long> orderIds);
}