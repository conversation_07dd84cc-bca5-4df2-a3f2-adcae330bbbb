package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.BillProfitSharingRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述:
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Mapper
public interface BillProfitSharingRuleMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(BillProfitSharingRule record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(BillProfitSharingRule record);

    /**
     * 根据主键Id查询
     *
     * @param id
     * @return
     */
    BillProfitSharingRule selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BillProfitSharingRule record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(BillProfitSharingRule record);

    /**
     * 查询品牌方分账规则
     *
     * @param tenantId
     * @return
     */
    List<BillProfitSharingRule> selectByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 查询品牌方分账规则
     *
     * @param tenantId
     * @return
     */
    List<BillProfitSharingRule> selectByTenantIdAndDeliveryType(@Param("tenantId") Long tenantId, @Param("deliveryType") Integer deliveryType);

    /**
     * 查询汇付分账对应分账方租户
     *
     * @param tenantId
     * @param roleCode
     * @return
     */
    BillProfitSharingRule queryProfitSharingTenant(@Param("tenantId") Long tenantId,
                                                   @Param("roleCode") Integer roleCode);
}