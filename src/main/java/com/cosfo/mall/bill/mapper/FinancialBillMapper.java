package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.FinancialBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
@Mapper
public interface FinancialBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinancialBill record);

    int insertSelective(FinancialBill record);

    FinancialBill selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinancialBill record);

    int updateByPrimaryKey(FinancialBill record);

    /**
     * 查询门店应付对账单
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    List<FinancialBill> selectByStoreIdAndTenantId(@Param("storeId") Long storeId, @Param("tenantId") Long tenantId);

    /**
     * 分页查询
     *
     * @param storeId
     * @param tenantId
     * @param status
     * @param type
     * @param startTime
     * @param endTime
     * @return
     */
    List<FinancialBill> list(@Param("storeId") Long storeId,
                             @Param("tenantId") Long tenantId,
                             @Param("status") Integer status,
                             @Param("type") Integer type,
                             @Param("startTime")String startTime,
                             @Param("endTime")String endTime);

    /**
     * 查询时间段内应付金额
     *
     * @param storeId
     * @param tenantId
     * @param status
     * @param type
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal getYearReceivablePrice(@Param("storeId") Long storeId,
                                      @Param("tenantId") Long tenantId,
                                      @Param("status") Integer status,
                                      @Param("type") Integer type,
                                      @Param("startTime")String startTime,
                                      @Param("endTime")String endTime);

    /**
     * 更新账单状态
     *
     * @param billId
     * @param status
     */
    void uploadStatus(@Param("billId") Long billId,
                      @Param("status") Integer status);

    /**
     * 查询是否有待支付订单
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    List<FinancialBill> queryNeedPayBill(@Param("storeId") Long storeId,@Param("tenantId") Long tenantId);
}