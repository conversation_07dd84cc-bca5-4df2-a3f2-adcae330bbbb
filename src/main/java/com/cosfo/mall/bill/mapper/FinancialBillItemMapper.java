package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.FinancialBillItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface FinancialBillItemMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinancialBillItem record);

    int insertSelective(FinancialBillItem record);

    FinancialBillItem selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinancialBillItem record);

    int updateByPrimaryKey(FinancialBillItem record);

    /**
     * 查询账单项
     *
     * @param billId
     * @return
     */
    List<FinancialBillItem> selectByBillId(@Param("tenantId") Long tenantId,
                                           @Param("billId") Long billId,
                                           @Param("businessType") Integer businessType);
}