package com.cosfo.mall.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分账退款明细快照表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-29
 */
@Mapper
public interface BillProfitSharingRefundSnapshotMapper extends BaseMapper<BillProfitSharingRefundSnapshot> {

    /**
     * 批量保存
     *
     * @param billProfitSharingRefundSnapshots 退款快照
     * @return int
     */
    int saveBatch(@Param("billProfitSharingRefundSnapshots") List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots);

}
