package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BillProfitSharingOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BillProfitSharingOrder record);

    int insertSelective(BillProfitSharingOrder record);

    BillProfitSharingOrder selectByPrimaryKey(Long id);

    /**
     * 根据分账单号查询
     *
     * @param profitSharingNo
     * @return
     */
    BillProfitSharingOrder selectByProfitSharingNo(String profitSharingNo);

    int updateByPrimaryKeySelective(BillProfitSharingOrder record);

    int updateByPrimaryKey(BillProfitSharingOrder record);

    /**
     * 查询分账处理中的订单
     *
     * @return
     */
    List<BillProfitSharingOrder> queryProfitSharingOrderForProcessIng();

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @return
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新状态
     *
     * @param tenantId
     * @param orderId
     * @param status
     * @return
     */
    int updateStatusByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId, @Param("status") Integer status, @Param("originStatus") Integer originStatus);

    /**
     * 查询等待分账订单
     *
     * @return
     */
    List<BillProfitSharingOrder> queryWaitingProfitSharingOrder(Integer retryNum);
    List<BillProfitSharingOrder> queryWaitingProfitSharingOrderByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单Id查询
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<BillProfitSharingOrder> queryByOrderIdAndTenantId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);

    /**
     * 根据订单Id查询
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<BillProfitSharingOrder> queryByOrderIdsAndTenantId(@Param("tenantId") Long tenantId,@Param("orderIds") List<Long> orderIds);

    /**
     * 重置状态和自增次数
     *
     * @param id
     * @return
     */
    int resetStatusAndIncRetryNum(Long id);

    /**
     * 根据id更新状态
     *
     * @param id
     * @param finalStatus
     * @param orgStatus
     * @return
     */
    int updateStatusById(@Param("id") Long id, @Param("finalStatus") Integer finalStatus, @Param("orgStatus") Integer orgStatus);

    /**
     * 根据租户id、订单id、供应商id查询
     *
     * @param tenantId
     * @param orderId
     * @param supplierId
     * @return
     */
    BillProfitSharingOrder queryByTenantAndOrderAndSupplierId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId, @Param("supplierId") Long supplierId);

    /**
     * 查询初始化状态的订单
     *
     * @return
     */
    List<Long> queryInitStatusOrderIds();

    /**
     * 更新分账单号
     *
     * @param id
     * @param profitSharingNo
     * @return
     */
    int updateProfitSharingNo(@Param("id") Long id, @Param("profitSharingNo") String profitSharingNo);
}