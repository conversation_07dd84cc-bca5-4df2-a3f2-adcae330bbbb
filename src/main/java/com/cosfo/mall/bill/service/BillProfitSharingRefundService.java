package com.cosfo.mall.bill.service;

import com.cosfo.mall.bill.model.bo.RefundCalculateResultBO;

/**
 * @description: 退款分账业务层
 * @author: <PERSON>
 * @date: 2023-07-29
 **/
public interface BillProfitSharingRefundService {


    /**
     * 退款分账
     *
     * @param orderAfterSaleId 订单售后id
     */
    RefundCalculateResultBO doRefundSharing(Long orderAfterSaleId);

    /**
     * 计算退款金额
     *
     * @param orderAfterSaleId
     */
    RefundCalculateResultBO calculateRefundSharing(Long orderAfterSaleId);
}
