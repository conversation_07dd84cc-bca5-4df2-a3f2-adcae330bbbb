package com.cosfo.mall.bill.service.impl;

import com.cosfo.mall.bill.dao.BillProfitSharingConfigDao;
import com.cosfo.mall.bill.model.po.BillProfitSharingConfig;
import com.cosfo.mall.bill.repository.BillProfitSharingRefundSnapshotRepository;
import com.cosfo.mall.bill.service.BillProfitSharingConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-08-29
 **/
@Service
public class BillProfitSharingConfigServiceImpl implements BillProfitSharingConfigService {

    @Resource
    private BillProfitSharingConfigDao billProfitSharingConfigDao;

    @Override
    public BillProfitSharingConfig queryByTenantId(Long tenantId) {
        return billProfitSharingConfigDao.queryByTenantId(tenantId);
    }
}
