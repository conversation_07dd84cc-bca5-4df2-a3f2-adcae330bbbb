package com.cosfo.mall.bill.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.bill.mapper.BillProfitSharingMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.bo.RefundCalculateBO;
import com.cosfo.mall.bill.model.bo.RefundCalculateResultBO;
import com.cosfo.mall.bill.model.dto.BillInvertedDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.repository.BillProfitSharingRefundSnapshotRepository;
import com.cosfo.mall.bill.service.BillInvertedService;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.BillProfitSharingRefundService;
import com.cosfo.mall.common.ProfitSharingRefundRuleTypeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.BillProfitSharingAccountIdEnum;
import com.cosfo.mall.common.context.BillProfitSharingOrderStatusEnum;
import com.cosfo.mall.common.context.OrderItemFeeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.order.mapper.OrderAgentSkuFeeRuleSnapshotMapper;
import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionQueryDTO;
import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import com.cosfo.mall.order.model.po.OrderItem;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;
import com.cosfo.mall.order.model.po.OrderItemSnapshot;
import com.cosfo.mall.order.service.*;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.supplier.SupplierDeliveryInfoService;
import com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo;
import com.cosfo.mall.tenant.model.dto.TenantIdDTO;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.PayTypeEnum;
import com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ROUND_DOWN;
import static java.math.BigDecimal.ZERO;

/**
 * @description: 退款分账业务层
 * @author: George
 * @date: 2023-07-29
 **/
@Slf4j
@Service
public class BillProfitSharingRefundServiceImpl implements BillProfitSharingRefundService {

    @Resource
    private PaymentService paymentService;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private OrderItemSnapshotService orderItemSnapshotService;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private BillProfitSharingRefundSnapshotRepository billProfitSharingRefundSnapshotRepository;
    @Resource
    private OrderAgentSkuFeeRuleService orderAgentSkuFeeRuleService;
    @Resource
    private OrderItemFeeTransactionService orderItemFeeTransactionService;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private BillInvertedService billInvertedService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @Resource
    private OrderAgentSkuFeeRuleSnapshotMapper orderAgentSkuFeeRuleSnapshotMapper;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private SupplierDeliveryInfoService supplierDeliveryInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RefundCalculateResultBO doRefundSharing(Long orderAfterSaleId) {
        // 计算退款金额
        RefundCalculateResultBO refundCalculateResultBO = calculateRefundSharing(orderAfterSaleId);
        if (refundCalculateResultBO == null) {
            return null;
        }

        // 退款记录留存
        generateBillProfitSharingRefundSnapshot(refundCalculateResultBO);

        // 账户分账记录留存
        generateRefundAcctSplitDetailRecord(refundCalculateResultBO);

        // 倒挂留存
        generateBillInvertedRecord(refundCalculateResultBO);

        return refundCalculateResultBO;
    }

    /**
     * 倒挂留存
     * @param refundCalculateResultBO
     */
    private void generateBillInvertedRecord(RefundCalculateResultBO refundCalculateResultBO) {
        Long orderId = refundCalculateResultBO.getOrderId();
        Long tenantId = refundCalculateResultBO.getTenantId();
        Long orderAfterSaleId = refundCalculateResultBO.getOrderAfterSaleId();
        List<BillInvertedDTO> billInvertedDTOList = Lists.newArrayList();

        // 应退和实退不一致则触发了倒挂
        Boolean tenantInvertFlag = refundCalculateResultBO.getTenantShouldRefundPrice().compareTo(refundCalculateResultBO.getTenantActualRefundPrice()) != 0;
        Boolean ftInvertFlag = refundCalculateResultBO.getFtShouldRefundPrice().compareTo(refundCalculateResultBO.getFtActualRefundPrice()) != 0;
        Boolean supplierInvertFlag = refundCalculateResultBO.getSupplierShouldRefundPrice().compareTo(refundCalculateResultBO.getSupplierActualRefundPrice()) != 0;
        Boolean supplierReverseInvertFlag = refundCalculateResultBO.getSupplierShouldRefundPrice().compareTo(BigDecimal.ZERO) > 0 && refundCalculateResultBO.getSupplierShouldRefundPrice().compareTo(refundCalculateResultBO.getSupplierRefundPrice()) < 0;
        if (tenantInvertFlag || ftInvertFlag || supplierInvertFlag || supplierReverseInvertFlag) {
            BillInvertedDTO tenantBillInvertedDTO = new BillInvertedDTO();
            tenantBillInvertedDTO.setTenantId(tenantId);
            tenantBillInvertedDTO.setOrderId(orderId);
            tenantBillInvertedDTO.setAfterSaleId(orderAfterSaleId);
            tenantBillInvertedDTO.setAcctSplitTenantId(tenantId);
            tenantBillInvertedDTO.setRefundableAmount(refundCalculateResultBO.getTenantShouldRefundPrice());
            tenantBillInvertedDTO.setActualAmount(refundCalculateResultBO.getTenantActualRefundPrice());
            billInvertedDTOList.add(tenantBillInvertedDTO);

            BillInvertedDTO ftBillInvertedDTO = new BillInvertedDTO();
            ftBillInvertedDTO.setTenantId(tenantId);
            ftBillInvertedDTO.setOrderId(orderId);
            ftBillInvertedDTO.setAfterSaleId(orderAfterSaleId);
            ftBillInvertedDTO.setAcctSplitTenantId(BillProfitSharingAccountIdEnum.FT.getId());
            ftBillInvertedDTO.setRefundableAmount(refundCalculateResultBO.getFtShouldRefundPrice());
            ftBillInvertedDTO.setActualAmount(refundCalculateResultBO.getFtActualRefundPrice());
            billInvertedDTOList.add(ftBillInvertedDTO);

            BillInvertedDTO supplierBillInvertedDTO = new BillInvertedDTO();
            supplierBillInvertedDTO.setTenantId(tenantId);
            supplierBillInvertedDTO.setOrderId(orderId);
            supplierBillInvertedDTO.setAfterSaleId(orderAfterSaleId);
            supplierBillInvertedDTO.setAcctSplitTenantId(refundCalculateResultBO.getSupplierId());
            // 反向记录倒挂
            supplierBillInvertedDTO.setRefundableAmount(supplierReverseInvertFlag ? refundCalculateResultBO.getSupplierRefundPrice() : refundCalculateResultBO.getSupplierShouldRefundPrice());
            supplierBillInvertedDTO.setActualAmount(refundCalculateResultBO.getSupplierActualRefundPrice());
            billInvertedDTOList.add(supplierBillInvertedDTO);
        }

        if (!CollectionUtils.isEmpty(billInvertedDTOList)) {
            billInvertedService.save(billInvertedDTOList);
        }
    }

    /**
     * 账户分账记录留存
     */
    private void generateRefundAcctSplitDetailRecord(RefundCalculateResultBO refundCalculateResultBO) {
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.querySuccessByTenantIdAndOrderId(refundCalculateResultBO.getTenantId(), refundCalculateResultBO.getOrderId(), ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
        if (CollectionUtils.isEmpty(billProfitSharings)) {
            log.info("bill_profit_sharing正向单数据异常，兜底走默认查询支付配置，{},{}", JSON.toJSONString(refundCalculateResultBO), JSON.toJSONString(billProfitSharings));
        }
        Map<Long, BillProfitSharing> billProfitSharingMap = Collections.EMPTY_MAP;
        if (!CollectionUtils.isEmpty(billProfitSharings)) {
            billProfitSharingMap = billProfitSharings.stream().collect(Collectors.toMap(BillProfitSharing::getReceiverTenantId, Function.identity(), (v1, v2) -> v1));
        }

        Long tenantId = refundCalculateResultBO.getTenantId();
        ArrayList<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateResultBO.getBillProfitSharingRefundSnapshots();
        billProfitSharingRefundSnapshots.forEach(el -> {
            if (Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode())) {
                el.setActualRefundPrice(el.getActualRefundPrice().negate());
            }
        });
        // 根据账户ID对退款金额汇总求和
        Map<Long, BigDecimal> accountRefundMap = billProfitSharingRefundSnapshots.stream()
                .collect(Collectors.groupingBy(BillProfitSharingRefundSnapshot::getAccountId,
                        Collectors.reducing(BigDecimal.ZERO, BillProfitSharingRefundSnapshot::getActualRefundPrice, BigDecimal::add)));
        log.info("账户分账记录留存，账户退款金额: {}", JSON.toJSONString(accountRefundMap));

        ArrayList<RefundAcctSplitDetailDTO> list = Lists.newArrayListWithCapacity(3);
        BigDecimal tenantRefundPrice = accountRefundMap.getOrDefault(tenantId, ZERO);
        if (tenantRefundPrice.compareTo(BigDecimal.ZERO) > 0) {
            String merchantNo = Optional.ofNullable(billProfitSharingMap.get(tenantId)).map(BillProfitSharing::getAccount).orElse(StringUtils.EMPTY);
            RefundAcctSplitDetailDTO dto = buildRefundAcctSplit(tenantId, tenantId, merchantNo, accountRefundMap);
            list.add(dto);
        }

        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(refundCalculateResultBO.getTenantId(), refundCalculateResultBO.getOrderId());
        if (!CollectionUtils.isEmpty(billProfitSharingSnapshots)) {
            BillProfitSharingSnapshot billProfitSharingSnapshot = billProfitSharingSnapshots.get(0);
            Integer deliveryType = billProfitSharingSnapshot.getDeliveryType();
            if (Objects.equals(deliveryType, ProfitSharingDeliveryTypeEnums.BRAND_DELIVERY.getType())) {
                Map<Long, BigDecimal> accountMap = billProfitSharingSnapshots.stream()
                        .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                        .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingSnapshot::getProfitSharingPrice, BigDecimal::add)));
                Pair<Long, Long> accountIds = getDynamicAccountIds(accountMap, refundCalculateResultBO.getTenantId());
                Long ftAccountId = accountIds.getKey();
                BigDecimal ftRefundPrice = accountRefundMap.getOrDefault(ftAccountId, ZERO);
                if (ftRefundPrice.compareTo(BigDecimal.ZERO) > 0) {
                    String merchantNo = Optional.ofNullable(billProfitSharingMap.get(ftAccountId)).map(BillProfitSharing::getAccount).orElse(StringUtils.EMPTY);
                    RefundAcctSplitDetailDTO dto = buildRefundAcctSplit(tenantId, ftAccountId, merchantNo, accountRefundMap);
                    list.add(dto);
                }
            }
        }
        BigDecimal supplierRefundPrice = accountRefundMap.getOrDefault(refundCalculateResultBO.getSupplierId(), ZERO);
        if (supplierRefundPrice.compareTo(BigDecimal.ZERO) > 0) {
            String merchantNo = Optional.ofNullable(billProfitSharingMap.get(refundCalculateResultBO.getSupplierId())).map(BillProfitSharing::getAccount).orElse(StringUtils.EMPTY);
            RefundAcctSplitDetailDTO dto = buildRefundAcctSplit(tenantId, refundCalculateResultBO.getSupplierId(), merchantNo, accountRefundMap);
            list.add(dto);
        }
        refundCalculateResultBO.setRefundAcctSplitDetailDTOS(list);
    }

    public RefundAcctSplitDetailDTO buildRefundAcctSplit(Long tenantId, Long accountId, String merchantNo, Map<Long, BigDecimal> accountRefundMap) {
        RefundAcctSplitDetailDTO dto = new RefundAcctSplitDetailDTO();
        dto.setTenantId(tenantId);
        dto.setHuifuId(merchantNo);
        dto.setMerchantNo(merchantNo);
        dto.setAcctSplitTenantId(accountId);
        dto.setDivAmt(accountRefundMap.getOrDefault(accountId, BigDecimal.ZERO));
        return dto;
    }

    /**
     * 退款记录留存
     */
    private void generateBillProfitSharingRefundSnapshot(RefundCalculateResultBO refundCalculateResultBO) {
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateResultBO.getBillProfitSharingRefundSnapshots();
        billProfitSharingRefundSnapshotRepository.saveBatch(billProfitSharingRefundSnapshots);
    }

    @Override
    public RefundCalculateResultBO calculateRefundSharing(Long orderAfterSaleId) {
        OrderAfterSaleResp orderAfterSale = orderAfterSaleService.queryById(orderAfterSaleId);
        if (orderAfterSale.getTotalPrice() != null && orderAfterSale.getTotalPrice().compareTo(ZERO) == 0) {
            log.info("0元售后单：{}无需处理分账退款，计算流程结束", orderAfterSaleId);
            return null;
        }

        List<Integer> realPayList = Arrays.asList(PayTypeEnum.WECHAT_PAY.getCode(), PayTypeEnum.ALI_PAY.getCode());
        if(orderAfterSale.getPayType() != null && !realPayList.contains(orderAfterSale.getPayType())){
            log.info("非微信、支付宝售后单：{}无需处理分账退款，计算流程结束", orderAfterSaleId);
            return null;
        }

        if (OrderAfterSaleServiceTypeEnum.getNativeCombinedServiceType().contains(orderAfterSale.getServiceType())) {
            log.info("组合服务单：{}无需处理分账退款，计算流程结束", orderAfterSaleId);
            return null;
        }
        Long tenantId = orderAfterSale.getTenantId();
        Long orderId = orderAfterSale.getOrderId();
        Long orderItemId = orderAfterSale.getOrderItemId();
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderService.getBillProfitSharingOrder(tenantId, orderId, orderItemId);
        if (Objects.isNull(billProfitSharingOrder)) {
            log.info("售后单：{}未查询到分账记录，计算流程结束", orderAfterSaleId);
            return null;
        }

        if (!Objects.equals(billProfitSharingOrder.getStatus(), BillProfitSharingOrderStatusEnum.FINISHED.getStatus())) {
            log.error("售后单：{}对应的订单并未完成正向分账，计算流程结束", orderAfterSaleId);
            return null;
        }

        // 支付税率
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(orderId, tenantId);
        // 查询是否是该订单项最后一次售后
        Boolean lastOrderAfterSaleFlag = orderAfterSaleService.verifyLastOrderItemOrderAfterSaleFlag(orderAfterSaleId);
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryOrderSharingSnapshots(tenantId, orderId, billProfitSharingOrder.getProfitSharingNo());
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = billProfitSharingRefundSnapshotRepository.queryByOrderId(tenantId, orderId, billProfitSharingOrder.getProfitSharingNo());
        // 组装计算业务对象
        RefundCalculateBO refundCalculateBO = RefundCalculateBO.builder()
                .tenantId(orderAfterSale.getTenantId())
                .orderId(orderAfterSale.getOrderId())
                .orderItemId(orderAfterSale.getOrderItemId())
                .orderAfterSaleId(orderAfterSaleId)
                .itemRefundFee(NumberUtil.sub(orderAfterSale.getTotalPrice(), orderAfterSale.getDeliveryFee()))
                .deliveryRefundFee(Optional.ofNullable(orderAfterSale.getDeliveryFee()).orElse(ZERO))
                .afterSaleTotalPrice(orderAfterSale.getTotalPrice())
                .afterSaleType(orderAfterSale.getAfterSaleType())
                .serviceType(orderAfterSale.getServiceType())
                .warehouseType(orderAfterSale.getWarehouseType())
                .responsibilityType(orderAfterSale.getResponsibilityType())
                .paymentDTO(paymentDTO)
                .lastOrderAfterSaleFlag(lastOrderAfterSaleFlag)
                .billProfitSharingSnapshots(billProfitSharingSnapshots)
                .billProfitSharingRefundSnapshots(billProfitSharingRefundSnapshots)
                .orgProfitSharingNo(billProfitSharingOrder.getProfitSharingNo())
                .supplierId(Optional.ofNullable(billProfitSharingOrder.getSupplierId()).orElse(BillProfitSharingAccountIdEnum.SUPPLIER.getId()))
                .build();

        // 全部分账给个人
        RefundCalculateResultBO result;
        boolean personalSharingFlag = billProfitSharingSnapshots.stream().anyMatch(el -> Objects.equals(el.getType(), BillProfitSharingSnapshotTypeEnum.ALL.getCode()));
        if (personalSharingFlag) {
            return calculatePersonalRefundSharing(refundCalculateBO);
        }

        Integer profitSharingDeliveryType = billProfitSharingSnapshots.get(0).getDeliveryType();
        ProfitSharingDeliveryTypeEnums profitSharingDeliveryTypeEnums = ProfitSharingDeliveryTypeEnums.getByDeliveryType(profitSharingDeliveryType);
        switch (profitSharingDeliveryTypeEnums) {
            case THIRD_DELIVERY:
                // 三方仓订单处理
                result = calculateThreePartiesOrderRefund(refundCalculateBO);
                break;
            case NO_WAREHOUSE:
                // 无仓订单处理
                result = calculateNoWarehouseOrderRefund(refundCalculateBO);
                break;
            case BRAND_DELIVERY:
                // 自营仓订单处理
                result = calculateProprietaryOrderRefund(refundCalculateBO);
                break;
            default:
                throw new ProviderException("非法的订单类型");
        }
        result.setSupplierId(refundCalculateBO.getSupplierId());
        return result;
    }

    private RefundCalculateResultBO calculateNoWarehouseOrderRefund(RefundCalculateBO refundCalculateBO) {
        // 退款金额
        BigDecimal itemRefundFee = refundCalculateBO.getItemRefundFee();
        OrderItemAndSnapshotResp orderItemAndSnapshotDTO = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById(refundCalculateBO.getOrderItemId()));
        // 是否最后一次退款
        Boolean lastOrderAfterSaleFlag = refundCalculateBO.getLastOrderAfterSaleFlag();
        BigDecimal supplierRefundPrice;
        BigDecimal totalSupplierPrice = NumberUtil.mul(orderItemAndSnapshotDTO.getSupplyPrice(), orderItemAndSnapshotDTO.getAmount());
        if (lastOrderAfterSaleFlag) {
            BigDecimal historySupplyRefundPrice = getHistoryRefundPrice(refundCalculateBO, ProfitSharingRefundRuleTypeEnum.SUPPLY_SKU.getCode(), refundCalculateBO.getSupplierId());
            supplierRefundPrice = NumberUtil.sub(totalSupplierPrice, historySupplyRefundPrice);
        } else {
            // 售后产生的费用
            BigDecimal rate = NumberUtil.div(itemRefundFee, orderItemAndSnapshotDTO.getTotalPrice());
            supplierRefundPrice = NumberUtil.mul(totalSupplierPrice, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        }
        BigDecimal tenantRefundPrice = NumberUtil.sub(itemRefundFee, supplierRefundPrice);

        // 应退金额（定责过后）因为无仓订单不定责 所以应退金额 = 退款金额
        BigDecimal tenantShouldRefundPrice = tenantRefundPrice;
        BigDecimal supplierShouldRefundPrice = supplierRefundPrice;

        //供应商需实退款金额 = 供应商应退 > 退款金额 ？ 退款金额 ：供应商应退
        supplierShouldRefundPrice = supplierShouldRefundPrice.compareTo(itemRefundFee) >= NumberConstant.ZERO ? itemRefundFee : supplierShouldRefundPrice;
        // 租户应退金额 = 租户应退金额 < 0 ? 0 : 租户应退金额
        tenantShouldRefundPrice = tenantShouldRefundPrice.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : tenantShouldRefundPrice;

        // 实退金额（退款倒挂过后）因为无仓订单没有定责 所以不会有退款倒挂 所以实际退款金额 = 应退金额
        BigDecimal tenantActualRefundPrice = tenantShouldRefundPrice;
        BigDecimal supplierActualRefundPrice = supplierShouldRefundPrice;

        RefundCalculateResultBO refundCalculateResultBO = RefundCalculateResultBO.builder()
                .tenantId(refundCalculateBO.getTenantId())
                .orderId(refundCalculateBO.getOrderId())
                .orderAfterSaleId(refundCalculateBO.getOrderAfterSaleId())
                .originPrice(itemRefundFee)
                .supplierRefundPrice(supplierRefundPrice).supplierShouldRefundPrice(supplierShouldRefundPrice).supplierActualRefundPrice(supplierActualRefundPrice)
                .tenantRefundPrice(tenantRefundPrice).tenantShouldRefundPrice(tenantShouldRefundPrice).tenantActualRefundPrice(tenantActualRefundPrice)
                .ftRefundPrice(BigDecimal.ZERO).ftShouldRefundPrice(BigDecimal.ZERO).ftActualRefundPrice(BigDecimal.ZERO)
                .billProfitSharingRefundSnapshots(new ArrayList<>())
                .build();

        // 运费退款 无仓全部给租户
        calculateNoWarehouseDeliveryRefund(refundCalculateBO, refundCalculateResultBO);

        // 处理金额不足的场景
        processPriceInsufficient(refundCalculateBO, refundCalculateResultBO);

        // 计算手续费
        calculateServiceChargeRefund(refundCalculateBO, refundCalculateResultBO);

        // 填充租户退款快照
        populateTenantRefundSnapshot(refundCalculateBO, refundCalculateResultBO, ProfitSharingRefundRuleTypeEnum.SUPPLY_SKU.getCode());

        // 供应商退款快照
        populateSupplierRefundSnapshot(refundCalculateBO, refundCalculateResultBO, ProfitSharingRefundRuleTypeEnum.SUPPLY_SKU.getCode(), refundCalculateBO.getSupplierId());

        return refundCalculateResultBO;
    }

    private void calculateNoWarehouseDeliveryRefund(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO) {
        refundCalculateResultBO.setFtDeliveryRefundFee(BigDecimal.ZERO);
        refundCalculateResultBO.setSupplierDeliveryRefundFee(BigDecimal.ZERO);
        refundCalculateResultBO.setTenantDeliveryRefundFee(refundCalculateBO.getDeliveryRefundFee());
        populateDeliveryRefundSnapshot(refundCalculateBO, refundCalculateResultBO, refundCalculateResultBO.getTenantId(), refundCalculateResultBO.getTenantDeliveryRefundFee());
    }

    /**
     * 分账给个人计算
     */
    private RefundCalculateResultBO calculatePersonalRefundSharing(RefundCalculateBO refundCalculateBO) {
        Long tenantId = refundCalculateBO.getTenantId();
//        BigDecimal itemRefundFee = refundCalculateBO.getItemRefundFee();
//        BigDecimal deliveryRefundFee = refundCalculateBO.getDeliveryRefundFee();
        BigDecimal afterSaleTotalPrice = refundCalculateBO.getAfterSaleTotalPrice();

        RefundCalculateResultBO refundCalculateResultBO = RefundCalculateResultBO.builder()
                .tenantId(tenantId)
                .orderId(refundCalculateBO.getOrderId())
                .orderAfterSaleId(refundCalculateBO.getOrderAfterSaleId())
                .originPrice(afterSaleTotalPrice)
                .ftRefundPrice(BigDecimal.ZERO).ftShouldRefundPrice(BigDecimal.ZERO).ftActualRefundPrice(BigDecimal.ZERO)
                .supplierRefundPrice(BigDecimal.ZERO).supplierShouldRefundPrice(BigDecimal.ZERO).supplierActualRefundPrice(BigDecimal.ZERO)
                .tenantRefundPrice(afterSaleTotalPrice).tenantShouldRefundPrice(afterSaleTotalPrice).tenantActualRefundPrice(afterSaleTotalPrice)
                .billProfitSharingRefundSnapshots(new ArrayList<>())
                .build();

        // 计算手续费
        calculateServiceChargeRefund(refundCalculateBO, refundCalculateResultBO);

        // 填充退款快照
        populateTenantRefundSnapshot(refundCalculateBO, refundCalculateResultBO, null);

        return refundCalculateResultBO;
    }

    /**
     * 计算三方仓订单退款
     *
     * @param refundCalculateBO 退款计算业务对象
     */
    private RefundCalculateResultBO calculateThreePartiesOrderRefund(RefundCalculateBO refundCalculateBO) {
        Long tenantId = refundCalculateBO.getTenantId();
        Long orderItemId = refundCalculateBO.getOrderItemId();
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotService.selectByOrderItemId(tenantId, orderItemId);
        refundCalculateBO.setGoodsType(orderItemSnapshot.getGoodsType());

        GoodsTypeEnum goodsTypeEnum = GoodsTypeEnum.getTypeByCode(orderItemSnapshot.getGoodsType());
        RefundCalculateResultBO refundCalculateResultBO = null;
        switch (goodsTypeEnum) {
            case QUOTATION_TYPE:
                // 报价货品
                refundCalculateResultBO = calculateQuotationOrderRefund(refundCalculateBO);
                break;
            case SELF_GOOD_TYPE:
                // 代仓货品
                refundCalculateResultBO = calculateAgentWarehouseOrderRefund(refundCalculateBO);
                break;
            default:
                throw new ProviderException("非法的货品类型");
        }
        return refundCalculateResultBO;
    }

    /**
     * 计算报价订单退款
     * 供应商分账实收货款(成本价 > 商品售卖价 ？ 实收货款 = 商品售卖价 ：实收货款 = 成本价)
     * (成本总价/商品金额 * 退款金额) = 供应商应退金额  品牌方 = 实际应退总金额 - 供应商应退金额
     * 责任方在鲜沐 应退 = 供应商应退金额 品牌方 = 品牌方实际应退总金额
     * 责任方在品牌方 鲜沐应退 = 0 品牌方应退 = 鲜沐应退 + 品牌方应退
     * 供应商需实退款金额 = 供应商应退 > 退款金额 ？ 退款金额 ：供应商应退
     *
     * @param refundCalculateBO 退款计算业务对象
     */
    private RefundCalculateResultBO calculateQuotationOrderRefund(RefundCalculateBO refundCalculateBO) {
        Long tenantId = refundCalculateBO.getTenantId();
        Long orderItemId = refundCalculateBO.getOrderItemId();
        // 售后单退款费
        BigDecimal itemRefundFee = refundCalculateBO.getItemRefundFee();
        OrderItem orderItem = orderItemService.queryById(refundCalculateBO.getOrderItemId());
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotService.selectByOrderItemId(tenantId, orderItemId);
        // 最后一次售后则进行末尾倒减
        Boolean lastOrderAfterSaleFlag = refundCalculateBO.getLastOrderAfterSaleFlag();
        // 鲜沐对应成本金额
        BigDecimal xmRefundPrice;
        if (lastOrderAfterSaleFlag) {
            BigDecimal historySupplyRefundPrice = getHistoryRefundPrice(refundCalculateBO, ProfitSharingRefundRuleTypeEnum.SUPPLY_SKU.getCode(), BillProfitSharingAccountIdEnum.SUPPLIER.getId());
            // 报价单商品项总金额
            BigDecimal totalSupplyPrice = NumberUtil.mul(orderItemSnapshot.getSupplyPrice(), orderItem.getAmount());
            xmRefundPrice = NumberUtil.sub(totalSupplyPrice, historySupplyRefundPrice);
        } else {
            // 售后产生的费用
            BigDecimal rate = NumberUtil.div(itemRefundFee, orderItem.getTotalPrice());
            BigDecimal supplyPrice = NumberUtil.mul(orderItemSnapshot.getSupplyPrice(), orderItem.getAmount());
            xmRefundPrice = NumberUtil.mul(supplyPrice, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        }

        // 品牌方需承担金额
        BigDecimal tenantRefundPrice = NumberUtil.sub(itemRefundFee, xmRefundPrice);

        // 应退 退货退款不定责均摊 已到货仅退款需要定责
        Integer serviceType = refundCalculateBO.getServiceType();
        Integer afterSaleType = refundCalculateBO.getAfterSaleType();
        Integer responsibilityType = refundCalculateBO.getResponsibilityType();
        BigDecimal xmShouldRefundPrice = xmRefundPrice;
        BigDecimal tenantShouldRefundPrice = tenantRefundPrice;
        if (Objects.equals(afterSaleType, OrderAfterSaleTypeEnum.DELIVERED.getType()) && Objects.equals(serviceType, OrderAfterSaleServiceTypeEnum.REFUND.getValue())) {
            // 非供应商责任
            if (!Objects.equals(responsibilityType, ResponsibilityTypeEnum.SUPPLIER.getType())) {
                xmShouldRefundPrice = BigDecimal.ZERO;
                tenantShouldRefundPrice = NumberUtil.add(tenantRefundPrice, xmRefundPrice);
            }
        }

        //供应商需实退款金额 = 供应商应退 > 退款金额 ？ 退款金额 ：供应商应退
        xmShouldRefundPrice = xmShouldRefundPrice.compareTo(itemRefundFee) >= NumberConstant.ZERO ? itemRefundFee : xmShouldRefundPrice;
        // 租户应退金额 = 租户应退金额 < 0 ? 0 : 租户应退金额
        tenantShouldRefundPrice = tenantShouldRefundPrice.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : tenantShouldRefundPrice;

        RefundCalculateResultBO refundCalculateResultBO = RefundCalculateResultBO.builder()
                .tenantId(tenantId)
                .orderId(refundCalculateBO.getOrderId())
                .orderAfterSaleId(refundCalculateBO.getOrderAfterSaleId())
                .originPrice(itemRefundFee)
                .ftRefundPrice(BigDecimal.ZERO).ftShouldRefundPrice(BigDecimal.ZERO)
                .supplierRefundPrice(xmRefundPrice).supplierShouldRefundPrice(xmShouldRefundPrice)
                .tenantRefundPrice(tenantRefundPrice).tenantShouldRefundPrice(tenantShouldRefundPrice)
                .billProfitSharingRefundSnapshots(new ArrayList<>())
                .build();

        // 1、运费退款
        calculateThreePartiesOrderDeliveryFeeRefund(refundCalculateBO, refundCalculateResultBO);

        // 2、处理金额不足的情况(倒挂)
        processPriceInsufficient(refundCalculateBO, refundCalculateResultBO);

        // 3、计算手续费
        calculateServiceChargeRefund(refundCalculateBO, refundCalculateResultBO);

        // 4、填充供应价退款快照
        populateRefundSnapshot(refundCalculateBO, refundCalculateResultBO, ProfitSharingRefundRuleTypeEnum.SUPPLY_SKU.getCode());

        return refundCalculateResultBO;
    }

    private void calculateThreePartiesOrderDeliveryFeeRefund(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO) {
        BigDecimal deliveryRefundFee = refundCalculateBO.getDeliveryRefundFee();
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(refundCalculateBO.getOrderId()));
        SupplierDeliveryInfo supplierDeliveryInfo = supplierDeliveryInfoService.querySupplierDeliveryInfo(orderDTO.getOrderNo());
        BigDecimal supplierRefundDeliveryFee = ZERO;
        if (supplierDeliveryInfo != null) {
            BigDecimal deliveryFee = orderDTO.getDeliveryFee();
            if (deliveryFee == null || deliveryFee.compareTo(ZERO) == 0) {
                supplierRefundDeliveryFee = supplierDeliveryInfo.getSupplierDeliveryFee();
            } else {
                BigDecimal rate = NumberUtil.div(deliveryRefundFee, deliveryFee);
                supplierRefundDeliveryFee = NumberUtil.mul(supplierDeliveryInfo.getSupplierDeliveryFee(), rate).setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            }
        }
        BigDecimal tenantRefundDeliveryFee = NumberUtil.sub(deliveryRefundFee, supplierRefundDeliveryFee);

        //供应商需实退款金额 = 供应商应退 > 退款金额 ？ 退款金额 ：供应商应退
        supplierRefundDeliveryFee = supplierRefundDeliveryFee.compareTo(deliveryRefundFee) >= NumberConstant.ZERO ? deliveryRefundFee : supplierRefundDeliveryFee;
        // 租户应退金额 = 租户应退金额 < 0 ? 0 : 租户应退金额
        tenantRefundDeliveryFee = tenantRefundDeliveryFee.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : tenantRefundDeliveryFee;

        refundCalculateResultBO.setFtDeliveryRefundFee(ZERO);
        refundCalculateResultBO.setSupplierDeliveryRefundFee(supplierRefundDeliveryFee);
        refundCalculateResultBO.setTenantDeliveryRefundFee(tenantRefundDeliveryFee);

        // 退款记录留存
        Long tenantId = refundCalculateBO.getTenantId();
        populateDeliveryRefundSnapshot(refundCalculateBO, refundCalculateResultBO, tenantId, tenantRefundDeliveryFee);
        populateDeliveryRefundSnapshot(refundCalculateBO, refundCalculateResultBO, refundCalculateBO.getSupplierId(), supplierRefundDeliveryFee);
    }

    /**
     * 处理账户金额不足的情况
     *
     * @param refundCalculateBO       退款计算波
     * @param refundCalculateResultBO 退款计算结果波
     */
    private void processPriceInsufficient(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO) {
        BigDecimal supplierShouldRefundPrice = refundCalculateResultBO.getSupplierShouldRefundPrice();
        BigDecimal tenantShouldRefundPrice = refundCalculateResultBO.getTenantShouldRefundPrice();

        // 1、查询该笔订单分账明细账户金额
        Map<Long, BigDecimal> usableRefundPriceMap = getUsableRefundPrice(refundCalculateBO);
        BigDecimal tenantUsableRefundPrice = usableRefundPriceMap.get(refundCalculateBO.getTenantId());
        BigDecimal supplierUsableRefundPrice = usableRefundPriceMap.get(refundCalculateBO.getSupplierId());

        // 2、倒挂
        BigDecimal supplierActualRefundPrice = supplierShouldRefundPrice;
        BigDecimal tenantActualRefundPrice = tenantShouldRefundPrice;
        if (tenantShouldRefundPrice.compareTo(tenantUsableRefundPrice) > 0) {
            // 品牌方应退大于可退金额
            BigDecimal difference = NumberUtil.sub(tenantShouldRefundPrice, tenantUsableRefundPrice);
            BigDecimal supplierCouldPay4tenantProfitSharingPrice = NumberUtil.sub(supplierUsableRefundPrice, supplierShouldRefundPrice);
            if (supplierCouldPay4tenantProfitSharingPrice.compareTo(difference) >= 0) {
                tenantActualRefundPrice = tenantUsableRefundPrice;
                supplierActualRefundPrice = NumberUtil.add(supplierShouldRefundPrice, difference);
            } else {
                throw new ProviderException("交易确认退款计算逻辑错误");
            }
        }
        refundCalculateResultBO.setTenantActualRefundPrice(tenantActualRefundPrice);
        refundCalculateResultBO.setFtActualRefundPrice(BigDecimal.ZERO);
        refundCalculateResultBO.setSupplierActualRefundPrice(supplierActualRefundPrice);
    }

    /**
     * 让历史退款金额总计查询
     */
    private BigDecimal getHistoryRefundPrice(RefundCalculateBO refundCalculateBO, Integer ProfitSharingRefundRuleType, Long accountId) {
        Long orderItemId = refundCalculateBO.getOrderItemId();
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateBO.getBillProfitSharingRefundSnapshots();
        return billProfitSharingRefundSnapshots.stream().filter(el -> Objects.equals(el.getOrderItemId(), orderItemId) && Objects.equals(el.getProfitSharingType(), ProfitSharingRefundRuleType) && Objects.equals(el.getAccountId(), accountId)).map(BillProfitSharingRefundSnapshot::getRefundPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取可退金额
     */
    private Map<Long, BigDecimal> getUsableRefundPrice(RefundCalculateBO refundCalculateBO) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateBO.getBillProfitSharingRefundSnapshots();
        // 此处是订单维度的分账账户
        Map<Long, BigDecimal> accountProfitSharingMap = billProfitSharingSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId,
                        Collectors.reducing(BigDecimal.ZERO, BillProfitSharingSnapshot::getProfitSharingPrice, BigDecimal::add)));
        BigDecimal supplierProfitSharingPrice = accountProfitSharingMap.get(refundCalculateBO.getSupplierId());
        BigDecimal tenantProfitSharingPrice = accountProfitSharingMap.get(refundCalculateBO.getTenantId());
        BigDecimal ftProfitSharingPrice = accountProfitSharingMap.get(BillProfitSharingAccountIdEnum.FT.getId());
        // 如果是代仓 代仓的账户在order_agent_sku_fee_rule_snapshot表
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleService.queryByTenantIdAndOrderId(refundCalculateBO.getTenantId(), refundCalculateBO.getOrderId());
        if (!CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)) {
            Map<Long, BigDecimal> accountAgentWarehouseMap = orderAgentSkuFeeRuleSnapshots.stream().collect(Collectors.groupingBy(OrderAgentSkuFeeRuleSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, OrderAgentSkuFeeRuleSnapshot::getPrice, BigDecimal::add)));
            BigDecimal xmAgentWarehousePrice = accountAgentWarehouseMap.get(BillProfitSharingAccountIdEnum.SUPPLIER.getId());
            BigDecimal ftAgentWarehousePrice = accountAgentWarehouseMap.get(BillProfitSharingAccountIdEnum.FT.getId());
            BigDecimal tenantAgentWarehousePrice = accountAgentWarehouseMap.get(refundCalculateBO.getTenantId());
            supplierProfitSharingPrice = NumberUtil.add(supplierProfitSharingPrice, xmAgentWarehousePrice);
            ftProfitSharingPrice = NumberUtil.add(ftProfitSharingPrice, ftAgentWarehousePrice);
            tenantProfitSharingPrice = NumberUtil.add(tenantProfitSharingPrice, tenantAgentWarehousePrice);
        }

        // 如果品牌方分账总金额为负值，供应商实际分账金额 = 品牌方分账金额 + 供应商分账金额
        if (tenantProfitSharingPrice.compareTo(BigDecimal.ZERO) <= 0) {
            supplierProfitSharingPrice = NumberUtil.add(supplierProfitSharingPrice, tenantProfitSharingPrice);
            tenantProfitSharingPrice = BigDecimal.ZERO;
        }

        // 逆向分账记录
        Map<Long, BigDecimal> accountProfitSharingRefundMap = billProfitSharingRefundSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRefundRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .collect(Collectors.groupingBy(BillProfitSharingRefundSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingRefundSnapshot::getActualRefundPrice, BigDecimal::add)));
        BigDecimal tenantProfitSharingRefundPrice = accountProfitSharingRefundMap.get(refundCalculateBO.getTenantId());
        BigDecimal supplierProfitSharingRefundPrice = accountProfitSharingRefundMap.get(refundCalculateBO.getSupplierId());
        BigDecimal ftProfitSharingRefundPrice = accountProfitSharingRefundMap.get(BillProfitSharingAccountIdEnum.FT.getId());

        // 账户可用的退款金额
        BigDecimal supplierUsableRefundPrice = NumberUtil.sub(supplierProfitSharingPrice, supplierProfitSharingRefundPrice);
        BigDecimal tenantUsableRefundPrice = NumberUtil.sub(tenantProfitSharingPrice, tenantProfitSharingRefundPrice);
        BigDecimal ftUsableRefundPrice = NumberUtil.sub(ftProfitSharingPrice, ftProfitSharingRefundPrice);

        return ImmutableMap.of(refundCalculateBO.getTenantId(), tenantUsableRefundPrice, BillProfitSharingAccountIdEnum.FT.getId(), ftUsableRefundPrice, refundCalculateBO.getSupplierId(), supplierUsableRefundPrice);
    }

    /**
     * 计算手续费手续费返还
     * 1、先计算出来，本次交易退款，汇付需要返还的手续费
     * 2、将手续费均摊给各方
     * @param refundCalculateBO
     * @param refundCalculateResultBO
     */
    private void calculateServiceChargeRefund(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO) {
        Long tenantId = refundCalculateBO.getTenantId();
        Long orderId = refundCalculateBO.getOrderId();
        Pair<Long, Long> accountIds = getDynamicAccountIdsByBO(refundCalculateBO);
        Long ftAccountId = accountIds.getKey();

        // 先计算出来，本次交易退款，汇付需要返还的手续费
        BigDecimal thisOneRefundServiceCharge = getThisOneRefundServiceCharge(refundCalculateBO);
        log.info("本次确认退款:{}，预计渠道商会返还的手续费{}元", refundCalculateBO.getOrderAfterSaleId(), thisOneRefundServiceCharge);
        // 拿本次汇付返还的手续费，分摊到各方
        TreeMap<Long, BigDecimal> eachPartyServiceChargeRefundMap = calculateEachOtherServiceChargeRefund(thisOneRefundServiceCharge, refundCalculateBO, refundCalculateResultBO);
        log.info("本次确认退款:{}，各方返还手续费:{}", refundCalculateBO.getOrderAfterSaleId(), JSON.toJSONString(eachPartyServiceChargeRefundMap));

        // 各方按照退款金额均摊
        BigDecimal ftServiceChargeRefund = eachPartyServiceChargeRefundMap.getOrDefault(ftAccountId, BigDecimal.ZERO);
        BigDecimal supplierServiceChargeRefund = eachPartyServiceChargeRefundMap.getOrDefault(refundCalculateBO.getSupplierId(), BigDecimal.ZERO);
        BigDecimal tenantServiceChargeRefund = eachPartyServiceChargeRefundMap.getOrDefault(tenantId, BigDecimal.ZERO);

        // 退款记录留存
        BillProfitSharingRefundSnapshot tenantBillProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        tenantBillProfitSharingRefundSnapshot.setTenantId(tenantId);
        tenantBillProfitSharingRefundSnapshot.setOrderId(orderId);
        tenantBillProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        tenantBillProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        tenantBillProfitSharingRefundSnapshot.setProfitSharingType(ProfitSharingRefundRuleTypeEnum.SERVICE_CHARGE.getCode());
        tenantBillProfitSharingRefundSnapshot.setAccountId(tenantId);
        tenantBillProfitSharingRefundSnapshot.setOriginPrice(thisOneRefundServiceCharge);
        tenantBillProfitSharingRefundSnapshot.setRefundPrice(tenantServiceChargeRefund);
        tenantBillProfitSharingRefundSnapshot.setShouldRefundPrice(tenantServiceChargeRefund);
        tenantBillProfitSharingRefundSnapshot.setActualRefundPrice(tenantServiceChargeRefund);
        tenantBillProfitSharingRefundSnapshot.setFinalRefundPrice(tenantServiceChargeRefund);
        tenantBillProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());

        BillProfitSharingRefundSnapshot ftBillProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        ftBillProfitSharingRefundSnapshot.setTenantId(tenantId);
        ftBillProfitSharingRefundSnapshot.setOrderId(orderId);
        ftBillProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        ftBillProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        ftBillProfitSharingRefundSnapshot.setAccountId(ftAccountId);
        ftBillProfitSharingRefundSnapshot.setProfitSharingType(ProfitSharingRefundRuleTypeEnum.SERVICE_CHARGE.getCode());
        ftBillProfitSharingRefundSnapshot.setRefundPrice(ftServiceChargeRefund);
        ftBillProfitSharingRefundSnapshot.setShouldRefundPrice(ftServiceChargeRefund);
        ftBillProfitSharingRefundSnapshot.setActualRefundPrice(ftServiceChargeRefund);
        ftBillProfitSharingRefundSnapshot.setFinalRefundPrice(ftServiceChargeRefund);
        ftBillProfitSharingRefundSnapshot.setOriginPrice(thisOneRefundServiceCharge);
        ftBillProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());

        BillProfitSharingRefundSnapshot supplierBillProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        supplierBillProfitSharingRefundSnapshot.setTenantId(tenantId);
        supplierBillProfitSharingRefundSnapshot.setOrderId(orderId);
        supplierBillProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        supplierBillProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        supplierBillProfitSharingRefundSnapshot.setAccountId(refundCalculateBO.getSupplierId());
        supplierBillProfitSharingRefundSnapshot.setProfitSharingType(ProfitSharingRefundRuleTypeEnum.SERVICE_CHARGE.getCode());
        supplierBillProfitSharingRefundSnapshot.setOriginPrice(thisOneRefundServiceCharge);
        supplierBillProfitSharingRefundSnapshot.setRefundPrice(supplierServiceChargeRefund);
        supplierBillProfitSharingRefundSnapshot.setShouldRefundPrice(supplierServiceChargeRefund);
        supplierBillProfitSharingRefundSnapshot.setActualRefundPrice(supplierServiceChargeRefund);
        supplierBillProfitSharingRefundSnapshot.setFinalRefundPrice(supplierServiceChargeRefund);
        supplierBillProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());

        // 填充最终金额
//        refundCalculateResultBO.setTenantFinalRefundPrice(tenantFinalRefundPrice);
//        refundCalculateResultBO.setSupplierFinalRefundPrice(supplierFinalRefundPrice);
//        refundCalculateResultBO.setFtFinalRefundPrice(ftFinalRefundPrice);
//        refundCalculateResultBO.setBillProfitSharingRefundSnapshots(Lists.newArrayList(tenantBillProfitSharingRefundSnapshot, ftBillProfitSharingRefundSnapshot, supplierBillProfitSharingRefundSnapshot));
        refundCalculateResultBO.getBillProfitSharingRefundSnapshots().add(tenantBillProfitSharingRefundSnapshot);
        refundCalculateResultBO.getBillProfitSharingRefundSnapshots().add(ftBillProfitSharingRefundSnapshot);
        refundCalculateResultBO.getBillProfitSharingRefundSnapshots().add(supplierBillProfitSharingRefundSnapshot);
    }

    public Pair<Long, Long> getDynamicAccountIdsByBO(RefundCalculateBO refundCalculateBO) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        Map<Long, BigDecimal> accountMap = billProfitSharingSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingSnapshot::getProfitSharingPrice, BigDecimal::add)));
        return getDynamicAccountIds(accountMap, refundCalculateBO.getTenantId());
    }

    /**
     * 计算本次各方手续费返还
     *
     * @param thisOneRefundServiceCharge
     * @param refundCalculateBO
     * @param refundCalculateResultBO
     */
    private TreeMap<Long, BigDecimal> calculateEachOtherServiceChargeRefund(BigDecimal thisOneRefundServiceCharge, RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO) {
        // 因为是四舍五入，所以势必存在误差
        BigDecimal supplierActualRefundPrice = NumberUtil.add(refundCalculateResultBO.getSupplierActualRefundPrice(), refundCalculateResultBO.getSupplierDeliveryRefundFee());
        BigDecimal ftActualRefundPrice = NumberUtil.add(refundCalculateResultBO.getFtActualRefundPrice(), refundCalculateResultBO.getFtDeliveryRefundFee());
        BigDecimal thisOneRefundPrice = refundCalculateBO.getAfterSaleTotalPrice();

        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        Map<Long, BigDecimal> accountMap = billProfitSharingSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingSnapshot::getProfitSharingPrice, BigDecimal::add)));
        Pair<Long, Long> accountIds = getDynamicAccountIds(accountMap, refundCalculateBO.getTenantId());
        Long ftAccountId = accountIds.getKey();

        // 获取手续费的承担规则
        BigDecimal ftServiceChargeRefund = ZERO;
        BigDecimal supplierServiceChargeRefund = ZERO;
        BigDecimal tenantServiceChargeRefund = ZERO;
        BillProfitSharingSnapshot billProfitSharingSnapshot = billProfitSharingSnapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .findFirst()
                .orElseThrow(() -> new ProviderException("分账手续费快照明细异常"));
        if (Objects.equals(billProfitSharingSnapshot.getMappingType(), ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode())) {
            // 按比例退还
            Map<Long, BigDecimal> accountRateMap = refundCalculateBO.getBillProfitSharingSnapshots()
                    .stream()
                    .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                    .collect(Collectors.toMap(BillProfitSharingSnapshot::getAccountId, BillProfitSharingSnapshot::getNumber));
            ftServiceChargeRefund = NumberUtil.mul(thisOneRefundServiceCharge, NumberUtil.div(accountRateMap.get(ftAccountId), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            supplierServiceChargeRefund = NumberUtil.mul(thisOneRefundServiceCharge, NumberUtil.div(accountRateMap.get(refundCalculateBO.getSupplierId()), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        } else {
            // 各方按照退款金额均摊
            ftServiceChargeRefund = NumberUtil.mul(thisOneRefundServiceCharge, NumberUtil.div(ftActualRefundPrice, thisOneRefundPrice)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            supplierServiceChargeRefund = NumberUtil.mul(thisOneRefundServiceCharge, NumberUtil.div(supplierActualRefundPrice, thisOneRefundPrice)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        }
        tenantServiceChargeRefund = NumberUtil.sub(thisOneRefundServiceCharge, supplierServiceChargeRefund, ftServiceChargeRefund);
        log.info("本次售后:{}分账回退，{}应退手续费：{}，{}应退手续费：{}，租户应退手续费：{}", refundCalculateBO.getOrderAfterSaleId(), ftAccountId, ftServiceChargeRefund, refundCalculateBO.getSupplierId(), supplierServiceChargeRefund, tenantServiceChargeRefund);
        // 各方应当返还的手续费
        // 获取各账户可用的手续费
        TreeMap<Long, BigDecimal> usableServiceChargeMap = getUsableServiceCharge(refundCalculateBO);
        TreeMap<Long, BigDecimal> usableServiceChargeSnapshotMap = new TreeMap<>(usableServiceChargeMap);

        BigDecimal totalUsableServiceCharge = usableServiceChargeMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalUsableServiceCharge.compareTo(thisOneRefundServiceCharge) < 0) {
            throw new ProviderException("本次交易确认退款，退款手续费不足，请关注");
        }
        // 尝试扣减 平台->供应商->租户依次扣减，不足的情况进行垫付
        Integer counter = 0;
        tryDeductServiceChargeRefund(++counter, usableServiceChargeMap, ftAccountId, ftServiceChargeRefund);
        tryDeductServiceChargeRefund(++counter, usableServiceChargeMap, refundCalculateBO.getSupplierId(), supplierServiceChargeRefund);
        tryDeductServiceChargeRefund(++counter, usableServiceChargeMap, refundCalculateBO.getTenantId(), tenantServiceChargeRefund);

        usableServiceChargeSnapshotMap.forEach((k, v) -> {
            BigDecimal refundPrice = usableServiceChargeMap.getOrDefault(k, BigDecimal.ZERO);
            // 使用merge方法将v和refundPrice相减的结果存入accountDimensionMap
            usableServiceChargeSnapshotMap.merge(k, refundPrice, BigDecimal::subtract);
        });
        log.info("本次售后:{}分账回退，各账户退款手续费：{}", refundCalculateBO.getOrderAfterSaleId(), JSON.toJSONString(usableServiceChargeSnapshotMap));
        return usableServiceChargeSnapshotMap;
    }

    private void tryDeductServiceChargeRefund(Integer counter, TreeMap<Long, BigDecimal> usableServiceChargeMap, Long account, BigDecimal amount) {
        if (counter > 10) {
            // 这里是防止递归调用过深，其实最多就3次
            throw new ProviderException("本次交易确认退款，退款手续费不足，请关注");
        }
        BigDecimal remainingAmount = usableServiceChargeMap.getOrDefault(account, BigDecimal.ZERO).subtract(amount);
        usableServiceChargeMap.put(account, remainingAmount.max(BigDecimal.ZERO));

        if (remainingAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 如果该账户资金不足，由下一个账户垫付
            Long nextAccount = usableServiceChargeMap.higherKey(account);
            if (nextAccount == null) {
                nextAccount = usableServiceChargeMap.firstKey();
            }
            tryDeductServiceChargeRefund(++counter, usableServiceChargeMap, nextAccount, remainingAmount.abs());
        }
    }

    /**
     * 获取分账维度的各账户可返还的手续费
     *
     * @param refundCalculateBO
     * @return
     */
    private TreeMap<Long, BigDecimal> getUsableServiceCharge(RefundCalculateBO refundCalculateBO) {
        // 分账维度的手续费金额
        List<BillProfitSharingSnapshot> snapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        Map<Long, BigDecimal> accountDimensionMap = snapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingSnapshot::getProfitSharingPrice, BigDecimal::add)));
        accountDimensionMap.replaceAll((key, value) -> value.abs());

        // 历史退款手续费金额
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateBO.getBillProfitSharingRefundSnapshots();
        Map<Long, BigDecimal> accountDimensionRefundMap = billProfitSharingRefundSnapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getActualRefundPrice()))
                .collect(Collectors.groupingBy(BillProfitSharingRefundSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingRefundSnapshot::getActualRefundPrice, BigDecimal::add)));
        accountDimensionMap.forEach((k, v) -> {
            BigDecimal refundPrice = accountDimensionRefundMap.getOrDefault(k, BigDecimal.ZERO);
            // 使用merge方法将v和refundPrice相减的结果存入accountDimensionMap
            accountDimensionMap.merge(k, refundPrice, BigDecimal::subtract);
        });
        // 排好序返回
        return new TreeMap<>(accountDimensionMap);
    }

    /**
     * 获取本次返还的手续费
     * @param refundCalculateBO
     * @return
     */
    private BigDecimal getThisOneRefundServiceCharge(RefundCalculateBO refundCalculateBO) {
        // 1、分账维度的交易确认、交易确认退款情况，判断是否是最后一次进行到减
        Pair<Boolean, BigDecimal> isLastProfitSharingRefundPair = judgeIsLastProfitSharingRefund(refundCalculateBO);
        Boolean lastProfitSharingFlag = isLastProfitSharingRefundPair.getKey();
        if (lastProfitSharingFlag) {
            return isLastProfitSharingRefundPair.getValue();
        }

        // 2、智付四舍五入 汇付向下取整
        PaymentDTO paymentDTO = refundCalculateBO.getPaymentDTO();
        Integer onlinePayChannel = paymentDTO.getOnlinePayChannel();
        BigDecimal payTotalPrice = paymentDTO.getTotalPrice();
        BigDecimal payServiceChargeRate = paymentDTO.getFeeRate();
        BigDecimal totalRefundPrice = refundCalculateBO.getAfterSaleTotalPrice();
        BigDecimal totalServiceCharge = NumberUtil.mul(payTotalPrice, NumberUtil.div(payServiceChargeRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        int roundingMode = getRoundingModeByChannel(onlinePayChannel);
        return NumberUtil.mul(totalServiceCharge, NumberUtil.div(totalRefundPrice, payTotalPrice)).setScale(NumberConstant.TWO, roundingMode);
    }

    /**
     * 智付四舍五入
     * 汇付向下取整
     *
     * @param onlinePayChannel
     * @return
     */
    private int getRoundingModeByChannel(Integer onlinePayChannel) {
        if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.DIN_PAY.getChannel())) {
            return ROUND_HALF_UP;
        }
        return ROUND_DOWN;
    }

    /**
     * 分账维度的交易确认、交易确认退款情况，判断是否是最后一次进行到减
     *
     * @param refundCalculateBO
     * @return
     */
    private Pair<Boolean, BigDecimal> judgeIsLastProfitSharingRefund(RefundCalculateBO refundCalculateBO) {
        List<BillProfitSharingSnapshot> profitSharingSnapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        List<BillProfitSharingRefundSnapshot> profitSharingRefundSnapshots = refundCalculateBO.getBillProfitSharingRefundSnapshots();

        BigDecimal totalProfitSharingPrice = calculateTotalProfitSharingPrice(profitSharingSnapshots);
        BigDecimal totalProfitSharingRefundPrice = calculateTotalProfitSharingRefundPrice(profitSharingRefundSnapshots);
        totalProfitSharingRefundPrice = NumberUtil.add(totalProfitSharingRefundPrice, refundCalculateBO.getAfterSaleTotalPrice());

        if (totalProfitSharingRefundPrice.compareTo(totalProfitSharingPrice) >= 0) {
            BigDecimal totalServiceChargeProfitSharing = calculateServiceChargeProfitSharing(profitSharingSnapshots);
            BigDecimal totalServiceChargeProfitSharingRefund = calculateServiceChargeProfitSharingRefund(profitSharingRefundSnapshots);

            return Pair.of(Boolean.TRUE, totalServiceChargeProfitSharing.subtract(totalServiceChargeProfitSharingRefund));
        }

        return Pair.of(Boolean.FALSE, null);
    }

    private List<BillProfitSharingSnapshot> getProfitSharingSnapshots(Long tenantId, List<Long> orderIds) {
        return billProfitSharingSnapshotMapper.queryByTenantIdAndOrderIds(tenantId, orderIds);
    }

    private List<BillProfitSharingRefundSnapshot> getProfitSharingRefundSnapshots(Long tenantId, List<Long> orderIds) {
        return billProfitSharingRefundSnapshotRepository.queryByTenantAndOrderIds(tenantId, orderIds);
    }

    private BigDecimal calculateTotalProfitSharingPrice(List<BillProfitSharingSnapshot> snapshots) {
        BigDecimal totalProfitSharingPrice = snapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .map(BillProfitSharingSnapshot::getProfitSharingPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 分账给个人就已经包含代仓了
        boolean personalFlag = snapshots.stream().anyMatch(el -> Objects.equals(el.getType(), BillProfitSharingSnapshotTypeEnum.ALL.getCode()));
        if (personalFlag) {
            return totalProfitSharingPrice;
        }
        Long orderId = snapshots.get(0).getOrderId();
        Long tenantId = snapshots.get(0).getTenantId();
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper.queryByTenantIdAndOrderIds(tenantId, Lists.newArrayList(orderId));
        BigDecimal agentWarehousePrice = orderAgentSkuFeeRuleSnapshots.stream()
                .filter(el -> Objects.nonNull(el.getPrice()))
                .map(OrderAgentSkuFeeRuleSnapshot::getPrice)
                .reduce(ZERO, BigDecimal::add);
        return NumberUtil.add(totalProfitSharingPrice, agentWarehousePrice);
    }

    private BigDecimal calculateTotalProfitSharingRefundPrice(List<BillProfitSharingRefundSnapshot> snapshots) {
        return snapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getActualRefundPrice()))
                .map(BillProfitSharingRefundSnapshot::getActualRefundPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateServiceChargeProfitSharing(List<BillProfitSharingSnapshot> snapshots) {
        return snapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .map(BillProfitSharingSnapshot::getProfitSharingPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .negate();
    }

    private BigDecimal calculateServiceChargeProfitSharingRefund(List<BillProfitSharingRefundSnapshot> snapshots) {
        return snapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getActualRefundPrice()))
                .map(BillProfitSharingRefundSnapshot::getActualRefundPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    /**
     * 填充退款快照
     *
     * @param refundCalculateResultBO 退款计算结果波
     */
    private void populateRefundSnapshot(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO, Integer profitSharingType) {
        // 动态获取账户ID
        Pair<Long, Long> accountIds = getDynamicAccountIdsByBO(refundCalculateBO);
        Long ftAccountId = accountIds.getKey();
        Long xmAccountId = accountIds.getValue();

        // 租户退款快照
        populateTenantRefundSnapshot(refundCalculateBO, refundCalculateResultBO, profitSharingType);
        // 帆台退款快照
        populateFtRefundSnapshot(refundCalculateBO, refundCalculateResultBO, profitSharingType, ftAccountId);
        // 供应商退款快照
        populateSupplierRefundSnapshot(refundCalculateBO, refundCalculateResultBO, profitSharingType, xmAccountId);
    }

    /**
     * 填充供应商退款快照
     */
    private void populateSupplierRefundSnapshot(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO, Integer profitSharingType, Long supplierAccountId) {
        BillProfitSharingRefundSnapshot supplierBillProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        supplierBillProfitSharingRefundSnapshot.setTenantId(refundCalculateBO.getTenantId());
        supplierBillProfitSharingRefundSnapshot.setOrderId(refundCalculateBO.getOrderId());
        supplierBillProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        supplierBillProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        supplierBillProfitSharingRefundSnapshot.setAccountId(supplierAccountId);
        supplierBillProfitSharingRefundSnapshot.setProfitSharingType(profitSharingType);
        supplierBillProfitSharingRefundSnapshot.setOriginPrice(refundCalculateResultBO.getOriginPrice());
        supplierBillProfitSharingRefundSnapshot.setRefundPrice(refundCalculateResultBO.getSupplierRefundPrice());
        supplierBillProfitSharingRefundSnapshot.setShouldRefundPrice(refundCalculateResultBO.getSupplierShouldRefundPrice());
        supplierBillProfitSharingRefundSnapshot.setActualRefundPrice(refundCalculateResultBO.getSupplierActualRefundPrice());
        supplierBillProfitSharingRefundSnapshot.setFinalRefundPrice(refundCalculateResultBO.getSupplierActualRefundPrice());
        supplierBillProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());
        ArrayList<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateResultBO.getBillProfitSharingRefundSnapshots();
        billProfitSharingRefundSnapshots.add(supplierBillProfitSharingRefundSnapshot);
    }

    /**
     * 填充帆台退款快照
     */
    private void populateFtRefundSnapshot(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO, Integer profitSharingType, Long ftAccountId) {
        BillProfitSharingRefundSnapshot ftBillProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        ftBillProfitSharingRefundSnapshot.setTenantId(refundCalculateBO.getTenantId());
        ftBillProfitSharingRefundSnapshot.setOrderId(refundCalculateBO.getOrderId());
        ftBillProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        ftBillProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        ftBillProfitSharingRefundSnapshot.setAccountId(ftAccountId);
        ftBillProfitSharingRefundSnapshot.setProfitSharingType(profitSharingType);
        ftBillProfitSharingRefundSnapshot.setOriginPrice(refundCalculateResultBO.getOriginPrice());
        ftBillProfitSharingRefundSnapshot.setRefundPrice(refundCalculateResultBO.getFtRefundPrice());
        ftBillProfitSharingRefundSnapshot.setShouldRefundPrice(refundCalculateResultBO.getFtShouldRefundPrice());
        ftBillProfitSharingRefundSnapshot.setActualRefundPrice(refundCalculateResultBO.getFtActualRefundPrice());
        ftBillProfitSharingRefundSnapshot.setFinalRefundPrice(refundCalculateResultBO.getFtActualRefundPrice());
        ftBillProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());
        ArrayList<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateResultBO.getBillProfitSharingRefundSnapshots();
        billProfitSharingRefundSnapshots.add(ftBillProfitSharingRefundSnapshot);
    }

    /**
     * 填充租户退款快照
     */
    private void populateTenantRefundSnapshot(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO, Integer profitSharingType) {
        Long tenantId = refundCalculateBO.getTenantId();
        BillProfitSharingRefundSnapshot tenantBillProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        tenantBillProfitSharingRefundSnapshot.setTenantId(refundCalculateBO.getTenantId());
        tenantBillProfitSharingRefundSnapshot.setOrderId(refundCalculateBO.getOrderId());
        tenantBillProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        tenantBillProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        tenantBillProfitSharingRefundSnapshot.setAccountId(tenantId);
        tenantBillProfitSharingRefundSnapshot.setProfitSharingType(profitSharingType);
        tenantBillProfitSharingRefundSnapshot.setOriginPrice(refundCalculateResultBO.getOriginPrice());
        tenantBillProfitSharingRefundSnapshot.setRefundPrice(refundCalculateResultBO.getTenantRefundPrice());
        tenantBillProfitSharingRefundSnapshot.setShouldRefundPrice(refundCalculateResultBO.getTenantShouldRefundPrice());
        tenantBillProfitSharingRefundSnapshot.setActualRefundPrice(refundCalculateResultBO.getTenantActualRefundPrice());
        tenantBillProfitSharingRefundSnapshot.setFinalRefundPrice(refundCalculateResultBO.getTenantActualRefundPrice());
        tenantBillProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());
        ArrayList<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateResultBO.getBillProfitSharingRefundSnapshots();
        billProfitSharingRefundSnapshots.add(tenantBillProfitSharingRefundSnapshot);
    }

    /**
     * 计算代仓订单退款
     *
     * @param refundCalculateBO 退款计算业务对象
     */
    private RefundCalculateResultBO calculateAgentWarehouseOrderRefund(RefundCalculateBO refundCalculateBO) {
        BigDecimal itemRefundFee = refundCalculateBO.getItemRefundFee();
        Long tenantId = refundCalculateBO.getTenantId();
        Boolean lastOrderAfterSaleFlag = refundCalculateBO.getLastOrderAfterSaleFlag();

        // 最后一次售后倒减
        BigDecimal xmRefundPrice = BigDecimal.ZERO;
        if (lastOrderAfterSaleFlag) {
            OrderItemFeeTransactionQueryDTO query = OrderItemFeeTransactionQueryDTO.builder().orderItemId(refundCalculateBO.getOrderItemId()).feeType(OrderItemFeeEnum.FeeType.AGENT_FEE.getCode()).transactionType(OrderItemFeeEnum.TransactionType.PAY.getCode()).build();
            List<OrderItemFeeTransaction> orderItemFeeTransactions = orderItemFeeTransactionService.queryByCondition(query);
            if (!CollectionUtils.isEmpty(orderItemFeeTransactions)) {
                OrderItemFeeTransaction orderItemFeeTransaction = orderItemFeeTransactions.get(0);
                BigDecimal agentWarehouseFee = orderItemFeeTransaction.getFee();
                BigDecimal historyAgentWarehouseRefundFee = getHistoryRefundPrice(refundCalculateBO, ProfitSharingRefundRuleTypeEnum.AGENT_WAREHOUSE.getCode(), BillProfitSharingAccountIdEnum.SUPPLIER.getId());
                xmRefundPrice = NumberUtil.sub(agentWarehouseFee, historyAgentWarehouseRefundFee);
            }
        } else {
            // 本次售后产生的费用
            OrderAfterSaleResp orderAfterSaleResultDTO = orderAfterSaleService.queryById(refundCalculateBO.getOrderAfterSaleId());
            xmRefundPrice = orderAgentSkuFeeRuleService.calculateAgentFeeByOrder(orderAfterSaleResultDTO);
        }
        BigDecimal tenantRefundPrice = NumberUtil.sub(itemRefundFee, xmRefundPrice);

        // 应退
        BigDecimal xmShouldRefundPrice = xmRefundPrice;
        BigDecimal tenantShouldRefundPrice = tenantRefundPrice;
        if (!Objects.equals(refundCalculateBO.getResponsibilityType(), ResponsibilityTypeEnum.SUPPLIER.getType())) {
            xmShouldRefundPrice = BigDecimal.ZERO;
            tenantShouldRefundPrice = NumberUtil.add(tenantShouldRefundPrice, xmRefundPrice);
        }

        //供应商需实退款金额 = 供应商应退 > 退款金额 ？ 退款金额 ：供应商应退
        xmShouldRefundPrice = xmShouldRefundPrice.compareTo(itemRefundFee) >= NumberConstant.ZERO ? itemRefundFee : xmShouldRefundPrice;
        // 租户应退金额 = 租户应退金额 < 0 ? 0 : 租户应退金额
        tenantShouldRefundPrice = tenantShouldRefundPrice.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : tenantShouldRefundPrice;

        RefundCalculateResultBO refundCalculateResultBO = RefundCalculateResultBO.builder()
                .tenantId(tenantId)
                .orderId(refundCalculateBO.getOrderId())
                .orderAfterSaleId(refundCalculateBO.getOrderAfterSaleId())
                .originPrice(itemRefundFee)
                .supplierRefundPrice(xmRefundPrice).supplierShouldRefundPrice(xmShouldRefundPrice)
                .tenantRefundPrice(tenantRefundPrice).tenantShouldRefundPrice(tenantShouldRefundPrice)
                .ftRefundPrice(BigDecimal.ZERO).ftShouldRefundPrice(BigDecimal.ZERO)
                .billProfitSharingRefundSnapshots(new ArrayList<>())
                .build();

        // 1、运费退款
        calculateThreePartiesOrderDeliveryFeeRefund(refundCalculateBO, refundCalculateResultBO);

        // 2、处理金额不足的情况(倒挂)
        processPriceInsufficient(refundCalculateBO, refundCalculateResultBO);

        // 3、处理手续费
        calculateServiceChargeRefund(refundCalculateBO, refundCalculateResultBO);

        // 4、填充代仓退款快照
        populateRefundSnapshot(refundCalculateBO, refundCalculateResultBO, ProfitSharingRefundRuleTypeEnum.AGENT_WAREHOUSE.getCode());

        return refundCalculateResultBO;
    }


    /**
     * 计算自营仓订单退款
     *
     * @param refundCalculateBO 退款计算业务对象
     */
    private RefundCalculateResultBO calculateProprietaryOrderRefund(RefundCalculateBO refundCalculateBO) {
        BigDecimal itemRefundFee = refundCalculateBO.getItemRefundFee();
        // 订单维度的扣减
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        Map<Long, BigDecimal> accountMap = billProfitSharingSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode())
                        && !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.DELIVERY.getCode()))
                .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingSnapshot::getProfitSharingPrice, BigDecimal::add)));

        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = refundCalculateBO.getBillProfitSharingRefundSnapshots();
        Map<Long, BigDecimal> accountRefundMap = billProfitSharingRefundSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRefundRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .collect(Collectors.groupingBy(BillProfitSharingRefundSnapshot::getAccountId, Collectors.reducing(BigDecimal.ZERO, BillProfitSharingRefundSnapshot::getActualRefundPrice, BigDecimal::add)));

        // 动态获取账户ID
        Pair<Long, Long> accountIds = getDynamicAccountIdsByBO(refundCalculateBO);
        Long ftAccountId = accountIds.getKey();
        Long xmAccountId = accountIds.getValue();
        refundCalculateBO.setSupplierId(xmAccountId);

        BigDecimal totalSharingPrice = accountMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalRefundSharingPrice = accountRefundMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal thisOneSharingPrice = NumberUtil.add(totalRefundSharingPrice, itemRefundFee);

        BigDecimal ftItemRefundPrice;
        BigDecimal xmItemRefundPrice;
        BigDecimal tenantItemRefundPrice;
        if (thisOneSharingPrice.compareTo(totalSharingPrice) == 0) {
            // 到减
            tenantItemRefundPrice = NumberUtil.sub(accountMap.get(refundCalculateBO.getTenantId()), accountRefundMap.get(refundCalculateBO.getTenantId()));
            xmItemRefundPrice = NumberUtil.sub(accountMap.get(xmAccountId), accountRefundMap.get(xmAccountId));
            ftItemRefundPrice = NumberUtil.sub(accountMap.get(ftAccountId), accountRefundMap.get(ftAccountId));
        } else {

            // 1、获取分摊比例
            Pair<BigDecimal, BigDecimal> ratePair = getRate(refundCalculateBO, ftAccountId, xmAccountId, ProfitSharingRuleTypeEnum.PROPRIETARY_SKU.getCode());
            BigDecimal xmRate = ratePair.getKey();
            BigDecimal ftRate = ratePair.getValue();

            // 如果是最后一笔售后 需要倒减
            Boolean lastOrderAfterSaleFlag = refundCalculateBO.getLastOrderAfterSaleFlag();
            if (lastOrderAfterSaleFlag) {
                Long orderItemId = refundCalculateBO.getOrderItemId();
                OrderItem orderItem = orderItemService.queryById(orderItemId);
                BigDecimal totalPrice = orderItem.getTotalPrice();
                BigDecimal ftTotalPrice = NumberUtil.mul(totalPrice, NumberUtil.div(ftRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                BigDecimal xmTotalPrice = NumberUtil.mul(totalPrice, NumberUtil.div(xmRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                // 历史的退款金额
                BigDecimal ftHistoryRefundPrice = getHistoryRefundPrice(refundCalculateBO, ProfitSharingRefundRuleTypeEnum.PROPRIETARY_SKU.getCode(), ftAccountId);
                BigDecimal xmHistoryRefundPrice = getHistoryRefundPrice(refundCalculateBO, ProfitSharingRefundRuleTypeEnum.PROPRIETARY_SKU.getCode(), xmAccountId);
                ftItemRefundPrice = NumberUtil.sub(ftTotalPrice, ftHistoryRefundPrice);
                xmItemRefundPrice = NumberUtil.sub(xmTotalPrice, xmHistoryRefundPrice);
            } else {
                ftItemRefundPrice = NumberUtil.mul(itemRefundFee, NumberUtil.div(ftRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                xmItemRefundPrice = NumberUtil.mul(itemRefundFee, NumberUtil.div(xmRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            }
            // 2、计算商品退款金额
            tenantItemRefundPrice = NumberUtil.sub(itemRefundFee, ftItemRefundPrice, xmItemRefundPrice);
        }

        // 3、自营场景下退款不定责
        RefundCalculateResultBO refundCalculateResultBO = RefundCalculateResultBO.builder()
                .tenantId(refundCalculateBO.getTenantId())
                .orderId(refundCalculateBO.getOrderId())
                .orderAfterSaleId(refundCalculateBO.getOrderAfterSaleId())
                .originPrice(itemRefundFee)
                .ftRefundPrice(ftItemRefundPrice).ftShouldRefundPrice(ftItemRefundPrice).ftActualRefundPrice(ftItemRefundPrice)
                .supplierRefundPrice(xmItemRefundPrice).supplierShouldRefundPrice(xmItemRefundPrice).supplierActualRefundPrice(xmItemRefundPrice)
                .tenantRefundPrice(tenantItemRefundPrice).tenantShouldRefundPrice(tenantItemRefundPrice).tenantActualRefundPrice(tenantItemRefundPrice)
                .billProfitSharingRefundSnapshots(new ArrayList<>())
                .build();

        // 运费退款
        calculateProprietaryDeliveryRefund(refundCalculateBO, refundCalculateResultBO);

        // 4、计算手续费
        calculateServiceChargeRefund(refundCalculateBO, refundCalculateResultBO);

        // 5、留存退款快照
        populateRefundSnapshot(refundCalculateBO, refundCalculateResultBO, ProfitSharingRefundRuleTypeEnum.PROPRIETARY_SKU.getCode());
        return refundCalculateResultBO;
    }

    private void calculateProprietaryDeliveryRefund(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO) {
        Pair<Long, Long> accountIds = getDynamicAccountIdsByBO(refundCalculateBO);
        Long ftAccountId = accountIds.getKey();
        Long xmAccountId = accountIds.getValue();
        Pair<BigDecimal, BigDecimal> deliveryRatePair = getRate(refundCalculateBO, ftAccountId, xmAccountId, ProfitSharingRuleTypeEnum.DELIVERY.getCode());
        BigDecimal supplierRate = deliveryRatePair.getKey();
        BigDecimal ftRate = deliveryRatePair.getValue();
        BigDecimal deliveryRefundFee = refundCalculateBO.getDeliveryRefundFee();
        BigDecimal supplierDeliveryRefundFee = NumberUtil.mul(deliveryRefundFee, NumberUtil.div(supplierRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
        BigDecimal ftDeliveryRefundFee = NumberUtil.mul(deliveryRefundFee, NumberUtil.div(ftRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
        BigDecimal tenantDeliveryRefundFee = NumberUtil.sub(deliveryRefundFee, supplierDeliveryRefundFee, ftDeliveryRefundFee);
        refundCalculateResultBO.setSupplierDeliveryRefundFee(supplierDeliveryRefundFee);
        refundCalculateResultBO.setFtDeliveryRefundFee(ftDeliveryRefundFee);
        refundCalculateResultBO.setTenantDeliveryRefundFee(tenantDeliveryRefundFee);

        // 退款记录留存
        Long tenantId = refundCalculateBO.getTenantId();
        populateDeliveryRefundSnapshot(refundCalculateBO, refundCalculateResultBO, tenantId, tenantDeliveryRefundFee);
        populateDeliveryRefundSnapshot(refundCalculateBO, refundCalculateResultBO, xmAccountId, supplierDeliveryRefundFee);
        populateDeliveryRefundSnapshot(refundCalculateBO, refundCalculateResultBO, ftAccountId, ftDeliveryRefundFee);
    }

    public void populateDeliveryRefundSnapshot(RefundCalculateBO refundCalculateBO, RefundCalculateResultBO refundCalculateResultBO, Long accountId, BigDecimal deliveryRefundFee) {
        Long tenantId = refundCalculateBO.getTenantId();
        Long orderId = refundCalculateBO.getOrderId();
        BillProfitSharingRefundSnapshot billProfitSharingRefundSnapshot = new BillProfitSharingRefundSnapshot();
        billProfitSharingRefundSnapshot.setTenantId(tenantId);
        billProfitSharingRefundSnapshot.setOrderId(orderId);
        billProfitSharingRefundSnapshot.setOrderAfterSaleId(refundCalculateBO.getOrderAfterSaleId());
        billProfitSharingRefundSnapshot.setOrderItemId(refundCalculateBO.getOrderItemId());
        billProfitSharingRefundSnapshot.setProfitSharingType(ProfitSharingRefundRuleTypeEnum.DELIVERY.getCode());
        billProfitSharingRefundSnapshot.setAccountId(accountId);
        billProfitSharingRefundSnapshot.setOriginPrice(deliveryRefundFee);
        billProfitSharingRefundSnapshot.setRefundPrice(deliveryRefundFee);
        billProfitSharingRefundSnapshot.setShouldRefundPrice(deliveryRefundFee);
        billProfitSharingRefundSnapshot.setActualRefundPrice(deliveryRefundFee);
        billProfitSharingRefundSnapshot.setFinalRefundPrice(deliveryRefundFee);
        billProfitSharingRefundSnapshot.setOrgProfitSharingNo(refundCalculateBO.getOrgProfitSharingNo());
        refundCalculateResultBO.getBillProfitSharingRefundSnapshots().add(billProfitSharingRefundSnapshot);
        log.info("添加运费回退记录:{}", JSON.toJSONString(billProfitSharingRefundSnapshot));
    }

    /**
     * 获取分账快照中的各方比例
     */
    private Pair<BigDecimal, BigDecimal> getRate(RefundCalculateBO refundCalculateBO, Long ftAccountId, Long xmAccountId, Integer type) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = refundCalculateBO.getBillProfitSharingSnapshots();
        Map<Long, List<BillProfitSharingSnapshot>> billProfitSharingSnapshotMap = billProfitSharingSnapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), type))
                .collect(Collectors.groupingBy(BillProfitSharingSnapshot::getAccountId));
        List<BillProfitSharingSnapshot> xmBillProfitSharingSnapshots = billProfitSharingSnapshotMap.get(xmAccountId);
        List<BillProfitSharingSnapshot> ftBillProfitSharingSnapshots = billProfitSharingSnapshotMap.get(ftAccountId);
        if (CollectionUtils.isEmpty(xmBillProfitSharingSnapshots) || CollectionUtils.isEmpty(ftBillProfitSharingSnapshots)) {
            throw new ProviderException("非法的分账数据");
        }
        return Pair.of(xmBillProfitSharingSnapshots.get(0).getNumber(), ftBillProfitSharingSnapshots.get(0).getNumber());
    }

    /**
     * 从accountMap动态获取FT和供应商的账户ID
     * 规则：按accountId从小到大排序，第一个是FT，第二个是供应商
     */
    private Pair<Long, Long> getDynamicAccountIds(Map<Long, BigDecimal> accountMap, Long tenantId) {
        List<Long> sortedAccountIds = accountMap.keySet().stream()
                .filter(accountId -> !accountId.equals(tenantId))
                .sorted()
                .collect(Collectors.toList());

        if (sortedAccountIds.size() < 2) {
            return Pair.of(BillProfitSharingAccountIdEnum.FT.getId(), BillProfitSharingAccountIdEnum.SUPPLIER.getId());
        }

        Long ftAccountId = sortedAccountIds.get(0);
        Long supplierAccountId = sortedAccountIds.get(1);

        log.debug("动态映射账户ID - FT: {}, 供应商: {}, 租户: {}", ftAccountId, supplierAccountId, tenantId);

        return Pair.of(ftAccountId, supplierAccountId);
    }

    @Override
    public void updateBySelective(BillProfitSharingRefundSnapshot billProfitSharingRefundSnapshot) {
        billProfitSharingRefundSnapshotRepository.updateById(billProfitSharingRefundSnapshot);
    }
}
