package com.cosfo.mall.bill.service.impl;

import com.cosfo.mall.bill.convert.BillInvertedConvert;
import com.cosfo.mall.bill.dao.BillInvertedDao;
import com.cosfo.mall.bill.model.dto.BillInvertedDTO;
import com.cosfo.mall.bill.model.po.BillInverted;
import com.cosfo.mall.bill.service.BillInvertedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/24
 */
@Slf4j
@Service
public class BillInvertedServiceImpl implements BillInvertedService {
    @Resource
    private BillInvertedDao billInvertedDao;

    @Override
    public boolean save(List<BillInvertedDTO> billInvertedDTOList) {
        if(CollectionUtils.isEmpty(billInvertedDTOList)){
            return true;
        }

        List<BillInverted> billInverteds = BillInvertedConvert.INSTANCE.toBillInvertedList(billInvertedDTOList);
        billInvertedDao.saveBatch(billInverteds);
        return true;
    }
}
