package com.cosfo.mall.bill.service;

import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/24
 */
public interface BillProfitSharingOrderService {

    /**
     * 查询等待分账订单
     *
     * @return
     */
    List<BillProfitSharingOrderDTO> queryWaitingProfitSharingOrder(Integer maxRetryNum);


    /**
     * 更新分账订单状态
     *
     * @param tenantId
     * @param orderId
     * @param status
     */
    int updateBillProfitSharingOrderStatus(Long tenantId, Long orderId, Integer status, Integer originStatus);

    /**
     * 保存
     *
     * @param billProfitSharingOrder
     */
    void save(BillProfitSharingOrder billProfitSharingOrder);

    /**
     * 查询分账单
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<BillProfitSharingOrderDTO> queryByOrderIds(List<Long> orderIds, Long tenantId);


    /**
     * 重置状态和自增次数
     *
     * @param id
     * @return
     */
    int resetStatusAndIncRetryNum(Long id);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    BillProfitSharingOrder queryById(Long id);

    /**
     * 根据分账单号查询
     *
     * @param profitSharingNo
     * @return
     */
    BillProfitSharingOrder queryByProfitSharingNo(String profitSharingNo);

    int updateStatusById(Long id, Integer finalStatus, Integer orgStatus);

    /**
     * 获取分账订单信息
     *
     * @param tenantId
     * @param orderId
     * @param orderItemId
     * @return
     */
    BillProfitSharingOrder getBillProfitSharingOrder(Long tenantId, Long orderId, Long orderItemId);

    /**
     * 检查确认分账
     */
    void confirmProfitSharingTask();

    List<BillProfitSharingOrder> queryByTenantAndOrderId(Long tenantId, Long orderId);

    /**
     * 更新分账单号
     *
     * @param id
     * @param profitSharingNo
     * @return
     */
    int updateProfitSharingNo(Long id, String profitSharingNo);
}
