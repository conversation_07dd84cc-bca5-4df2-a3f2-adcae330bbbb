package com.cosfo.mall.bill.service.impl;

import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.service.BillProfitSharingSnapshotService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-01-15
 **/
@Service
public class BillProfitSharingSnapshotServiceImpl implements BillProfitSharingSnapshotService {

    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;

    @Override
    public List<BillProfitSharingSnapshot> queryByTenantIdAndOrderId(Long tenantId, Long orderId) {
        return billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
    }
}
