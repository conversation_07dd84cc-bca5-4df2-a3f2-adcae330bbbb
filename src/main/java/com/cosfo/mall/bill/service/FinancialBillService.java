package com.cosfo.mall.bill.service;

import com.cosfo.mall.bill.model.dto.BillCredentialsDTO;
import com.cosfo.mall.bill.model.dto.FinancialBillDTO;
import com.cosfo.mall.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.mall.bill.model.po.FinancialBill;
import com.cosfo.mall.bill.model.vo.FinancialBillVO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
public interface FinancialBillService {

    /**
     * 查询账单信息
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    List<FinancialBillDTO> queryFinancialBill(Long storeId, Long tenantId);

    /**
     * 查询账期规则
     *
     * @param tenantId
     * @return
     */
    FinancialBillRuleDTO queryBillRule(Long tenantId);

    /**
     * 分页查询
     *
     * @param pageSize
     * @param pageIndex
     * @param status
     * @return
     */
    ResultDTO<PageInfo<FinancialBillVO>> list(Integer pageSize, Integer pageIndex, Integer status, String year, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询年度总金额
     *
     * @param status
     * @param year
     * @return
     */
    ResultDTO<BigDecimal> getYearReceivablePrice(Integer status, String year, LoginContextInfoDTO contextInfoDTO);

    /**
     * 账单详情
     *
     * @param billId
     * @return
     */
    ResultDTO<FinancialBillVO> detail(Long billId, LoginContextInfoDTO contextInfoDTO);

    /**
     * 上传证书
     *
     * @param billCredentialsDTO
     * @param contextInfoDTO
     * @return
     */
    ResultDTO uploadCredentials(BillCredentialsDTO billCredentialsDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 查询时候有待支付订单
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    List<FinancialBill> queryNeedPayBill(Long storeId, Long tenantId);
}
