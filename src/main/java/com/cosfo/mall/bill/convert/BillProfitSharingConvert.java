package com.cosfo.mall.bill.convert;

import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/20
 */
@Mapper
public interface BillProfitSharingConvert {
    BillProfitSharingConvert INSTANCE = Mappers.getMapper(BillProfitSharingConvert.class);

    /**
     * 转化为BillProfitSharingDTO
     *
     * @param billProfitSharingList
     * @return
     */
    List<BillProfitSharingDTO> toBillProfitSharingDTO(List<BillProfitSharing> billProfitSharingList);
}
