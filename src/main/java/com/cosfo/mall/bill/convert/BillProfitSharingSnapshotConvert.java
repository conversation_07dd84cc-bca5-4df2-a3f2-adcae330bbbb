package com.cosfo.mall.bill.convert;

import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-01-09
 **/
public class BillProfitSharingSnapshotConvert {

    public static final List<BillProfitSharingSnapshotDTO> toDTOList(List<BillProfitSharingSnapshot> billProfitSharingSnapshots) {
        if (billProfitSharingSnapshots == null) {
            return null;
        }
        List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDTOList = new ArrayList<>();
        for (BillProfitSharingSnapshot billProfitSharingSnapshot : billProfitSharingSnapshots) {
            billProfitSharingSnapshotDTOList.add(toDTO(billProfitSharingSnapshot));
        }
        return billProfitSharingSnapshotDTOList;
    }

    public static final BillProfitSharingSnapshotDTO toDTO(BillProfitSharingSnapshot billProfitSharingSnapshot) {

        if (billProfitSharingSnapshot == null) {
            return null;
        }
        BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = new BillProfitSharingSnapshotDTO();
        billProfitSharingSnapshotDTO.setId(billProfitSharingSnapshot.getId());
        billProfitSharingSnapshotDTO.setTenantId(billProfitSharingSnapshot.getTenantId());
        billProfitSharingSnapshotDTO.setOrderId(billProfitSharingSnapshot.getOrderId());
        billProfitSharingSnapshotDTO.setAccountId(billProfitSharingSnapshot.getAccountId());
        billProfitSharingSnapshotDTO.setDeliveryType(billProfitSharingSnapshot.getDeliveryType());
        billProfitSharingSnapshotDTO.setProfitSharingType(billProfitSharingSnapshot.getProfitSharingType());
        billProfitSharingSnapshotDTO.setMappingType(billProfitSharingSnapshot.getMappingType());
        billProfitSharingSnapshotDTO.setNumber(billProfitSharingSnapshot.getNumber());
        billProfitSharingSnapshotDTO.setOriginPrice(billProfitSharingSnapshot.getOriginPrice());
        billProfitSharingSnapshotDTO.setProfitSharingPrice(billProfitSharingSnapshot.getProfitSharingPrice());
        billProfitSharingSnapshotDTO.setType(billProfitSharingSnapshot.getType());
        billProfitSharingSnapshotDTO.setProfitSharingNo(billProfitSharingSnapshot.getProfitSharingNo());
        billProfitSharingSnapshotDTO.setAccountType(billProfitSharingSnapshot.getAccountType());
        return billProfitSharingSnapshotDTO;
    }

}
