package com.cosfo.mall.bill.convert;

import com.cosfo.mall.bill.model.dto.BillInvertedDTO;
import com.cosfo.mall.bill.model.po.BillInverted;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/24
 */
@Mapper
public interface BillInvertedConvert {

    BillInvertedConvert INSTANCE = Mappers.getMapper(BillInvertedConvert.class);

    /**
     * 转化为BillInvertedList
     *
     * @param billInvertedDTOS
     * @return
     */
    List<BillInverted> toBillInvertedList(List<BillInvertedDTO> billInvertedDTOS);
}
