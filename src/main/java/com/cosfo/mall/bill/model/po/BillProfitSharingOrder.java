package com.cosfo.mall.bill.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * bill_profit_sharing_order
 * <AUTHOR>
@Data
public class BillProfitSharingOrder implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 分账状态 0、待分账 1、分账金额计算完毕 2、分账中 3、分账成功 4、初始化
     */
    private Integer status;

    /**
     * 重试次数
     */
    private Integer retryNum;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * @see com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum
     */
    private Integer profitSharingType;

    /**
     * 分账单号
     */
    private String profitSharingNo;

    private static final long serialVersionUID = 1L;
}