package com.cosfo.mall.bill.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述: 倒挂账单表
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/24
 */
@Getter
@Setter
@TableName("bill_inverted")
@NoArgsConstructor
@AllArgsConstructor
public class BillInverted implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 售后Id
     */
    private Long afterSaleId;
    /**
     * 分账接收方租户Id
     */
    private Long acctSplitTenantId;
    /**
     * 应退金额
     */
    private BigDecimal refundableAmount;
    /**
     * 实退金额
     */
    private BigDecimal actualAmount;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
