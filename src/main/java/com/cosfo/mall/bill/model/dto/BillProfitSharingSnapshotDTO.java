package com.cosfo.mall.bill.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Data
public class BillProfitSharingSnapshotDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 账号Id
     */
    private Long accountId;

    /**
     * 账号类型
     *
     * @see com.cosfo.mall.common.context.shard.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 订单配送类型 0自营 1三方
     */
    private Integer deliveryType;

    /**
     * 配送方式
     */
    private String deliveryTypeDesc;

    /**
     * 分账金额类型
     */
    private Integer profitSharingType;

    /**
     * 分账金额类型方式
     */
    private String profitSharingTypeDesc;

    /**
     * 分账方式0按价格1按比例2按分账比例均摊
     */
    private Integer mappingType;

    /**
     * 分账方式0按价格1按比例2按分账比例均摊
     */
    private String mappingTypeDesc;

    /**
     * 对应值
     */
    private BigDecimal number;

    /**
     * 原始金额
     */
    private BigDecimal originPrice;

    /**
     * 分账金额
     */
    private BigDecimal profitSharingPrice;

    /**
     * 0,部分分给品牌方1全部分给品牌方
     */
    private Integer type;

    /**
     * 分账单号
     */
    private String profitSharingNo;
}
