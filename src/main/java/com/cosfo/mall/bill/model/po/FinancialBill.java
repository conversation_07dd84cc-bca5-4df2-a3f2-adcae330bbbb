package com.cosfo.mall.bill.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * financial_bill
 * <AUTHOR>
@Data
public class FinancialBill implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 收款人Id
     */
    private Long payeeId;

    /**
     * 付款人Id
     */
    private Long payerId;

    /**
     * 账期类型0 周账单 1月账单
     */
    private Integer billType;

    /**
     * 订单应收总金额
     */
    private BigDecimal orderReceivablePrice;

    /**
     * 售后单总金额
     */
    private BigDecimal orderAfterSaleTotalPrice;

    /**
     * 应收金额
     */
    private BigDecimal receivablePrice;

    /**
     * 实收金额
     */
    private BigDecimal receivedPrice;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 账单类型 0应收账单 1应付账单
     */
    private Integer type;

    /**
     * 账单状态0未收款1待审核2已还款3线下核对
     */
    private Integer status;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}