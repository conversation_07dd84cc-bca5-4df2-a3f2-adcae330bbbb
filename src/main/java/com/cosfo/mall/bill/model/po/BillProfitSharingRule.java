package com.cosfo.mall.bill.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * bill_profit_sharing_rule
 * <AUTHOR>
@Data
public class BillProfitSharingRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 角色Id
     */
    private Long accountId;

    /**
     * 账户类型
     * @see com.cosfo.mall.common.context.shard.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 配送方式0品牌方1三方仓2无仓
     */
    private Integer deliveryType;

    /**
     * 分账金额类型分账金额类型 1,自营商品金额2，供应商商品金额3，代仓商品金额4运费5订单手续费
     */
    private Integer type;

    /**
     * 分账方式 0, 按价格 1，按比例，2按分账比例均摊
     */
    private Integer mappingType;

    /**
     * 对应值
     */
    private BigDecimal number;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}