package com.cosfo.mall.bill.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * financial_bill_item
 * <AUTHOR>
@Data
public class FinancialBillItem implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 账单Id
     */
    private Long billId;

    /**
     * 业务Id
     */
    private Long businessId;

    /**
     * 业务类型0订单1售后单
     */
    private Byte businessType;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}