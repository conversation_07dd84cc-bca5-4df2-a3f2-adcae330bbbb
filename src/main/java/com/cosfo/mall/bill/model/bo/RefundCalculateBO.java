package com.cosfo.mall.bill.model.bo;

import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2023-07-25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RefundCalculateBO {


    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单明细id
     */
    private Long orderItemId;

    /**
     * 售后单id
     */
    private Long orderAfterSaleId;

    /**
     * 商品金额金额
     */
    private BigDecimal itemRefundFee;

    /**
     * 运费退款金额
     */
    private BigDecimal deliveryRefundFee;

    /**
     * 总售后金额
     */
    private BigDecimal afterSaleTotalPrice;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;

    /**
     * 责任方
     *
     * @see com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum
     */
    private Integer responsibilityType;

    /**
     * 支付信息dto
     */
    private PaymentDTO paymentDTO;

    /**
     * 是否是最后一次售后
     */
    private Boolean lastOrderAfterSaleFlag;

    /**
     * 服务类型
     * @see com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum
     */
    private Integer serviceType;

    /**
     * 售后类型
     * @see com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum
     */
    private Integer afterSaleType;

    /**
     * 正向分账快照
     */
    private List<BillProfitSharingSnapshot> billProfitSharingSnapshots;

    /**
     * 退款分账快照
     */
    private List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots;

    /**
     * 原分账单号
     */
    private String orgProfitSharingNo;

    /**
     * 供应商id
     */
    private Long supplierId;
}
