package com.cosfo.mall.bill.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@Data
public class FinancialBillVO {
    /**
     * 账单编号
     */
    private Long billId;
    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 应收金额
     */
    private BigDecimal receivablePrice;
    /**
     * 账单类型
     */
    private Integer billType;
    /**
     * 账单开始周期
     */
    @JSONField(format="MM.dd")
    private LocalDateTime billStartTime;
    /**
     * 账单结束时间
     */
    @JSONField(format="MM.dd")
    private LocalDateTime billEndTime;
    /**
     * 订单数
     */
    private Integer orderNum;
    /**
     * 订单金额
     */
    private BigDecimal orderPrice;
    /**
     * 售后单信息
     */
    private Integer orderAfterSaleNum;
    /**
     * 售后单金额
     */
    private BigDecimal orderAfterSalePrice;
    /**
     * 门店上传凭证
     */
    private FinancialBillCredentialsVO storeCredentials;
    /**
     * 品牌方上传凭证
     */
    private FinancialBillCredentialsVO tenantCredentials;

    /**
     * 账单状态
     */
    private Integer status;

    /**
     * 完成收款时间
     */
    private LocalDateTime auditTime;
}
