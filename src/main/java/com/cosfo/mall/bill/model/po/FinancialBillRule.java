package com.cosfo.mall.bill.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * financial_bill_rule
 * <AUTHOR>
@Data
public class FinancialBillRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 账期类型 0 按周 1按月
     */
    private Integer type;

    /**
     * 第多少天 1-28
     */
    private Integer day;

    /**
     * 账期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 账期结束时间
     */
    private LocalDateTime endTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}