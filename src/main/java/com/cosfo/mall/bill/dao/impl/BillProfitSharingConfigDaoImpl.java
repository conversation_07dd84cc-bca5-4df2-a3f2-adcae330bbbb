package com.cosfo.mall.bill.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.bill.dao.BillProfitSharingConfigDao;
import com.cosfo.mall.bill.mapper.BillProfitSharingConfigMapper;
import com.cosfo.mall.bill.model.po.BillProfitSharingConfig;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 分账配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Service
public class BillProfitSharingConfigDaoImpl extends ServiceImpl<BillProfitSharingConfigMapper, BillProfitSharingConfig> implements BillProfitSharingConfigDao {

    @Override
    public BillProfitSharingConfig queryByTenantId(Long tenantId) {
        return lambdaQuery().eq(BillProfitSharingConfig::getTenantId, tenantId).one();
    }
}
