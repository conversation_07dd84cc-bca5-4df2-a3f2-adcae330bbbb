package com.cosfo.mall.bill.repository;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;

import java.util.List;

/**
 * <p>
 * 分账退款明细快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-29
 */
public interface BillProfitSharingRefundSnapshotRepository extends IService<BillProfitSharingRefundSnapshot> {

    /**
     * 查询订单id
     *
     * @param tenantId 承租者id
     * @param orderId  订单id
     * @param profitSharingNo  分账单号
     * @return {@link List}<{@link BillProfitSharingRefundSnapshot}>
     */
    List<BillProfitSharingRefundSnapshot> queryByOrderId(Long tenantId, Long orderId, String profitSharingNo);

    /**
     * 批量插入
     *
     * @param billProfitSharingRefundSnapshots
     * @return
     */
    int saveBatch(List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots);

    /**
     * 查询订单ids
     *
     * @param tenantId 承租者id
     * @param orderIds 订单id
     * @return {@link List}<{@link BillProfitSharingRefundSnapshot}>
     */
    List<BillProfitSharingRefundSnapshot> queryByTenantAndOrderIds(Long tenantId, List<Long> orderIds);

    /**
     * 根据售后单id查询
     *
     * @param tenantId
     * @param afterSaleId
     * @return
     */
    List<BillProfitSharingRefundSnapshot> queryByAfterSaleId(Long tenantId, Long afterSaleId);
}
