package com.cosfo.mall.bill.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.bill.mapper.BillProfitSharingRefundSnapshotMapper;
import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.mall.bill.repository.BillProfitSharingRefundSnapshotRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 分账退款明细快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-29
 */
@Service
public class BillProfitSharingRefundSnapshotRepositoryImpl extends ServiceImpl<BillProfitSharingRefundSnapshotMapper, BillProfitSharingRefundSnapshot> implements BillProfitSharingRefundSnapshotRepository {

    @Resource
    private BillProfitSharingRefundSnapshotMapper billProfitSharingRefundSnapshotMapper;

    @Override
    public List<BillProfitSharingRefundSnapshot> queryByOrderId(Long tenantId, Long orderId, String orgProfitSharingNo) {
        LambdaQueryWrapper<BillProfitSharingRefundSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillProfitSharingRefundSnapshot::getTenantId, tenantId);
        queryWrapper.eq(BillProfitSharingRefundSnapshot::getOrderId, orderId);
        queryWrapper.eq(Objects.nonNull(orgProfitSharingNo), BillProfitSharingRefundSnapshot::getOrgProfitSharingNo, orgProfitSharingNo);
        return list(queryWrapper);
    }

    @Override
    public int saveBatch(List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots) {
        return billProfitSharingRefundSnapshotMapper.saveBatch(billProfitSharingRefundSnapshots);
    }

    @Override
    public List<BillProfitSharingRefundSnapshot> queryByTenantAndOrderIds(Long tenantId, List<Long> orderIds) {
        LambdaQueryWrapper<BillProfitSharingRefundSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillProfitSharingRefundSnapshot::getTenantId, tenantId);
        queryWrapper.in(BillProfitSharingRefundSnapshot::getOrderId, orderIds);
        return list(queryWrapper);
    }

    @Override
    public List<BillProfitSharingRefundSnapshot> queryByAfterSaleId(Long tenantId, Long afterSaleId) {
        LambdaQueryWrapper<BillProfitSharingRefundSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillProfitSharingRefundSnapshot::getTenantId, tenantId);
        queryWrapper.eq(BillProfitSharingRefundSnapshot::getOrderAfterSaleId, afterSaleId);
        return list(queryWrapper);
    }
}
