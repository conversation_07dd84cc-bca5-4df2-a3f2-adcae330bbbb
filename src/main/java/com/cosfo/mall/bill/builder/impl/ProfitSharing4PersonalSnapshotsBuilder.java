package com.cosfo.mall.bill.builder.impl;

import com.cosfo.mall.bill.builder.ProfitSharingSnapshotBuilder;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRule;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.common.constants.BillProfitSharingSnapshotTypeEnum;
import com.cosfo.mall.common.constants.ProfitSharingRuleMappingTypeEnum;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 分账给个人的快照构建器（包含个人分账快照和必要的手续费快照）
 * @author: George
 * @date: 2024-01-10
 **/
public class ProfitSharing4PersonalSnapshotsBuilder extends ProfitSharingSnapshotBuilder {

    @Override
    public void build(BillProfitSharingOrder billProfitSharingOrder, List<BillProfitSharingRule> billProfitSharingRules) {
        Long tenantId = billProfitSharingOrder.getTenantId();
        Long orderId = billProfitSharingOrder.getOrderId();
        // 个人
        BillProfitSharingSnapshot snapshot = createSnapshot(tenantId, orderId, tenantId, AccountTypeEnum.TENANT.getType(), BigDecimal.ZERO, null, null, null, BillProfitSharingSnapshotTypeEnum.ALL.getCode(), billProfitSharingOrder.getProfitSharingNo(), null);
        billProfitSharingSnapshots.add(snapshot);

        // 手续费
        BillProfitSharingSnapshot serviceFeeBillProfitSharingSnapshot = createSnapshot(tenantId, orderId, tenantId, AccountTypeEnum.TENANT.getType(), BigDecimal.ZERO, ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode(), ProfitSharingRuleMappingTypeEnum.AVERAGE_RATIO.getCode(), null, BillProfitSharingSnapshotTypeEnum.ALL.getCode(), billProfitSharingOrder.getProfitSharingNo(), null);
        billProfitSharingSnapshots.add(serviceFeeBillProfitSharingSnapshot);
    }
}
