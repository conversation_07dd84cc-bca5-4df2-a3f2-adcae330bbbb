package com.cosfo.mall.bill.builder.impl;

import com.cosfo.mall.bill.builder.ProfitSharingSnapshotBuilder;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRule;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.common.constants.BillProfitSharingSnapshotTypeEnum;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description: 运费分账快照构建器
 * @author: George
 * @date: 2024-01-10
 **/
public class ProfitSharing4DeliveryFeeSnapshotBuilder extends ProfitSharingSnapshotBuilder {

    @Override
    public void build(BillProfitSharingOrder billProfitSharingOrder, List<BillProfitSharingRule> billProfitSharingRules) {
        Integer deliveryType = billProfitSharingRules.get(0).getDeliveryType();
        billProfitSharingRules.stream()
                .filter(el -> Objects.equals(el.getType(), ProfitSharingRuleTypeEnum.DELIVERY.getCode()))
                .forEach(el -> {
                            Long accountId = Objects.isNull(el.getAccountId()) ? billProfitSharingOrder.getSupplierId() : el.getAccountId();
                            if (accountId == null) {
                                return;
                            }
                    BillProfitSharingSnapshot snapshot = createSnapshot(el.getTenantId(), billProfitSharingOrder.getOrderId(), accountId, el.getAccountType(), BigDecimal.ZERO, el.getType(), el.getMappingType(), el.getNumber(), BillProfitSharingSnapshotTypeEnum.PART.getCode(), billProfitSharingOrder.getProfitSharingNo(), deliveryType);
                            this.billProfitSharingSnapshots.add(snapshot);
                        }
                );
    }
}
