package com.cosfo.mall.bill.builder;

import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRule;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 订单分账快照构建器
 * @author: George
 * @date: 2024-01-10
 **/
public abstract class ProfitSharingSnapshotBuilder {

    /**
     * 需要自定义的分账快照
     */
    protected List<BillProfitSharingSnapshot> billProfitSharingSnapshots = Lists.newArrayList();

    protected BillProfitSharingSnapshot createSnapshot(Long tenantId, Long orderId, Long accountId, Integer accountType, BigDecimal profitSharingPrice, Integer profitSharingType, Integer mappingType, BigDecimal number, Integer type, String profitSharingNo, Integer deliveryType) {
        BillProfitSharingSnapshot billProfitSharingSnapshot = new BillProfitSharingSnapshot();
        billProfitSharingSnapshot.setTenantId(tenantId);
        billProfitSharingSnapshot.setOrderId(orderId);
        billProfitSharingSnapshot.setAccountId(accountId);
        billProfitSharingSnapshot.setAccountType(accountType);
        billProfitSharingSnapshot.setProfitSharingPrice(profitSharingPrice);
        billProfitSharingSnapshot.setProfitSharingType(profitSharingType);
        billProfitSharingSnapshot.setMappingType(mappingType);
        billProfitSharingSnapshot.setNumber(number);
        billProfitSharingSnapshot.setType(type);
        billProfitSharingSnapshot.setProfitSharingNo(profitSharingNo);
        billProfitSharingSnapshot.setDeliveryType(deliveryType);
        return billProfitSharingSnapshot;
    }

    /**
     * 每个场景的快照都是根据订单和分账规则构建的
     *
     * @param billProfitSharingOrder 订单分账状态表
     * @param billProfitSharingRules 订单分账规则表
     */
    public abstract void build(BillProfitSharingOrder billProfitSharingOrder, List<BillProfitSharingRule> billProfitSharingRules);


    /**
     * 获取构建好的分账快照
     *
     * @return
     */
    public List<BillProfitSharingSnapshot> getBillProfitSharingSnapshots() {
        return billProfitSharingSnapshots;
    }
}
