package com.cosfo.mall.bill.builder.impl;

import com.cosfo.mall.bill.builder.ProfitSharingSnapshotBuilder;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRule;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.common.constants.BillProfitSharingSnapshotTypeEnum;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description: 自营商品分账快照构建器
 * @author: George
 * @date: 2024-01-10
 **/
public class ProfitSharing4ProprietaryGoodsSnapshotBuilder extends ProfitSharingSnapshotBuilder {

    @Override
    public void build(BillProfitSharingOrder billProfitSharingOrder, List<BillProfitSharingRule> billProfitSharingRules) {
        billProfitSharingRules.stream()
                .filter(el -> Objects.equals(el.getType(), ProfitSharingRuleTypeEnum.PROPRIETARY_SKU.getCode()))
                .forEach(el -> {
                    BillProfitSharingSnapshot snapshot = createSnapshot(el.getTenantId(), billProfitSharingOrder.getOrderId(), el.getAccountId(), el.getAccountType(), BigDecimal.ZERO, el.getType(), el.getMappingType(), el.getNumber(), BillProfitSharingSnapshotTypeEnum.PART.getCode(), billProfitSharingOrder.getProfitSharingNo(), ProfitSharingDeliveryTypeEnums.BRAND_DELIVERY.getType());
                    this.billProfitSharingSnapshots.add(snapshot);
                });
    }
}
