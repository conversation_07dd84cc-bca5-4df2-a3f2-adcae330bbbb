package com.cosfo.mall.wechat.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: fansongsong
 * @Date: 2024-01-17
 * @Description:
 */
@Data
public class WeChatPhoneLoginDTO {

    /**
     * appId
     */
    @NotNull(message = "appId不能为空")
    private String appId;

    /**
     * 微信获取手机号的code
     */
    private String code;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 微信获取openId的code
     */
    @NotNull(message = "weChatCode不能为空")
    private String weChatCode;
}
