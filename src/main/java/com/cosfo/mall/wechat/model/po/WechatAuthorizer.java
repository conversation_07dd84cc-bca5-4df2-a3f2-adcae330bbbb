package com.cosfo.mall.wechat.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * wechat_authorizer
 * <AUTHOR>
@Data
public class WechatAuthorizer implements Serializable {
    private Long id;

    /**
     * 授权人APPID
     */
    private String appId;

    /**
     * 授权方类型,0-第三发开发者,1-微信服务号,2-微信订阅号'
     */
    private Integer appType;

    /**
     * 授权人接口调用凭据
     */
    private String accessToken;

    /**
     * 授权人访问令牌过期时间
     */
    private Date accessTokenExpiretime;

    /**
     * 授权人接口调用凭据刷新令牌
     */
    private String refreshToken;

    /**
     * 授权人权限集列表
     */
    private String funcInfo;

    /**
     * 授权码
     */
    private String authorizationCode;

    /**
     * 授权码过期时间
     */
    private Date authorizationCodeExpiretime;

    /**
     * 1-正常
     */
    private Integer status;

    /**
     * 授权跳转地址
     */
    private String authorizeurl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}