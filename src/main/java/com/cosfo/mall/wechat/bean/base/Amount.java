package com.cosfo.mall.wechat.bean.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/24  08:11
 */
@Data
public class Amount {
    /**
     * 订单总金额，单位为分。
     */
    private Integer total;
    /**
     * 小数金额
     */
    private BigDecimal totalPrice;
    /**
     * 用户支付金额，单位为分。
     */
    @JsonProperty("payer_total")
    private Integer payerTotal;
    /**
     * CNY：人民币，境内商户号仅支持人民币。
     */
    private String currency;
    /**
     * 用户支付币种
     */
    @JsonProperty("payer_currency")
    private String payerCurrency;

    /**
     * 退款金额，单位：分
     */
    private Integer refund;
}
