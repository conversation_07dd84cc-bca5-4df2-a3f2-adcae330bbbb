package com.cosfo.mall.wechat.bean.constant;

/**
 * <AUTHOR>
 * @date 2022/5/24  17:29
 */
public class WechatPayTradeResult {
    /**
     * SUCCESS：支付成功
     */
    public static final String SUCCESS = "SUCCESS";
    /**
     * REFUND：转入退款
     */
    public static final String REFUND = "REFUND";
    /**
     * NOTPAY：未支付
     */
    public static final String NOTPAY = "NOTPAY";
    /**
     * CLOSED：已关闭
     */
    public static final String CLOSED = "CLOSED";
    /**
     *  REVOKED：已撤销（付款码支付）
     */
    public static final String REVOKED = "REVOKED";
    /**
     * USERPAYING：用户支付中（付款码支付）
     */
    public static final String USERPAYING = "USERPAYING";
    /**
     *  PAYERROR：支付失败(其他原因，如银行返回失败)
     */
    public static final String PAYERROR = "PAYERROR";
}
