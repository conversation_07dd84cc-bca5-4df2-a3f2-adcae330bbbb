package com.cosfo.mall.wechat.bean.huifu;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/20 17:27
 */
@Data
public class SettleConfigDTO implements Serializable {
    /**
     *结算状态 0：关闭 1：打开；示例值：1
     */
    private String settle_status;
    /**
     *结算周期 T1：下个工作日到账；D1：下个自然日到账；示例值：T1
     */
    private String settle_cycle;

    /**
     *起结金额
     */
    private String min_amt;
    /**
     *留存金额
     */
    private String remained_amt;

    /**
     *结算摘要
     */
    private String settle_abstract;
    /**
     *手续费外扣标记 1:外扣 2:内扣；示例值：1
     */
    private String out_settle_flag;
    /**
     *结算手续费外扣商户号
     */
    private String out_settle_huifuid;
    /**
     *结算手续费外扣账户类型 01:基本户 05:充值户；示例值：0
     */
    private String out_settle_acct_type;
    /**
     * 节假日结算手续费率
     */
    private String fixed_ratio;
    /**
     *结算批次号
     * 0：0点昨日余额结算批次
     * 200：2点余额结算批次
     * 300：3点余额结算批次
     * 400：4点余额结算批次
     * 500：5点余额结算批次
     * 600：6点余额结算批次
     * 700：7点余额结算批次
     * 800：8点余额结算批次
     * 900：9点余额结算批次
     * 1000：10点余额结算批次
     * 1100：11点余额结算批次
     * 1200：12点余额结算批次
     * 无值则结算昨日全部余额
     * 示例值：1100
     *
     */
    private String settle_batch_no;

}
