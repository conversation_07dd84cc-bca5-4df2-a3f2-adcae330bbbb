package com.cosfo.mall.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class MchOrderQuery extends MchVersion{

    @XmlElement
    private String appid;

    @XmlElement
    private String mch_id;

    @XmlElement
    private String transaction_id;

    @XmlElement
    private String out_trade_no;

    @XmlElement
    private String nonce_str;

    @XmlElement
    private String sign;

    @XmlElement
    private String sign_type;

    /**
     * @since 2.8.5
     */
    @XmlElement
    private String sub_appid;

    /**
     * @since 2.8.5
     */
    @XmlElement
    private String sub_mch_id;
}
