package com.cosfo.mall.wechat.bean.notify;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/24  08:42
 */
@Data
public class NotifyResponse {
    private String code;
    private String message;

    public static NotifyResponse success() {
        NotifyResponse response = new NotifyResponse();
        response.code = "SUCCESS";
        response.message = "成功";
        return response;
    }

    public static NotifyResponse fail() {
        NotifyResponse response = new NotifyResponse();
        response.code = "FAIL";
        response.message = "失败";
        return response;
    }

    public static NotifyResponse failWithMsg(String msg) {
        NotifyResponse response = new NotifyResponse();
        response.code = "FAIL";
        response.message = msg;
        return response;
    }
}
