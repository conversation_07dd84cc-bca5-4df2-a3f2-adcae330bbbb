package com.cosfo.mall.wechat.bean;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class privateSettingResp extends BaseResult implements Serializable{

   private int code_exist;
   private List<String> privacy_list;
   private List<SettingDto> setting_list;
   private Date update_time;
   private List<ownerSettingDto> owner_setting;
   private privacyDescList privacy_desc;

    @Data
    public static class SettingDto {
        private String privacy_key;//用户信息类型的英文名称
        private String privacy_text;//该用户信息类型的用途
        private String privacy_label; //用户信息类型的中文名称
    }

    @Data
    public static class ownerSettingDto {
        private String contact_email;//信息收集方（开发者）的邮箱
        private String contact_phone;//信息收集方（开发者）的手机号
        private String contact_qq; //信息收集方（开发者）的qq
        private String contact_weixin; //信息收集方（开发者）的微信号
        private String notice_method; //通知方式，指的是当开发者收集信息有变动时，通过该方式通知用户
        private String store_expire_timestamp; //存储期限，指的是开发者收集用户信息存储多久
        private String ext_file_media_id; //自定义 用户隐私保护指引文件的media_id
    }

    @Data
    public static class privacyDescList {
       private List<privacyDescDto> privacy_desc_list;
    }

    @Data
    public static class privacyDescDto {
        private String privacy_desc;//用户信息类型的中文描述
        private String privacy_key;//用户信息类型的英文key
    }



}
