package com.cosfo.mall.wechat.bean.refund;

import com.cosfo.mall.wechat.bean.base.Amount;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/26  16:19
 */
@Data
public class RefundNotify {
    private String mchid;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JsonProperty("refund_id")
    private String refundId;
    @JsonProperty("out_refund_no")
    private String outRefundNo;
    @JsonProperty("refund_status")
    private String refundStatus;
    @JsonProperty("success_time")
    private LocalDateTime successTime;
    @JsonProperty("user_received_account")
    private String userReceivedAccount;
    private Amount amount;
}