package com.cosfo.mall.wechat.bean.profitsharing;

import lombok.Data;

import java.util.List;

/**
 * 直连商户-请求分账API-请求参数
 *
 * <AUTHOR>
 * @since 1.0.11.RELEASE
 */
@Data
public class ProfitSharingOrderResult {
    /**
     * 微信订单号，必填
     */
    private String transactionId;
    /**
     * 商户分账单号，必填
     * <p>
     * 商户系统内部的分账单号，在商户系统内部唯一，同一分账单号多次请求等同一次。
     * 只能是数字、大小写字母_-|*@
     */
    private String outOrderNo;
    /**
     * 微信分账单号
     */
    private String orderId;
    /**
     * 分账单状态
     * 分账单状态（每个接收方的分账结果请查看receivers中的result字段），枚举值：
     * 1、PROCESSING：处理中
     * 2、FINISHED：分账完成
     */
    private String state;
    /**
     * 分账接收方列表，选填
     * <p>
     * 可以设置出资商户作为分账接受方，最多可有50个分账接收方
     */
    private List<ReceiverResult> receivers;
}