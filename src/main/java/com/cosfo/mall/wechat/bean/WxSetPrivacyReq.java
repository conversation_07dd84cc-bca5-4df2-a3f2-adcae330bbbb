package com.cosfo.mall.wechat.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WxSetPrivacyReq implements Serializable {

    private Integer privacy_ver; //2生产 1开发
    private OwnerSetting owner_setting;
    private List<Privacy> setting_list;


    @Data
    public static class OwnerSetting{
        private String contact_email;
        private String contact_phone;
        private String contact_qq;
        private String contact_weixin;
        private String ext_file_media_id;
        private String notice_method;
        private String store_expire_timestamp;
    }

    @Data
    public static class Privacy{
        private String privacy_key;
        private String privacy_text;
    }
}
