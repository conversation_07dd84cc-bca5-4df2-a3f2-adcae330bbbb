package com.cosfo.mall.wechat.bean.paymch;

import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022/12/19 22:53
 */
@Data
public class WxData {

    /**
     *子商户公众账号id，示例值：wxec280d4c8a1cc2ca
     */
    private String sub_appid;
    /**
     *用户标识,在商户appid下的唯一标志；示例值：oGhiSxIAPtEnPfe9Xo000000B
     */
    private String openid;
    /**
     *子商户用户标识，微信js/小程序支付必传；示例值：oWNHX5RNaCUmZR
     */
    private String sub_openid;
    /**
     *附加数据
     */
    private String attach;
    /**
     *商品描述
     */
    private String body;
    /**
     *商品详情
     */
    private Detail detail;
    /**
     *设备号
     */
    private String device_info;
    /**
     *订单优惠标记
     */
    private String goods_tag;
    /**
     *实名支付
     */
    private String identity;
    /**
     *开发票入口开放标识
     */
    private String receipt;
    /**
     *场景信息
     */
    private SceneInfo scene_info;
    /**
     *终端ip
     */
    private String spbill_create_ip;

}
