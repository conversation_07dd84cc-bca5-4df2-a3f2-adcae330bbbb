package com.cosfo.mall.wechat.bean.huifu;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/21 2:00
 * 结算卡信息
 */
@Data
public class CardInfoDTO implements Serializable {

    /**
     *	卡类型，0：对公； 1：对私；示例值：1
     */
    private String card_type;
    /**
     *	银行卡户名
     */
    private String card_name;
    /**
     *银行卡号
     */
    private String card_no;
    /**
     *	银行所在市
     */
    private String prov_id;
    /**
     *银行所在省
     */
    private String area_id;
    /**
     *	银行号
     */
    private String bank_code;
    /**
     *联行号
     */
    private String branch_code;
    /**
     *支行名称
     */
    private String branch_name;
    /**
     *持卡人证件类型
     */
    private String cert_type;
    /**
     *持卡人证件号码
     */
    private String cert_no;
    /**
     *持卡人证件有效期类型
     */
    private String cert_validity_type;
    /**
     *持卡人证件有效期起始日期
     */
    private String cert_begin_date;
    /**
     *持卡人证件有效期截止日期
     */
    private String cert_end_date;
    /**
     *银行卡绑定手机号
     */
    private String mp;
    /**
     *绑卡序列号
     */
    private String token_no;

}
