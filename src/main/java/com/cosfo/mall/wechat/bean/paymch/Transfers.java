package com.cosfo.mall.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Transfers {

    private String mch_appid;

    private String mchid;

    private String device_info;

    private String nonce_str;

    private String sign;

    private String partner_trade_no;

    private String openid;

    private String check_name;

    private String re_user_name;

    private Integer amount;

    private BigDecimal yuan_amount;

    private String desc;

    private String spbill_create_ip;

    private String sign_type;

}
