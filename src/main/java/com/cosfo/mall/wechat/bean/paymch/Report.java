package com.cosfo.mall.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Report {

	private String appid;
	private String mch_id;
	private String device_info;
	private String nonce_str;
	private String sign;
	private String sign_type;
	private String interface_url;
	private Integer execute_time_;
	private String return_code;
	private String return_msg;
	private String result_code;
	private String err_code;
	private String err_code_des;
	private String out_trade_no;
	private String user_ip;
	private String time;

	/**
	 * @since 2.8.5
	 */
	@XmlElement
	private String sub_appid;

	/**
	 * @since 2.8.5
	 */
	@XmlElement
	private String sub_mch_id;

}
