package com.cosfo.mall.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class MchDownloadbill {

	private String appid;

	private String mch_id;

	private String device_info;

	private String out_trade_no;

	private String nonce_str;

	private String sign;
	
	private String sign_type;

	private String bill_date;

	private String bill_type;
	
	/**
	 * @since 2.8.19
	 */
	private String tar_type;

	/**
	 * @since 2.8.5
	 */
	@XmlElement
	private String sub_appid;

	/**
	 * @since 2.8.5
	 */
	@XmlElement
	private String sub_mch_id;
	
}
