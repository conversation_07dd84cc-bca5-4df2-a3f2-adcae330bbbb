package com.cosfo.mall.wechat.bean;

import lombok.Data;

/**
 * @Package: com.cosfo.bean
 * @Description:    微信基础返回类
 * @author: <EMAIL>
 * @Date: 2022/4/27
 */
@Data
public class BaseResult {
    private static final Integer SC_SUCC = 0;

    private Integer errcode;
    private String errmsg;

    public boolean successed(){
        return errcode == null || SC_SUCC.equals(errcode);
    }

    public static void assertSuccess(BaseResult response) throws Exception {
        if(null == response){
            throw new Exception("调用微信服务失败");
        }else if(!response.successed()){
            throw new Exception(String.valueOf(response.getErrcode()));
        }
    }
}
