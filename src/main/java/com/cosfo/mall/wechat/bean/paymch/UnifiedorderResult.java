package com.cosfo.mall.wechat.bean.paymch;

import com.cosfo.mall.wechat.bean.AdaptorCDATA;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

@XmlRootElement(name="xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class UnifiedorderResult extends MchBase{

    @XmlElement
    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String device_info;

    @XmlElement
    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String trade_type;

    @XmlElement
    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String prepay_id;

    @XmlElement
    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String code_url;

    @XmlElement
    @XmlJavaTypeAdapter(value = AdaptorCDATA.class)
    private String mweb_url;

}
