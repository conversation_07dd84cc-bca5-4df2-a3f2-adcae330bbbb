package com.cosfo.mall.wechat.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class GetAuthorizerInfoResp extends BaseResult{

    private WxAuthorizerInfo authorizer_info;
    private WxAuthorizationInfo authorization_info;

    @Data
    public static class WxAuthorizerInfo implements Serializable{
        private String nick_name;
        private String head_img;
        private WxIdObject service_type_info;//小程序类型
        private WxIdObject verify_type_info;//小程序认证类型
        private String user_name; //小程序原始id
        private String principal_name;//小程序主体名称
        @JsonProperty("MiniProgramInfo")
        private Map<String,Object> miniProgramInfo;//不定义了单纯拿来判断是不是小程序
    }

    @Data
    public static class WxAuthorizationInfo implements Serializable{
        private String authorizer_appid;
        private List<WxFuncInfo> func_info;
    }
}
