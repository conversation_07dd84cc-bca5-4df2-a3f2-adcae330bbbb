package com.cosfo.mall.wechat.bean.paymch;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class SecapiPayRefund {

    private String appid;

    private String mch_id;

    private String device_info;

    private String nonce_str;

    private String sign;

    private String sign_type;

    private String transaction_id;

    private String out_trade_no;

    private String out_refund_no;

    private Integer total_fee;

    private BigDecimal order_amount;

    private Integer refund_fee;

    private String refund_fee_type;

    /**
     * @since 2.8.21
     */
    private String refund_desc;

    private String op_user_id;

    /**
     * @since 2.8.5
     */
    private String sub_appid;

    /**
     * @since 2.8.5
     */
    private String sub_mch_id;

    /**
     * @since 2.8.5
     */
    private String refund_account;		//退款资金来源
    //REFUND_SOURCE_UNSETTLED_FUNDS ---未结算资金退款（默认使用未结算资金退款）
    //REFUND_SOURCE_RECHARGE_FUNDS  ---可用余额退款
    /**
     * @since 2.8.19
     */
    private String notify_url;
}
