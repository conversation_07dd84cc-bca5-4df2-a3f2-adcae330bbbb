package com.cosfo.mall.wechat.bean.refund;

import com.cosfo.mall.wechat.bean.base.Amount;
import com.cosfo.mall.wechat.bean.base.PromotionDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25  14:51
 * @see {https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_9.shtml}
 */
@Data
public class RefundResult {
    @JsonProperty("refund_id")
    private String refundId;
    @JsonProperty("out_refund_no")
    private String outRefundNo;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    private String channel;
    @JsonProperty("user_received_account")
    private String userReceivedAccount;
    @JsonProperty("success_time")
    private LocalDateTime successTime;
    @JsonProperty("create_time")
    private LocalDateTime createTime;
    private String status;
    @JsonProperty("funds_account")
    private String fundsAccount;
    private Amount amount;
    @JsonProperty("promotion_detail")
    private List<PromotionDetail> promotionDetail;
}