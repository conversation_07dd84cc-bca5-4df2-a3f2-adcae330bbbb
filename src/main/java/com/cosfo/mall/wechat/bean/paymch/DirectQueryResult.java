package com.cosfo.mall.wechat.bean.paymch;

import com.cosfo.mall.wechat.bean.base.Amount;
import com.cosfo.mall.wechat.bean.base.Payer;
import com.cosfo.mall.wechat.bean.base.PromotionDetail;
import com.cosfo.mall.wechat.bean.base.SceneInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/24  08:07
 */
@Data
public class DirectQueryResult {
    private String appid;

    private String mchid;

    @JsonProperty("out_trade_no")
    private String outTradeNo;

    @JsonProperty("transaction_id")
    private String transactionId;

    @JsonProperty("trade_type")
    private String tradeType;

    @JsonProperty("trade_state")
    private String tradeState;

    @JsonProperty("trade_state_desc")
    private String tradeStateDesc;

    @JsonProperty("bank_type")
    private String bankType;

    private String attach;

    @JsonProperty("success_time")
    private LocalDateTime successTime;

    private Payer payer;

    private Amount amount;

    @JsonProperty("scene_info")
    private SceneInfo sceneInfo;

    @JsonProperty("promotion_detail")
    private List<PromotionDetail> promotionDetail;
}
