package com.cosfo.mall.wechat.bean.paymch;

import com.cosfo.mall.wechat.bean.DynamicField;
import com.cosfo.mall.wechat.bean.base.PromotionDetail;
import lombok.Data;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@XmlRootElement(name="xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class MchOrderInfoResult extends MchBase implements DynamicField {

    private String trade_state;

    private String device_info;

    private String openid;

    private String is_subscribe;

    private String trade_type;

    private String bank_type;

    private Integer total_fee;

    private String fee_type;

    private Integer cash_fee;

    private String cash_fee_type;

    private Integer coupon_fee;

    private Integer coupon_count;

    private String transaction_id;

    private String out_trade_no;

    private String attach;

    private String time_end;

    private String trade_state_desc;

    private String sub_openid;

    private String sub_is_subscribe;

    private Integer settlement_total_fee;


    /** 代金券或立减优惠
     * @since 2.8.12
     * 使用  getCoupons() 获取 List.
     * List.size() = coupon_count
     */
    @XmlTransient
    private List<Coupon> coupons;

    /**
     * 单品优惠 ,请求参数 version=1.0
     * @since 2.8.12
     */
    @XmlElement
    @XmlJavaTypeAdapter(value = PromotionDetailXmlAdapter.class)
    private List<PromotionDetail> promotion_detail;

    /**
     * 委托代扣协议id
     * @since 2.8.24
     */
    private String contract_id;

    @Override
    public void buildDynamicField(Map<String, String> dataMap) {
        if(dataMap != null){
            String coupon_countStr = dataMap.get("coupon_count");
            if(coupon_countStr != null){
                List<Coupon> list = new ArrayList<Coupon>();
                for (int i = 0; i < Integer.parseInt(coupon_countStr); i++) {
                    Coupon coupon = new Coupon(
                            dataMap.get("coupon_type_"+i),
                            dataMap.get("coupon_id_"+i),
                            Integer.parseInt(dataMap.get("coupon_fee_"+i)),
                            i);
                    list.add(coupon);
                }
                this.coupons = list;
            }

        }
    }
}
