package com.cosfo.mall.wechat.bean.huifu;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 行政区域划分表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AdministrativeDivision implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 省份名称
     */
    private String proName;

    /**
     * 省份编码
     */
    private String proNo;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市编码
     */
    private String cityNo;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 区县编码
     */
    private String areaNo;


}
