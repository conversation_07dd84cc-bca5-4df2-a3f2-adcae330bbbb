package com.cosfo.mall.wechat.bean.huifu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2022/12/20 14:52
 */
@Data
public class HuiFuBasicDataReceiveDTO implements Serializable {
    /**
     *	业务返回码
     */
    private String resp_code;
    /**
     *业务返回描述
     */
    private String resp_desc;

    /**
     *产品号
     */
    private String product_id;
    /**
     *上级机构号
     */
    private String upper_huifu_id;
    /**
     *商户注册名称
     */
    private String reg_name;
    /**
     *商户类型
     */
    private String cust_type;
    /**
     *商户简称
     */
    private String short_name;
    /**
     *小票名称
     */
    private String receipt_name;
    /**
     *商户种类
     */
    private String ent_type;
    /**
     *附加信息列表
     */
    private String file_info_list;
    /**
     *营业执照类型
     */
    private String license_type;
    /**
     *营业执照编号
     */
    private String license_code;
    /**
     *证照有效期类型
     */
    private String license_validity_type;
    /**
     *证照开始日期
     */
    private String license_begin_date;
    /**
     *证照结束日期
     */
    private String license_end_date;
    /**
     *注册省
     */
    private String reg_prov_id;
    /**
     *注册市
     */
    private String reg_area_id;
    /**
     *注册区
     */
    private String reg_district_id;
    /**
     *注册详细地址
     */
    private String reg_detail;
    /**
     *法人姓名
     */
    private String legal_name;
    /**
     *法人证件类型
     */
    private String legal_cert_type;
    /**
     *法人证件号码
     */
    private String legal_cert_no;
    /**
     *法人证件有效期类型
     */
    private String legal_cert_validity_type;
    /**
     *法人证件有效期开始日期
     */
    private String legal_cert_begin_date;
    /**
     *法人证件有效期结束日期
     */
    private String legal_cert_end_date;
    /**
     *经营地址所在省
     */
    private String prov_id;
    /**
     *经营地址所在市
     */
    private String area_id;
    /**
     *经营地址所在区县
     */
    private String district_id;
    /**
     *商户经营详细地址
     */
    private String detail_addr;
    /**
     *商户联系人
     */
    private String contact_name;
    /**
     *联系人手机
     */
    private String contact_mobile_no;
    /**
     *联系人邮箱
     */
    private String contact_email;
    /**
     *客服电话
     */
    private String service_phone;

    /**
     *管理员账号
     */
    private String login_name;
    /**
     *注册短信发送标记
     */
    private String sms_send_flag;
    /**
     *经营类型
     */
    private String busi_type;
    /**
     *所属行业（MCC）
     */
    private String mcc;
    /**
     *商户英文名称
     */
    private String mer_en_name;
    /**
     *商户主页URL
     */
    private String mer_url;
    /**
     *商户ICP备案编号
     */
    private String mer_icp;
    /**
     *开户许可证核准号
     */
    private String open_licence_no;

//    /**
//     *卡信息
//     */
//    private List<QryCashCardInfoDTO> qry_cash_card_info_list;
//    /**
//     *取现配置列表
//     */
//    private List<QryCashConfigDTO> qry_cash_config_list;
//    /**
//     *结算配置
//     */
//    private List<QrySettleConfigDTO> qry_settle_config_list;
//    /**
//     *	协议信息
//     */
//    private List<AgreementInfoDTO> agreement_info_list;
    /**
     *卡信息
     */
    private String qry_cash_card_info_list;
    /**
     *取现配置列表
     */
    private String qry_cash_config_list;
    /**
     *结算配置
     */
    private String qry_settle_config_list;
    /**
     *	协议信息
     */
    private String agreement_info_list;
    /**
     *是否开通快捷
     */
    private String quick_flag;
    /**
     *是否开通网银
     */
    private String online_flag;
    /**
     *是否开通代扣
     */
    private String withhold_flag;
    /**
     *是否开通支付宝预授权
     */
    private String pre_authorization_flag;
    /**
     *是否开通手机WAP支付
     */
    private String web_flag;
    /**
     *是否开通余额支付
     */
    private String balance_pay_flag;
    /**
     *余额支付配置对象
     */
    private String qry_balance_pay_config;
    /**
     *是否开通延迟入账
     */
    private String delay_flag;
    /**
     *商户开通强制延迟标记
     */
    private String forced_delay_flag;
    /**
     *交易手续费外扣标记
     */
    private String out_fee_flag;
    /**
     *交易手续费外扣汇付商户号
     */
    private String out_fee_huifu_id;
    /**
     *交易手续费外扣账户类型
     */
    private String out_fee_acct_type;
    /**
     *银行卡支付配置信息
     */
    private String qry_bank_card_conf;
    /**
     *银联二维码支付配置信息
     */
    private String qry_union_conf;
//
//    /**
//     *微信支付配置信息
//     */
//    private List<QryWxConfDTO> qry_wx_conf_list;
//    /**
//     *支付宝支付配置信息
//     */
//    private List<QryAliConfDTO> qry_ali_conf_list;
    /**
     *微信支付配置信息
     */
    private String qry_wx_conf_list;
    /**
     *支付宝支付配置信息
     */
    private String qry_ali_conf_list;
    /**
     *交易应答异步通知地址
     */
    private String recon_resp_addr;
//    /**
//     *签约人列表
//     */
//    private List<SignUserInfoDTO> sign_user_info_list;
    /**
     *签约人列表
     */
    private String sign_user_info_list;
    /**
     *银行大额转账对象
     */
    private String bank_big_amt_pay_config;
    /**
     *微信直连配置对象
     */
    private String wx_zl_conf;
}
