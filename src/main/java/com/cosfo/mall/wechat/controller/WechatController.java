package com.cosfo.mall.wechat.controller;


import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.vo.LoginVO;
import com.cosfo.mall.wechat.model.dto.WeChatPhoneLoginDTO;
import com.cosfo.mall.wechat.model.dto.WechatCodeDTO;
import com.cosfo.mall.wechat.model.vo.SessionKeyVo;
import com.cosfo.mall.wechat.model.vo.UserLiteAuthVo;
import com.cosfo.mall.wechat.service.WeChatAuthService;
import com.cosfo.mall.wechat.service.WeixinService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 微信API管理
 *
 * @date 2022/11/29 11:27
 */
@RestController
@Slf4j
public class WechatController extends BaseController {

    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    WeixinService weixinService;
    @Resource
    private WeChatAuthService weChatAuthService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;


    /**
     * 小程序微信授权获取openId和unionId
     */
    @RequestMapping(value = "/user/getOpenInfo", method = RequestMethod.POST)
    public ResultDTO<SessionKeyVo> wxLoginByCode(@RequestBody UserLiteAuthVo userLiteAuthVo) throws Exception {
//        if (grayReleaseConfig.executeAuthGray(getRequestContextInfoDTO().getTenantId())) {
            return ResultDTO.success(weChatAuthService.getMiniProgramSessionKey(userLiteAuthVo.getAppId(), userLiteAuthVo.getCode()));
//        }
//        return weixinService.getCode2Session(userLiteAuthVo.getAppId(), userLiteAuthVo.getCode());
    }

    /**
     * 获取微信绑定手机号
     *
     * @param userLiteAuthVo
     * @return
     */
    @RequestMapping(value = "/getUserPhoneNumber", method = RequestMethod.POST)
    public ResultDTO getUserPhoneNumber(@RequestBody UserLiteAuthVo userLiteAuthVo) {
        return weixinService.getUserPhoneNumber(userLiteAuthVo);
    }

    /**
     * 小程序用户授权登录
     * 小程序存在两种情况登录
     * 1、手机号授权登录
     * 2、小程序已缓存手机号，直接通过获取openId登录
     * 因此该接口提供code、phone二选一登录
     *
     * @param weChatPhoneLoginDTO
     * @return
     */
    @RequestMapping(value = "/getWeChatPhoneLogin", method = RequestMethod.POST)
    public ResultDTO<LoginVO> getWeChatPhoneLogin(@RequestBody WeChatPhoneLoginDTO weChatPhoneLoginDTO) {
        return weixinService.getWeChatPhoneLogin(weChatPhoneLoginDTO);
    }

    /**
     * 返回公众号获取code Url
     *
     * @param wechatCodeDTO
     * @return
     */
    @RequestMapping(value = "/getWechatCode", method = RequestMethod.POST)
    public CommonResult<String> getWechatCode(@RequestBody WechatCodeDTO wechatCodeDTO) {
        String wechatCode = weixinService.getWechatCode(wechatCodeDTO.getUrl(), wechatCodeDTO.getScope(), getRequestContextInfoDTO().getTenantId());
        return CommonResult.ok(wechatCode);
    }
}
