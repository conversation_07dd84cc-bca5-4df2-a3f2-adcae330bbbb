package com.cosfo.mall.wechat.mapper;

import com.cosfo.mall.wechat.model.po.WechatAuthorizer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface WechatAuthorizerMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WechatAuthorizer record);

    int insertSelective(WechatAuthorizer record);

    WechatAuthorizer selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WechatAuthorizer record);

    int updateByPrimaryKey(WechatAuthorizer record);

    /**
     * 根据appId获取小程序认证信息
     *
     * @param appId
     * @return
     */
    WechatAuthorizer selectByAppId(@Param("appId") String appId);
}