package com.cosfo.mall.wechat.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.vo.LoginVO;
import com.cosfo.mall.wechat.model.dto.WeChatPhoneLoginDTO;
import com.cosfo.mall.wechat.model.vo.UserLiteAuthVo;

public interface WeixinService {

    /**
     * 用户登录--通过第三方平台获取小程序登录session_key
     * @param code
     * @param appId
     */
//    @Deprecated
//    ResultDTO getCode2Session(String appId, String code) throws Exception;

    /**
     * 获取微信绑定手机号--通过第三方平台获取微信绑定手机号
     *
     * @param userLiteAuthVo
     * @return
     */
    ResultDTO getUserPhoneNumber(UserLiteAuthVo userLiteAuthVo);

    /**
     * 获取公众号code
     *
     * @param url
     * @param tenantId
     * @return
     */
    String getWechatCode(String url, String scope, Long tenantId);

    /**
     * 获取openId
     *
     * @param code
     * @param tenantId
     * @return
     */
    String getOaOpenId(String code, Long tenantId);

    /**
     * 获取微信手机号登录
     * @param weChatPhoneLoginDTO
     * @return
     */
    ResultDTO<LoginVO> getWeChatPhoneLogin(WeChatPhoneLoginDTO weChatPhoneLoginDTO);
}
