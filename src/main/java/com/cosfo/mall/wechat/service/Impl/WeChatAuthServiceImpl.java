package com.cosfo.mall.wechat.service.Impl;

import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.facade.auth.AuthClientLoginFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.mall.merchant.model.po.Merchant;
import com.cosfo.mall.system.model.dto.SystemAuthorizerDto;
import com.cosfo.mall.system.service.SystemService;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.bean.BaseResult;
import com.cosfo.mall.wechat.model.vo.SessionKeyVo;
import com.cosfo.mall.wechat.model.vo.UserLiteAuthVo;
import com.cosfo.mall.wechat.model.vo.UserPhoneInfoVO;
import com.cosfo.mall.wechat.service.WeChatAuthService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WeChatAuthServiceImpl implements WeChatAuthService {

    /**
     * 正常使用
     */
    private static final Integer NORMAL_STATUS = 1;

    @Resource
    private SystemService systemService;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Resource
    private AuthClientLoginFacade authClientLoginFacade;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;


    @Override
    public SessionKeyVo getMiniProgramSessionKey(String appId, String code) throws Exception {
        SystemAuthorizerDto systemAuthorizerDto = systemService.getAuthorizer();

        //查询所属租户
        TenantAuthConnection query = new TenantAuthConnection();
        query.setAppId(appId);
        query.setStatus(NORMAL_STATUS);
        TenantAuthConnection connection = tenantAuthConnectionMapper.selectOne(query);
        if (connection == null) {
            throw new BizException("获取租户信息失败");
        }

        SessionKeyVo sessionKeyVo = new SessionKeyVo();

        if (grayReleaseConfig.executeAuthGray(connection.getTenantId())) {
            AuthQueryWechatInfoDTO authQueryWechatInfoDTO = authClientLoginFacade.queryWeChatInfo(code, appId, systemAuthorizerDto.getAppId(), systemAuthorizerDto.getAccessToken());

            sessionKeyVo.setSessionKey(authQueryWechatInfoDTO.getSessionKey());
            sessionKeyVo.setOpenId(authQueryWechatInfoDTO.getOpenid());
            sessionKeyVo.setUnionId(authQueryWechatInfoDTO.getUnionid());

        } else {
//            Jscode2sessionResult sessionResult = AuthAPI.componentJscode2session(appId, code, systemAuthorizerDto.getAppId(), systemAuthorizerDto.getAccessToken());
//            log.info("sessionResult:{}, systemAuthorizerDto:{} ", JSONObject.toJSONString(sessionResult), JSONObject.toJSONString(systemAuthorizerDto));
//            assertSuccess(appId, sessionResult);
//            sessionKeyVo = WxConverter.toSessionKeyVo(sessionResult);
        }

        Merchant merchant = userCenterMerchantInfoFacade.getMerchantByTenantId(connection.getTenantId());
        if (merchant == null) {
            throw new BizException("获取门店信息失败");
        }
        sessionKeyVo.setTenantId(connection.getTenantId());
        sessionKeyVo.setTenantName(merchant.getMerchantName());
        return sessionKeyVo;
    }

    public void assertSuccess(String appId, BaseResult response) throws Exception {
        if (null == response) {
            throw new Exception("wechat.api.call.fail,调用微信服务失败");
        } else if (!response.successed()) {
            throw new Exception(response.getErrcode() + Optional.ofNullable(response.getErrmsg()).orElse(String.valueOf(response.getErrcode())));
        }
    }

    @Override
    public UserPhoneInfoVO getUserPhoneNumber(UserLiteAuthVo userLiteAuthVo) {
        return null;
    }
}
