package com.cosfo.mall.wechat.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.wechat.model.vo.SessionKeyVo;
import com.cosfo.mall.wechat.model.vo.UserLiteAuthVo;
import com.cosfo.mall.wechat.model.vo.UserPhoneInfoVO;

/**
 * <AUTHOR>
 */
public interface WeChatAuthService {

    /**
     * 用户登录--通过第三方平台获取小程序登录session_key
     * @param code
     * @param appId
     */
    SessionKeyVo getMiniProgramSessionKey(String appId, String code) throws Exception;

    /**
     * 获取微信绑定手机号--通过第三方平台获取微信绑定手机号
     *
     * @param userLiteAuthVo
     * @return
     */
    UserPhoneInfoVO getUserPhoneNumber(UserLiteAuthVo userLiteAuthVo);
}
