package com.cosfo.mall.wechat.service.Impl;

import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.mall.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.mall.wechat.model.po.WechatAuthorizer;
import com.cosfo.mall.wechat.service.WechatAuthorizerService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/19
 */
@Service
public class WechatAuthorizerServiceImpl implements WechatAuthorizerService {
    @Resource
    private WechatAuthorizerMapper wechatAuthorizerMapper;

    @Override
    public WechatAuthorizerDto getWechatAuthorizer(String appId) {
        AssertCheckParams.notNull(appId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "appId不能为空");
        // 查询小程序认证信息
        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(appId);
        WechatAuthorizerDto wechatAuthorizerDto = new WechatAuthorizerDto();
        BeanUtils.copyProperties(wechatAuthorizer, wechatAuthorizerDto);
        return wechatAuthorizerDto;
    }
}
