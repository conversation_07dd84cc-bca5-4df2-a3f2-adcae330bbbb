package com.cosfo.mall.wechat.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.mall.merchant.model.vo.LoginVO;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.system.service.SystemService;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.api.AuthAPI;
import com.cosfo.mall.wechat.api.WeChatOAApi;
import com.cosfo.mall.wechat.bean.BaseResult;
import com.cosfo.mall.wechat.bean.user.UserPhoneInfo;
import com.cosfo.mall.wechat.model.dto.WeChatPhoneLoginDTO;
import com.cosfo.mall.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.mall.wechat.model.vo.UserLiteAuthVo;
import com.cosfo.mall.wechat.model.vo.UserPhoneInfoVO;
import com.cosfo.mall.wechat.service.WeChatAuthService;
import com.cosfo.mall.wechat.service.WechatAuthorizerService;
import com.cosfo.mall.wechat.service.WeixinService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class WeixinServiceImpl implements WeixinService {
//    @Resource
//    private MerchantMapper merchantMapper;

    @Resource
    SystemService systemService;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private WechatAuthorizerService wechatAuthorizerService;
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Value("${mall.url}")
    private String mallUrl;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private WeChatAuthService weChatAuthService;

    /**
     * 正常使用
     */
    private static final Integer NORMAL_STATUS = 1;

//    @Override
//    public ResultDTO getCode2Session(String appId, String code) throws Exception {
//        Assert.notNull(appId, "appId不能为空");
//        Assert.notNull(code, "登录code不能为空");
//        SystemAuthorizerDto systemAuthorizerDto = systemService.getAuthorizer();
//        Jscode2sessionResult sessionResult = AuthAPI.componentJscode2session(appId, code, systemAuthorizerDto.getAppId(), systemAuthorizerDto.getAccessToken());
//        log.info("sessionResult:{}, systemAuthorizerDto:{} ", JSONObject.toJSONString(sessionResult),JSONObject.toJSONString(systemAuthorizerDto));
//        assertSuccess(appId, sessionResult);
//        SessionKeyVo sessionKeyVo = WxConverter.toSessionKeyVo(sessionResult);
//
//        //查询所属租户
//        TenantAuthConnection query = new TenantAuthConnection();
//        query.setAppId(appId);
//        query.setStatus(NORMAL_STATUS);
//        TenantAuthConnection connection = tenantAuthConnectionMapper.selectOne(query);
//        if (Objects.isNull(connection)) {
//            return ResultDTO.fail(ResultDTOEnum.TENANT_NOT_FOUND);
//        }
//        Long tenantId = connection.getTenantId();
//        log.info("显示租户id:{}，显示appId:{}", tenantId, appId);
////        Merchant merchant = merchantMapper.selectByTenantId(tenantId);
//        Merchant merchant = userCenterMerchantInfoFacade.getMerchantByTenantId(tenantId);
//        sessionKeyVo.setTenantId(tenantId);
//        sessionKeyVo.setTenantName(merchant.getMerchantName());
//        return ResultDTO.success(sessionKeyVo);
//    }

    public void assertSuccess(String appId, BaseResult response) throws Exception {
        if (null == response) {
            throw new Exception("wechat.api.call.fail,调用微信服务失败");
        } else if (!response.successed()) {
            throw new Exception(response.getErrcode() + Optional.ofNullable(response.getErrmsg()).orElse(String.valueOf(response.getErrcode())));
        }
    }

    @Override
    public ResultDTO<UserPhoneInfoVO> getUserPhoneNumber(UserLiteAuthVo userLiteAuthVo) {
        AssertCheckParams.notNull(userLiteAuthVo.getCode(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "code不能为空");
        AssertCheckParams.notNull(userLiteAuthVo.getAppId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "appId不能为空");
        // 获取accessToken
        WechatAuthorizerDto wechatAuthorizer = wechatAuthorizerService.getWechatAuthorizer(userLiteAuthVo.getAppId());
        UserPhoneInfo userPhoneNumber = AuthAPI.getUserPhoneNumber(wechatAuthorizer.getAccessToken(), userLiteAuthVo.getCode());
        log.info("获取用户绑定手机号：{}", JSONObject.toJSONString(userPhoneNumber));

        try {
            assertSuccess(userLiteAuthVo.getAppId(), userPhoneNumber);
        } catch (Exception e) {
            log.error("异常信息：{}", e.getMessage(), e);
            throw new DefaultServiceException("获取用户微信绑定手机号失败");
        }

        UserPhoneInfoVO userPhoneInfoVO = new UserPhoneInfoVO();
        userPhoneInfoVO.setPhone(userPhoneNumber.getPhone_info().getPhoneNumber());
        log.info("获取用户绑定手机号最终返回信息：{}", JSONObject.toJSONString(userPhoneInfoVO));
        return ResultDTO.success(userPhoneInfoVO);
    }

    @Override
    public String getWechatCode(String url, String scope, Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        if(Objects.isNull(tenantAuthConnection)){
            throw new BizException("租户Id不存在");
        }

        AssertCheckParams.notNull(tenantAuthConnection.getOaAppId(), ResultDTOEnum.OAAPPID_IS_NULL.getCode(), ResultDTOEnum.OAAPPID_IS_NULL.getMessage());
        url = url.contains(mallUrl) ? url : mallUrl + url;
        String weChatCode = WeChatOAApi.getWeChatCode(url, scope, tenantAuthConnection.getOaAppId());
        return weChatCode;
    }

    @Override
    public String getOaOpenId(String code, Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        if(Objects.isNull(tenantAuthConnection)){
            throw new BizException("租户Id不存在");
        }

        AssertCheckParams.notNull(tenantAuthConnection.getOaAppId(), ResultDTOEnum.OAAPPID_IS_NULL.getCode(), ResultDTOEnum.OAAPPID_IS_NULL.getMessage());

        JSONObject jsonObject = WeChatOAApi.getOpenId(code, tenantAuthConnection.getOaAppId(), tenantAuthConnection.getOaAppSecret());
        return (String)jsonObject.get("openid");
    }

    @Override
    public ResultDTO<LoginVO> getWeChatPhoneLogin(WeChatPhoneLoginDTO loginDTO) {
        AssertCheckParams.isTrue(StringUtils.isNotEmpty(loginDTO.getCode()) || StringUtils.isNotEmpty(loginDTO.getPhone()),
                ResultDTOEnum.PARAMETER_MISSING.getCode(), "code和phone二选一必传，不可全部为空");
        LoginVO loginVO = LoginVO.builder().build();
        if (!getUserPhone(loginDTO, loginVO)) {
            return ResultDTO.success(loginVO);
        }

        loginVO = merchantStoreService.loginV2(loginDTO.getAppId(), loginDTO.getWeChatCode(), loginDTO.getPhone(), Boolean.FALSE, null, loginVO);
        return ResultDTO.success(loginVO);
    }

    /**
     * 获取用户手机号
     *
     * @param loginDTO
     * @param loginVO
     * @return
     */
    private boolean getUserPhone(WeChatPhoneLoginDTO loginDTO, LoginVO loginVO) {
        if (StringUtils.isNotEmpty(loginDTO.getPhone())) {
            loginVO.setPhone(loginDTO.getPhone());
            return true;
        }
        // 微信获取手机号
        UserLiteAuthVo userLiteAuthVo = UserLiteAuthVo.builder().appId(loginDTO.getAppId()).code(loginDTO.getCode()).build();
        UserPhoneInfoVO userPhoneInfoVO = getUserPhoneNumber(userLiteAuthVo).getData();
        Optional.ofNullable(userPhoneInfoVO).ifPresent(vo -> loginVO.setPhone(vo.getPhone()));
        if (StringUtils.isEmpty(loginVO.getPhone())) {
            return false;
        }
        return true;
    }
}
