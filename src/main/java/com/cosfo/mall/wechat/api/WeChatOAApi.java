package com.cosfo.mall.wechat.api;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.ApiConstant;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.wechat.bean.user.UserPhoneInfo;
import com.cosfo.mall.wechat.client.LocalHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/28
 */
@Component
@Slf4j
public class WeChatOAApi {


    /**
     *  获取授权code
     *  switch (scope)
     *  {
     *  case snsapi_base : 静默授权; break;
     *  case snsapi_userinfo : 主动授权; break;
     *  }
     *  强制主动授权
     * @param url
     * @param scope
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getWeChatCode(String url, String scope, String appId) {
        String code = null;
        try {
            code = "https://open.weixin.qq.com/connect/oauth2/authorize?"
                    + "appid="+ URLEncoder.encode(appId,"UTF-8")
                    +"&redirect_uri="+URLEncoder.encode(url,"UTF-8")
                    + "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            log.error("获取微信授权码失败", e);
        }
        return code;
    }

    /**
     * 获取openID
     *
     * @param code
     * @return
     */
    public static JSONObject getOpenId(String code, String appId, String appSecret) {
        String url = String.format(ApiConstant.WX_USER_BASE_INFO, appId, appSecret, code);

        HttpUriRequest httpUriRequest = RequestBuilder.get()
                .setUri(url)
                .build();
        JSONObject userInfo = LocalHttpClient.executeJsonResult(httpUriRequest, JSONObject.class);

        log.info(String.valueOf(userInfo));
        if(!StringUtils.isBlank(userInfo.getString("errcode"))){
            log.error("获取openid错误");
            return null;
        }

        return userInfo;
    }
}
