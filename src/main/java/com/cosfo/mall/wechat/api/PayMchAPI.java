package com.cosfo.mall.wechat.api;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.PemUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.HuiFuPaymentConstant;
import com.cosfo.mall.common.constant.WechatPaymentConstant;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.*;
import com.cosfo.mall.order.model.dto.*;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.payment.model.request.WechatPaymentRequest;
import com.cosfo.mall.payment.model.request.WechatRefundExecuteRequest;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.bean.paymch.*;
import com.cosfo.mall.wechat.bean.profitsharing.*;
import com.cosfo.mall.wechat.bean.refund.RefundResult;
import com.cosfo.mall.wechat.client.LocalHttpClient;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.*;

@Slf4j
public class PayMchAPI extends BaseAPI {

    private static ThreadLocal<Boolean> sandboxnew = new ThreadLocal<Boolean>();

    /**
     * 仿真测试 开始
     *
     * @since 2.8.6
     */
    public static void sandboxnewStart() {
        sandboxnew.set(true);
    }

    /**
     * 仿真测试 结束
     *
     * @since 2.8.6
     */
    public static void sandboxnewEnd() {
        sandboxnew.set(null);
    }

    /**
     * 获取支付base URI路径
     *
     * @return baseURI
     */
    private static String baseURI() {
        if (sandboxnew.get() == null) {
            return MCH_URI;
        } else {
            return MCH_URI + "/sandboxnew";
        }
    }

    /**
     * 获取支付汇付天下base URI路径
     *
     * @return baseURI
     */
    public static String huiFuBaseURI() {
        return HUIFU_URI;
    }

    /**
     * 获取仿真测试验签秘钥
     *
     * @param mch_id mch_id
     * @param key    key
     * @return sandbox_signkey
     * @since 2.8.13
     */
    public static SandboxSignkey sandboxnewPayGetsignkey(String mch_id, String key) {
        MchBaseResult mchBaseResult = new MchBaseResult();
        mchBaseResult.setMch_id(mch_id);
        mchBaseResult.setNonce_str(UUID.randomUUID().toString().replace("-", ""));
        Map<String, String> map = MapUtil.objectToMap(mchBaseResult);
        String sign = SignatureUtil.generateSign(map, mchBaseResult.getSign_type(), key);
        mchBaseResult.setSign(sign);
        String closeorderXML = XMLConverUtil.convertToXML(mchBaseResult);
        HttpUriRequest httpUriRequest = RequestBuilder.post()
                .setHeader(xmlHeader)
                .setUri(MCH_URI + "/sandboxnew/pay/getsignkey")
                .setEntity(new StringEntity(closeorderXML, Charset.forName("utf-8")))
                .build();
        return LocalHttpClient.executeXmlResult(httpUriRequest, SandboxSignkey.class, mchBaseResult.getSign_type(), key);
    }

    /**
     * JSAPI下单 <br>
     *
     * @return TransfersResult
     */
    public static String jsapiPay(WechatPaymentRequest wechatPaymentRequest, PrivateKey wechatPrivateKey, String payCertPath) {
        String json = JSON.toJSONString(wechatPaymentRequest);

        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);
        String signRes = headerSign("POST", "/v3/pay/transactions/jsapi", timeStr, randomStr, json, wechatPrivateKey);
        String authorization = getAuthorizationHeader(wechatPaymentRequest.getMchid(), randomStr, timeStr, signRes, payCertPath);

        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());

        cn.hutool.http.HttpResponse response = HttpUtil.createPost(baseURI() + "/v3/pay/transactions/jsapi")
                .contentType(ContentType.APPLICATION_JSON.toString())
                .header("Authorization", authorization).timeout (3000)
                .body(json).execute();

        String result = response.body();
        log.info("请求微信支付，request：{}，response：{}", JSON.toJSONString(wechatPaymentRequest), response.body());
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new BizException("本次支付交易失败，请稍后再试");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        String prepayId = jsonObject.getString("prepay_id");
        return prepayId;
    }


    /**
     * JSAPI下单 <br>
     *
     * @return TransfersResult
     */
    public static DirectQueryResult queryDirectPayResult(String mchId, String paymentNo, String certPath) {
        PrivateKey privateKey = null;
        try {
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(certPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            throw new DefaultServiceException("证书读取异常：" + e.getMessage(), e);
        }

        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);
        String url = "/v3/pay/transactions/out-trade-no/" + paymentNo + "?mchid=" + mchId;
        String signRes = headerSign("GET", url, timeStr, randomStr, null, privateKey);
        String authorization = getAuthorizationHeader(mchId, randomStr, timeStr, signRes, certPath);

        cn.hutool.http.HttpResponse response = HttpUtil.createGet(baseURI() + url)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .header("Authorization", authorization).timeout (3000)
                .execute();

        String body = response.body();
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, url + "请求失败：" + body);
        }

        return JSONObject.parseObject(body, DirectQueryResult.class);
    }

    /**
     * 申请退款
     *
     * @param request
     * @return
     */
    public static RefundResult refundRequest(WechatRefundExecuteRequest request) {
        String json = JSON.toJSONString(request);
        String mchId = request.getSpMchid();
        String certPath = request.getPayCertPath();

        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);
        PrivateKey privateKey = null;
        try {
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(certPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            log.error("证书读取异常，e={}", e.getMessage(), e);
            throw new ProviderException("退款证书异常");
        }
        String signRes = headerSign("POST", "/v3/refund/domestic/refunds", timeStr, randomStr, json, privateKey);
        String authorization = getAuthorizationHeader(mchId, randomStr, timeStr, signRes, certPath);

        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());

        log.info("微信退款请求参数 json:{}", json);
        long startTime = System.currentTimeMillis();
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(baseURI() + "/v3/refund/domestic/refunds")
                .contentType(ContentType.APPLICATION_JSON.toString())
                .header("Authorization", authorization).timeout (3000)
                .body(json).execute();

        String body = response.body();
        log.info("微信退款请求响应 response.body:{},耗时:{}ms", body, System.currentTimeMillis() - startTime);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException(body);
        }

        return JSONObject.parseObject(body, RefundResult.class);
    }

    /**
     * 微信查询单笔退款结果
     *
     * @param outRefundNo
     * @param mchId
     * @param certPath
     * @return
     */
    public static RefundResult queryWeChatRefundResultRequest(String outRefundNo, String mchId, String certPath) {
        String json = StringUtils.EMPTY;

        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);
        PrivateKey privateKey = null;
        try {
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(certPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            log.error("证书读取异常，e={}", e.getMessage(), e);
            throw new ProviderException(500, "退款证书异常");
        }
        String signRes = headerSign("GET", "/v3/refund/domestic/refunds/" + outRefundNo, timeStr, randomStr, json, privateKey);
        String authorization = getAuthorizationHeader(mchId, randomStr, timeStr, signRes, certPath);

        log.info("微信查询单笔退款结果 outRefundNo:{}", outRefundNo);
        long startTime = System.currentTimeMillis();
        cn.hutool.http.HttpResponse response = HttpUtil.createGet(baseURI() + "/v3/refund/domestic/refunds/" + outRefundNo)
                .header("Authorization", authorization).timeout (3000)
                .body(json).execute();

        String body = response.body();
        log.info("微信查询单笔退款结果 response.body:{},status:{},耗时:{}ms", body, response.getStatus(), System.currentTimeMillis() - startTime);

        // 退款单不存在也要重试
        // {"code":"RESOURCE_NOT_EXISTS","message":"退款单不存在"},status:404
        if (Objects.equals(HttpStatus.HTTP_NOT_FOUND, response.getStatus())) {
            if (StringUtils.isNotEmpty(body) && body.contains("RESOURCE_NOT_EXISTS")) {
                throw new BizException(HttpStatus.HTTP_NOT_FOUND, "/v3/refund/domestic/refunds/" + outRefundNo + "请求失败：" + body);
            }
        }

        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException(500, "/v3/refund/domestic/refunds/" + outRefundNo + "请求失败：" + body);
        }

        return JSONObject.parseObject(body, RefundResult.class);
    }

    /**
     * 请求头签名
     */
    private static String headerSign(String method, String url, long timestamp, String nonceStr, String body, PrivateKey privateKey) {
        String msg = method + "\n"
                + url + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + (body == null ? "" : body) + "\n";
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA);
        sign.setPrivateKey(privateKey);
        return Base64.getEncoder().encodeToString(sign.sign(msg));
    }

    /**
     * 构造请求头数据
     */
    private static String getAuthorizationHeader(String mchId, String randomStr, long timeStr, String signRes, String certPath) {
        InputStream fis = null;
        X509Certificate cert = null;
        try {
            fis = new FileInputStream(certPath + "/apiclient_cert.pem");
            BufferedInputStream bis = new BufferedInputStream(fis);
            CertificateFactory cf = CertificateFactory.getInstance("X509");
            cert = (X509Certificate) cf.generateCertificate(bis);
            cert.checkValidity();
            return "WECHATPAY2-SHA256-RSA2048 mchid=\"" + mchId + "\"," +
                    "nonce_str=\"" + randomStr + "\"," +
                    "signature=\"" + signRes + "\"," +
                    "timestamp=\"" + timeStr + "\"," +
                    "serial_no=\"" + cert.getSerialNumber().toString(16).toUpperCase() + "\"";
        } catch (FileNotFoundException | CertificateException e) {
            throw new ProviderException("证书读取异常：" + e.getMessage(), e);
        }
    }

    /**
     * 汇付请求分账
     *
     * @param profitSharingOrder 分账映射订单
     * @param huiFuPayment       支付记录
     * @param huiFuConfig        汇付配置
     * @return
     */
    public static HuiFuConfirmResponseDTO profitsharingOrders(ProfitSharingOrder profitSharingOrder, HuiFuPayment huiFuPayment, HuiFuConfig huiFuConfig) {

        String url = HuiFuPaymentConstant.HUIFU_CONFIRM;
        HuiFuConfirmDTO huiFuConfirmDTO = new HuiFuConfirmDTO();
        huiFuConfirmDTO.setReq_date(profitSharingOrder.getOrgReqDate());
        huiFuConfirmDTO.setReq_seq_id(profitSharingOrder.getOut_order_no());
        huiFuConfirmDTO.setHuifu_id(huiFuPayment.getHuifuId());
        huiFuConfirmDTO.setOrg_req_date(huiFuPayment.getReqDate());
        huiFuConfirmDTO.setOrg_req_seq_id(huiFuPayment.getReqSeqId());
        huiFuConfirmDTO.setOrg_hf_seq_id(huiFuPayment.getHfSeqId());
        List<AcctInfoDTO> acctInfoDTOS = new ArrayList<>();
        AcctSplitBunchDTO acctSplitBunchDTO = new AcctSplitBunchDTO();
        for (Receiver receiver : profitSharingOrder.getReceivers()) {
            AcctInfoDTO acctInfoDTO = new AcctInfoDTO();
            acctInfoDTO.setHuifu_id(receiver.getHuifuId());
            // 保留两位小数
            acctInfoDTO.setDiv_amt(String.format("%.2f", new BigDecimal(receiver.getAmount()).divide(new BigDecimal(100))));
            acctInfoDTOS.add(acctInfoDTO);
        }
        acctSplitBunchDTO.setAcct_infos(acctInfoDTOS);
        huiFuConfirmDTO.setAcct_split_bunch(acctSplitBunchDTO);
        String signRes = SignatureUtil.bodySign(huiFuConfirmDTO, huiFuConfig.getPrivateKey());
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuConfirmDTO);
        huiFuDTO.setSys_id(huiFuConfig.getSysId());
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSign(signRes);

        log.info("请求分账入参：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.HUIFU_CONFIRM)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("请求分账返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            log.error("请求分账返回结果：{}", body);
            throw new DefaultServiceException(500, url + "请求失败：" + body);
        }

        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuConfirmResponseDTO huiFuConfirmResponseDTO = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuConfirmResponseDTO.class);
        return huiFuConfirmResponseDTO;
    }

    /**
     * 查询分账结果API
     * <p>
     * 发起分账请求后，可调用此接口查询分账结果
     * <p>
     * 注意：
     * <ul>
     *     <li>发起解冻剩余资金请求后，可调用此接口查询解冻剩余资金的结果</li>
     * </ul>
     *
     * @param queryOrderParams the query order params
     * @return the wechat response entity
     */
    public static ProfitSharingOrderResult queryProfitsharingOrder(QueryOrderParams queryOrderParams, String certPath, String mchId) {
        PrivateKey privateKey = null;
        try {
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(certPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            throw new DefaultServiceException("证书读取异常：" + e.getMessage(), e);
        }

        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);
        String url = WechatPaymentConstant.DIRECT_PROFIT_SHARING_QUERT + queryOrderParams.getOutOrderNo() + "?transaction_id=" + queryOrderParams.getTransactionId();
        log.error("查询分账请求信息：{}", url);
        String signRes = headerSign("GET", url, timeStr, randomStr, null, privateKey);
        String authorization = getAuthorizationHeader(mchId, randomStr, timeStr, signRes, certPath);

        cn.hutool.http.HttpResponse response = HttpUtil.createGet(baseURI() + url)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .header("Authorization", authorization).timeout (3000)
                .execute();

        String body = response.body();
        log.info("查询分账返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            log.error("分账结果查询失败：" + body);
            throw new DefaultServiceException(500, url + "请求失败：" + body);
        }

        return JSONObject.parseObject(body, ProfitSharingOrderResult.class);
    }

    /**
     * 查询汇付分账结果
     *
     * @param queryOrderParams
     * @param huiFuConfig
     * @return
     */
    public static HuiFuConfirmResponseDTO queryHuiFuProfitsharingOrder(QueryOrderParams queryOrderParams, HuiFuConfig huiFuConfig) {
        String url = HuiFuPaymentConstant.HUIFU_CONFIRM_QUERY;
        HuiFuPaymentQueryRequestDTO huiFuPaymentQueryRequestDTO = new HuiFuPaymentQueryRequestDTO();
        huiFuPaymentQueryRequestDTO.setOrgReqDate(queryOrderParams.getOrgReqDate());
        // huiFu payment
        huiFuPaymentQueryRequestDTO.setHuiFuId(queryOrderParams.getHuiFuId());
        huiFuPaymentQueryRequestDTO.setOrgReqSeqId(queryOrderParams.getOutOrderNo());
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentQueryRequestDTO);
        String sign = SignatureUtil.bodySign(huiFuDTO.getData(), huiFuConfig.getPrivateKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuConfig.getSysId());
        log.info("汇付支付查询请求参数：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + url)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("汇付支付查询返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, url + "请求失败：" + body);
        }
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuConfirmResponseDTO huiFuConfirmResponseDTO = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuConfirmResponseDTO.class);
        return huiFuConfirmResponseDTO;
    }


    /**
     * 汇付支付结果查询
     *
     * @param huiFuPayment
     * @param tenantAuthConnection
     * @param huiFuConfig
     * @return
     */
    public static HuiFuiPaymentReceive queryHuiFuPaymentStat(HuiFuPayment huiFuPayment, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig) {
        String url = HuiFuPaymentConstant.HUIFU_QUERY;
        HuiFuPaymentQueryRequestDTO huiFuPaymentQueryRequestDTO = new HuiFuPaymentQueryRequestDTO();
        huiFuPaymentQueryRequestDTO.setOrgReqDate(huiFuPayment.getReqDate());
        huiFuPaymentQueryRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
        huiFuPaymentQueryRequestDTO.setOrgHfSeqId(huiFuPayment.getHfSeqId());
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentQueryRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPayment.getHuifuId());
        log.info("汇付支付查询请求参数：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.HUIFU_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("汇付支付查询返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException(500, url + "请求失败：" + body);
        }
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuiPaymentReceive.class);
        return huiFuPaymentReceive;
    }

    public static HuiFuPaymentRefundResponseDTO HuiFuPaymentRefund(HuiFuPaymentRefundRequestDTO huiFuPaymentRefundRequestDTO, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig) {
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentRefundRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPaymentRefundRequestDTO.getHuifu_id());
        long startTime = System.currentTimeMillis();
        log.info("请求退款入参 huiFuDTO：{}", JSON.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.HUIFU_REFUND)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("请求退款返回结果：{}，响应耗时:{}ms", body, System.currentTimeMillis() - startTime);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, "请求失败：" + body);
        }
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuPaymentRefundResponseDTO huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuPaymentRefundResponseDTO.class);
        AcctSplitBunchDTO acctSplitBunchDTO = JSONObject.parseObject(huiFuPaymentReceive.getAcct_split_bunch(), AcctSplitBunchDTO.class);
        log.info("请求退款封装的对象：{}", JSONObject.toJSONString(huiFuPaymentReceive));
        log.info("请求退款封装的对象acctSplitBunchDTO：{}", JSONObject.toJSONString(acctSplitBunchDTO));
        return huiFuPaymentReceive;
    }

    /**
     * 查询剩余待分金额API
     * <p>
     * 可调用此接口查询订单剩余待分金额
     *
     * @param transactionId the transaction id
     * @return the wechat response entity
     */
    public static ProfitSharingOrderAmount queryAmounts(String transactionId, String certPath, String mchId) {
        PrivateKey privateKey = null;
        try {
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(certPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            throw new DefaultServiceException("证书读取异常：" + e.getMessage(), e);
        }

        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);
        String url = WechatPaymentConstant.DIRECT_PROFIT_SHARING_AMOUNTS + transactionId + "/amounts";
        String signRes = headerSign("GET", url, timeStr, randomStr, null, privateKey);
        String authorization = getAuthorizationHeader(mchId, randomStr, timeStr, signRes, certPath);

        cn.hutool.http.HttpResponse response = HttpUtil.createGet(baseURI() + url)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .header("Authorization", authorization).timeout (3000)
                .execute();

        String body = response.body();
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, url + "请求失败：" + body);
        }

        return JSONObject.parseObject(body, ProfitSharingOrderAmount.class);
    }

    public static HuiFuPaymentRefundQueryResponseDTO queryHuiFuPaymentRefundResult(HuiFuQueryRefundRequestDTO huiFuPaymentRefundRequestDTO, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig) {
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentRefundRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPaymentRefundRequestDTO.getHuiFuId());
        long startTime = System.currentTimeMillis();
        log.info("查询退款结果入参 huiFuDTO：{}", JSON.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.HUIFU_REFUND_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("查询退款结果响应：{}，响应耗时:{}ms", body, System.currentTimeMillis() - startTime);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, "请求失败：" + body);
        }
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuPaymentRefundQueryResponseDTO huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuPaymentRefundQueryResponseDTO.class);
        log.info("查询退款结果封装的对象：{}", JSONObject.toJSONString(huiFuPaymentReceive));
        return huiFuPaymentReceive;
    }

    /**
     * jsapi交易关单
     *
     * @param outTradeNo
     * @param mchId
     * @param certPath
     * @return
     */
    public static Boolean wechatCloseJsapiPayOrder(String outTradeNo, String mchId, String certPath) {
        Map<String, String> requsetBody = new HashMap();
        requsetBody.put("mchid", mchId);
        String json = JSON.toJSONString(requsetBody);
        long timeStr = System.currentTimeMillis() / 1000;
        String randomStr = RandomUtil.randomString(32);

        PrivateKey privateKey = null;
        try {
            privateKey = PemUtil.readPemPrivateKey(new FileInputStream(certPath + "/apiclient_key.pem"));
        } catch (FileNotFoundException e) {
            throw new ProviderException("证书读取异常：" + e.getMessage(), e);
        }

        String url = WechatPaymentConstant.JSAPI_CLOSE_PAY_ORDER.replace("{out_trade_no}", outTradeNo);
        String signRes = headerSign("POST", url, timeStr, randomStr, json, privateKey);
        String authorization = getAuthorizationHeader(mchId, randomStr, timeStr, signRes, certPath);

        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());

        cn.hutool.http.HttpResponse response = HttpUtil.createPost(baseURI() + url)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .header("Authorization", authorization).timeout (3000)
                .body(json).execute();

        log.info("关闭支付订单{}，返回信息{}", outTradeNo, response.getStatus());
        // 202	USERPAYING	用户支付中，需要输入密码	等待5秒，然后调用被扫订单结果查询API，查询当前订单的不同状态，决定下一步的操作
        if (Objects.equals(HttpStatus.HTTP_ACCEPTED, response.getStatus())) {
            throw new BizException("该订单正在支付中，请稍后重试");
        }

        return Objects.equals(HttpStatus.HTTP_NO_CONTENT, response.getStatus());
    }
}
