package com.cosfo.mall.wechat.api;

import org.apache.http.Header;
import org.apache.http.HttpHeaders;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicHeader;

/**
 * @Package: com.cosfo.api
 * @Description:
 * @author: <EMAIL>
 * @Date: 2022/4/25
 */
public class BaseAPI {
    protected static final String BASE_URI = "https://api.weixin.qq.com";
    protected static final String MEDIA_URI = "http://file.api.weixin.qq.com";
    protected static final String MP_URI = "https://mp.weixin.qq.com";
    protected static final String MCH_URI = "https://api.mch.weixin.qq.com";
    protected static final String OPEN_URI = "https://open.weixin.qq.com";
    protected static final String WIFI_URI = "https://wifi.weixin.qq.com";
    protected static final String HUIFU_URI = "https://api.huifu.com";

    protected static Header jsonHeader = new BasicHeader(HttpHeaders.CONTENT_TYPE,ContentType.APPLICATION_JSON.toString());
    protected static Header xmlHeader = new BasicHeader(HttpHeaders.CONTENT_TYPE,ContentType.APPLICATION_XML.toString());

    protected static final String PARAM_ACCESS_TOKEN = "xianmuSaaS";
    protected static final String PARAM_ACCESS_TOKEN_DE = "access_token";


    protected static final String  componentAppid= "wxb44500a1f8b0eabf";
    protected static final String  componentAppsecret= "8c68741c044d6e1c24efb35275e25854";

}
