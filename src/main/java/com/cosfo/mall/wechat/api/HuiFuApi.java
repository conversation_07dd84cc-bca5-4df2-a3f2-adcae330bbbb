package com.cosfo.mall.wechat.api;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.HuiFuPaymentConstant;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.order.model.dto.*;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.bean.huifu.*;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/10
 */
@Slf4j
public class HuiFuApi extends BaseAPI {

    /**
     * 汇付支付成功code
     */
    public static final String PAY_SUCCESS_CODE = "********";
    /**
     * 汇付支付插件预下单成功code
     */
    public static final String PLUGIN_PAY_SUCCESS_CODE = "********";
    /**
     * 交易未查询到bank code
     */
    public static final String TRADE_NOT_FOUND_BANK_CODE = "40004";

    /**
     * 获取支付汇付天下base URI路径
     *
     * @return baseURI
     */
    public static String huiFuBaseURI() {
        return HUIFU_URI;
    }

    /**
     * v2/merchant/basicdata/query
     */
    public static SettleMentInfoDTO queryMerchantBasicData(TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig) {
        SettleMentInfoDTO settleMentInfoDTO = new SettleMentInfoDTO();
        // 创建请求流水号
        String reqSeqId = "";
        reqSeqId = Global.createHuiFuNo(Global.HUIFU_USER_CODE);
        HuiFuBasicDataRequestDTO huiFuBasicDataRequestDTO = new HuiFuBasicDataRequestDTO();
        huiFuBasicDataRequestDTO.setReq_date(TimeUtils.changeDate2String(new Date(), Constants.HUIFU_DATE));
        huiFuBasicDataRequestDTO.setHuifu_id(tenantAuthConnection.getHuifuId());
        huiFuBasicDataRequestDTO.setReq_seq_id(reqSeqId);
        // 封装汇付请求体
        HuiFuDTO huiFuDTO = getHuiFuDTO(huiFuBasicDataRequestDTO, huiFuConfig);
        // 当前商户商户详细信息
        HuiFuDTO dataAll = huiFuInterface(huiFuDTO, HuiFuPaymentConstant.MERCHANT_QUERY);
        HuiFuBasicDataReceiveDTO huiFuBasicDataReceiveDTO = JSONObject.parseObject(JSONObject.toJSONString(dataAll.getData()), HuiFuBasicDataReceiveDTO.class);

        if (!StringUtils.isEmpty(huiFuBasicDataReceiveDTO.getQry_wx_conf_list())&&!Constants.HUIFU_EMPTY_LIST.equals(huiFuBasicDataReceiveDTO.getQry_wx_conf_list())){
            List<QryWxConfDTO> qryWxConfDTOList = JSONObject.parseArray(huiFuBasicDataReceiveDTO.getQry_wx_conf_list(), QryWxConfDTO.class);
            settleMentInfoDTO.setQryWxConfDTO(qryWxConfDTOList);
        }

        if (!StringUtils.isEmpty(huiFuBasicDataReceiveDTO.getQry_ali_conf_list())&&!Constants.HUIFU_EMPTY_LIST.equals(huiFuBasicDataReceiveDTO.getQry_ali_conf_list())){
            List<QryAliConfDTO> qryAliConfDTOList = JSONObject.parseArray(huiFuBasicDataReceiveDTO.getQry_ali_conf_list(), QryAliConfDTO.class);
            settleMentInfoDTO.setQryAliConfDTO(qryAliConfDTOList);
        }

        // 将返回的数组封装成对应对象
        List<SettleConfigDTO> settleConfigDTOList = new ArrayList<>();
        List<SettleConfigDTO> settleDTO = JSONObject.parseArray(huiFuBasicDataReceiveDTO.getQry_settle_config_list(), SettleConfigDTO.class);
        settleMentInfoDTO.setSettleConfig(settleDTO);

        List<CardInfoDTO> cardInfoDTOList = new ArrayList<>();
        List<CardInfoDTO> cardInfoDTO = JSONObject.parseArray(huiFuBasicDataReceiveDTO.getQry_cash_card_info_list(), CardInfoDTO.class);
        settleMentInfoDTO.setCardInfo(cardInfoDTO);

        return settleMentInfoDTO;
    }

    public static HuiFuDTO getHuiFuDTO(Object data, HuiFuConfig huiFuConfig){
        HuiFuDTO huiFuUserDTO = new HuiFuDTO(data);
        String signUser = SignatureUtil.sign(JSONObject.toJSONString(huiFuUserDTO.getData()),huiFuConfig.getPrivateKey());
//        String signUser = SignatureUtil.sign(JSONObject.toJSONString(huiFuUserDTO.getData()),tenantAuthConnection.getSecretKey());
        huiFuUserDTO.setSign(signUser);
        huiFuUserDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuUserDTO.setSys_id(huiFuConfig.getSysId());
        return huiFuUserDTO;
    }

    /**
     * 汇付通用接口调用类 <br>
     *
     * @return TransfersResult
     */
    public static HuiFuDTO huiFuInterface(HuiFuDTO jsapiPayInfo, String url) {
        String json = JSON.toJSONString(jsapiPayInfo);
        log.error("查询汇付商户详细信息请求参数：{}", json);
        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(HUIFU_URI + url)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(30000)
                .body(json).execute();
        String body = response.body();
        log.error("查询汇付商户详细信息返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, url+"请求失败：" + body);
        }
        return JSONObject.parseObject(body,HuiFuDTO.class);
    }

    /**
     * 汇付交易关单API
     *
     * @param huiFuPayment
     * @param tenantAuthConnection
     * @param huiFuConfig
     * @return
     */
    public static HuiFuPaymentCloseResponseDTO huiFuPaymentClose(HuiFuPayment huiFuPayment, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig) {
        HuiFuPaymentCloseRequestDTO huiFuPaymentCloseRequestDTO = new HuiFuPaymentCloseRequestDTO();
        huiFuPaymentCloseRequestDTO.setReqDate(TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_STRING));
        huiFuPaymentCloseRequestDTO.setReqSeqId(Global.createHuiFuNo(Global.HUIFU_PAY_CLOSE));
        huiFuPaymentCloseRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
        huiFuPaymentCloseRequestDTO.setOrgHfSeqId(huiFuPayment.getHfSeqId());
        huiFuPaymentCloseRequestDTO.setOrgReqDate(huiFuPayment.getReqDate());
        if (StringUtils.isEmpty(huiFuPaymentCloseRequestDTO.getOrgHfSeqId())) {
            huiFuPaymentCloseRequestDTO.setOrgReqSeqId(huiFuPayment.getReqSeqId());
        }
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentCloseRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPayment.getHuifuId());
        log.info("交易关单请求信息：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(HUIFU_URI + HuiFuPaymentConstant.HUIFU_CLOSE)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("交易关单返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new BizException(500, "汇付扫码交易关单请求失败：" + body);
        }

        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuPaymentCloseResponseDTO huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuPaymentCloseResponseDTO.class);
        return huiFuPaymentReceive;
    }

    /**
     * 汇付扫码交易关单查询
     *
     * @param huiFuPayment
     * @param tenantAuthConnection
     * @param huiFuConfig
     * @return
     */
    public static HuiFuPaymentCloseResponseDTO huiFuPaymentCloseQuery(HuiFuPayment huiFuPayment, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig) {
        HuiFuPaymentCloseRequestDTO huiFuPaymentCloseRequestDTO = new HuiFuPaymentCloseRequestDTO();
        huiFuPaymentCloseRequestDTO.setReqDate(TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_STRING));
        huiFuPaymentCloseRequestDTO.setReqSeqId(Global.createHuiFuNo(Global.HUIFU_PAY_CLOSE));
        huiFuPaymentCloseRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
        huiFuPaymentCloseRequestDTO.setOrgHfSeqId(huiFuPayment.getHfSeqId());
        huiFuPaymentCloseRequestDTO.setOrgReqDate(huiFuPayment.getReqDate());
        if (StringUtils.isEmpty(huiFuPaymentCloseRequestDTO.getOrgHfSeqId())) {
            huiFuPaymentCloseRequestDTO.setOrgReqSeqId(huiFuPayment.getReqSeqId());
        }
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentCloseRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPayment.getHuifuId());
        log.info("交易关单查询请求信息：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(HUIFU_URI + HuiFuPaymentConstant.HUIFU_CLOSE_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("交易关单查询返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new BizException(500, "汇付扫码交易关单查询请求失败：" + body);
        }

        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuPaymentCloseResponseDTO huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuPaymentCloseResponseDTO.class);
        return huiFuPaymentReceive;
    }


    /**
     * 账户余额信息查询
     *
     * @param huiFuBalanceQueryDTO
     * @param tenantAuthConnection
     * @param huiFuConfig
     * @return
     */
    public static List<HuiFuAcctInfoDTO> huiFuBalanceQuery(HuiFuBalanceQueryDTO huiFuBalanceQueryDTO, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig){
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuBalanceQueryDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(tenantAuthConnection.getHuifuId());
        log.info("账户余额信息查询请求信息：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(HUIFU_URI+ HuiFuPaymentConstant.HUIFU_BALANCE_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();

        String body = response.body();
        log.info("账户余额信息查询查询返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new BizException(500, "账户余额信息查询请求失败：" + body);
        }

        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuBalanceQueryResultDTO huiFuBalanceQueryResultDTO = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuBalanceQueryResultDTO.class);
        List<HuiFuAcctInfoDTO> huiFuAcctInfoDTOS = JSONObject.parseArray(huiFuBalanceQueryResultDTO.getAcctInfoList(), HuiFuAcctInfoDTO.class);
        return huiFuAcctInfoDTOS;
    }

    /**
     * 交易确认退款
     *
     * @param huiFuConfirmRefundRequestDTO
     * @param tenantAuthConnection
     * @param huiFuConfig
     * @return
     */
    public static HuiFuConfirmRefundResultDTO huiFuConfirmRefund(HuiFuConfirmRefundRequestDTO huiFuConfirmRefundRequestDTO, TenantAuthConnection tenantAuthConnection, HuiFuConfig huiFuConfig){
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuConfirmRefundRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(tenantAuthConnection.getHuifuId());
        log.info("交易确认退款请求信息：{}", JSONObject.toJSONString(huiFuDTO));
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(HUIFU_URI+ HuiFuPaymentConstant.HUIFU_CONFIRM_REFUND)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();
        String body = response.body();
        log.info("交易确认退款返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException(body);
        }

        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(body, HuiFuDTO.class);
        HuiFuConfirmRefundResultDTO huiFuConfirmRefundResultDTO = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuConfirmRefundResultDTO.class);
        return huiFuConfirmRefundResultDTO;
    }

    /**
     * 查询交易结果
     *
     * @param huiFuDTO
     * @return
     */
    public static String queryPaymentResult(HuiFuDTO huiFuDTO) {
        HttpResponse response = HttpUtil.createPost(huiFuBaseURI() + HuiFuPaymentConstant.HUIFU_QUERY)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSONObject.toJSONString(huiFuDTO)).execute();
        String body = response.body();
        log.info("汇付支付查询返回结果：{}", body);
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException("查询支付交易结果异常");
        }
        return body;
    }

    /**
     * 汇付支付正扫接口
     *
     * @param huiFuDTO
     * @return
     */
    public static String huiFuJsPay(HuiFuDTO huiFuDTO) {
        HttpResponse response = HttpUtil.createPost(PayMchAPI.huiFuBaseURI() + "/v2/trade/payment/jspay")
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSON.toJSONString(huiFuDTO)).execute();
        String body = response.body();
        log.info("请求汇付支付，request：{}，response：{}", JSON.toJSONString(huiFuDTO), response.body());
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException("本次支付异常，请稍后再试");
        }
        return body;
    }

    /**
     * 汇付托管插件支付接口
     *
     * @param huiFuDTO
     * @return
     */
    public static String huiFuHostingPay(HuiFuDTO huiFuDTO) {
        HttpResponse response = HttpUtil.createPost(PayMchAPI.huiFuBaseURI() + "/v2/trade/hosting/payment/preorder")
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout(10000)
                .body(JSON.toJSONString(huiFuDTO)).execute();
        String body = response.body();
        log.info("请求汇付托管插件支付，request：{}，response：{}", JSON.toJSONString(huiFuDTO), response.body());
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new ProviderException("本次支付异常，请稍后再试");
        }
        return body;
    }
}
