package com.cosfo.mall.marketing.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.marketing.model.vo.BannerVO;
import com.cosfo.mall.marketing.service.BannerService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/banner")
public class BannerController extends BaseController {

    @Resource
    private BannerService bannerService;

    /**
     * 获取Banner列表信息
     * @param
     * @return
     */
    @RequestMapping(value = "/query/list", method = RequestMethod.POST)
    public CommonResult<List<BannerVO>> getBannerList() {
        return CommonResult.ok(bannerService.getBannerList(getRequestContextInfoDTO()));
    }

}
