package com.cosfo.mall.marketing.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.marketing.model.dto.ActivityInfoDetailDTO;
import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import com.cosfo.mall.marketing.model.vo.BannerVO;
import com.cosfo.mall.marketing.service.ActivityService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: lzh
 * @Date: 2025/10/16 11:01
 * @Param:
 * @Return:
 * @Description:
 **/
@RestController
@RequestMapping("/activity")
public class ActivityController extends BaseController {

    @Resource
    private ActivityService activityService;

    /**
     * 获取活动详情信息
     * @param
     * @return
     */
    @RequestMapping(value = "/query/activity-info", method = RequestMethod.POST)
    public CommonResult<ActivityInfoVO> getActivityInfoDetail(@RequestBody @Valid ActivityInfoDetailDTO req) {
        return CommonResult.ok(activityService.getActivityInfoDetail(getRequestContextInfoDTO(), req));
    }


}
