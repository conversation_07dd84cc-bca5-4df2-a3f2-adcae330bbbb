package com.cosfo.mall.marketing.service.impl;

import com.cosfo.mall.facade.BannerFacade;
import com.cosfo.mall.facade.dto.BannerDTO;
import com.cosfo.mall.facade.input.BannerInput;
import com.cosfo.mall.marketing.model.converter.BannerConvert;
import com.cosfo.mall.marketing.model.vo.BannerVO;
import com.cosfo.mall.marketing.service.BannerService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BannerServiceImpl implements BannerService {

    @Resource
    private BannerFacade bannerFacade;

    @Override
    public List<BannerVO> getBannerList(LoginContextInfoDTO requestContextInfoDTO) {
        BannerInput input = new BannerInput();
        input.setTenantId(requestContextInfoDTO.getTenantId());
        List<BannerDTO> bannerDTOS = bannerFacade.getAllBanner(input);
        List<BannerVO> bannerVOS = BannerConvert.toBannerVOList(bannerDTOS);
        bannerVOS = bannerVOS.stream()
                .sorted(Comparator.comparing(BannerVO::getWeight, Comparator.reverseOrder())
                        .thenComparing(BannerVO::getId, Comparator.reverseOrder())).collect(
                        Collectors.toList());
        return bannerVOS;
    }
}
