package com.cosfo.mall.marketing.service;


import com.cosfo.mall.marketing.model.dto.ItemSaleLimitConfigDTO;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ItemSaleLimitConfigService {

    /**
     * 查询租户下商品的限购配置
     * @param tenantId
     * @param itemIdList
     * @return
     */
    Map<Long, ItemSaleLimitConfigDTO> queryItemSaleLimitConfigMap(Long tenantId, Collection<Long> itemIdList);

}
