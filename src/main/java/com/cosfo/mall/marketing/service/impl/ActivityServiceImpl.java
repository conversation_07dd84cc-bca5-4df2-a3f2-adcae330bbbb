package com.cosfo.mall.marketing.service.impl;

import com.cosfo.mall.facade.input.ActivityDetailQueryInput;
import com.cosfo.mall.facade.marketcenter.ActivityFacade;
import com.cosfo.mall.marketing.model.dto.ActivityInfoDetailDTO;
import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import com.cosfo.mall.marketing.service.ActivityService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: lzh
 * @Date: 2025/10/16 11:02
 * @Param:
 * @Return:
 * @Description:
 **/
@Service
@Slf4j
public class ActivityServiceImpl implements ActivityService {

    @Resource
    private ActivityFacade activityFacade;


    @Override
    public ActivityInfoVO getActivityInfoDetail(LoginContextInfoDTO requestContextInfoDTO, ActivityInfoDetailDTO req) {
        ActivityDetailQueryInput queryInput = new ActivityDetailQueryInput();
        queryInput.setType(req.getType());
        queryInput.setTenantId(requestContextInfoDTO.getTenantId());
        queryInput.setStoreId(requestContextInfoDTO.getStoreId());
        return activityFacade.getActivityDetail(queryInput);
    }
}
