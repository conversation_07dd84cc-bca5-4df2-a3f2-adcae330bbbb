package com.cosfo.mall.marketing.service;

import com.cosfo.mall.marketing.model.dto.ActivityInfoDetailDTO;
import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;

import javax.validation.Valid;

/**
 * @Author: lzh
 * @Date: 2025/10/16 11:01
 * @Param:
 * @Return:
 * @Description:
 **/
public interface ActivityService {

    /**
     * 获取活动详情
     * @param requestContextInfoDTO
     * @param req
     * @return
     */
    ActivityInfoVO getActivityInfoDetail(LoginContextInfoDTO requestContextInfoDTO, ActivityInfoDetailDTO req);
}
