package com.cosfo.mall.marketing.service.impl;

import com.cosfo.mall.marketing.model.converter.ItemSaleLimitConfigConverter;
import com.cosfo.mall.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.mall.marketing.model.po.ItemSaleLimitConfig;
import com.cosfo.mall.marketing.repository.ItemSaleLimitConfigRepository;
import com.cosfo.mall.marketing.service.ItemSaleLimitConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ItemSaleLimitConfigServiceImpl implements ItemSaleLimitConfigService {

    @Resource
    private ItemSaleLimitConfigRepository itemSaleLimitConfigRepository;

    @Override
    public Map<Long, ItemSaleLimitConfigDTO> queryItemSaleLimitConfigMap(Long tenantId, Collection<Long> itemIdList) {
        List<ItemSaleLimitConfig> itemSaleLimitConfigs = itemSaleLimitConfigRepository.queryByTenantIdAndItemIdList(tenantId, itemIdList);
        if (!CollectionUtils.isEmpty(itemSaleLimitConfigs)) {
            return itemSaleLimitConfigs.stream().collect(Collectors.toMap(ItemSaleLimitConfig::getMarketItemId, ItemSaleLimitConfigConverter.INSTANCE::toDTO));
        }
        return Collections.emptyMap();
    }


}
