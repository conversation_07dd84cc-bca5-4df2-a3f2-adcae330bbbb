package com.cosfo.mall.marketing.model.converter;

import com.cosfo.mall.facade.dto.BannerDTO;
import com.cosfo.mall.marketing.model.vo.BannerVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class BannerConvert {
    public static List<BannerVO> toBannerVOList(List<BannerDTO> bannerDTOS) {
        if (CollectionUtils.isEmpty(bannerDTOS)) {
            return Collections.emptyList();
        }
        List<BannerVO> bannerVOS = new ArrayList<>(bannerDTOS.size());
        bannerDTOS.forEach(bannerDTO -> {
            BannerVO bannerVO = new BannerVO();
            bannerVO.setId(bannerDTO.getId());
            bannerVO.setName(bannerDTO.getName());
            bannerVO.setType(bannerDTO.getType());
            bannerVO.setUrl(bannerDTO.getUrl());
            bannerVO.setWeight(bannerDTO.getWeight());
            bannerVO.setDisplayFormat(bannerDTO.getDisplayFormat());
            bannerVO.setEndTime(bannerDTO.getEndTime());
            bannerVO.setStartTime(bannerDTO.getStartTime());
            bannerVO.setShowRule(bannerDTO.getShowRule());
            bannerVO.setTenantId(bannerDTO.getTenantId());
            bannerVOS.add(bannerVO);
        });
        return bannerVOS;
    }
}
