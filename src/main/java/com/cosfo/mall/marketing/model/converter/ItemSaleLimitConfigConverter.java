package com.cosfo.mall.marketing.model.converter;

import com.cosfo.mall.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.mall.marketing.model.po.ItemSaleLimitConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ItemSaleLimitConfigConverter {

    ItemSaleLimitConfigConverter INSTANCE = Mappers.getMapper(ItemSaleLimitConfigConverter.class);

    ItemSaleLimitConfigDTO toDTO(ItemSaleLimitConfig itemSaleLimitConfig);

    ItemSaleLimitConfig toPO(ItemSaleLimitConfigDTO itemSaleLimitConfigDTO);
}
