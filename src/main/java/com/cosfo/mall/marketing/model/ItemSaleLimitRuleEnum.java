package com.cosfo.mall.marketing.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ItemSaleLimitRuleEnum {
    //0不限制,1每次,2每自然日，3每自然周，4每自然月
    NO_LIMIT(0, "无限制"),
    EVERY_TIME(1, "每次"),
    DAILY(2, "每自然日"),
    WEEKLY(3, "每自然周"),
    MONTHLY(4, "每自然月"),
    ;


    private final Integer code;
    private final String desc;

    public static ItemSaleLimitRuleEnum fromCode(Integer code) {
        for (ItemSaleLimitRuleEnum value : ItemSaleLimitRuleEnum.values()) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }

}
