package com.cosfo.mall.marketing.model.vo;

import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2022/12/1
 */
@Data
public class ActivitySkuDetailVO implements Serializable {

    private Long id;

    /**
     * 商品配置id
     */
    private Long itemConfigId;

    /**
     * 活动sku
     */
    private String sku;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整（定价方式）
     */
    //@NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请选择定价方式")
    private Integer roundingMode;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    //@NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请选择价格调整方式")
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    //@NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请填写调价幅度")
    private BigDecimal amount;

    /**
     * 展示排序，数字越小，排序值最高
     */
    private Integer sort;

    /**
     * 计划数量，首次添加的活动库存
     */
    private Integer planQuantity;

    /**
     * 实际数量，最终添加的活动库存
     */
    private Integer actualQuantity;

    /**
     * 冻结数量，已使用的活动库存
     */
    private Integer lockQuantity;

    /**
     * 账号限购类型，0 不限购，1 件数，2 件/日
     */
    private Integer accountLimit;

    /**
     * 限购数量，0表示不限制
     */
    private Integer limitQuantity;

    /**
     * 起购数量，0表示不限制
     */
    private Integer minSaleNum;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 基本信息id
     */
    private Long basicInfoId;

    /**
     * sap sku编码
     */
    private String sapSkuCode;

    /**
     * sap 物料编码
     */
    private String sapMaterialCode;

    /**
     * 一级分类名称
     */
    private String firstClassificationName;

    /**
     * 二级分类
     */
    private String secondClassificationName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品Id
     */
    private Long itemId;

    /**
     * 黑白名单类型 1：白名单  2：黑名单
     */
    private Integer blackWhiteType;
}
