package com.cosfo.mall.marketing.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class BannerVO implements Serializable {
    private static final long serialVersionUID = -8925412174943473644L;

    /**
     *主键id
     */
    private Integer id;

    /**
     * banner名称
     */
    private String name;

    /**
     * banner类型
     */
    private String type;

    /**
     * banner地址
     */
    private String url;

    /**
     * banner超链接地址
     */
    private String link;

    /**
     * banner状态：0不展示，1展示
     */
    private Integer status;

    /**
     * 展示规则
     */
    private Integer showRule;

    /**
     *开始时间
     */
    private LocalDateTime startTime;

    /**
     *结束时间
     */
    private LocalDateTime endTime;

    /**
     * 展示形式,0 图片,1 文字,2 商品卡片
     */
    private Integer displayFormat;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 权重值，值越大权重越高
     */
    private Integer weight;
}
