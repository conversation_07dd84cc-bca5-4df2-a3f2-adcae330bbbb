package com.cosfo.mall.marketing.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: lzh
 * @Date: 2025/10/16 11:03
 * @Param:
 * @Return:
 * @Description:
 **/
@Data
public class ActivityInfoVO {

    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否永久，0 否，1 是
     */
    private Integer isPermanent;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * 活动目的(标签)，0 滞销促销，1 临保清仓，2 新品推广，3 用户召回，4 潜力品推广
     */
    private Integer tag;

    /**
     * 活动备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 商品配置信息
     */
    private ActivityItemConfigVO activityItemConfigVO;
}
