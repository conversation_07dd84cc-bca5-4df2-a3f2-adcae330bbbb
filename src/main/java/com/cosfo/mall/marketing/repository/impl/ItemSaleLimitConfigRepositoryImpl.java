package com.cosfo.mall.marketing.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.marketing.mapper.ItemSaleLimitConfigMapper;
import com.cosfo.mall.marketing.model.po.ItemSaleLimitConfig;
import com.cosfo.mall.marketing.repository.ItemSaleLimitConfigRepository;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品限购配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Service
public class ItemSaleLimitConfigRepositoryImpl extends ServiceImpl<ItemSaleLimitConfigMapper, ItemSaleLimitConfig> implements ItemSaleLimitConfigRepository {

    @Override
    public List<ItemSaleLimitConfig> queryByTenantIdAndItemIdList(Long tenantId, Collection<Long> itemIdList) {
        LambdaQueryWrapper<ItemSaleLimitConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ItemSaleLimitConfig::getTenantId, tenantId);
        lambdaQueryWrapper.in(ItemSaleLimitConfig::getMarketItemId, itemIdList);
        return list(lambdaQueryWrapper);
    }
}
