package com.cosfo.mall.marketing.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.marketing.model.po.ItemSaleLimitConfig;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品限购配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
public interface ItemSaleLimitConfigRepository extends IService<ItemSaleLimitConfig> {

    List<ItemSaleLimitConfig> queryByTenantIdAndItemIdList(Long tenantId, Collection<Long> itemIdList);
}
