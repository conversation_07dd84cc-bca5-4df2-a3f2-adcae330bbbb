package com.cosfo.mall.order.factory;

import com.cosfo.mall.client.openapi.req.PlaceOrderReq;
import com.cosfo.mall.common.config.ChageeConfig;
import com.cosfo.mall.common.exception.code.OpenApiErrorCode;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.tenant.service.SpecialTenantService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 订单配送时间决策服务
 * <AUTHOR>
 * @Date 2025/4/15 15:37
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderDeliveryDateStrategy {

    @Autowired
    private SpecialTenantService specialTenantService;

    @Autowired
    private ChageeConfig chageeConfig;

    /**
     * 计算订单配送时间
     * @param tenantId 租户id
     * @param placeOrderReq 下单请求
     * @param ofcDeliveryDateResp ofc返回的配送时效
     * @return 配送日期
     */
    public LocalDateTime calculateOrderDeliveryDate(Long tenantId, PlaceOrderReq placeOrderReq, DeliveryDateQueryResp ofcDeliveryDateResp){
        // 需要期望配送日期
        boolean needExpectedDeliveryDate = specialTenantService.placeOrderWithExpectedDeliveryDate(tenantId);
        if (!needExpectedDeliveryDate){
            return ofcDeliveryDateResp.getDeliveryDate().atStartOfDay();
        }

        LocalDate expectedDeliveryDate = TimeUtils.parseStringToLocalDate(placeOrderReq.getExpectedDeliveryDate());
        if (expectedDeliveryDate == null){
            throw new BizException("期望配送日期不能为空");
        }

        if (chageeConfig.getForceExpectedDeliveryDateSwitch()){
            return expectedDeliveryDate.atStartOfDay();
        }

        // 期望配送日期不满足仓网最早可配送日期，下单失败
        if (!ofcDeliveryDateResp.getDeliveryDate().equals(expectedDeliveryDate)){
            String failReason = "期望配送日期："+ expectedDeliveryDate + "不匹配目前的最早可配送日期：" + ofcDeliveryDateResp.getDeliveryDate() + "，下单失败。";
            // 飞书告警
            log.info("【SaaS开放平台接口下单失败告警】外部订单号 {} 外部订单额外编号 {} 期望配送日期：{}不匹配仓网最早可配送日期{}，下单失败。",
                    placeOrderReq.getCustomerOrderId(), placeOrderReq.getCustomerOrderExtraNo(), expectedDeliveryDate, ofcDeliveryDateResp.getDeliveryDate());
            throw new BizException(failReason, OpenApiErrorCode.OR_DELIVERY_DATE_ERROR);
        }

        return expectedDeliveryDate.atStartOfDay();
    }


}
