package com.cosfo.mall.order.converter;

import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.vo.MerchantAddressVO;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderAddressConverter {

    OrderAddressConverter INSTANCE = Mappers.getMapper(OrderAddressConverter.class);

    @Mapping(target = "contactPhone", source = "phone")
    @Mapping(target = "contactName", source = "name")
    OrderAddressDTO toAddressDTO(MerchantAddressVO merchantAddressVO);

    OrderAddressDTO merchantAddress2OrderAddress(MerchantAddress merchantAddress);
}
