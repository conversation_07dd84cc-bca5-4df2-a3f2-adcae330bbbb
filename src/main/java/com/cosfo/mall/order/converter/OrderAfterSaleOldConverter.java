//package com.cosfo.mall.order.converter;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.cosfo.erp.client.aftersale.req.OrderAfterSaleCheckTimeReq;
//import com.cosfo.erp.client.aftersale.req.OrderAfterSaleCreateReq;
//import com.cosfo.erp.client.aftersale.req.OrderAfterSaleProcessFinishReq;
//import com.cosfo.erp.client.aftersale.req.OrderAfterSaleQueryReq;
//import com.cosfo.erp.client.aftersale.req.OrderAfterSaleUpdateReq;
//import com.cosfo.erp.client.aftersale.resp.OrderAfterSaleDetailResultResp;
//import com.cosfo.erp.client.aftersale.resp.OrderAfterSaleItemResp;
//import com.cosfo.erp.client.aftersale.resp.OrderAfterSaleResp;
//import com.cosfo.erp.client.aftersale.resp.OrderAfterSaleRuleResp;
//import com.cosfo.erp.client.aftersale.resp.OrderItemAfterSaleDetailInfoResp;
//import com.cosfo.mall.facade.dto.OrderAfterSaleItemDTO;
//import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
//import com.cosfo.mall.facade.dto.OrderAfterSaleRuleDTO;
//import com.cosfo.mall.facade.dto.OrderItemAfterSaleDetailInfoDTO;
//import com.cosfo.mall.facade.input.OrderAfterSaleCheckTimeInput;
//import com.cosfo.mall.facade.input.OrderAfterSaleCreateInput;
//import com.cosfo.mall.facade.input.OrderAfterSaleInput;
//import com.cosfo.mall.facade.input.OrderAfterSaleProcessFinishInput;
//import com.cosfo.mall.order.model.dto.OrderAfterSaleCreateDTO;
//import com.cosfo.summerfarm.model.dto.order.AfterOrderItemVO;
//import com.google.common.collect.Lists;
//import org.springframework.util.CollectionUtils;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//import java.util.Objects;
//
///**
// * @desc
// * <AUTHOR>
// * @date 2022/12/30 11:23
// */
//public class OrderAfterSaleOldConverter {
//
//
//    public static OrderAfterSaleUpdateReq orderAfterSaleInput2UpdateReq(OrderAfterSaleInput orderAfterSaleInput) {
//
//        if (orderAfterSaleInput == null) {
//            return null;
//        }
//        OrderAfterSaleUpdateReq orderAfterSaleUpdateReq = new OrderAfterSaleUpdateReq();
//        orderAfterSaleUpdateReq.setId(orderAfterSaleInput.getId());
//        orderAfterSaleUpdateReq.setTenantId(orderAfterSaleInput.getTenantId());
//        orderAfterSaleUpdateReq.setOrderId(orderAfterSaleInput.getOrderId());
//        orderAfterSaleUpdateReq.setOrderItemId(orderAfterSaleInput.getOrderItemId());
//        orderAfterSaleUpdateReq.setAccountId(orderAfterSaleInput.getAccountId());
//        orderAfterSaleUpdateReq.setStoreId(orderAfterSaleInput.getStoreId());
//        orderAfterSaleUpdateReq.setAfterSaleOrderNo(orderAfterSaleInput.getAfterSaleOrderNo());
//        orderAfterSaleUpdateReq.setAmount(orderAfterSaleInput.getAmount());
//        orderAfterSaleUpdateReq.setAfterSaleType(orderAfterSaleInput.getAfterSaleType());
//        orderAfterSaleUpdateReq.setServiceType(orderAfterSaleInput.getServiceType());
//        orderAfterSaleUpdateReq.setApplyPrice(orderAfterSaleInput.getApplyPrice());
//        orderAfterSaleUpdateReq.setDeliveryFee(orderAfterSaleInput.getDeliveryFee());
//        orderAfterSaleUpdateReq.setTotalPrice(orderAfterSaleInput.getTotalPrice());
//        orderAfterSaleUpdateReq.setReason(orderAfterSaleInput.getReason());
//        orderAfterSaleUpdateReq.setUserRemark(orderAfterSaleInput.getUserRemark());
//        orderAfterSaleUpdateReq.setProofPicture(orderAfterSaleInput.getProofPicture());
//        orderAfterSaleUpdateReq.setStatus(orderAfterSaleInput.getStatus());
//        orderAfterSaleUpdateReq.setHandleRemark(orderAfterSaleInput.getHandleRemark());
//        orderAfterSaleUpdateReq.setOperatorName(orderAfterSaleInput.getOperatorName());
//        orderAfterSaleUpdateReq.setUpdateTime(orderAfterSaleInput.getUpdateTime());
//        orderAfterSaleUpdateReq.setCreateTime(orderAfterSaleInput.getCreateTime());
//        orderAfterSaleUpdateReq.setFinishedTime(orderAfterSaleInput.getFinishedTime());
//        orderAfterSaleUpdateReq.setHandleTime(orderAfterSaleInput.getHandleTime());
//        orderAfterSaleUpdateReq.setRecycleTime(orderAfterSaleInput.getRecycleTime());
//        orderAfterSaleUpdateReq.setResponsibilityType(orderAfterSaleInput.getResponsibilityType());
//        orderAfterSaleUpdateReq.setRecycleDetails(orderAfterSaleInput.getRecycleDetails());
//        orderAfterSaleUpdateReq.setStoreNo(orderAfterSaleInput.getStoreNo());
//        return orderAfterSaleUpdateReq;
//    }
//
//    public static OrderAfterSaleItemDTO orderAfterSaleItemResp2OrderAfterSaleItemDTO(OrderAfterSaleItemResp data) {
//
//        if (data == null) {
//            return null;
//        }
//        OrderAfterSaleItemDTO orderAfterSaleItemDTO = new OrderAfterSaleItemDTO();
//        orderAfterSaleItemDTO.setId(data.getId());
//        orderAfterSaleItemDTO.setItemId(data.getItemId());
//        orderAfterSaleItemDTO.setSkuId(data.getSkuId());
//        orderAfterSaleItemDTO.setSupplierSkuId(data.getSupplierSkuId());
//        orderAfterSaleItemDTO.setAreaItemId(data.getAreaItemId());
//        orderAfterSaleItemDTO.setAmount(data.getAmount());
//        orderAfterSaleItemDTO.setPrice(data.getPrice());
//        orderAfterSaleItemDTO.setTotalPrice(data.getTotalPrice());
//        orderAfterSaleItemDTO.setSupplierTenantId(data.getSupplierTenantId());
//        orderAfterSaleItemDTO.setSupplierName(data.getSupplierName());
//        orderAfterSaleItemDTO.setTitle(data.getTitle());
//        orderAfterSaleItemDTO.setMainPicture(data.getMainPicture());
//        orderAfterSaleItemDTO.setSpecification(data.getSpecification());
//        orderAfterSaleItemDTO.setSpecificationUnit(data.getSpecificationUnit());
//        orderAfterSaleItemDTO.setWarehouseType(data.getWarehouseType());
//        return orderAfterSaleItemDTO;
//    }
//
//    public static AfterOrderItemVO orderAfterSaleItemDTO2AfterOrderItemVO(OrderAfterSaleItemDTO orderAfterSaleItemDTO) {
//
//        if (orderAfterSaleItemDTO == null) {
//            return null;
//        }
//        AfterOrderItemVO afterOrderItemVO = new AfterOrderItemVO();
//        afterOrderItemVO.setId(orderAfterSaleItemDTO.getId());
//        afterOrderItemVO.setItemId(orderAfterSaleItemDTO.getItemId());
//        afterOrderItemVO.setSkuId(orderAfterSaleItemDTO.getSkuId());
//        afterOrderItemVO.setSupplierSkuId(orderAfterSaleItemDTO.getSupplierSkuId());
//        afterOrderItemVO.setAmount(orderAfterSaleItemDTO.getAmount());
//        afterOrderItemVO.setAreaItemId(orderAfterSaleItemDTO.getAreaItemId());
//        afterOrderItemVO.setPrice(orderAfterSaleItemDTO.getPrice());
//        afterOrderItemVO.setTotalPrice(orderAfterSaleItemDTO.getTotalPrice());
//        afterOrderItemVO.setSupplierName(orderAfterSaleItemDTO.getSupplierName());
//        afterOrderItemVO.setSupplierTenantId(orderAfterSaleItemDTO.getSupplierTenantId());
//        afterOrderItemVO.setTitle(orderAfterSaleItemDTO.getTitle());
//        afterOrderItemVO.setMainPicture(orderAfterSaleItemDTO.getMainPicture());
//        afterOrderItemVO.setWarehouseType(orderAfterSaleItemDTO.getWarehouseType());
//        afterOrderItemVO.setSpecification(orderAfterSaleItemDTO.getSpecification());
//        afterOrderItemVO.setSpecificationUnit(orderAfterSaleItemDTO.getSpecificationUnit());
//        return afterOrderItemVO;
//    }
//
//    public static OrderAfterSaleProcessFinishReq orderAfterSaleProcessFinishInput(OrderAfterSaleProcessFinishInput orderAfterSaleProcessFinishInput) {
//
//        if (orderAfterSaleProcessFinishInput == null) {
//            return null;
//        }
//        OrderAfterSaleProcessFinishReq orderAfterSaleProcessFinishReq = new OrderAfterSaleProcessFinishReq();
//        orderAfterSaleProcessFinishReq.setOrderNo(orderAfterSaleProcessFinishInput.getOrderNo());
//        orderAfterSaleProcessFinishReq.setSku(orderAfterSaleProcessFinishInput.getSku());
//        orderAfterSaleProcessFinishReq.setShouldCount(orderAfterSaleProcessFinishInput.getShouldCount());
//        orderAfterSaleProcessFinishReq.setDeliveryType(orderAfterSaleProcessFinishInput.getDeliveryType());
//        orderAfterSaleProcessFinishReq.setShortCount(orderAfterSaleProcessFinishInput.getShortCount());
//        orderAfterSaleProcessFinishReq.setState(orderAfterSaleProcessFinishInput.getState());
//        orderAfterSaleProcessFinishReq.setRemark(orderAfterSaleProcessFinishInput.getRemark());
//        return orderAfterSaleProcessFinishReq;
//    }
//
//    public static OrderAfterSaleRuleDTO orderAfterSaleRuleResp2DTO(OrderAfterSaleRuleResp orderAfterSaleRuleResp) {
//
//        if (orderAfterSaleRuleResp == null) {
//            return null;
//        }
//        OrderAfterSaleRuleDTO orderAfterSaleRuleDTO = new OrderAfterSaleRuleDTO();
//        orderAfterSaleRuleDTO.setId(orderAfterSaleRuleResp.getId());
//        orderAfterSaleRuleDTO.setTenantId(orderAfterSaleRuleResp.getTenantId());
//        orderAfterSaleRuleDTO.setDeliveryType(orderAfterSaleRuleResp.getDeliveryType());
//        orderAfterSaleRuleDTO.setApplyEndTime(orderAfterSaleRuleResp.getApplyEndTime());
//        orderAfterSaleRuleDTO.setAutoFinishedTime(orderAfterSaleRuleResp.getAutoFinishedTime());
//        orderAfterSaleRuleDTO.setDealType(orderAfterSaleRuleResp.getDealType());
//        orderAfterSaleRuleDTO.setCreateTime(orderAfterSaleRuleResp.getCreateTime());
//        orderAfterSaleRuleDTO.setUpdateTime(orderAfterSaleRuleResp.getUpdateTime());
//        return orderAfterSaleRuleDTO;
//    }
//
//    public static OrderAfterSaleResultDTO orderAfterSaleDetailResultResp2ResultDTO(OrderAfterSaleDetailResultResp data) {
//
//        if (data == null) {
//            return null;
//        }
//        OrderAfterSaleResultDTO orderAfterSaleResultDTO = new OrderAfterSaleResultDTO();
//        orderAfterSaleResultDTO.setId(data.getId());
//        orderAfterSaleResultDTO.setTenantId(data.getTenantId());
//        orderAfterSaleResultDTO.setOrderId(data.getOrderId());
//        orderAfterSaleResultDTO.setOrderItemId(data.getOrderItemId());
//        orderAfterSaleResultDTO.setStoreId(data.getStoreId());
//        orderAfterSaleResultDTO.setAccountId(data.getAccountId());
//        orderAfterSaleResultDTO.setAfterSaleOrderNo(data.getAfterSaleOrderNo());
//        orderAfterSaleResultDTO.setAmount(data.getAmount());
//        orderAfterSaleResultDTO.setAfterSaleType(data.getAfterSaleType());
//        orderAfterSaleResultDTO.setServiceType(data.getServiceType());
//        orderAfterSaleResultDTO.setApplyPrice(data.getApplyPrice());
//        orderAfterSaleResultDTO.setTotalPrice(data.getTotalPrice());
//        orderAfterSaleResultDTO.setDeliveryFee(data.getDeliveryFee());
//        orderAfterSaleResultDTO.setReason(data.getReason());
//        orderAfterSaleResultDTO.setUserRemark(data.getUserRemark());
//        orderAfterSaleResultDTO.setProofPicture(data.getProofPicture());
//        orderAfterSaleResultDTO.setStatus(data.getStatus());
//        orderAfterSaleResultDTO.setHandleRemark(data.getHandleRemark());
//        orderAfterSaleResultDTO.setOperatorName(data.getOperatorName());
//        orderAfterSaleResultDTO.setCreateTime(data.getCreateTime());
//        orderAfterSaleResultDTO.setUpdateTime(data.getUpdateTime());
//        orderAfterSaleResultDTO.setFinishedTime(data.getFinishedTime());
//        orderAfterSaleResultDTO.setAccountName(data.getAccountName());
//        orderAfterSaleResultDTO.setTitle(data.getTitle());
//        orderAfterSaleResultDTO.setRecycleTime(data.getRecycleTime());
//        orderAfterSaleResultDTO.setRecycleDetails(data.getRecycleDetails());
//        orderAfterSaleResultDTO.setHandleTime(data.getHandleTime());
//        orderAfterSaleResultDTO.setMainPicture(data.getMainPicture());
//        orderAfterSaleResultDTO.setSpecification(data.getSpecification());
//        orderAfterSaleResultDTO.setSpecificationUnit(data.getSpecificationUnit());
//        orderAfterSaleResultDTO.setOrderAmount(data.getOrderAmount());
//        orderAfterSaleResultDTO.setOrderPrice(data.getOrderPrice());
//        orderAfterSaleResultDTO.setPrice(data.getPrice());
//        orderAfterSaleResultDTO.setWarehouseType(data.getWarehouseType());
//        orderAfterSaleResultDTO.setApplyAccount(data.getApplyAccount());
//        orderAfterSaleResultDTO.setAfterSaleUnit(data.getAfterSaleUnit());
//        orderAfterSaleResultDTO.setMaxAfterSaleAmount(data.getMaxAfterSaleAmount());
//        orderAfterSaleResultDTO.setResponsibilityType(data.getResponsibilityType());
//        orderAfterSaleResultDTO.setOrderType(data.getOrderType());
//        return orderAfterSaleResultDTO;
//    }
//
//    public static OrderAfterSaleQueryReq orderAfterSaleInput2Req(OrderAfterSaleInput orderAfterSaleInput) {
//
//        if (orderAfterSaleInput == null) {
//            return null;
//        }
//        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
//        orderAfterSaleQueryReq.setOrderIds(Objects.isNull(orderAfterSaleInput.getOrderId()) ? null : Collections.singletonList(orderAfterSaleInput.getOrderId()));
//        orderAfterSaleQueryReq.setTenantId(orderAfterSaleInput.getTenantId());
//        orderAfterSaleQueryReq.setOrderIds(orderAfterSaleInput.getOrderIds());
//        orderAfterSaleQueryReq.setOrderItemIds(Objects.isNull(orderAfterSaleInput.getOrderItemId()) ? null : Collections.singletonList(orderAfterSaleInput.getOrderItemId()));
//        orderAfterSaleQueryReq.setStoreId(orderAfterSaleInput.getStoreId());
//        orderAfterSaleQueryReq.setAfterSaleType(orderAfterSaleInput.getAfterSaleType());
//        List<Integer> serviceType = new ArrayList<>();
//        if (Objects.nonNull(orderAfterSaleInput.getServiceType())){
//            serviceType.add(orderAfterSaleInput.getServiceType());
//        }
//        if (CollectionUtil.isNotEmpty(orderAfterSaleInput.getServiceTypes())){
//            serviceType.addAll(orderAfterSaleInput.getServiceTypes());
//        }
//        orderAfterSaleQueryReq.setServiceType(serviceType);
//        ArrayList<Integer> statusList = Lists.newArrayList();
//        if (!CollectionUtils.isEmpty(orderAfterSaleInput.getStatusList())) {
//            statusList.addAll(orderAfterSaleInput.getStatusList());
//        }
//        if (Objects.nonNull(orderAfterSaleInput.getStatus())) {
//            statusList.add(orderAfterSaleInput.getStatus());
//        }
//        orderAfterSaleQueryReq.setStatus(CollectionUtils.isEmpty(statusList) ? null : statusList);
//        orderAfterSaleQueryReq.setResponsibilityType(orderAfterSaleInput.getResponsibilityType());
//        orderAfterSaleQueryReq.setProcessFlag(orderAfterSaleInput.getProcessFlag());
//        orderAfterSaleQueryReq.setAccountId(orderAfterSaleInput.getAccountId());
//        return orderAfterSaleQueryReq;
//    }
//
//    public static OrderAfterSaleResultDTO orderAfterSaleResp2ResultDTO(OrderAfterSaleResp orderAfterSaleResp) {
//
//        if (orderAfterSaleResp == null) {
//            return null;
//        }
//        OrderAfterSaleResultDTO orderAfterSaleResultDTO = new OrderAfterSaleResultDTO();
//        orderAfterSaleResultDTO.setId(orderAfterSaleResp.getId());
//        orderAfterSaleResultDTO.setTenantId(orderAfterSaleResp.getTenantId());
//        orderAfterSaleResultDTO.setOrderId(orderAfterSaleResp.getOrderId());
//        orderAfterSaleResultDTO.setOrderItemId(orderAfterSaleResp.getOrderItemId());
//        orderAfterSaleResultDTO.setStoreId(orderAfterSaleResp.getStoreId());
//        orderAfterSaleResultDTO.setAccountId(orderAfterSaleResp.getAccountId());
//        orderAfterSaleResultDTO.setAfterSaleOrderNo(orderAfterSaleResp.getAfterSaleOrderNo());
//        orderAfterSaleResultDTO.setAmount(orderAfterSaleResp.getAmount());
//        orderAfterSaleResultDTO.setAfterSaleType(orderAfterSaleResp.getAfterSaleType());
//        orderAfterSaleResultDTO.setServiceType(orderAfterSaleResp.getServiceType());
//        orderAfterSaleResultDTO.setApplyPrice(orderAfterSaleResp.getApplyPrice());
//        orderAfterSaleResultDTO.setTotalPrice(orderAfterSaleResp.getTotalPrice());
//        orderAfterSaleResultDTO.setDeliveryFee(orderAfterSaleResp.getDeliveryFee());
//        orderAfterSaleResultDTO.setReason(orderAfterSaleResp.getReason());
//        orderAfterSaleResultDTO.setUserRemark(orderAfterSaleResp.getUserRemark());
//        orderAfterSaleResultDTO.setProofPicture(orderAfterSaleResp.getProofPicture());
//        orderAfterSaleResultDTO.setStatus(orderAfterSaleResp.getStatus());
//        orderAfterSaleResultDTO.setHandleRemark(orderAfterSaleResp.getHandleRemark());
//        orderAfterSaleResultDTO.setOperatorName(orderAfterSaleResp.getOperatorName());
//        orderAfterSaleResultDTO.setCreateTime(orderAfterSaleResp.getCreateTime());
//        orderAfterSaleResultDTO.setUpdateTime(orderAfterSaleResp.getUpdateTime());
//        orderAfterSaleResultDTO.setFinishedTime(orderAfterSaleResp.getFinishedTime());
//        orderAfterSaleResultDTO.setRecycleTime(orderAfterSaleResp.getRecycleTime());
//        orderAfterSaleResultDTO.setRecycleDetails(orderAfterSaleResp.getRecycleDetails());
//        orderAfterSaleResultDTO.setHandleTime(orderAfterSaleResp.getHandleTime());
//        return orderAfterSaleResultDTO;
//    }
//
//    public static OrderAfterSaleCreateReq orderAfterSaleCreateInput2Req(OrderAfterSaleCreateInput input) {
//
//        if (input == null) {
//            return null;
//        }
//        OrderAfterSaleCreateReq orderAfterSaleCreateReq = new OrderAfterSaleCreateReq();
//        orderAfterSaleCreateReq.setId(input.getId());
//        orderAfterSaleCreateReq.setTenantId(input.getTenantId());
//        orderAfterSaleCreateReq.setOrderId(input.getOrderId());
//        orderAfterSaleCreateReq.setOrderItemId(input.getOrderItemId());
//        orderAfterSaleCreateReq.setStoreId(input.getStoreId());
//        orderAfterSaleCreateReq.setAccountId(input.getAccountId());
//        orderAfterSaleCreateReq.setAfterSaleOrderNo(input.getAfterSaleOrderNo());
//        orderAfterSaleCreateReq.setAmount(input.getAmount());
//        orderAfterSaleCreateReq.setAfterSaleType(input.getAfterSaleType());
//        orderAfterSaleCreateReq.setServiceType(input.getServiceType());
//        orderAfterSaleCreateReq.setApplyPrice(input.getApplyPrice());
//        orderAfterSaleCreateReq.setTotalPrice(input.getTotalPrice());
//        orderAfterSaleCreateReq.setDeliveryFee(input.getDeliveryFee());
//        orderAfterSaleCreateReq.setReason(input.getReason());
//        orderAfterSaleCreateReq.setUserRemark(input.getUserRemark());
//        orderAfterSaleCreateReq.setProofPicture(input.getProofPicture());
//        orderAfterSaleCreateReq.setStatus(input.getStatus());
//        orderAfterSaleCreateReq.setHandleRemark(input.getHandleRemark());
//        orderAfterSaleCreateReq.setOperatorName(input.getOperatorName());
//        orderAfterSaleCreateReq.setCreateTime(input.getCreateTime());
//        orderAfterSaleCreateReq.setUpdateTime(input.getUpdateTime());
//        orderAfterSaleCreateReq.setFinishedTime(input.getFinishedTime());
//        orderAfterSaleCreateReq.setHandleTime(input.getHandleTime());
//        orderAfterSaleCreateReq.setRecycleTime(input.getRecycleTime());
//        orderAfterSaleCreateReq.setRecycleDetails(input.getRecycleDetails());
//        orderAfterSaleCreateReq.setResponsibilityType(input.getResponsibilityType());
//        orderAfterSaleCreateReq.setStoreNo(input.getStoreNo());
//        orderAfterSaleCreateReq.setWarehouseType(input.getWarehouseType());
//        orderAfterSaleCreateReq.setReturnWarehouseNo(input.getReturnWarehouseNo());
//        return orderAfterSaleCreateReq;
//    }
//
//    public static OrderAfterSaleCreateReq orderAfterSaleDto2Input(OrderAfterSaleCreateDTO orderAfterSaleDTO) {
//
//        if (orderAfterSaleDTO == null) {
//            return null;
//        }
//        OrderAfterSaleCreateReq orderAfterSaleCreateInput = new OrderAfterSaleCreateReq();
//        orderAfterSaleCreateInput.setId(orderAfterSaleDTO.getId());
//        orderAfterSaleCreateInput.setTenantId(orderAfterSaleDTO.getTenantId());
//        orderAfterSaleCreateInput.setOrderId(orderAfterSaleDTO.getOrderId());
//        orderAfterSaleCreateInput.setOrderItemId(orderAfterSaleDTO.getOrderItemId());
//        orderAfterSaleCreateInput.setStoreId(orderAfterSaleDTO.getStoreId());
//        orderAfterSaleCreateInput.setAccountId(orderAfterSaleDTO.getAccountId());
//        orderAfterSaleCreateInput.setAfterSaleOrderNo(orderAfterSaleDTO.getAfterSaleOrderNo());
//        orderAfterSaleCreateInput.setAmount(orderAfterSaleDTO.getAmount());
//        orderAfterSaleCreateInput.setAfterSaleType(orderAfterSaleDTO.getAfterSaleType());
//        orderAfterSaleCreateInput.setServiceType(orderAfterSaleDTO.getServiceType());
//        orderAfterSaleCreateInput.setApplyPrice(orderAfterSaleDTO.getApplyPrice());
//        orderAfterSaleCreateInput.setTotalPrice(orderAfterSaleDTO.getTotalPrice());
//        orderAfterSaleCreateInput.setDeliveryFee(orderAfterSaleDTO.getDeliveryFee());
//        orderAfterSaleCreateInput.setReason(orderAfterSaleDTO.getReason());
//        orderAfterSaleCreateInput.setUserRemark(orderAfterSaleDTO.getUserRemark());
//        orderAfterSaleCreateInput.setProofPicture(orderAfterSaleDTO.getProofPicture());
//        orderAfterSaleCreateInput.setStatus(orderAfterSaleDTO.getStatus());
//        orderAfterSaleCreateInput.setHandleRemark(orderAfterSaleDTO.getHandleRemark());
//        orderAfterSaleCreateInput.setOperatorName(orderAfterSaleDTO.getOperatorName());
//        orderAfterSaleCreateInput.setCreateTime(orderAfterSaleDTO.getCreateTime());
//        orderAfterSaleCreateInput.setUpdateTime(orderAfterSaleDTO.getUpdateTime());
//        orderAfterSaleCreateInput.setFinishedTime(orderAfterSaleDTO.getFinishedTime());
//        orderAfterSaleCreateInput.setHandleTime(orderAfterSaleDTO.getHandleTime());
//        orderAfterSaleCreateInput.setRecycleTime(orderAfterSaleDTO.getRecycleTime());
//        orderAfterSaleCreateInput.setRecycleDetails(orderAfterSaleDTO.getRecycleDetails());
//        return orderAfterSaleCreateInput;
//    }
//
//    public static OrderItemAfterSaleDetailInfoDTO afterSaleDetailInfoResp2DTO(OrderItemAfterSaleDetailInfoResp resp) {
//
//        if (resp == null) {
//            return null;
//        }
//        OrderItemAfterSaleDetailInfoDTO orderItemAfterSaleDetailInfoDTO = new OrderItemAfterSaleDetailInfoDTO();
//        orderItemAfterSaleDetailInfoDTO.setOrderItemId(resp.getOrderItemId());
//        orderItemAfterSaleDetailInfoDTO.setAmount(resp.getAmount());
//        orderItemAfterSaleDetailInfoDTO.setPrice(resp.getPrice());
//        orderItemAfterSaleDetailInfoDTO.setTotalPrice(resp.getTotalPrice());
//        orderItemAfterSaleDetailInfoDTO.setTitle(resp.getTitle());
//        orderItemAfterSaleDetailInfoDTO.setMainPicture(resp.getMainPicture());
//        orderItemAfterSaleDetailInfoDTO.setSpecification(resp.getSpecification());
//        orderItemAfterSaleDetailInfoDTO.setEnableApplyAmount(resp.getEnableApplyAmount());
//        orderItemAfterSaleDetailInfoDTO.setAfterSaleType(resp.getAfterSaleType());
//        orderItemAfterSaleDetailInfoDTO.setPayType(resp.getPayType());
//        orderItemAfterSaleDetailInfoDTO.setExpireTime(resp.getExpireTime());
//        orderItemAfterSaleDetailInfoDTO.setAfterSaleUnit(resp.getAfterSaleUnit());
//        orderItemAfterSaleDetailInfoDTO.setMaxAfterSaleAmount(resp.getMaxAfterSaleAmount());
//        orderItemAfterSaleDetailInfoDTO.setEnableApplyQuantity(resp.getEnableApplyQuantity());
//        return orderItemAfterSaleDetailInfoDTO;
//    }
//
//    public static OrderAfterSaleCheckTimeReq orderAfterSaleCheckTimeInput2Req(OrderAfterSaleCheckTimeInput input) {
//
//        if (input == null) {
//            return null;
//        }
//        OrderAfterSaleCheckTimeReq orderAfterSaleCheckTimeReq = new OrderAfterSaleCheckTimeReq();
//        orderAfterSaleCheckTimeReq.setWarehouseType(input.getWarehouseType());
//        orderAfterSaleCheckTimeReq.setTenantId(input.getTenantId());
//        orderAfterSaleCheckTimeReq.setFinishedTime(input.getFinishedTime());
//        return orderAfterSaleCheckTimeReq;
//    }
//
//}
