package com.cosfo.mall.order.converter;

import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: monna.chen
 * @Date: 2023/8/23 15:03
 * @Description:
 */
@Mapper
public interface OrderDeliveryConvert {
    OrderDeliveryConvert INSTANCE = Mappers.getMapper(OrderDeliveryConvert.class);

    MerchantDeliveryFeeSnapshotDTO convert2Dto(MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotResp);
}
