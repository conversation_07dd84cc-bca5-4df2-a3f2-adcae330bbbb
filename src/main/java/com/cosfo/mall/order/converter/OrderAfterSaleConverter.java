package com.cosfo.mall.order.converter;

import com.cosfo.mall.common.model.dto.PageDTO;
import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
import com.cosfo.mall.order.model.dto.OrderAfterSaleCreateDTO;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAddReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.github.pagehelper.PageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderAfterSaleConverter {

    OrderAfterSaleConverter INSTANCE = Mappers.getMapper(OrderAfterSaleConverter.class);

    OrderAfterSaleDTO toAfterSaleDTO(OrderAfterSaleCreateDTO createDTO);

    OrderAfterSaleAddReq toAfterSaleReq(OrderAfterSaleCreateDTO createDTO);

    OrderAfterSaleResultDTO orderAfterSaleResp2ResultDTO(OrderAfterSaleResp resp);

    PageDTO<OrderAfterSaleResultDTO> pageInfoToDTO(PageInfo<OrderAfterSaleWithOrderDTO> pageInfo);

    PageDTO<OrderAfterSaleResultDTO> pageInfoToDTONew(PageInfo<OrderAfterSaleWithOrderResp> pageInfo);


    @Mapping(target = "status", expression = "java(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.getShowStatus(withOrderDTO.getStatus(), withOrderDTO.getServiceType()))")
    OrderAfterSaleResultDTO toResultDTO(OrderAfterSaleWithOrderDTO withOrderDTO);

    @Mapping(target = "status", expression = "java(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.getShowStatus(withOrderResp.getStatus(), withOrderResp.getServiceType()))")
    OrderAfterSaleResultDTO toResultDTO(OrderAfterSaleWithOrderResp withOrderResp);


    OrderAfterSaleResultDTO toResultDTO(OrderAfterSaleDTO orderAfterSaleDTO);

    OrderAfterSaleResultDTO toResultDTO(OrderAfterSaleResp orderAfterSaleDTO);
}
