package com.cosfo.mall.order.converter;

import com.cosfo.mall.order.model.vo.OrderItemVO;
import com.cosfo.ordercenter.client.req.OrderItemCreateReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.summerfarm.model.dto.order.AfterOrderItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderItemConverter {
    OrderItemConverter INSTANCE = Mappers.getMapper(OrderItemConverter.class);

    OrderItemCreateReq voToReq(OrderItemVO orderItemVO);

    List<OrderItemVO> dtoToVOList(List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOS);
    @Mapping(target = "price", source = "payablePrice")
    @Mapping(target = "id", source = "orderItemId")
    OrderItemVO dtoToVO(OrderItemAndSnapshotResp orderItemAndSnapshotDTO);

    @Mapping(target = "id", source = "orderItemId")
    com.cosfo.mall.order.model.dto.OrderItemDTO snapshotToItemDTO(OrderItemAndSnapshotResp orderItemAndSnapshotDTO);
    List<com.cosfo.mall.order.model.dto.OrderItemDTO> snapshotToItemDTOList(List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTO);

    AfterOrderItemVO toItemVO(OrderItemSnapshotResp snapshotDTO);

}
