package com.cosfo.mall.order.converter;

import com.cosfo.mall.openapi.model.bo.OrderBO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.model.vo.OrderVO;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderConverter {
    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);

    OrderDTO voToDTO(OrderVO orderVO);

    @Mapping(target = "orderTime", source = "createTime")
    OrderVO dtoToVO(OrderResp dto);

    List<OrderVO> dtoToVO(List<OrderResp> orderDTOS);

    Order dtoToOrder(OrderResp orderDTO);

    OrderDTO boToDTO(OrderBO orderBO);
}
