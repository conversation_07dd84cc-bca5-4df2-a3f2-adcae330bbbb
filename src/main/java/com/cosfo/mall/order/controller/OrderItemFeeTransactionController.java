package com.cosfo.mall.order.controller;

import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionInitDTO;
import com.cosfo.mall.order.service.OrderItemFeeCalculateService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单费用明细
 * <AUTHOR>
 */
@RestController
@RequestMapping("/order-item-fee")
public class OrderItemFeeTransactionController {

    @Resource
    private OrderItemFeeCalculateService orderItemFeeCalculateService;


    /**
     * 初始化指定订单代仓明细
     * @param initDTO
     * @return
     */
    @PostMapping("/upsert/init")
    public CommonResult<Boolean> init(@RequestBody OrderItemFeeTransactionInitDTO initDTO) {
        orderItemFeeCalculateService.initAgentFee(initDTO.getOrderIds());
        return CommonResult.ok(true);
    }
}
