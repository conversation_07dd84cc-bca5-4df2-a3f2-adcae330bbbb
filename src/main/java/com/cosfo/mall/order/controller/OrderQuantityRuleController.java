package com.cosfo.mall.order.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleCheckVO;
import com.cosfo.mall.order.model.dto.OrderPreferentialThresholdQueryDTO;
import com.cosfo.mall.order.model.dto.OrderRuleCheckDTO;
import com.cosfo.mall.order.model.vo.OrderPreferentialRuleVO;
import com.cosfo.mall.order.service.OrderQuantityRuleService;
import com.cosfo.mall.order.service.impl.OrderPreferentialService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 起订量检测接口
 * <AUTHOR>
 */

@RestController
@RequestMapping("/order-rule")
@Slf4j
public class OrderQuantityRuleController extends BaseController {

    @Resource
    private OrderQuantityRuleService orderQuantityRuleService;

    @Resource
    private OrderPreferentialService orderPreferentialService;


    /**
     * 起订量检测
     *
     * @param orderRuleCheckDTO
     * @return
     */
    @PostMapping("/query/quantity/rule/v2")
    public CommonResult<List<OrderQuantityRuleCheckVO>> orderQuantityRuleCheckV2(@RequestBody OrderRuleCheckDTO orderRuleCheckDTO) {
        return CommonResult.ok(orderQuantityRuleService.orderQuantityRuleCheck(orderRuleCheckDTO, getRequestContextInfoDTO()));
    }

    /**
     * 查询购物车优惠门槛
     *
     * @param orderPreferentialThresholdQueryDTO
     * @return
     */
    @PostMapping("/query/preferential/threshold")
    public CommonResult<OrderPreferentialRuleVO> queryOrderPreferentialThreshold(@RequestBody OrderPreferentialThresholdQueryDTO orderPreferentialThresholdQueryDTO) {
        return CommonResult.ok(orderPreferentialService.queryOrderPreferentialThreshold(orderPreferentialThresholdQueryDTO, getRequestContextInfoDTO()));
//        return CommonResult.ok();
    }

}
