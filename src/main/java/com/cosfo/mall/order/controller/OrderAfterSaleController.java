package com.cosfo.mall.order.controller;

import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.model.dto.PageDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
import com.cosfo.mall.order.model.dto.LogisticsDTO;
import com.cosfo.mall.order.model.dto.OrderAfterSaleCreateDTO;
import com.cosfo.mall.order.model.dto.OrderAfterSaleQueryDTO;
import com.cosfo.mall.order.model.vo.OrderAfterSaleQueryVO;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.warehouse.model.vo.FastMallVO;
import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.summerfarm.model.dto.order.AfterOrderItemVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述: 售后控制类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/21
 */
@RestController
@RequestMapping("/order/after/sale")
@Slf4j
public class OrderAfterSaleController extends BaseController {

    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    /**
     * 订单项售后详情
     *
     * @param orderItemId
     * @return
     */
    @RequestMapping(value = "get/orderItem/detail", method = RequestMethod.GET)
    public ResultDTO getOrderItemDetail(Long orderItemId) {
        return orderAfterSaleService.getOrderItemAfterSale(orderItemId, getRequestContextInfoDTO());
    }

    /**
     * 批量获取售后详情明细
     * @param orderItemId
     * @return
     */
    @RequestMapping(value = "batch/orderItem/detail", method = RequestMethod.GET)
    public ResultDTO getOrderItemDetailBatch(Long orderItemId) {
        return orderAfterSaleService.getOrderItemAfterSale(orderItemId, getRequestContextInfoDTO());
    }
    /**
     * 新建售后订单，【配送后售后】调此接口
     *
     * @param afterSaleCreateDTO
     * @return
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public ResultDTO<Long> save(@RequestBody OrderAfterSaleCreateDTO afterSaleCreateDTO) {
        return ResultDTO.success(orderAfterSaleService.save(afterSaleCreateDTO));
    }

    /**
     * 售后列表
     *
     * @param orderAfterSaleDTO
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultDTO<PageDTO<OrderAfterSaleResultDTO>> list(@RequestBody OrderAfterSaleQueryDTO orderAfterSaleDTO) {
        return orderAfterSaleService.list(orderAfterSaleDTO, getRequestContextInfoDTO());
    }

    /**
     * 取消售后单
     *
     * @return
     */
    @PostMapping(value = "/cancel")
    public ResultDTO<Boolean> cancel(Long orderAfterSaleId) {
        return ResultDTO.success(orderAfterSaleService.cancel(orderAfterSaleId));
    }

    /**
     * 售后单详情
     *
     * @return
     */
    @RequestMapping(value = "detail", method = RequestMethod.GET)
    public ResultDTO<List<OrderAfterSaleResultDTO>> detail(Long orderItemId) {
        return orderAfterSaleService.detail(orderItemId, getRequestContextInfoDTO());
    }

    /**
     * 客服订单信息详情
     *
     * @param orderItemId
     * @return
     */
    @RequestMapping(value = "consultation-detail", method = RequestMethod.GET)
    public ResultDTO consultationDetail(Long orderItemId) {
        return orderAfterSaleService.consultationDetail(orderItemId, getRequestContextInfoDTO());
    }

    /**
     * 查询该售后单的订单下所有的售后单
     *
     * @param orderAfterSaleQueryVO
     * @return
     */
    @RequestMapping(value = "queryAllAfterSaleOrder", method = RequestMethod.POST)
    public ResultDTO<List<OrderAfterSaleResp>> queryAllAfterSale(@RequestBody OrderAfterSaleQueryVO orderAfterSaleQueryVO) {
        String afterSaleOrderNo = orderAfterSaleQueryVO.getAfterSaleOrderNo();
        return orderAfterSaleService.queryAllAfterSale(afterSaleOrderNo);
    }

    /**
     * 查询售后商品的item信息
     *
     * @param orderAfterSaleQueryVO
     * @return
     */
    @RequestMapping(value = "query-item-info", method = RequestMethod.POST)
    public ResultDTO<AfterOrderItemVO> queryItemInfo(@RequestBody OrderAfterSaleQueryVO orderAfterSaleQueryVO) {
        String afterSaleOrderNo = orderAfterSaleQueryVO.getAfterSaleOrderNo();
        return orderAfterSaleService.queryItemInfo(afterSaleOrderNo);
    }

    /**
     * 查询售后金额
     *
     * @param orderAfterSaleQueryVO
     * @return
     */
    @RequestMapping(value = "query/refund-price", method = RequestMethod.POST)
    public CommonResult<BigDecimal> queryRefundPrice(@RequestBody OrderAfterSaleQueryVO orderAfterSaleQueryVO) {
        BigDecimal refundPrice = orderAfterSaleService.calculateRefundPrice(orderAfterSaleQueryVO.getOrderItemId(), orderAfterSaleQueryVO.getAmount());
        return CommonResult.ok(refundPrice);
    }

    /**
     * 批量新建售后订单，【配送前退款】调此接口
     *
     * @param afterSaleCreateDTOS
     * @return
     */
    @RequestMapping(value = "addBatch", method = RequestMethod.POST)
    public CommonResult<List<Long>> saveBatch(@RequestBody List<OrderAfterSaleCreateDTO> afterSaleCreateDTOS) {
        return CommonResult.ok(orderAfterSaleService.saveBatch(afterSaleCreateDTOS));
    }

    /**
     * 填写物流信息
     *
     * @param dto
     * @return
     */
    @RequestMapping(value = "addSaleLogistics", method = RequestMethod.POST)
    public CommonResult addSaleLogistics(@RequestBody @Valid LogisticsDTO dto) {
        orderAfterSaleService.addSaleLogistics(dto, getRequestContextInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 修改物流信息
     *
     * @param dto
     * @return
     */
    @RequestMapping(value = "upadteSaleLogistics", method = RequestMethod.POST)
    public CommonResult upadteSaleLogistics(@RequestBody @Valid LogisticsDTO dto) {
        orderAfterSaleService.upadteSaleLogistics(dto, getRequestContextInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 获取物流公司信息
     *
     * @return
     */
    @RequestMapping(value = "queryFastMallList", method = RequestMethod.POST)
    public CommonResult<List<FastMallVO>> queryFastMallList() {
        return CommonResult.ok(orderAfterSaleService.queryFastMallList());
    }

    /**
     * 获取卖家售后收货地址
     *
     * @return
     */
    @RequestMapping(value = "queryOneWarehouseStorage", method = RequestMethod.POST)
    public CommonResult<WarehouseStorageVO> queryOneWarehouseStorage(String afterSaleOrderNo) {
        WarehouseStorageVO warehouseStorageVO = orderAfterSaleService.queryOneWarehouseStorage(afterSaleOrderNo);
        return CommonResult.ok(warehouseStorageVO);
    }
}
