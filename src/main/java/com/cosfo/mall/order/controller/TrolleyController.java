package com.cosfo.mall.order.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.order.model.vo.TrolleyDetailVO;
import com.cosfo.mall.order.model.vo.TrolleyItemVO;
import com.cosfo.mall.order.service.TrolleyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 购物车控制类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@RestController
@RequestMapping("/trolley")
public class TrolleyController extends BaseController {
    @Resource
    private TrolleyService trolleyService;

    /**
     * 获取购物车详情
     *
     * @return
     */
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public ResultDTO<List<TrolleyDetailVO>> get() {
        return trolleyService.get(getRequestContextInfoDTO());
    }

    /**
     * 清空购物车
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/clear", method = RequestMethod.DELETE)
    public ResultDTO clear() {
        return trolleyService.clear(getRequestContextInfoDTO());
    }

    /**
     * 添加商品到购物车
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/add/{itemId}", method = RequestMethod.POST)
    public ResultDTO<Integer> add(@PathVariable Long itemId, Integer amount, Integer type) {
        return trolleyService.add(itemId, amount, type, getRequestContextInfoDTO());
    }


    /**
     * 查询商品在购物车数量
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getByItemIds", method = RequestMethod.POST)
    public ResultDTO<List<TrolleyItemVO>> getByItemIds(@RequestBody List<Long> itemIds) {
        return ResultDTO.success(trolleyService.getByItemIds(itemIds, getRequestContextInfoDTO()));
    }

    /**
     * 删除商品
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    public ResultDTO remove(@RequestBody List<Long> itemIds) {
        return trolleyService.remove(itemIds, getRequestContextInfoDTO());
    }
}
