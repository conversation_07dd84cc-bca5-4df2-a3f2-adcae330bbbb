package com.cosfo.mall.order.controller;

import com.cosfo.mall.common.config.SystemTenantProperties;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.PageResultDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.order.model.dto.OrderAmountRefreshDTO;
import com.cosfo.mall.order.model.dto.OrderDeliveryQueryDTO;
import com.cosfo.mall.order.model.dto.OrderQueryDTO;
import com.cosfo.mall.order.model.dto.PlaceOrderDTO;
import com.cosfo.mall.order.model.vo.*;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 订单管理
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/21
 */
@RestController
@RequestMapping("/order")
@Slf4j
public class OrderController extends BaseController {
    @Resource
    private OrderService orderService;

    @Resource
    private SystemTenantProperties systemTenantProperties;

    /**
     * 预下单
     *
     * @param preOrderDTO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/pre-order", method = RequestMethod.POST)
    public ResultDTO<List<OrderVO>> preOrder(@RequestBody PlaceOrderDTO preOrderDTO) {
        long startTime = System.currentTimeMillis();
        log.info("线程{}：整个预下单开始调用执行时间：{}ms", Thread.currentThread().getName(), startTime);
        ResultDTO resultDTO = orderService.preOrder(preOrderDTO, getRequestContextInfoDTO());
        long endTime = System.currentTimeMillis();
        log.info("线程{}：整个预下单结束调用执行时间：{}ms", Thread.currentThread().getName(), endTime);
        log.info("线程{}：预下单接口本次执行时间：{}ms", Thread.currentThread().getName(), endTime - startTime);
        return resultDTO;
    }

    /**
     * 下单
     *
     * @param preOrderDTOList
     * @return
     */
    @RequestMapping(value = "/place-order", method = RequestMethod.POST)
    public ResultDTO<List<String>> newPlaceOrder(@RequestBody List<PlaceOrderDTO> preOrderDTOList) {
        long startTime = System.currentTimeMillis();
        log.info("线程{}：整个下单开始调用执行时间：{}ms", Thread.currentThread().getName(), startTime);
        ResultDTO<List<String>> resultDTO;
        resultDTO = orderService.newPlaceOrder(preOrderDTOList, getRequestContextInfoDTO());
        long endTime = System.currentTimeMillis();
        log.info("线程{}：整个下单结束调用执行时间：{}ms", Thread.currentThread().getName(), endTime);
        log.info("线程{}：下单接口本次执行时间：{}ms", Thread.currentThread().getName(), endTime - startTime);
        return resultDTO;
    }


    /**
     * 下单前校验订单库存，返回库存不足的商品列表
     * @param preOrderDTOList
     * @return
     */
    @RequestMapping(value = "/check-order-stock", method = RequestMethod.POST)
    public ResultDTO<List<MarketItemVO>> checkOrderStock(@RequestBody List<PlaceOrderDTO> preOrderDTOList) {
        return ResultDTO.success(orderService.checkOrderStock(preOrderDTOList, getRequestContextInfoDTO()));
    }

    /**
     * 查询订单状态
     */
    @RequestMapping(value = "/get/orderStatus", method = RequestMethod.POST)
    public ResultDTO<List<OrderVO>> queryOrderStatus(@RequestBody List<String> orderNos) {
        return ResultDTO.success(orderService.queryOrderStatus(orderNos));
    }


    /**
     * 订单列表
     *
     * @param orderQueryDTO
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public PageResultDTO<List<OrderVO>> list(@RequestBody OrderQueryDTO orderQueryDTO) {
        return orderService.list(orderQueryDTO, getRequestContextInfoDTO());
    }

    /**
     * 订单详情
     *
     * @param orderId 订单编码
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResultDTO<OrderVO> detail(Long orderId) {
        return orderService.detail(orderId, getRequestContextInfoDTO());
    }


    /**
     * 取消商品，查询订单金额
     *
     * @param
     * @return
     */
    @PostMapping("/queryNewOrderAmount")
    public ResultDTO<OrderAmountVO> queryNewOrderAmount(@RequestBody OrderItemVO orderItemVO) {
        return ResultDTO.success(orderService.queryNewOrderAmount(orderItemVO, getRequestContextInfoDTO()));
    }


    /**
     * 确认收货
     *
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    public ResultDTO confirm(Long orderId) {
        Boolean result = orderService.confirm(orderId, getRequestContextInfoDTO());
        if (result) {
            return ResultDTO.success(true);
        } else {
            return ResultDTO.fail(ResultDTOEnum.ORDER_STATUS_ERROR);
        }
    }

    /**
     * 取消订单
     *
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public ResultDTO<Boolean> cancel(Long orderId) {
        return ResultDTO.success(orderService.cancel(orderId, getRequestContextInfoDTO(), OrderCancelTypeEnum.MANUALLY_CANCEL.getType()));
    }

    /**
     * 订单首页
     *
     * @return
     */
    @RequestMapping(value = "/home", method = RequestMethod.GET)
    public ResultDTO<OrderHomeVO> home() {
        return orderService.home(getRequestContextInfoDTO());
    }

    /**
     * 对外提供查询订单接口
     *
     * @return
     */
    @RequestMapping(value = "/get/orderInfo", method = RequestMethod.GET)
    public ResultDTO getOrderInfo(String orderNo) {
        return orderService.getOrderInfo(orderNo, systemTenantProperties.getWurthTenantId());
    }

    /**
     * 根据条件查询订单
     * 鲜沐manage saas 退货入库任务使用
     *
     * @param orderVO
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public ResultDTO<OrderResp> queryOne(@RequestBody OrderVO orderVO) {
        OrderQueryDTO queryDTO = new OrderQueryDTO();
        BeanUtils.copyProperties(orderVO, queryDTO);
        return orderService.queryOne(queryDTO);
    }


    /**
     * 用户关单
     *
     * @param orderId
     * @return
     */
    @PostMapping("/upsert/close-order")
    public ResultDTO<Boolean> closeOrder(Long orderId) {
        return ResultDTO.success(orderService.closeOrder(orderId, getRequestContextInfoDTO()));
    }


    /**
     * 查询配送明细
     *
     * @param orderDeliveryQueryDTO
     * @return
     */
    @PostMapping("/query/delivery-details")
    public CommonResult<List<OrderDeliveryVO>> deliveredDetails(@Valid @RequestBody OrderDeliveryQueryDTO orderDeliveryQueryDTO) {
        List<OrderDeliveryVO> orderDeliveryVOS = orderService.deliveredDetails(orderDeliveryQueryDTO.getOrderId());
        return CommonResult.ok(orderDeliveryVOS);
    }


    /**
     * 查询待配送明细
     *
     * @param orderDeliveryQueryDTO
     * @return
     */
    @PostMapping("/query/undelivered-details")
    public CommonResult<List<OrderDeliveryItemVO>> undeliveredDetails(@Valid @RequestBody OrderDeliveryQueryDTO orderDeliveryQueryDTO) {
        List<OrderDeliveryItemVO> orderDeliveryItemVOS = orderService.pendingDeliveryDetails(orderDeliveryQueryDTO.getOrderId());
        return CommonResult.ok(orderDeliveryItemVOS);
    }

    /**
     * 再来一单
     *
     * @param orderDeliveryQueryDTO
     * @return
     */
    @PostMapping("/again")
    public CommonResult<List<Long>> again(@Valid @RequestBody OrderDeliveryQueryDTO orderDeliveryQueryDTO) {
        List<Long> resultList = orderService.again(orderDeliveryQueryDTO.getOrderId(), getRequestContextInfoDTO());
        return CommonResult.ok(resultList);
    }


    /**
     * 更新订单金额
     * @param dto
     * @return
     */
    @PostMapping("/upsert/refresh-amount")
    public CommonResult<OrderAmountRefreshVO> refreshOrderAmount(@Valid @RequestBody OrderAmountRefreshDTO dto){
        OrderAmountRefreshVO refreshVO = orderService.refreshOrderAmount(dto.getOrderNoList());
        return CommonResult.ok(refreshVO);
    }
}
