package com.cosfo.mall.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.mall.order.model.po.OrderAddress;
import com.cosfo.mall.order.model.vo.OrderAddressDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrderAddressMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderAddress record);

    int insertSelective(OrderAddress record);

    OrderAddress selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderAddress record);

    int updateByPrimaryKey(OrderAddress record);

    /**
     * 查询订单地址信息
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    OrderAddressDTO selectByOrderId(@Param("orderId") Long orderId, @Param("tenantId")Long tenantId);

    /**
     * 查询订单地址信息
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<OrderAddressDTO> selectByOrderIds(@Param("orderIds") List<Long> orderIds, @Param("tenantId")Long tenantId);
}