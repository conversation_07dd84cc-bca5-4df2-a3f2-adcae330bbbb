package com.cosfo.mall.order.mapper;

import com.cosfo.mall.order.model.dto.OrderSelfLiftingDTO;
import com.cosfo.mall.order.model.po.OrderSelfLifting;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OrderSelfLiftingMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(OrderSelfLifting record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(OrderSelfLifting record);

    /**
     * 查询
     * @param id
     * @return
     */
    OrderSelfLifting selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderSelfLifting record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderSelfLifting record);

    /**
     * 自提信息
     * @param orderNo
     * @return
     */
    List<OrderSelfLiftingDTO> selectByOrderNo(String orderNo);

    /**
     * 更新实际自提时间
     * @param orderSelfLifting
     */
    void updateActualTime(OrderSelfLifting orderSelfLifting);
}
