package com.cosfo.mall.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.mall.order.model.dto.OrderItemSkuDTO;
import com.cosfo.mall.order.model.po.OrderItemSnapshot;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrderItemSnapshotMapper {

    int deleteByPrimaryKey(Long id);

    int insert(OrderItemSnapshot record);

    int insertSelective(OrderItemSnapshot record);

    OrderItemSnapshot selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderItemSnapshot record);

    int updateByPrimaryKey(OrderItemSnapshot record);

    @InterceptorIgnore(tenantLine = "on")
    OrderItemSnapshot selectByItemId(@Param("tenantId") Long tenantId, @Param("orderItemId") Long orderItemId);

    @InterceptorIgnore(tenantLine = "on")
    List<OrderItemSnapshot> batchQuery(@Param("tenantId") Long tenantId, @Param("orderItemIds") Collection<Long> orderItemIds);

    @InterceptorIgnore(tenantLine = "on")
    List<OrderItemSkuDTO> batchQuerySkuByOrderItemIds(@Param("orderItemIds") List<Long> orderItemIds);


}