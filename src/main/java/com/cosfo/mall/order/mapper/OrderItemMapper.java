package com.cosfo.mall.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.model.po.OrderItem;
import com.cosfo.mall.order.model.vo.OrderItemVO;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
@Mapper
public interface OrderItemMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderItem record);

    Long insertSelective(OrderItem record);

    OrderItem selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderItem record);

    int updateByPrimaryKey(OrderItem record);

    /**
     * 更新订单项状态
     *
     * @param orderId
     * @param tenantId
     * @param status
     * @return
     */
    Integer updateOrderItemStatus(@Param("tenantId") Long tenantId,
                                  @Param("orderId") Long orderId,
                                  @Param("orderItemId") Long orderItemId,
                                  @Param("status") Integer status,
                                  @Param("afterSaleExpiryTime") LocalDateTime afterSaleExpiryTime);

    /**
     * 更新订单项状态
     *
     * @param orderId
     * @param tenantId
     * @param status
     * @return
     */
    Integer batchUpdateOrderItemStatus(@Param("tenantId") Long tenantId,
                                  @Param("orderId") Long orderId,
                                  @Param("status") Integer status);
    Integer batchUpdateOrderItemStatusAndStoreNo(@Param("tenantId")Long tenantId, @Param("orderId")Long orderId, @Param("status")Integer code, @Param("storeNo")Integer storeNo);

    /**
     * 查询订单项
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<OrderItem> selectByOrderId(@Param("tenantId") Long tenantId,
                                    @Param("orderId") Long orderId);

    /**
     * 查询订单项详情
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<OrderItemVO> queryOrderItemVOByOrderId(@Param("tenantId") Long tenantId,
                                                @Param("orderId") Long orderId);

    /**
     * 查询订单详情
     * @param tenantId 租户id
     * @param orderId 订单id
     * @param orderItemId 订单项id
     * @return 订单项
     */
    OrderItem queryOrderItemById(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId, @Param("orderItemId") Long orderItemId);

    /**
     * 根据订单项Id查询订单项详情
     *
     * @param tenantId
     * @param orderItemId
     * @return
     */
    OrderItemVO queryOrderItemVOById(@Param("tenantId") Long tenantId,@Param("orderItemId") Long orderItemId);

    /**
     * 更新订单项
     * @param updateItem 订单项
     * @return 影响行数
     */
    int updateByOrderId(OrderItem updateItem);

    /**
     * 查询订单项
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<OrderItemDTO> queryByOrderId(@Param("tenantId") Long tenantId,
                                      @Param("orderId") Long orderId);

    /**
     * 根据id批量查询
     * @param ids
     * @return
     */
    List<OrderItem> queryByIds(List<Long> ids);

    /**
     * 更新可申请售后时间
     *
     * @param id
     * @param afterSaleExpiryTime
     */
    void updateSharingTime(@Param("id")Long id,
                           @Param("afterSaleExpiryTime")LocalDateTime afterSaleExpiryTime);

    /**
     * 批量查询
     *
     * @param orderIds
     * @return
     */
    List<OrderItem> selectByOrderIds(@Param("orderIds") Collection<Long> orderIds);

    /**
     * 批量更新订单项状态
     *
     * @param orderIds
     * @param tenantId
     * @param status
     * @return
     */
    Integer batchUpdateOrderItemStatusByOrderIds(@Param("orderIds") List<Long> orderIds,
                             @Param("tenantId") Long tenantId,
                             @Param("status") Integer status);

    /**
     * 查询订单项详情
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<OrderItemDTO> queryOrderItemVOByOrderIds(@Param("tenantId") Long tenantId,
                                                @Param("orderIds") List<Long> orderIds);
}
