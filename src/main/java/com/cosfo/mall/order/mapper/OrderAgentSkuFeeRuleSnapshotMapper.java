package com.cosfo.mall.order.mapper;

import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 描述: 订单代仓品收费快照
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Mapper
public interface OrderAgentSkuFeeRuleSnapshotMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(OrderAgentSkuFeeRuleSnapshot record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(OrderAgentSkuFeeRuleSnapshot record);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    OrderAgentSkuFeeRuleSnapshot selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderAgentSkuFeeRuleSnapshot record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderAgentSkuFeeRuleSnapshot record);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<OrderAgentSkuFeeRuleSnapshot> list);

    /**
     * 查询订单代仓费用
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<OrderAgentSkuFeeRuleSnapshot> queryByTenantIdAndOrderId(@Param("tenantId") Long tenantId,
                                                                 @Param("orderId") Long orderId);

    /**
     * 更新订单命中规则
     * @param tenantId
     * @param orderId
     * @param rule
     * @return
     */
    int updateHitRule(@Param("tenantId") Long tenantId,
                      @Param("orderId") Long orderId,
                      @Param("rule") String rule);

    /**
     * 批量查询订单代仓费用
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<OrderAgentSkuFeeRuleSnapshot> queryByTenantIdAndOrderIds(@Param("tenantId") Long tenantId,
                                                                 @Param("orderIds") Collection<Long> orderIds);
}