package com.cosfo.mall.order.mapper;

import com.cosfo.mall.order.model.po.Trolley;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface TrolleyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Trolley record);

    int insertSelective(Trolley record);

    Trolley selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Trolley record);

    int updateByPrimaryKey(Trolley record);

    /**
     * 添加购物车商品，没有则创建，有则更新
     *
     * @param trolley
     * @return
     */
    int merge(Trolley trolley);

    /**
     * 删除商品
     * @param trolley
     * @return
     */
    int deleteByItemId(Trolley trolley);

    /**
     * 清空购物车
     *
     * @param trolley
     * @return
     */
    int delete(Trolley trolley);

    /**
     * 查询商户购物车商品信息
     *
     * @param tenantId
     * @param storeId
     * @param accountId
     * @return
     */
    List<Trolley> selectByAccountId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId, @Param("accountId") Long accountId);

    /**
     * 获取购物车中某个订单项数据
     *
     * @param tenantId
     * @param storeId
     * @param accountId
     * @param itemId
     * @return
     */
    Trolley selectByItemId(@Param("tenantId") Long tenantId,
                           @Param("storeId") Long storeId,
                           @Param("accountId") Long accountId,
                           @Param("itemId") Long itemId);

    /**
     * 获取购物车中商品列表的数据
     *
     * @param tenantId
     * @param storeId
     * @param accountId
     * @param itemIds
     * @return
     */
    List<Trolley> selectByItemIds(@Param("tenantId") Long tenantId,
                           @Param("storeId") Long storeId,
                           @Param("accountId") Long accountId,
                           @Param("itemIds") List<Long> itemIds);

    /**
     * 批量删除
     *
     * @param tenantId
     * @param storeId
     * @param accountId
     * @param itemIds
     * @return
     */
    Integer batchDelete(@Param("tenantId") Long tenantId,
                        @Param("storeId") Long storeId,
                        @Param("accountId") Long accountId,
                        @Param("itemIds") List itemIds);

    /**
     * 批量新增
     *
     * @param trolleys
     */
    void batchInsert(@Param("trolleys") List<Trolley> trolleys);
}