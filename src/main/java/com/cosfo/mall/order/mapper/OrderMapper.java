package com.cosfo.mall.order.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.order.model.dto.OrderQueryDTO;
import com.cosfo.mall.order.model.dto.OrderSynchronizedDTO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.model.vo.OrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
@Deprecated
@Mapper
public interface OrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Order record);

    Long insertSelective(Order record);

    Order selectByPrimaryKey(Long id);

    Order selectByPrimaryKeyForUpdate(Long id);

    int updateByPrimaryKeySelective(Order record);

    int updateByPrimaryKey(Order record);

    /**
     * 获取订单数量
     *
     * @param status 状态
     * @param tenantId 租户Id
     * @param storeId 门店Id
     * @return
     */
    Integer count(@Param("status") Integer status, @Param("tenantId") Long tenantId,@Param("storeId") Long storeId);

    /**
     * 获取订单列表
     *
     * @param status 状态
     * @param tenantId 租户Id
     * @param storeId 门店Id
     * @return
     */
    List<Order> list(@Param("status") Integer status,
                     @Param("tenantId") Long tenantId,
                     @Param("storeId") Long storeId,
                     @Param("offset") Integer offset,
                     @Param("limit") Integer limit);

    /**
     * 根据订单编号查询订单信息
     *
     * @param orderId
     * @param tenantId
     * @param storeId
     * @return
     */
    Order selectByOrderId(@Param("orderId") Long orderId,
                          @Param("tenantId") Long tenantId,
                          @Param("storeId") Long storeId);

    /**
     * 订单状态
     *
     * @param orderId
     * @param tenantId
     * @param storeId
     * @param status
     * @return
     */
    Integer updateOrderStatus(@Param("orderId") Long orderId,
                              @Param("orderNo") String orderNo,
                              @Param("tenantId") Long tenantId,
                              @Param("storeId") Long storeId,
                              @Param("status") Integer status,
                              @Param("deliveryTime")LocalDate deliveryTime);

    /**
     * 订单状态
     *
     * @param orderId
     * @param status
     * @param deliveryTime
     * @return
     */
    Integer finishOrderWithDeliveryTime(@Param("orderId") Long orderId,
                                        @Param("status") Integer status,
                                        @Param("deliveryTime") LocalDateTime deliveryTime);


    /**
     * 根据订单Id查询订单
     *
     * @param orderId
     * @param tenantId
     * @param storeId
     * @return
     */
    OrderVO queryOrderVOByOrderId(@Param("orderId") Long orderId,
                                  @Param("tenantId") Long tenantId,
                                  @Param("storeId") Long storeId);

    /**
     * 查询订单状态
     *
     * @param orderNos
     * @return
     */
    List<OrderVO> batchQueryByOrderNoList(@Param("orderNos") List<String> orderNos);

    /**
     * 查询订单
     *
     * @param orderIds
     * @param tenantId
     * @param storeId
     * @return
     */
    List<Order> selectByOrderIds(@Param("orderIds") List<Long> orderIds,
                                 @Param("tenantId") Long tenantId,
                                 @Param("storeId") Long storeId);

    /**
     * 只根据订单号查询订单信息
     * @param orderIds
     * @return
     */
    List<Order> selectOnlyByOrderIds(@Param("orderIds") List<Long> orderIds);
    /**
     * 查询当天6点前需要配送的订单
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<Order> queryNeedDeliveryOrder(@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 查询订单数量
     *
     * @param status
     * @param tenantId
     * @param storeId
     * @return
     */
    Integer queryOrderNum(@Param("status") Integer status,
                          @Param("tenantId") Long tenantId,
                          @Param("storeId")Long storeId);

    /**
     * 查询订单数据
     *
     * @param orderNo
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    Order selectByOrderNO(@Param("orderNo") String orderNo,@Param("tenantId")Long tenantId);

    /**
     * 订单完成
     *
     * @param orderIds
     */
    Integer orderFinished(@Param("orderIds")List<Long> orderIds);

    /**
     * 配送时间3天后自动完成
     *
     * @param endTime
     * @return
     */
    List<Order> selectNeedAutoFinishedOrder(@Param("endTime") LocalDateTime endTime);

    /**
     * 查询同步订单信息
     *
     * @return
     */
    List<OrderSynchronizedDTO> querySynchronizedOrderInfo(@Param("time") String time);

    /**
     * 根据条件查询订单
     * @param queryDTO
     * @return
     */
    List<OrderDTO> querySameDeliveryOrders(OrderQueryDTO queryDTO);

    /**
     * 账期支付订单
     *
     * @param orderNo
     * @param tenantId
     */
    void billPayOrder(@Param("orderNo") String orderNo,
                      @Param("tenantId") Long tenantId);

    /**
     * 更新支付方式
     * @param orderId
     */
    void updatePayType(@Param("orderId") Long orderId,
                       @Param("payType") Integer payType,
                       @Param("onlinePayChannel") Integer onlinePayChannel);

    /**
     * 查询订单
     * @param queryDTO
     * @return
     */
    Order queryOne(OrderQueryDTO queryDTO);

    /**
     * 变更订单状态
     *
     * @return
     */
    Integer updateOrderStatusById(@Param("orderId") Long orderId,
                                  @Param("beforeStatus") Integer beforeStatus,
                                  @Param("afterStatus") Integer afterStatus,
                                  @Param("deliveryTime")LocalDateTime deliveryTime);

    /**
     * 批量变更订单状态
     * @param orderIds
     * @param beforeStatus
     * @param afterStatus
     */
    Integer batchUpdateOrderStatusByIds(@Param("orderIds") List<Long> orderIds,
                                     @Param("beforeStatus") Integer beforeStatus,
                                     @Param("afterStatus") Integer afterStatus);

    /**
     * 变更订单配送时间
     *
     * @return
     */
    Integer updateOrderDeliveryTime(@Param("orderId") Long orderId,
                                  @Param("beforeStatus") Integer beforeStatus,
                                  @Param("deliveryTime")LocalDateTime deliveryTime);

    /**
     * 根据订单状态批量查询
     *
     * @param status
     * @return
     */
    @Deprecated
    List<Order> queryOrderByStatusAndWarehouseType2TmsPlan(@Param("status") Integer status,
                                   @Param("warehouseType") Integer warehouseType);

    /**
     * 批量根据单号查询订单
     *
     * @param orderNos
     * @return
     */
    List<Order> queryByOrderNos(Collection<String> orderNos);

    /**
     * 根据组合订单id查询
     *
     * @param combineOrderIds
     * @return
     */
    List<Order> queryByCombineOrderId(@Param("combineOrderIds") Collection<Long> combineOrderIds);

    /**
     * 批量取消订单
     *
     * @param orderIds
     * @param tenantId
     * @param status
     * @return
     */
    Integer batchCancelOrder(@Param("orderIds") List<Long> orderIds,
                             @Param("tenantId") Long tenantId,
                             @Param("status") Integer status);

    /**
     * 根据条件查询订单
     * @param queryDTO
     * @return
     */
    List<OrderDTO> queryEffectiveDeliveryOrders(OrderQueryDTO queryDTO);
}

