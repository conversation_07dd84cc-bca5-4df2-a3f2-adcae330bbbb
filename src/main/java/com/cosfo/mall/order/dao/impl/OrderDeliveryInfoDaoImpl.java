package com.cosfo.mall.order.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.order.dao.OrderDeliveryInfoDao;
import com.cosfo.mall.order.mapper.OrderDeliveryInfoMapper;
import com.cosfo.mall.order.model.po.OrderDeliveryInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单物流配送信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class OrderDeliveryInfoDaoImpl extends ServiceImpl<OrderDeliveryInfoMapper, OrderDeliveryInfo> implements OrderDeliveryInfoDao {

    @Override
    public List<OrderDeliveryInfo> queryByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderDeliveryInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDeliveryInfo::getOrderNo, orderNo);
        return list(queryWrapper);
    }

    @Override
    public boolean batchSave(List<OrderDeliveryInfo> list) {
        return saveBatch(list);
    }
}
