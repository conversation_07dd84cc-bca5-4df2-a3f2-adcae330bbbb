package com.cosfo.mall.order.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionQueryDTO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;

import java.util.List;

/**
 * <p>
 * 订单项费用明细流水 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface OrderItemFeeTransactionDao extends IService<OrderItemFeeTransaction> {


    /**
     * 根据条件查询
     * @param orderItemFeeTransactionQueryDTO
     * @return
     */
    List<OrderItemFeeTransaction> queryByCondition(OrderItemFeeTransactionQueryDTO orderItemFeeTransactionQueryDTO);

    /**
     * 根据订单id查询订单明细费用是否存在
     *
     * @param orderIds
     * @return
     */
    boolean existsByOrderIds(List<Long> orderIds);
}
