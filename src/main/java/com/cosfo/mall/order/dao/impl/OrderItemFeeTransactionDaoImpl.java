package com.cosfo.mall.order.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.order.dao.OrderItemFeeTransactionDao;
import com.cosfo.mall.order.mapper.OrderItemFeeTransactionMapper;
import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionQueryDTO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单项费用明细流水 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
public class OrderItemFeeTransactionDaoImpl extends ServiceImpl<OrderItemFeeTransactionMapper, OrderItemFeeTransaction> implements OrderItemFeeTransactionDao {

    @Override
    public List<OrderItemFeeTransaction> queryByCondition(OrderItemFeeTransactionQueryDTO queryDTO) {
        LambdaQueryWrapper<OrderItemFeeTransaction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryDTO.getOrderId()), OrderItemFeeTransaction::getOrderId, queryDTO.getOrderId());
        queryWrapper.eq(Objects.nonNull(queryDTO.getOrderItemId()), OrderItemFeeTransaction::getOrderItemId, queryDTO.getOrderItemId());
        queryWrapper.eq(Objects.nonNull(queryDTO.getFeeType()), OrderItemFeeTransaction::getFeeType, queryDTO.getFeeType());
        queryWrapper.eq(Objects.nonNull(queryDTO.getTransactionType()), OrderItemFeeTransaction::getTransactionType, queryDTO.getTransactionType());
        return list(queryWrapper);
    }

    @Override
    public boolean existsByOrderIds(List<Long> orderIds) {
        LambdaQueryWrapper<OrderItemFeeTransaction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderItemFeeTransaction::getOrderId, orderIds);
        return count(queryWrapper) > 0;
    }
}
