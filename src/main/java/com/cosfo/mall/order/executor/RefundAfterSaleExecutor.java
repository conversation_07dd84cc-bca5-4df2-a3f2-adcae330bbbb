//package com.cosfo.mall.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleServiceTypeEnum;
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.common.context.OrderAfterSaleTypeEnum;
//import com.cosfo.aftersale.model.dto.OrderAfterSaleDTO;
//import com.cosfo.aftersale.model.dto.OrderAfterSaleQueryDTO;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.mall.common.constant.NumberConstant;
//import com.cosfo.mall.order.mapper.OrderItemMapper;
//import com.cosfo.mall.order.mapper.OrderMapper;
//import com.cosfo.mall.order.model.po.OrderItem;
//import com.cosfo.mall.stock.service.StockService;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.ofc.client.common.message.cosfo.CosfoAfterSaleFinishDeliveryDTO;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.Iterator;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:11
// */
//@Slf4j
//@Component
//public class RefundAfterSaleExecutor extends AfterSaleAbstractExecutor {
//    @Resource
//    private OrderAfterSaleService afterSaleService;
//    @Resource
//    private OrderItemMapper orderItemMapper;
//
//    public boolean validationIsNeedRefundDeliveryFee(OrderAfterSale afterSale) {
//        // 查询所有成功售后单
//        OrderAfterSaleQueryDTO afterSaleQueryDTO = new OrderAfterSaleQueryDTO();
//        afterSaleQueryDTO.setTenantId(afterSale.getTenantId());
//        afterSaleQueryDTO.setOrderId(afterSale.getOrderId());
//        afterSaleQueryDTO.setStatusList(Arrays.asList(NumberConstant.TWO, NumberConstant.THREE, NumberConstant.FOUR, NumberConstant.SEVEN, NumberConstant.EIGHT, NumberConstant.NINE));
//        List<OrderAfterSale> orderAfterSales = afterSaleService.queryAll(afterSaleQueryDTO);
//        // 如果有之前运费算上的  则不算运费
//        boolean flag = orderAfterSales.stream().anyMatch(el -> Objects.nonNull(el.getDeliveryFee()));
//        if (flag) {
//            return Boolean.FALSE;
//        }
//
//        orderAfterSales.add(afterSale);
//        boolean needRefundDeliveryFee = true;
//        // 获取订单号
//        Long orderId = orderAfterSales.get(0).getOrderId();
//        Long tenantId = orderAfterSales.get(0).getTenantId();
//        // 查询所有订单项
//        List<OrderItem> orderItems = orderItemMapper.selectByOrderId(tenantId, orderId);
//        // 判断是否所有订单项已退款
//        for (OrderItem orderItem : orderItems) {
//            Iterator<OrderAfterSale> iterator = orderAfterSales.iterator();
//            Integer applyTotalAmount = NumberConstant.ZERO;
//            while (iterator.hasNext()) {
//                OrderAfterSale orderAfterSale = iterator.next();
//                // 过滤掉换货、补发
//                if (Objects.equals(orderAfterSale.getServiceType(), OrderAfterSaleServiceTypeEnum.EXCHANGE.getType()) || Objects.equals(orderAfterSale.getServiceType(), OrderAfterSaleServiceTypeEnum.RESEND.getType())) {
//                    continue;
//                }
//                if (orderAfterSale.getOrderItemId().equals(orderItem.getId())) {
//                    Integer amount = orderAfterSale.getAmount();
//                    applyTotalAmount += amount;
//                    iterator.remove();
//                }
//            }
//
//            if (!applyTotalAmount.equals(orderItem.getAmount())) {
//                needRefundDeliveryFee = false;
//                break;
//            }
//        }
//
//        return needRefundDeliveryFee;
//    }
//
//    @Override
//    public void afterSaleProcessFinish(OrderAfterSale orderAfterSale, List<CosfoAfterSaleFinishDeliveryDTO> dto) {
//        return;
//    }
//}
