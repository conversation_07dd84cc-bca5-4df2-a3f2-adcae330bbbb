//package com.cosfo.mall.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.mall.common.constant.Constants;
//import com.cosfo.mall.common.constant.NumberConstant;
//import com.cosfo.mall.common.context.DeliveryStateEnum;
//import com.cosfo.mall.common.executor.ExecutorFactory;
//import com.cosfo.mall.facade.OrderAfterSaleServiceFacade;
//import com.cosfo.mall.facade.input.OrderAfterSaleInput;
//import com.cosfo.mall.task.AsyncService;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.ofc.client.common.message.cosfo.CosfoAfterSaleFinishDeliveryDTO;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:13
// */
//@Slf4j
//@Component
//public class ResendAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    private OrderAfterSaleService afterSaleService;
//    @Resource
//    private AsyncService asyncService;
//    @Resource
//    private OrderAfterSaleServiceFacade orderAfterSaleServiceFacade;
//    @Resource
//    private com.cosfo.mall.order.service.OrderAfterSaleService orderAfterSaleService;
//
//    @Override
//    public void afterSaleProcessFinish(OrderAfterSale orderAfterSale, List<CosfoAfterSaleFinishDeliveryDTO> dtos) {
//        // 补发
//        CosfoAfterSaleFinishDeliveryDTO dto = dtos.get(NumberConstant.ZERO);
//        // 补发没有给备注 需要自己写
//        String recycleDetails = Objects.equals(dto.getState(), DeliveryStateEnum.NORMAL.getState()) ? Constants.NORMAL_DELIVERY_TEMPLATE : Constants.ABNORMAL_DELIVERY_TEMPLATE + "应配送" + dto.getShouldCount() + "缺货" + dto.getShortCount();
//        OrderAfterSaleInput input = OrderAfterSaleInput.builder().id(orderAfterSale.getId()).status(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode()).finishedTime(LocalDateTime.now()).recycleDetails(recycleDetails).build();
//        orderAfterSaleServiceFacade.updateByPrimaryKey(input);
//        log.info("售后订单:{}补发完成，状态变更为已完成", orderAfterSale.getAfterSaleOrderNo());
//
//        // 发送钉钉消息提示异常售后订单
//        if (Objects.equals(dto.getState(), DeliveryStateEnum.ABNORMAL.getState())) {
//            ExecutorFactory.messageExecutor.execute(() -> {
//                orderAfterSale.setRecycleDetails(recycleDetails);
//                asyncService.sendNotifyExceptionMessage(orderAfterSale);
//            });
//        }
//
//        orderAfterSaleService.createAfterSaleOrderIfNeed(dtos,orderAfterSale.getServiceType());
//    }
//}
