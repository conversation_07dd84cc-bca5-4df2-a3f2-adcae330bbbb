//package com.cosfo.mall.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.mall.common.constant.Constants;
//import com.cosfo.mall.common.constant.NumberConstant;
//import com.cosfo.mall.common.context.DeliveryStateEnum;
//import com.cosfo.mall.common.executor.ExecutorFactory;
//import com.cosfo.mall.facade.OrderAfterSaleServiceFacade;
//import com.cosfo.mall.facade.input.OrderAfterSaleInput;
//import com.cosfo.mall.order.mapper.OrderMapper;
//import com.cosfo.mall.payment.model.dto.RefundDTO;
//import com.cosfo.mall.payment.service.RefundService;
//import com.cosfo.mall.task.AsyncService;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.ofc.client.common.message.cosfo.CosfoAfterSaleFinishDeliveryDTO;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:11
// */
//@Slf4j
//@Component
//public class ReturnRefundAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    private OrderMapper orderMapper;
//    @Resource
//    private OrderAfterSaleService afterSaleService;
//    @Resource
//    private RefundService refundService;
//    @Resource
//    private AsyncService asyncService;
//    @Resource
//    private OrderAfterSaleServiceFacade orderAfterSaleServiceFacade;
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public void afterSaleProcessFinish(OrderAfterSale orderAfterSale, List<CosfoAfterSaleFinishDeliveryDTO> dtos) {
//        // 退货回收
//        OrderAfterSaleInput update = new OrderAfterSaleInput();
//        update.setId(orderAfterSale.getId());
//        CosfoAfterSaleFinishDeliveryDTO dto = dtos.get(NumberConstant.ZERO);
//        if (Objects.equals(dto.getState(), DeliveryStateEnum.NORMAL.getState())) {
//            update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getCode());
//            update.setRecycleDetails(Constants.NORMAL_RECYCLE_TEMPLATE);
//            orderAfterSaleServiceFacade.updateByPrimaryKey(update);
//            log.info("售后单号：{}回收完毕，开始退款", orderAfterSale.getAfterSaleOrderNo());
//
//            // 成功发起退款
//            // TODO: 2023/1/9 待交易拆出dubbo接口直接调用
//            RefundDTO refundDTO = new RefundDTO();
//            refundDTO.setAfterSaleId(orderAfterSale.getId());
//            refundDTO.setOrderId(orderAfterSale.getOrderId());
//            refundDTO.setTenantId(orderAfterSale.getTenantId());
//            refundDTO.setRefundPrice(orderAfterSale.getTotalPrice());
//            refundService.refundRequest(refundDTO);
//            return;
//        }
//
//        // 回收失败 变更订单状态为待退款
//        update.setStatus(OrderAfterSaleStatusEnum.WAIT_REFUND.getCode());
//        update.setRecycleDetails(Constants.ABNORMAL_RECYCLE_TEMPLATE + dto.getRemark());
//        orderAfterSaleServiceFacade.updateByPrimaryKey(update);
//
//        // 发送钉钉消息提示异常售后订单
//        ExecutorFactory.messageExecutor.execute(() -> {
//            orderAfterSale.setRecycleDetails(Constants.ABNORMAL_RECYCLE_TEMPLATE + dto.getRemark());
//            asyncService.sendNotifyExceptionMessage(orderAfterSale);
//        });
//    }
//}
