//package com.cosfo.mall.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.mall.common.constant.Constants;
//import com.cosfo.mall.common.constant.NumberConstant;
//import com.cosfo.mall.common.context.DeliveryStateEnum;
//import com.cosfo.mall.common.context.SummerfarmDeliveryTypeEnum;
//import com.cosfo.mall.common.executor.ExecutorFactory;
//import com.cosfo.mall.facade.OrderAfterSaleServiceFacade;
//import com.cosfo.mall.facade.input.OrderAfterSaleInput;
//import com.cosfo.mall.task.AsyncService;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.ofc.client.common.message.cosfo.CosfoAfterSaleFinishDeliveryDTO;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:13
// */
//@Slf4j
//@Component
//public class ExchangeAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    private AsyncService asyncService;
//    @Resource
//    private OrderAfterSaleServiceFacade orderAfterSaleServiceFacade;
//    @Resource
//    private com.cosfo.mall.order.service.OrderAfterSaleService orderAfterSaleService;
//
//    @Override
//    public void afterSaleProcessFinish(OrderAfterSale orderAfterSale, List<CosfoAfterSaleFinishDeliveryDTO> dtos) {
//        if (dtos.size() != NumberConstant.TWO) {
//            log.error("售后单号:{}换货数据格式有误", orderAfterSale.getAfterSaleOrderNo());
//            return;
//        }
//
//        StringBuffer recycleDetail = new StringBuffer();
//        for (CosfoAfterSaleFinishDeliveryDTO dto : dtos) {
//            String recycleDetails = "";
//            if (Objects.equals(dto.getDeliveryType(), SummerfarmDeliveryTypeEnum.DELIVERY.getType())) {
//                recycleDetails = Objects.equals(dto.getState(), DeliveryStateEnum.NORMAL.getState()) ? Constants.NORMAL_DELIVERY_TEMPLATE : Constants.ABNORMAL_DELIVERY_TEMPLATE + "应配送" + dto.getShouldCount() + "缺货" + dto.getShortCount() + " ";
//            } else {
//                recycleDetails = Objects.equals(dto.getState(), DeliveryStateEnum.NORMAL.getState()) ? Constants.NORMAL_RECYCLE_TEMPLATE : Constants.ABNORMAL_RECYCLE_TEMPLATE + dto.getRemark() + " ";
//            }
//            recycleDetail.append(recycleDetails);
//        }
//        OrderAfterSaleInput input = OrderAfterSaleInput.builder().id(orderAfterSale.getId()).status(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode()).recycleDetails(recycleDetail.toString()).finishedTime(LocalDateTime.now()).build();
//        orderAfterSaleServiceFacade.updateByPrimaryKey(input);
//
//        log.info("售后单号:{}换货完毕，变更状态为已完成", orderAfterSale.getAfterSaleOrderNo());
//
//        boolean allNormalFlag = dtos.stream().allMatch(el -> Objects.equals(el.getState(), DeliveryStateEnum.NORMAL.getState()));
//        // 发送钉钉消息提示异常售后订单
//        if (!allNormalFlag) {
//            ExecutorFactory.messageExecutor.execute(() -> {
//                orderAfterSale.setRecycleDetails(recycleDetail.toString());
//                asyncService.sendNotifyExceptionMessage(orderAfterSale);
//            });
//        }
//
//        orderAfterSaleService.createAfterSaleOrderIfNeed(dtos, orderAfterSale.getServiceType());
//    }
//}
