//package com.cosfo.mall.order.executor;
//
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import net.summerfarm.ofc.client.common.message.cosfo.CosfoAfterSaleFinishDeliveryDTO;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 15:29
// */
//public abstract class AfterSaleAbstractExecutor {
//
//    /**
//     * 售后对应三方任务处理完毕
//     * @param orderAfterSale
//     */
//    public abstract void afterSaleProcessFinish(OrderAfterSale orderAfterSale, List<CosfoAfterSaleFinishDeliveryDTO> dtos);
//}
