package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/21 15:02
 */
@Data
public class HuiFuConfirmDTO implements Serializable {

    /**
     * 	请求日期
     */
    private String req_date;
    /**
     * 	请求流水号
     */
    private String req_seq_id;
    /**
     * 	商户号
     */
    private String huifu_id;
    /**
     * 	原交易请求日期
     */
    private String org_req_date;
    /**
     * 	原交易请求流水号
     */
    private String org_req_seq_id;
    /**
     * 	原交易全局流水号
     */
    private String org_hf_seq_id;
    /**
     * 	分账对象
     */
    private AcctSplitBunchDTO acct_split_bunch;
    /**
     * 	安全信息
     */
    private String risk_check_data;
    /**
     * 	交易类型 原交易为快捷支付必填：QUICK_PAY，原交易为余额支付必填：ACCT_PAYMENT；
     * 示例值：ACCT_PAYMENT
     */
    private String pay_type;
    /**
     * 	备注
     */
    private String remark;
}
