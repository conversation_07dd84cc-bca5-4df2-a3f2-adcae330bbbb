package com.cosfo.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/22 2:26
 */
@Data
public class HuiFuPaymentCloseRequestDTO implements Serializable {

    /**
     * 请求日期
     */
    @JsonProperty("req_date")
    private String reqDate;
    /**
     * 请求流水号
     */
    @JsonProperty("req_seq_id")
    private String reqSeqId;

    /**
     * 商户号
     */
    @JsonProperty("huifu_id")
    private String huiFuId;
    /**
     * 原交易请求日期
     */
    @JsonProperty("org_req_date")
    private String orgReqDate;
    /**
     * 原交易全局流水号
     */
    @JsonProperty("org_hf_seq_id")
    private String orgHfSeqId;
    /**
     * 原交易请求流水号
     */
    @JsonProperty("org_req_seq_id")
    private String orgReqSeqId;
}
