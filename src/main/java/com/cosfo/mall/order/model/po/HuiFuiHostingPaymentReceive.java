package com.cosfo.mall.order.model.po;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: fansongsong
 * @Date: 2023-11-12
 * @Description:
 */
@Data
public class HuiFuiHostingPaymentReceive {

    /**
     * 业务响应码
     */
    @JsonProperty("resp_code")
    private String respCode;

    /**
     * 业务响应信息
     */
    @JsonProperty("resp_desc")
    private String respDesc;

    /**
     * 请求时间
     */
    @JsonProperty("req_date")
    private String reqDate;

    /**
     * 请求流水号
     */
    @JsonProperty("req_seq_id")
    private String reqSeqId;

    /**
     * 商户号
     */
    @JsonProperty("huifu_id")
    private String huifuId;

    /**
     * 交易金额
     */
    @JsonProperty("trans_amt")
    private String transAmt;

    /**
     * 预下单订单号
     */
    @JsonProperty("pre_order_id")
    private String preOrderId;

    /**
     * 微信小程序返回集合：用于app跳转微信支付
     */
    @JsonProperty("miniapp_data")
    private String miniappData;
}
