package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 描述: saas订单同步到鲜沐DTO类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/7
 */
@Data
public class OrderSynchronizedDTO {
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 门店编号
     */
    private Long storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单时间
     */
    private Date orderTime;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 归属DB
     */
    private String belongDB;
    /**
     * 状态
     */
    private Integer status;
}
