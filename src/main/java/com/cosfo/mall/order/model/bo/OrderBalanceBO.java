package com.cosfo.mall.order.model.bo;

import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.order.model.po.Order;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc 支付订单业务对象
 * <AUTHOR>
 * @date 2023/3/25 17:55
 */
@Data
public class OrderBalanceBO {

    /**
     * 订单
     */
    private Order order;

    /**
     * 该笔支付单的金额
     */
    private BigDecimal needPayPrice;

    /**
     * 扣减类型
     * @see com.cosfo.mall.common.context.MerchantStoreBalanceEnums.DecreaseTypeEnum
     */
    private Integer decreaseType;


}
