package com.cosfo.mall.order.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单项费用明细流水
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Getter
@Setter
@TableName("order_item_fee_transaction")
public class OrderItemFeeTransaction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * itemId编码
     */
    private Long orderItemId;

    /**
     * 明细费用
     */
    private BigDecimal fee;

    /**
     * 费用类型1.代仓费用
     */
    private Integer feeType;

    /**
     * 0.支付，1.退款
     */
    private Integer transactionType;

    /**
     * 售后单id
     */
    private Long orderAfterSaleId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
