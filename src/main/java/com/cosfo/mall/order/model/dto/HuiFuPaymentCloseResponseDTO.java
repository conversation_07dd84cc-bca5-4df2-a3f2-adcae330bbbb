package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/22 2:26
 */
@Data
public class HuiFuPaymentCloseResponseDTO implements Serializable {

    /**
     *业务响应码
     */
    private String resp_code;
    /**
     *业务响应信息
     */
    private String resp_desc;
    /**
     *商户号
     */
    private String huifu_id;
    /**
     *请求时间
     */
    private String req_date;
    /**
     *请求流水号
     */
    private String req_seq_id;

    /**
     *原交易请求日期
     */
    private String org_req_date;

    /**
     *原交易请求流水号
     */
    private String org_req_seq_id;
    /**
     *原交易的全局流水号
     */
    private String org_hf_seq_id;
    /**
     * 原交易状态 P：处理中、S：成功、F：失败；示例值：S
     */
    private String org_trans_stat;
    /**
     * 关单状态 P：处理中、S：成功、F：失败；示例值：S
     */
    private String trans_stat;
}
