package com.cosfo.mall.order.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date : 2022/12/16 2:08
 */
@Data
public class HuiFuPaymentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求流水号
     */
    private String reqSeqId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 通道返回码
     */
    private String bankCode;

    /**
     * js支付信息
     */
    private String payInfo;

    /**
     * 业务响应信息
     */
    private String respDesc;

    /**
     * 通道返回描述
     */
    private String bankMessage;

    /**
     * 全局流水号
     */
    private String hfSeqId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 请求时间
     */
    private String reqDate;

    /**
     * 业务响应码
     */
    private String respCode;

    /**
     * 商户号
     */
    private String huifuId;

    /**
     * 支付单id
     */
    private Long paymentId;

    /**
     * 用户账单上的交易订单号
     */
    private String outTransId;

    /**
     * 用户账单上的商户订单号
     */
    private String partyOrderId;

    /**
     * 是否分账交易；Y: 分账交易， N: 非分账交易
     */
    private String isDiv;

    /**
     * 是否延时交易；Y: 延迟， N: 不延迟
     */
    private String isDelayAcct;

    /**
     * 微信用户唯一标识码
     */
    private String wxUserId;

    /**
     * 微信返回的响应报文
     */
    private String wxResponse;

    /**
     * 支付宝返回的响应报文
     */
    private String alipayResponse;

    /**
     * 数字货币返回的响应报文
     */
    private String dcResponse;

    /**
     * 银联返回的响应报文
     */
    private String unionpayResponse;

    /**
     * 延时标记
     */
    private String delayAcctFlag;

    /**
     * 二维码链接
     */
    private String qrCode;

    /**
     * 账户号
     */
    private String acctId;

    /**
     * 结算金额
     */
    private String settlementAmt;

    /**
     * 手续费扣款标志
     */
    private Integer feeFlag;

    /**
     * 手续费金额
     */
    private String feeAmount;

    /**
     * 汇付侧交易完成时间
     */
    private String transFinshTime;

    /**
     * 入账时间
     */
    private String acctDate;

    /**
     * 分账对象
     */
    private String acctSplitBunch;

    /**
     * 商户终端定位
     */
    private String merDevLocation;

    /**
     * 手续费补贴信息
     */
    private String transFeeAllowanceInfo;

    /**
     * 营销补贴信息
     */
    private String combinedpayData;

    /**
     * 补贴部分的手续费
     */
    private String combinedpayFeeAmt;


}
