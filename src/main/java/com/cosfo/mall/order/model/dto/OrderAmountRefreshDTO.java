package com.cosfo.mall.order.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/15
 */
@Data
public class OrderAmountRefreshDTO {
    /**
     * 订单Id
     */
    @NotEmpty(message = "orderNoList不能为空")
    private List<String> orderNoList;

    /**
     * 支付方式，用于确定手续费
     */
    private Integer payType;
}
