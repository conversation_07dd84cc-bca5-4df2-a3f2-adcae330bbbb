package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderRuleCheckItemDTO implements Serializable {

    /**
     * 商品项id
     */
    private Long itemId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 货品类型
     */
    private Integer goodsType;
}
