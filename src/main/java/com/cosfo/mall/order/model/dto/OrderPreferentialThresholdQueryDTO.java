package com.cosfo.mall.order.model.dto;

import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.model.po.OrderAddress;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-10-15
 */
@Data
public class OrderPreferentialThresholdQueryDTO implements Serializable {
    private static final long serialVersionUID = -5928045514854742962L;

    /**
     * 订单商品明细
     */
    private List<OrderItemDTO> orderItemDTOList;
}
