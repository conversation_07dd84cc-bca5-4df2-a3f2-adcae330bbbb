package com.cosfo.mall.order.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 插入售后物流信息请求参数
 *
 * <AUTHOR>
 * @Date 2023-04-12
 **/
@Data
public class LogisticsDTO implements Serializable {
	private static final long serialVersionUID = -6680497020483587124L;

	@NotBlank(message = "售后单号不能为空")
	private String afterSaleOrderNo;

	@NotNull(message = "售后单id不能为空")
	private Long afterSaleOrderId;
	/**
	 * 配送类型,0:无需物流，1:商家物流配送
	 */
	@NotNull(message = "配送类型不能为空")
	private Integer type;
	/**
	 * 物流公司
	 */
	private String logisticsCompany;

	/**
	 * 物流单号
	 */
	private String logisticsNo;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 图片
	 */
	private String pics;

	/**
	 * 批次号
	 */
	private String batchNo;
}
