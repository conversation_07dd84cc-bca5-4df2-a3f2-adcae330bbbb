package com.cosfo.mall.order.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * order
 * <AUTHOR>
@Data
public class Order implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 店铺Id
     */
    private Long storeId;

    /**
     * 下单账号Id
     */
    private Long accountId;

    /**
     * 供应商Id
     */
    private Long supplierTenantId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 配送仓类型
     */
    private Integer warehouseType;

    /**
     * 应付价格
     */
    private BigDecimal payablePrice;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 支付方式 1,线上支付 2,账期
     */
    private Integer payType;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 可申请售后时间
     */
    private Integer applyEndTime;

    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 组合订单Id
     */
    private Long combineOrderId;

    /**
     * 订单类型 0普通订单 1组合订单
     */
    private Integer orderType;

    /**
     * 订单记录版本，用来区分旧下单链路，旧链路为空，新链路为1
     */
    private Integer orderVersion;

    /**
     * 服务费
     */
    private BigDecimal salesServiceFee;

    /**
     * 服务费费率
     */
    private BigDecimal salesServiceFeeRate;

    private static final long serialVersionUID = 1L;
}