package com.cosfo.mall.order.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述: 售后订单项详情
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/21
 */
@Data
public class OrderItemAfterSaleDetailInfo {
    /**
     * 订单项Id
     */
    private Long orderItemId;
    /**
     * 购买数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 可申请售后数量
     */
    private Integer enableApplyAmount;
    /**
     * 售后类型
     */
    private Integer afterSaleType;

    /**
     * 订单支付类型
     */
    private Integer payType;

    /**
     * 售后到期时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;
}
