package com.cosfo.mall.order.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
@Data
public class HuiFuConfirmRefundResultDTO implements Serializable {
    /**
     * 业务响应码
     */
    @JSONField(name = "resp_code")
    private String respCode;

    /**
     * 业务响应信息
     */
    @JSONField(name = "resp_desc")
    private String respDesc;

    /**
     * 交易状态
     */
    @JSONField(name = "trans_stat")
    private String transStat;

    /**
     * 全局流水号
     */
    @JSONField(name = "hf_seq_id")
    private String hfSeqId;

    /**
     * 请求日期
     */
    @JSONField(name = "req_date")
    private String reqDate;

    /**
     * 请求流水号
     */
    @JSONField(name = "req_seq_id")
    private String reqSeqId;

    /**
     * 商户号
     */
    @JSONField(name = "huifu_id")
    private String huifuId;

    /**
     * 原交易请求流水号
     */
    @JSONField(name = "org_req_seq_id")
    private String orgReqSeqId;

    /**
     * 是否垫资退款
     */
    @JSONField(name = "loan_flag")
    private String loanFlag;

    /**
     * 垫资承担者
     */
    @JSONField(name = "loan_undertaker")
    private String loanUnderTaker;

    /**
     * 垫资账户类型
     */
    @JSONField(name = "loan_acct_type")
    private String loanAcctType;

    /**
     * 待确认总金额
     */
    @JSONField(name = "unconfirm_amt")
    private String unConfirmAmt;

    /**
     * 已确认总金额
     */
    @JSONField(name = "confirmed_amt")
    private String confirmedAmt;

    /**
     * 支付交易业务请求时间
     */
    @JSONField(name = "org_req_date")
    private String orgReqDate;

    /**
     * 支付交易汇付全局流水号
     */
    @JSONField(name = "org_hf_seq_id")
    private String orgHfSeqId;
}
