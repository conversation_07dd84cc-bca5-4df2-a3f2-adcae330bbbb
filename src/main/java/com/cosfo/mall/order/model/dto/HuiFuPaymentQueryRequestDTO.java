package com.cosfo.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/22 1:46
 * 汇付支付结果查询DTO
 */
@Data
public class HuiFuPaymentQueryRequestDTO implements Serializable {

    /**
     * 原机构请求日期
     */
    @JsonProperty("org_req_date")
    private String orgReqDate;
    /**
     * 商户号
     */
    @JsonProperty("huifu_id")
    private String huiFuId;
    /**
     * 商户订单号
     */
    @JsonProperty("mer_ord_id")
    private String merOrdId;
    /**
     * 交易返回的全局流水号
     */
    @JsonProperty("org_hf_seq_id")
    private String orgHfSeqId;
    /**
     * 原机构请求流水号
     */
    @JsonProperty("org_req_seq_id")
    private String orgReqSeqId;
    /**
     * 用户账单上的交易订单号
     */
    @JsonProperty("out_trans_id")
    private String outTransId;
    /**
     * 用户账单上的商户订单号
     */
    @JsonProperty("party_order_id")
    private String partyOrderId;
}
