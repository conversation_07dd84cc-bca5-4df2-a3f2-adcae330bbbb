package com.cosfo.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-06-29
 * @Description:
 */
@Data
public class HuiFuQueryRefundRequestDTO implements Serializable {

    /**
     * 商户号
     */
    @JsonProperty("huifu_id")
    private String huiFuId;

    /**
     * 退款请求日期
     */
    @JsonProperty("org_req_date")
    private String orgReqDate;

    /************* 退款请求流水号,退款全局流水号,终端订单号三选一不能都为空 ***********/

    /**
     * 退款请求流水号
     */
    @JsonProperty("org_req_seq_id")
    private String orgReqSeqId;

    /**
     * 退款全局流水号
     */
    @JsonProperty("org_hf_seq_id")
    private String orgHfSeqId;

    /**
     * 终端订单号
     */
    @JsonProperty("mer_ord_id")
    private String merOrdId;

}
