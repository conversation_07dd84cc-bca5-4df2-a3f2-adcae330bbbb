package com.cosfo.mall.order.model.bo;

import com.cosfo.mall.tenant.model.po.TenantFundAccountConfig;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: George
 * @date: 2025-04-22
 **/
@Data
@Builder
public class OrderNonCashCalculationParamBO {

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 运费信息集合（订单ID -> 运费）
     */
    private Map<Long, BigDecimal> deliveryFeeMap;
    /**
     * 商品明细（包含订单ID, 商品ID, 商品金额, 分类ID）
     */
    private List<OrderItemNonCashCalculationParamBO> orderItemDetails;
    /**
     * 订单总金额
     */
    private BigDecimal totalOrderAmount;
}
