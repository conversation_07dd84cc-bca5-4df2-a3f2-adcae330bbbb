package com.cosfo.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2023-11-11
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class HuiFuHostingPaymentDTO {

    /**
     * 预下单类型
     * Y 微信预下单：3；示例值：3
     */
    @JsonProperty("pre_order_type")
    private String preOrderType;

    /**
     * 请求日期
     * Y 请求格式：yyyyMMdd；示例值：20221023
     */
    @JsonProperty("req_date")
    private String reqDate;

    /**
     * 请求流水号
     * Y 同一huifu_id下当天唯一；
     */
    @JsonProperty("req_seq_id")
    private String reqSeqId;

    /**
     * 汇付商户号
     * Y
     */
    @JsonProperty("huifu_id")
    private String huifuId;

    /**
     * 交易金额
     * Y 单位元，保留两位小数，示例值：1.00，最低传入0.01
     */
    @JsonProperty("trans_amt")
    private String transAmt;

    /**
     * 商品描述
     * Y 示例值：个人电脑
     */
    @JsonProperty("goods_desc")
    private String goodsDesc;

    /**
     * 结算单号
     * N 当前支付商户或所属服务商的huifu_id；
     */
    @JsonProperty("checkout_id")
    private String checkoutId;

    /**
     * 是否延迟交易
     * Y 为延迟 N为不延迟，不传默认N；示例值：N
     */
    @JsonProperty("delay_acct_flag")
    private String delayAcctFlag;

    /**
     * 微信小程序扩展参数集合
     */
    @JsonProperty("miniapp_data")
    private String miniappData;

    /**
     * 交易失效时间 yyyyMMddHHmmss
     * 为空默认失效时间为5分钟
     */
    @JsonProperty("time_expire")
    private String timeExpire;

    /**
     * 分账信息
     */
    @JsonProperty("acct_split_bunch")
    private AcctSplitBunchDTO acctSplitBunch;

    /**
     * 回调通知URL
     */
    @JsonProperty("notify_url")
    private String notifyUrl;

}
