package com.cosfo.mall.order.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * order_agent_sku_fee_rule_snapshot
 * <AUTHOR>
@Data
public class OrderAgentSkuFeeRuleSnapshot implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 账号Id
     */
    private Long accountId;

    /**
     * 费用规则类型0按比例1按件数
     */
    private Integer feeRuleType;

    /**
     * 规则
     */
    private String rule;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}