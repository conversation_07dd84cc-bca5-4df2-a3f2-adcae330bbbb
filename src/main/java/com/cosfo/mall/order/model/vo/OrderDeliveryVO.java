package com.cosfo.mall.order.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/28
 */
@Data
public class OrderDeliveryVO {
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 配送方式 0其他 1物流快递 2 自提
     */
    private Integer deliveryType;
    /**
     * 物流公司
     */
    private String deliveryCompany;
    /**
     * 配送单号
     */
    private String deliveryNo;

    /**
     * 快递查询URL
     */
    private String jumpUrl;

    /**
     * 备注
     */
    private String remark;
    /**
     * 仓库编号
     */
    private Long warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 自提地址
     */
    private String selfLiftingAddress;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 配送明细
     */
    private List<OrderDeliveryItemVO> orderDeliveryItemVOList;
}
