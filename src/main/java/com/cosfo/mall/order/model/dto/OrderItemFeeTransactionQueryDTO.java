package com.cosfo.mall.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/11 15:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemFeeTransactionQueryDTO {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单明细id
     */
    private Long orderItemId;

    /**
     * 费用类型1.代仓费用
     */
    private Integer feeType;

    /**
     * 0.支付，1.退款
     */
    private Integer transactionType;
}
