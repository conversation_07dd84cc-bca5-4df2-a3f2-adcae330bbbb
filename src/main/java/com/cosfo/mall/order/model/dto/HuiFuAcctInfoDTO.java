package com.cosfo.mall.order.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
@Data
public class HuiFuAcctInfoDTO implements Serializable {
    /**
     * 汇付Id
     */
    @JSONField(name = "huifu_id")
    private String huiFuId;
    /**
     * 账户号
     */
    @JSONField(name = "acct_id")
    private String acctId;
    /**
     * 账户类型
     */
    @JSONField(name = "acct_type")
    private String acctType;
    /**
     * 账户余额
     */
    @JSONField(name = "balance_amt")
    private String balanceAmt;
    /**
     * 可用余额
     */
    @JSONField(name = "avl_bal")
    private String avlBal;
    /**
     * 冻结余额
     */
    @JSONField(name = "frz_bal")
    private String frzBal;
    /**
     * 昨日日终余额
     */
    @JSONField(name = "last_avl_bal")
    private String lastAvlBal;
    /**
     * 状态
     */
    @JSONField(name = "acct_stat")
    private String acctStat;
}
