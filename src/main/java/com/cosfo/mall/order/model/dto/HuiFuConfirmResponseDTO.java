package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/21 15:06
 */
@Data
public class HuiFuConfirmResponseDTO implements Serializable {

    /**
     *业务响应码
     */
    private String resp_code;
    /**
     *业务响应信息
     */
    private String resp_desc;
    /**
     *交易状态
     */
    private String trans_stat;
    /**
     *请求日期
     */
    private String req_date;
    /**
     *业务请求流水号
     */
    private String req_seq_id;
    /**
     *商户号
     */
    private String huifu_id;
    /**
     *全局流水号
     */
    private String hf_seq_id;
    /**
     *账务返回码
     */
    private String acct_resp_code;
    /**
     *账务返回描述
     */
    private String acct_resp_desc;
    /**
     *待确认总金额
     */
    private String unconfirm_amt;
    /**
     *已确认总金额
     */
    private String confirmed_amt;
    /**
     *支付交易业务请求流水号
     */
    private String org_req_seq_id;
    /**
     *支付交易业务请求时间
     */
    private String org_req_date;
    /**
     *支付交易汇付全局流水号
     */
    private String org_hf_seq_id;

    /**
     * 分账对象
     */
    private String acct_split_bunch;
}
