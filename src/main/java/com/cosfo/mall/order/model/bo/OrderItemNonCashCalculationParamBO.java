package com.cosfo.mall.order.model.bo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-04-22
 **/
@Data
@Builder
public class OrderItemNonCashCalculationParamBO {
    // 订单ID
    private Long orderId;
    // 商品ID
    private Long itemId;
    // 商品金额
    private BigDecimal itemAmount;
    // 分类ID
    private Long classificationId;
}
