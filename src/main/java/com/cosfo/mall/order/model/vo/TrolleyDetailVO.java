package com.cosfo.mall.order.model.vo;

import com.cosfo.mall.market.model.vo.MarketItemVO;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class TrolleyDetailVO {
    /**
     * 供应商Id
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 仓类型
     */
    private Integer warehouseType;
    /**
     * 预计送达时间
     */
    private LocalDate deliveryTime;
    /**
     * 是否有效
     */
    private Boolean validFlag;
    /**
     * 商品信息
     */
    private List<MarketItemVO> goods;

    /**
     * 履约类型，0：城配履约，1：快递履约
     * 1快递履约 预计发货时间 deliveryTime
     */
    private Integer fulfillmentType;

}
