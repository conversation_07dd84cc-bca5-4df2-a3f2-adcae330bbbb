package com.cosfo.mall.order.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.mall.payment.model.vo.PaymentCombinedDetailVO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import lombok.Data;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class OrderVO {
    /**
     * 租户编码
     */
    private Long tenantId;
    /**
     * 门店Id
     */
    private Long storeId;
    /**
     * 订单编号
     */
    private Long orderId;
    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 配送仓类型 0无仓 1三方 2自营
     */
    private Integer warehouseType;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 应付金额
     */
    private BigDecimal payablePrice;
    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 税费
     */
    private BigDecimal taxAmount;

    /**
     * 税率，百分位，如5表示5%
     */
    private BigDecimal taxRate;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;


    /**
     * 一批下单拆单后多个订单的总金额
     */
    private BigDecimal mulOrderTotalPrice;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderTime;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 账号Id
     */
    private Long accountId;
    /**
     * 下单账号
     */
    private String accountName;
    /**
     * 配送时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDateTime deliveryTime;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;

    /**
     * 支付方式 1.微信支付 2.账期支付 3.余额支付 4.支付宝支付 5.无需支付 6.线下支付 7.非现金支付 8.组合支付
     */
    private Integer payType;
    /**
     * 支付渠道
     */
    private Integer onlinePayChannel;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 到期时间
     */
    private LocalDateTime expireTime;
    /**
     * 总件数
     */
    private Integer totalAmount;
    /**
     * 收获地址信息
     */
    private OrderAddressResp orderAddressDTO;
    /**
     * 订单项
     */
    private List<OrderItemVO> orderItemVOS;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 存在未关闭售后单标志
     */
    private Boolean saleAfterFlag;
    /**
     * 可申请售后时效
     */
    private Integer applyEndTime;
    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;
    /**
     * 库存仓编号
     */
    private Long warehouseNo;
    /**
     * 订单类型 0-普通订单,1=组合订单,2=预售订单
     */
    private Integer orderType;

    /**
     * 订单来源:0：内部系统; 1：openapi调用; 2:总部代下单
     */
    private Integer orderSource;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;

    /**
     * 组合品item_id
     */
    private Long combineItemId;
    /**
     * 组合包订单Id
     */
    private Long combineOrderId;

    /**
     * 组合包marketId
     */
    private Long combineMarketId;
    /**
     * 线下 付款 支付凭证
     */
    private String paymentReceipt;

    /**
     * 运费计算快照
     */
    private MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotVO;

    /**
     * 订单操作日志
     */
    private List<BizLogListResp> bizLogListRespList;

    /**
     * 组合支付详情
     */
    private List<PaymentCombinedDetailVO> paymentCombinedDetail;

    /**
     * 可用的非现金账户余额
     */
    private BigDecimal usableNonCashBalance;

    /**
     * 不可用的非现金账户余额
     */
    private BigDecimal unusableNonCashBalance;

    /**
     * 微信服务费
     */
    private BigDecimal wechatServiceFee;

    /**
     * 支付宝服务费
     */
    private BigDecimal alipayServiceFee;

    /**
     * 服务费率 百分位，如0.6表示0.6%
     */
    private BigDecimal salesServiceFeeRate;

    /**
     * 手续费
     */
    private BigDecimal salesServiceFee;
}
