package com.cosfo.mall.order.model.dto;

import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.model.po.OrderAddress;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/19  09:43
 */
@Data
public class OrderDTO extends Order {
    /**
     * 下单地址
     */
    private OrderAddress orderAddress;

    /**
     * 供应商租户id
     */
    private Long supplyTenantId;

    /**
     * 订单商品明细
     */
    private List<OrderItemDTO> orderItemDTOList;

    /**
     * 订单城市
     */
    private String city;

    /**
     * 订单区域
     */
    private String area;

    /**
     * 订单备注
     */
    private String remark;
    /**
     * 仓库编号
     */
    private Long warehouseNo;
}
