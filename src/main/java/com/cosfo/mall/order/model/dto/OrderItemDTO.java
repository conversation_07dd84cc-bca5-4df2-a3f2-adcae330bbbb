package com.cosfo.mall.order.model.dto;

import com.cosfo.mall.order.model.po.OrderItem;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/19  09:45
 */
@Data
public class OrderItemDTO extends OrderItem {
    /**
     * 货品skuId
     */
    private Long skuId;
    /**
     * 货品一级类目Id
     */
    private Long firstCategoryId;
    /**
     * 货品二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 货品三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 供应商商品Id
     */
    private Long supplySkuId;
    /**
     * 订单项参与免邮金额
     */
    private BigDecimal calcPartDeliveryFee;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 配送方式0品牌方配送1三方配送
     */
    private Integer deliveryType;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;
    /**
     * 供应商sku编码
     */
    private String supplySku;
    /**
     * 商品类型 0=实物商品,1=虚拟商品,2=组合品
     */
    private Integer itemType;

    /**
     * 货源类型 0无货 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 组合包itemId
     */
    private Long combineItemId;
    /**
     * 组合品子项数量配置
     */
    private Integer combineItemQuantity;

    /**
     * 组合包marketId
     */
    private Long combineMarketId;


    // --------- 计算供应商运费传参
    /**
     * sku编码
     */
    private String sku;

    /** 二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subAgentType;
    /** 所属类目类型 **/
    private Integer categoryType;


    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * sku归属租户id  自营货品为tenantId>1; 报价货品为1
     */
    private Long skuTenantId;

}
