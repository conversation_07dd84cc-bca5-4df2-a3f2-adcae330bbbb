package com.cosfo.mall.order.model.vo;

import com.cosfo.mall.order.model.dto.OrderItemAfterSaleRuleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@Data
public class OrderItemVO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 订单编码
     */
    private Long orderId;
    /**
     * sku编码
     */
    private Long itemId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 供应商商品skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 供应价
     */
    private BigDecimal supplyPrice;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 供应商Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 配送方式0品牌方配送1三方配送
     */
    private Integer deliveryType;
    /**
     * 是否可申请售后
     */
    private boolean isEnableApplyAfterSale;
    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 定价方式 0百分比上浮1定额上浮
     */
    private Integer pricingType;

    /**
     * 定价数值
     */
    private BigDecimal pricingNumber;

    /**
     * 需要配送数量
     */
    private Integer needSendAmount;

    /**
     * 申请售后时间快照
     */
    private OrderItemAfterSaleRuleDTO orderAfterSaleRule;

    /**
     * 可申请售后结束时间
     */
    private LocalDateTime afterSaleExpiryTime;
    /**
     * 货源类型 0无货 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 鲜沐sku编码
     */
    private String supplySku;

    /**
     * 售后按钮标识 0退款 1退款详情 2退款成功 3 配送后售后 4不能售后
     */
    private Integer afterSaleFlag;

    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 商品类型 0=实物商品,1=虚拟商品,2=组合品
     */
    private Integer itemType;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 组合品id
     */
    private Long combineItemId;
    /**
     * 组合订单项信息
     */
    private OrderCombineItemVO orderCombineItemVO;
    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;
    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;
    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 货品sku编码
     */
    private String skuCode;
    /**
     * 货品自有编码
     */
    private String customSkuCode;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 货品重量
     */
    private BigDecimal weight;

    /**
     * 分组id
     */
    private Long classificationId;
}
