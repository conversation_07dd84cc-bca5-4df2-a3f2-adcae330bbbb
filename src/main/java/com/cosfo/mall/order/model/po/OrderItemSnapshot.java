package com.cosfo.mall.order.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * order_item_snapshot
 * <AUTHOR>
@Data
public class OrderItemSnapshot implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单商品id
     */
    private Long orderItemId;

    /**
     * sku编码
     */
    private Long skuId;

    /**
     * 供应商Id
     */
    private Long supplierTenantId;

    /**
     * 供应商skuId
     */
    private Long supplierSkuId;

    /**
     * 区域商品Id
     */
    private Long areaItemId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 规格
     */
    private String specification;


    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 归属类型 0自营品 1三方品
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 配送方式0品牌方配送1三方配送
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 定价方式 0百分比上浮1定额上浮
     */
    private Integer pricingType;

    /**
     * 定价数值
     */
    private BigDecimal pricingNumber;

    /**
     * 售后规则快照
     */
    private String afterSaleRule;

    /**
     * 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 订单id
     */
    private Long orderId;
    
    private static final long serialVersionUID = 1L;
}
