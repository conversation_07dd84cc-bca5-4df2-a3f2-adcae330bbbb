package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/28 1:44
 * 交易确认退款接口响应接收DTO
 */
@Data
public class HuiFuPaymentRefundResponseDTO implements Serializable {

    /**
     *业务响应码
     */
    private String resp_code;
    private String sub_resp_code;
    /**
     *	业务响应信息
     */
    private String resp_desc;
    private String sub_resp_desc;
    /**
     *交易状态
     */
    private String trans_stat;
    /**
     *全局流水号
     */
    private String hf_seq_id;
    /**
     *请求日期
     */
    private String req_date;
    /**
     *请求流水号
     */
    private String req_seq_id;
    /**
     *商户号
     */
    private String huifu_id;
    /**
     *原交易请求流水号
     */
    private String org_req_seq_id;
    /**
     *是否垫资退款
     */
    private String loan_flag;
    /**
     *垫资承担者
     */
    private String loan_undertaker;
    /**
     *垫资账户类型
     */
    private String loan_acct_type;
    /**
     *待确认总金额
     */
    private String unconfirm_amt;
    /**
     *已确认总金额
     */
    private String confirmed_amt;
    /**
     *支付交易业务请求时间
     */
    private String org_req_date;
    /**
     *支付交易汇付全局流水号
     */
    private String org_hf_seq_id;
    /**
     * 分账信息
     */
    private String acct_split_bunch;

    /**************** fee_amt 主动查询结果时返回，回调通知要从 acct_split_bunch 中解析 ******************/

    /**
     * 手续费金额
     */
    private String fee_amt;
}
