package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
@Data
public class OrderPayDTO {
    /**
     * 订单编号
     */
    private List<String> orderNos;
    /**
     * 支付类型 1、线上支付 2、账期 3、余额支付 4、支付宝支付
     */
    private Integer payType;
    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 是否H5请求
     */
    private Boolean H5Request = false;
}
