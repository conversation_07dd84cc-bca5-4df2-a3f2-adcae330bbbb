package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-06-29
 * @Description:
 */
@Data
public class HuiFuPaymentRefundQueryResponseDTO implements Serializable {

    /**
     * 业务响应码
     */
    private String resp_code;
    /**
     * 业务响应信息
     */
    private String resp_desc;
    /**
     * 商户号
     */
    private String huifu_id;

    /**
     * 退款全局流水号
     */
    private String org_hf_seq_id;

    /**
     * 退款请求日期
     */
    private String org_req_date;

    /**
     * 退款请求流水号
     */
    private String org_req_seq_id;

    /**
     * 退款金额
     */
    private String ord_amt;

    /**
     * 实际退款金额
     */
    private String actual_ref_amt;
    /**
     * 交易发生日期
     */
    private String trans_date;
    /**
     * 交易发生时间
     */
    private String trans_time;
    /**
     * 交易类型
     */
    private String trans_type;
    /**
     * 交易状态
     */
    private String trans_stat;
    /**
     * 通道返回码
     */
    private String bank_code;
    /**
     * 通道返回描述
     */
    private String bank_message;
    /**
     * 手续费金额
     */
    private String fee_amt;
    /**
     * 分账对象
     */
    private String acct_split_bunch;
    /**
     * 分账手续费信息
     */
    private String split_fee_info;

    /**
     * 补贴支付信息
     */
    private String combinedpay_data;
    /**
     * 补贴部分的手续费
     */
    private String combinedpay_fee_amt;
    /**
     * 数字货币返回报文
     */
    private String dc_response;
    /**
     * 原交易用户账单上的商户订单号
     */
    private String org_party_order_id;
    /**
     * 授权号
     */
    private String auth_no;
    /**
     * 借贷标识
     */
    private String debit_flag;
    /**
     * 商户名称
     */
    private String mer_name;
    /**
     * 商户私有域
     */
    private String mer_priv;
    /**
     * 原授权号
     */
    private String org_auth_no;
    /**
     * 原外部订单号
     */
    private String org_out_order_id;
    /**
     * 预授权撤销返还手续费
     */
    private String pre_auth_cance_fee_amount;
    /**
     * 预授权撤销金额
     */
    private String pre_auth_cancel_amt;
    /**
     * 原预授权全局流水号
     */
    private String pre_auth_hf_seq_id;
    /**
     * 店铺名称
     */
    private String shop_name;
    /**
     * 分期退款金额
     */
    private String fq_acq_ord_amt;
    /**
     * 分期退款手续费金额
     */
    private String fq_acq_fee_amt;
    /**
     * 除分期外的退款金额
     */
    private String oth_ord_amt;
    /**
     * 除分期外的退款手续费金额
     */
    private String oth_fee_amt;
}
