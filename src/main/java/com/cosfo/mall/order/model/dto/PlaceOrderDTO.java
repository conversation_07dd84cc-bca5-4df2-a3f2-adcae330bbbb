package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@Data
public class PlaceOrderDTO {
    /**
     * 下单订单项
     */
    private List<OrderItemDTO> orderItemDTOS;
    /**
     * 下单地址
     */
    private Long merchantAddressId;
    /**
     * 下单联系人
     */
    private Long merchantContactId;
    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 仓库类型 0无仓 1三方 2自营
     */
    private Integer warehouseType;
    /**
     * 订单类型 0-普通订单,1=组合订单,2=预售订单
     */
    private Integer orderType;
    /**
     * 组合品item_id
     */
    private Long combineItemId;
    /**
     * 组合品marketId
     */
    private Long combineMarketId;


    /**
     * 计划单编号, 门店确认计划单下单时，必填
     */
    private String planOrderNo;

    /**
     * 是否是计划单自动创建订单调用
     * true - 是，下单接口内不更新计划单状态
     * false - 否，小程序下单，需要更新计划单状态
     */
    private boolean autoPlanOrderCreateOrder = false;
}
