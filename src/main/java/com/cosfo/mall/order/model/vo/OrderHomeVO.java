package com.cosfo.mall.order.model.vo;

import lombok.Data;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/24
 */
@Data
public class OrderHomeVO {
    /**
     * 全部订单
     */
    private Integer totalNum;
    /**
     * 待付款
     */
    private Integer waitPaymentNum;
    /**
     * 待收货
     */
    private Integer deliveringNum;
    /**
     * 已完成
     */
    private Integer finishedNum;
    /**
     * 售后
     */
    private Integer afterSaleNum;

    /**
     * 门店待确认的计划单数量
     */
    private Integer waitConfirmPlanOrderNum;
}
