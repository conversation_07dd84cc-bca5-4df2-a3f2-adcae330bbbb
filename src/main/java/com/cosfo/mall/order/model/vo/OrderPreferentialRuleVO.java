package com.cosfo.mall.order.model.vo;

import com.cosfo.mall.common.constants.OrderPreferentialTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/10/15 17:22
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderPreferentialRuleVO implements Serializable {
    private static final long serialVersionUID = -2091321560747346672L;

    /**
     * 关联活动id
     */
    private Long relatedId;

    /**
     * 优惠类型
     * @see OrderPreferentialTypeEnum
     */
    private Integer preferentialType;

    /**
     * 已经优惠金额
     */
    private BigDecimal alreadyPreferentialAmount;

    /**
     * 继续购买金额
     */
    private BigDecimal continueBuyAmount;

    /**
     * 下一档优惠金额
     */
    private BigDecimal nextPreferentialAmount;


}
