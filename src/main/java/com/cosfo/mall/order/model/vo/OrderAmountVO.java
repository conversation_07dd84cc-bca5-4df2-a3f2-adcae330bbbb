package com.cosfo.mall.order.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class OrderAmountVO {

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 应付金额
     */
    private BigDecimal payablePrice;
    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 税费
     */
    private BigDecimal taxAmount;

//    /**
//     * 税率，百分位，如5表示5%
//     */
//    private BigDecimal taxRate;

//    /**
//     * 总金额
//     */
//    private BigDecimal totalPrice;

    /**
     * 商品总金额
     */
    private BigDecimal itemTotalPrice;


    /**
     * 是否最后的商品取消 true-是，取消订单 false-否，返回新订单金额
     */
    private boolean lastItem;

}
