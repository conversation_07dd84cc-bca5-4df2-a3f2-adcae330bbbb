package com.cosfo.mall.order.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * order_combine_snapshot
 * <AUTHOR>
@Data
public class OrderCombineSnapshot implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 组合订单id
     */
    private Long combineOrderId;

    /**
     * 组合品id
     */
    private Long combineItemId;

    /**
     * 组合品子itemid
     */
    private Long itemId;

    /**
     * 组合品子item数量
     */
    private Integer quantity;

    /**
     * 原单价
     */
    private BigDecimal originalPrice;

    /**
     * 子订单id
     */
    private Long orderItemId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}