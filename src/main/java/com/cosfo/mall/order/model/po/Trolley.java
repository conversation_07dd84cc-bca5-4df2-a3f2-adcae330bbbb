package com.cosfo.mall.order.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * trolley
 * <AUTHOR>
@Data
public class Trolley implements Serializable {
    /**
     * 主键自增
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 店铺Id
     */
    private Long storeId;

    /**
     * 商品sku编码
     */
    private Long itemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}