package com.cosfo.mall.order.model.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cosfo.mall.merchant.model.dto.DeliveryItemFeeDTO;
import com.cosfo.mall.merchant.model.dto.DeliveryRuleInfoDTO;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 下单运费快照(OrderDeliveryFeeSnapshot)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:46:12
 */
@Data
@TableName("order_delivery_fee_snapshot")
public class OrderDeliveryFeeSnapshot implements Serializable {
    private static final long serialVersionUID = -46348791476110468L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单运费
     */
    private BigDecimal orderDeliveryFee;
    /**
     * 订单信息
     */
    private String orderInfo;
    /**
     * 规则列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DeliveryRuleInfoDTO> ruleInfo;
    /**
     * 命中规则的运费
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DeliveryItemFeeDTO> hitRuleFee;

    /**
     * 备注
     */
    private String remark;

    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;

}

