package com.cosfo.mall.order.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * order_self_lifting
 * <AUTHOR>
@Data
public class OrderSelfLifting implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 自提地址
     */
    private String address;

    /**
     * 预计自提时间
     */
    private LocalDateTime expectTime;

    /**
     * 实际自提时间
     */
    private LocalDateTime actualTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
