package com.cosfo.mall.order.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
@Data
public class HuiFuBalanceQueryResultDTO implements Serializable {
    /**
     * 业务响应码
     */
    @JSONField(name = "resp_code")
    private String respCode;

    /**
     * 业务响应信息
     */
    @JSONField(name = "resp_desc")
    private String respDesc;

    /**
     * 请求日期
     */
    @JSONField(name = "req_date")
    private String reqDate;

    /**
     * 请求流水号
     */
    @JSONField(name = "req_seq_id")
    private String reqSeqId;

    /**
     * 账户信息列表
     */
    @JSONField(name = "acctInfo_list")
    private String acctInfoList;
}
