package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2022/12/28 1:44
 * 交易确认退款接口请求DTO
 */
@Data
public class HuiFuPaymentRefundRequestDTO implements Serializable {

    /**
     *请求日期
     */
    private String req_date;
    /**
     *	请求流水号
     */
    private String req_seq_id;
    /**
     *商户号
     */
    private String huifu_id;

    /**
     * 申请退款金额[两位小数]
     */
    private String ord_amt;
    /**
     *原交易请求日期
     */
    private String org_req_date;
    /**
     *原交易请求流水号
     */
    private String org_req_seq_id;

    /**
     * 回调地址
     */
    private String notify_url;
}
