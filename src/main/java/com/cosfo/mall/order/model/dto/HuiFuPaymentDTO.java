package com.cosfo.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022/12/16 2:29
 */
@Data
public class HuiFuPaymentDTO {

    /**
     * 请求日期
     */
    @JsonProperty("req_date")
    private String reqDate;
    /**
     * 请求流水号
     */
    @JsonProperty("req_seq_id")
    private String reqSeqId;
    /**
     * 商户号
     */
    @JsonProperty("huifu_id")
    private String huiFuId;
    /**
     * 交易类型
     */
    @JsonProperty("trade_type")
    private String tradeType;
    /**
     * 交易金额
     */
    @JsonProperty("trans_amt")
    private String transAmt;
    /**
     * 商品描述
     */
    @JsonProperty("goods_desc")
    private String goodsDesc;
    /**
     * 交易有效期
     */
    @JsonProperty("time_expire")
    private String timeExpire;
    /**
     * 禁用信用卡标记
     */
    @JsonProperty("limit_pay_type")
    private String limitPayType;
    /**
     * 是否延迟交易
     */
    @JsonProperty("delay_acct_flag")
    private String delayAcctFlag;
    /**
     * 渠道号
     */
    @JsonProperty("channel_no")
    private String channelNo;
    /**
     * 手续费扣款标志
     */
    @JsonProperty("fee_flag")
    private String feeFlag;
    /**
     * 场景类型
     */
    @JsonProperty("pay_scene")
    private String payScene;
    /**
     * 安全信息
     */
    @JsonProperty("risk_check_data")
    private String riskCheckData;
    /**
     * 设备信息
     */
    @JsonProperty("terminal_device_data")
    private String terminalDeviceData;
    /**
     * 分账对象
     */
    @JsonProperty("acct_split_bunch")
    private String acctSplitBunch;
    /**
     * 传入分帐遇到优惠的处理规则
     */
    @JsonProperty("term_div_coupon_type")
    private String termDivCouponType;
    /**
     * 聚合正扫微信拓展参数集合
     */
    @JsonProperty("wx_data")
    private String wxData;
    /**
     * 支付宝扩展参数集合
     */
    @JsonProperty("alipay_data")
    private String alipayData;
    /**
     * 银联参数集合
     */
    @JsonProperty("unionpay_data")
    private String unionpayData;
    /**
     * 数字人民币参数集合
     */
    @JsonProperty("dc_data")
    private String dcData;
    /**
     * 商户贴息标记
     */
    @JsonProperty("fq_mer_discount_flag")
    private String fqMerDiscountFlag;
    /**
     * 异步通知地址
     */
    @JsonProperty("notify_url")
    private String notifyUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 账户号
     */
    @JsonProperty("acct_id")
    private String acctId;

    /**
     * payment_id支付单id
     */
    @JsonProperty("payment_id")
    private Long paymentId;
}
