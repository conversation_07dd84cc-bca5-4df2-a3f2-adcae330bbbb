package com.cosfo.mall.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: fansongsong
 * @Date: 2023-12-22
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class OrderAfterSaleRecycleDataDTO {

    /**
     * 规格单位数量(大规格单位)
     */
    private BigDecimal specificationQuantity;

    /**
     * 规格单位(大规格单位)
     */
    private String specificationUnit;

    /**
     * 基础规格单位数量(小规格单位)
     */
    private BigDecimal basicSpecQuantity;

    /**
     * 基础规格单位(小规格单位)
     */
    private String basicSpecUnit;

    /**
     * 计划履约数量
     */
    private Integer shouldCount;
}
