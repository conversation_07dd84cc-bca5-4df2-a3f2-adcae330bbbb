package com.cosfo.mall.order.model.dto;

import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 刷新订单运费dto
 *
 * @author: xiaowk
 * @date: 2025/3/5 下午1:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderAmountRefreshTotalDTO implements Serializable {
    private static final long serialVersionUID = 3823137924085681719L;

    private OrderResp orderResp;

    /**
     * 一批下单的订单总金额
     */
    private BigDecimal mulOrderTotalPrice;
}
