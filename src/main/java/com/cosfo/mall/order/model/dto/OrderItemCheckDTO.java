package com.cosfo.mall.order.model.dto;

import lombok.Data;

/**
 * @author: xiaowk
 * @time: 2024/2/26 下午3:19
 */
@Data
public class OrderItemCheckDTO {

    private Long itemId;

    /**
     * 商品数量
     */
    private Integer itemQuantity;

    /**
     * 检查成功状态 true-检查通过 false-检查失败
     */
    private boolean checkFlag;

    /**
     * 检查失败原因
     */
    private String failMsg;


    public static OrderItemCheckDTO checkOk(Long itemId, Integer itemQuantity){
        OrderItemCheckDTO orderItemCheckDTO = new OrderItemCheckDTO();
        orderItemCheckDTO.setItemId(itemId);
        orderItemCheckDTO.setItemQuantity(itemQuantity);
        orderItemCheckDTO.setCheckFlag(true);
        return orderItemCheckDTO;
    }

    public static OrderItemCheckDTO checkFail(Long itemId, Integer itemQuantity, String failMsg){
        OrderItemCheckDTO orderItemCheckDTO = new OrderItemCheckDTO();
        orderItemCheckDTO.setItemId(itemId);
        orderItemCheckDTO.setItemQuantity(itemQuantity);
        orderItemCheckDTO.setCheckFlag(false);
        orderItemCheckDTO.setFailMsg(failMsg);
        return orderItemCheckDTO;
    }
}
