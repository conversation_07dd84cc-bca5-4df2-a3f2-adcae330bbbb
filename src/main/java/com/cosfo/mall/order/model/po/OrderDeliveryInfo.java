package com.cosfo.mall.order.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单物流配送信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Getter
@Setter
@TableName("order_delivery_info")
public class OrderDeliveryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * itemId编码
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 配送数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 物流公司
     */
    @TableField("logistics_company")
    private String logisticsCompany;

    /**
     * 物流单号
     */
    @TableField("logistics_no")
    private String logisticsNo;

    /**
     * 物流链接地址
     */
    @TableField("logistics_url")
    private String logisticsUrl;

    /**
     * 配送类型,0:无需物流，1: 商家物流配送，2: 仓库物流配送，3: 买家退货
     */
    @TableField("delivery_type")
    private Integer deliveryType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
