package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleQueryDTO implements Serializable {

    //        OrderAfterSaleInput input = OrderAfterSaleInput.builder().tenantId(loginContextInfoDTO.getTenantId()).storeId(loginContextInfoDTO.getStoreId()).processFlag(Objects.nonNull(orderAfterSaleDTO.getStatus())).pageIndex(orderAfterSaleDTO.getPageNum()).pageSize(orderAfterSaleDTO.getPageSize()).build();

    private Long tenantId;

    private Long storeId;

    private Integer status;

    private Integer pageNum;

    private Integer pageSize;


}
