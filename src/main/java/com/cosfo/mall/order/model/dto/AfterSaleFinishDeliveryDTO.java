package com.cosfo.mall.order.model.dto;

import lombok.Data;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessageDetailRecycle;

import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-12-22
 * @Description:
 */
@Data
public class AfterSaleFinishDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orderNo;
    private String sku;

    /**
     * 应履约数量
     */
    private Integer shouldCount;

    /**
     * 配送类型 0 配送 1 回收
     */
    private Integer deliveryType;

    /**
     * 缺货数量
     */
    private Integer shortCount;
    private Integer state;
    private String remark;

    /********回收处理*********/

    /**
     * 配送类型 0配送1回收
     */
    private Integer type;

    /**
     *  回收详情，只有配送类型为1回收时才可能有值，注意历史的回收单可能也没有回收详情
     */
    private CommonFulfillmentFinishMessageDetailRecycle detailRecycle;
}
