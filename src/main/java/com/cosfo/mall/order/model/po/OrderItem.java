package com.cosfo.mall.order.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * order_item
 * <AUTHOR>
@Data
public class OrderItem implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 商品Id
     */
    private Long itemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 城配仓 编码
     */
    private Integer storeNo;

    /**
     * 可申请售后结束时间
     */
    private LocalDateTime afterSaleExpiryTime;

    /**
     * 订单类型 0普通订单 1组合订单
     */
    private Integer orderType;

    private static final long serialVersionUID = 1L;
}