package com.cosfo.mall.order.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
@Data
public class HuiFuConfirmRefundRequestDTO implements Serializable {
    /**
     * 请求日志
     */
    @JSONField(name = "req_date")
    private String reqDate;
    /**
     * 请求流水号
     */
    @JSONField(name = "req_seq_id")
    private String reqSeqId;
    /**
     * 商户号
     */
    @JSONField(name = "huifu_id")
    private String huiFuId;

    /**
     * 原交易请求日期
     */
    @JSONField(name = "org_req_date")
    private String orgReqDate;

    /**
     * 原交易请求流水号
     */
    @JSONField(name = "org_req_seq_id")
    private String orgReqSeqId;

    /**
     * 分账对象
     */
    @JSONField(name = "acct_split_bunch")
    private String acctSplitBunch;

    /**
     * 是否垫资退款
     */
    @JSONField(name = "loan_flag")
    private String loanFlag;

    /**
     * 垫资承担者
     */
    @JSONField(name = "loan_undertaker")
    private String loanUndertaker;

    /**
     * 垫资账户类型
     */
    @JSONField(name = "loan_acct_type")
    private String loanAcctType;
}
