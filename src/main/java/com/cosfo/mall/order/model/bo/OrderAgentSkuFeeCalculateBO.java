package com.cosfo.mall.order.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/3/29 15:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderAgentSkuFeeCalculateBO {

    /**
     * 订单itemId
     */
    private Long orderItemId;

    /**
     * 责任类型0供应商1品牌方2门店
     */
    private Integer responsibilityType;

    /**
     * 售后类型
     */
    private Integer afterSaleType;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;


}
