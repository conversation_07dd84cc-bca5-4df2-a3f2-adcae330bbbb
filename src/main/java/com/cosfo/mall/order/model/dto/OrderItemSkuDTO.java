package com.cosfo.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: fansongsong
 * @Date: 2023-04-07
 * @Description:
 */
@Data
public class OrderItemSkuDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 子订单id(orderItemId)
     */
    private Long id;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal payablePrice;
}
