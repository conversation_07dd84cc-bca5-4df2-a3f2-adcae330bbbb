package com.cosfo.mall.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.time.LocalDate;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderQueryDTO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 配送日
     */
    private LocalDate deliveryTime;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应商租户id
     */
    private Long supplierTenantId;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;

}
