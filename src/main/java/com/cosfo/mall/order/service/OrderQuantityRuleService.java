package com.cosfo.mall.order.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleCheckVO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleManageVO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleVO;
import com.cosfo.mall.order.model.dto.OrderRuleCheckDTO;
import com.cosfo.mall.order.service.impl.OrderQuantityRuleServiceImpl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderQuantityRuleService {

    /**
     * 订单起订量检查
     * @param orderRuleCheckDTO
     * @param loginContextInfoDTO
     */
    List<OrderQuantityRuleCheckVO> orderQuantityRuleCheck(OrderRuleCheckDTO orderRuleCheckDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 订单起订量检查包含规则
     * @param orderRuleCheckDTO
     * @param tenantId
     * @param ruleManageVO
     * @return
     */
    List<OrderQuantityRuleCheckVO> orderQuantityRuleCheckByRuleManage(OrderRuleCheckDTO orderRuleCheckDTO, Long tenantId, OrderQuantityRuleManageVO ruleManageVO);

    /**
     * 获取商品销售方式
     * @param itemIds
     * @param tenantId
     * @return
     */
    Map<Long, Integer> getItemSaleMode(List<Long> itemIds, Long tenantId);

    /**
     * 起订量判断
     *
     * @param orderRuleCheckDTO
     * @param tenantId
     * @return
     */
    List<OrderQuantityRuleCheckVO> orderQuantityRuleCheck(OrderRuleCheckDTO orderRuleCheckDTO, Long tenantId);


    /**
     * 查询品牌方商城起订量配置规则
     *
     * @param tenantId 品牌方
     * @return 规则列表
     */
    OrderQuantityRuleManageVO queryOrderQuantityRule(Long tenantId);

    /**
     * 处理其他规则
     * 返回 命中的其他规则是否满足条件
     *     是否有未命中任何规则的商品
     * @param orderRuleCheckDTO
     * @param otherRule
     * @param itemSaleModeMap
     * @return
     */
    OrderQuantityRuleServiceImpl.PartCheckResult handleOtherRule(OrderRuleCheckDTO orderRuleCheckDTO, List<OrderQuantityRuleVO> otherRule, Map<Long, Integer> itemSaleModeMap);

    /**
     * 处理规则详情
     * 判断金额或者数量是否满足规则
     * @param rule
     * @param quantity
     * @param price
     * @return
     */
    OrderQuantityRuleCheckVO ruleHitResult(OrderQuantityRuleVO rule, Integer quantity, BigDecimal price);

    /**
     * 前端提示转换
     * @param orderQuantityRuleCheckVOS
     * @return
     */
    List<OrderQuantityRuleCheckVO> orderQuantityLimitResultForShow(List<OrderQuantityRuleCheckVO> orderQuantityRuleCheckVOS);
}
