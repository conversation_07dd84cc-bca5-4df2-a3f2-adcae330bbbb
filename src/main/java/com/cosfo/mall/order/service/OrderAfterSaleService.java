package com.cosfo.mall.order.service;

import com.cosfo.mall.common.model.dto.PageDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
import com.cosfo.mall.facade.dto.OrderItemAfterSaleDetailInfoDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.model.dto.AfterSaleFinishDeliveryDTO;
import com.cosfo.mall.order.model.dto.LogisticsDTO;
import com.cosfo.mall.order.model.dto.OrderAfterSaleCreateDTO;
import com.cosfo.mall.order.model.dto.OrderAfterSaleQueryDTO;
import com.cosfo.mall.warehouse.model.vo.FastMallVO;
import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.summerfarm.model.dto.order.AfterOrderItemVO;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/21
 */
public interface OrderAfterSaleService {



    /**
     * 新建售后订单
     *
     * @param createDTO
     * @return
     */
    Long save(OrderAfterSaleCreateDTO createDTO);

    /**
     * 批量新建售后单
     *
     * @param afterSaleCreateDTOS
     * @return
     */
    List<Long> saveBatch(List<OrderAfterSaleCreateDTO> afterSaleCreateDTOS);

    /**
     * 取消售后单
     *
     * @param orderAfterSaleId
     * @return
     */
    Boolean cancel(Long orderAfterSaleId);

    /**
     * 售后订单详情
     *
     * @param orderItemId
     * @return
     */
    ResultDTO<List<OrderAfterSaleResultDTO>> detail(Long orderItemId, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 退款成功后售后单处理
     *
     * @param afterSaleId
     */
    void payRefundSuccessDeal(Long afterSaleId);


    /**
     * 查询该售后单的订单下所有的售后单
     *
     * @param afterSaleOrderNo
     * @return
     */
    ResultDTO<List<OrderAfterSaleResp>> queryAllAfterSale(String afterSaleOrderNo);

    /**
     * 对应售后任务处理完毕
     */
    void afterSaleProcessFinish(List<AfterSaleFinishDeliveryDTO> list);

    /**
     * 对应售后任务处理完毕
     */
    void afterSaleProcessFinishByMessage(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage);

    /**
     * 查询售后商品的item信息
     *
     * @param afterSaleOrderNo
     * @return
     */
    ResultDTO<AfterOrderItemVO> queryItemInfo(String afterSaleOrderNo);

    /**
     * 售后单自动完成
     */
    void orderAfterSaleAutoFinished();

    /**
     * 计算退款金额
     *
     * @param orderItemId
     * @param amount
     * @return
     */
    BigDecimal calculateRefundPrice(Long orderItemId, Integer amount);

    /**
     * 填写物流信息
     *
     * @param dto
     * @return
     */
    void addSaleLogistics(LogisticsDTO dto, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 修改物流信息
     *
     * @param dto
     * @return
     */
    void upadteSaleLogistics(LogisticsDTO dto, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 获取物流公司
     */
    List<FastMallVO> queryFastMallList();

    /**
     * 获取卖家售后收货地址
     *
     * @return
     */
    WarehouseStorageVO queryOneWarehouseStorage(String afterSaleOrderNo);

    /**
     * 该售后单是否是最后一次售后
     *
     * @param id
     * @return
     */
    Boolean verifyLastOrderItemOrderAfterSaleFlag(Long id);

    /**
     * 售后单获取拉起客服的展示信息
     *
     * @param orderItemId
     * @param requestContextInfoDTO
     * @return
     */
    ResultDTO consultationDetail(Long orderItemId, LoginContextInfoDTO requestContextInfoDTO);

    /**
     * 获取售后订单项页面
     *
     * @param orderItemId
     * @param requestContextInfoDTO
     * @return
     */
    ResultDTO<OrderItemAfterSaleDetailInfoDTO> getOrderItemAfterSale(Long orderItemId, LoginContextInfoDTO requestContextInfoDTO);



    /**
     * 售后列表
     * @param orderAfterSaleDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<PageDTO<OrderAfterSaleResultDTO>> list(OrderAfterSaleQueryDTO orderAfterSaleDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询已完成售后单
     * @param orderId
     * @return
     */
    List<OrderAfterSaleResp> queryAllFinishedAfterSaleOrderByOrderId(Long tenantId, Long orderId);


    /**
     * 根据售后单id查询
     * @param orderAfterSaleId
     * @return
     */
    OrderAfterSaleResp queryById(Long orderAfterSaleId);
}
