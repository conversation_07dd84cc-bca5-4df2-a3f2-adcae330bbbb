package com.cosfo.mall.order.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.model.vo.TrolleyDetailVO;
import com.cosfo.mall.order.model.vo.TrolleyItemVO;

import java.util.List;

/**
 * 购物车服务类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
public interface TrolleyService {
    /**
     * 获取购物车详情
     *
     * @return
     */
    ResultDTO<List<TrolleyDetailVO>> get(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 清空购物车
     *
     * @return
     */
    ResultDTO clear(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 添加商品到购物车
     *
     * @return
     */
    ResultDTO<Integer> add(Long itemId, Integer quantity, Integer type, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 删除商品
     *
     * @return
     */
    List<TrolleyItemVO> getByItemIds(List<Long> itemIds, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 删除商品
     *
     * @return
     */
    ResultDTO remove(List<Long> itemIds, LoginContextInfoDTO loginContextInfoDTO);
}
