package com.cosfo.mall.order.service;

import com.cosfo.mall.order.model.po.OrderItemSnapshot;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 11:26
 */
public interface OrderItemSnapshotService {


    /**
     * 根据orderItemId查询
     *
     * @param tenantId
     * @param orderItemId
     * @return
     */
    OrderItemSnapshot selectByOrderItemId(Long tenantId, Long orderItemId);


    List<OrderItemSnapshot> selectByOrderIds(Long tenantId, Set<Long> orderId);
}
