package com.cosfo.mall.order.service;

import com.cosfo.mall.order.model.bo.OrderItemFeeBO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderItemFeeCalculateService {

    /**
     * 初始化代仓
     * @param orderIds
     * @return
     */
    void initAgentFee(List<Long> orderIds);


    /**
     * 计算订单明细项代仓费用
     * @param orderItemAndSnapshotDTOS
     * @param initFlag
     * @param warehouseType
     * @return
     */
    List<OrderItemFeeTransaction> calculateAgentFee(List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOS, Boolean initFlag, Integer warehouseType);


    /**
     * 组装订单项售后产生的费用
     * @param orderAfterSaleId
     * @return
     */
    OrderItemFeeBO buildOrderItemRefundBO(Long orderAfterSaleId);
}
