package com.cosfo.mall.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.common.constants.GoodsSourceEnum;
import com.cosfo.mall.common.constants.ItemSaleModeEnum;
import com.cosfo.mall.common.constants.MarketItemEnums;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.context.PageSourceEnum;
import com.cosfo.mall.common.model.dto.CommonLocationCityDTO;
import com.cosfo.mall.common.service.AreaService;
import com.cosfo.mall.facade.ProductQueryFacade;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.convert.OrderQuantityRuleMapper;
import com.cosfo.mall.merchant.dao.MerchantOrderQuantityRuleDao;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.dto.OrderQuantityRuleDetailDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.po.MerchantOrderQuantityRule;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleCheckVO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleManageVO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleVO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.order.model.dto.OrderRuleCheckDTO;
import com.cosfo.mall.order.model.dto.OrderRuleCheckItemDTO;
import com.cosfo.mall.order.service.OrderQuantityRuleService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.stock.model.dto.PreDistributionOrderItemDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.dto.StockQueryDTO;
import com.cosfo.mall.stock.service.StockService;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.i18n.util.XianmuI18nUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderQuantityRuleServiceImpl implements OrderQuantityRuleService {

    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private StockService stockService;
    @Resource
    private AreaService areaService;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private MerchantOrderQuantityRuleDao merchantOrderQuantityRuleDao;
    @Resource
    private ProductQueryFacade productQueryFacade;


    @Override
    public List<OrderQuantityRuleCheckVO> orderQuantityRuleCheck(OrderRuleCheckDTO orderRuleCheckDTO, LoginContextInfoDTO loginContextInfoDTO) {

        //详情页补充仓库信息
        handleItemWarehouseInfo(orderRuleCheckDTO, loginContextInfoDTO);

        // 处理起订量规则
        List<OrderQuantityRuleCheckVO> checkVOList = orderQuantityRuleCheck(orderRuleCheckDTO, loginContextInfoDTO.getTenantId());

        return checkVOList;
    }



    /**
     * 起订量规则处理
     * <p>
     * 购物车页面（选中商品）
     * 1. 各个仓判断是否满足对应仓起订量逻辑
     * 2. 判断所有选中商品是否满足全商城规则
     * 3. 当仓和全商城规则都不满足是，对比仓和全商城规则大小，购物车总览展示大的值
     * (有金额展示金额大的，没有则比较数量)
     * 商品详情页面
     * 1. 有对应仓规则满足仓规则即可下单
     * 2. 无对应仓则判断全商城规则
     *
     * </p>
     *
     * @param orderRuleCheckDTO
     * @param tenantId
     * @return
     */
    @Override
    public List<OrderQuantityRuleCheckVO> orderQuantityRuleCheck(OrderRuleCheckDTO orderRuleCheckDTO, Long tenantId) {
        // 获取起订量规则
        OrderQuantityRuleManageVO ruleManageVO = queryOrderQuantityRule(tenantId);

        return orderQuantityRuleCheckByRuleManage(orderRuleCheckDTO, tenantId, ruleManageVO);
    }

    @Override
    public List<OrderQuantityRuleCheckVO> orderQuantityRuleCheckByRuleManage(OrderRuleCheckDTO orderRuleCheckDTO, Long tenantId, OrderQuantityRuleManageVO ruleManageVO) {
        // 获取商品销售方式
        List<Long> itemIds = orderRuleCheckDTO.getItemList().stream().map(OrderRuleCheckItemDTO::getItemId).collect(Collectors.toList());
        Map<Long, Integer> itemSaleModeMap = getItemSaleMode(itemIds, tenantId);

        if (ruleManageVO == null) {
            return Lists.newArrayList();
        }

        // 详情页命中特殊规则直接返回
        if (Objects.nonNull(orderRuleCheckDTO.getCheckSource()) && orderRuleCheckDTO.getCheckSource() == PageSourceEnum.GOODS_DETAIL.getCode()) {
            // 若不存在可独售商品，则异常提示返回
            boolean normalSale = orderRuleCheckDTO.getItemList().stream().anyMatch(orderRuleCheckItemDTO -> ItemSaleModeEnum.NORMAL_SALE.getMode().equals(itemSaleModeMap.get(orderRuleCheckItemDTO.getItemId())));
            if (!normalSale) {
                throw new BizException("下单规则发生了变化，请返回上一页重新调整后再试喔");
            }
        }

        //处理具体仓规则
        PartCheckResult partCheckResult = handleOtherRule(orderRuleCheckDTO, ruleManageVO.getOtherRule(), itemSaleModeMap);
        if (partCheckResult.earlyReturn) {
            return orderQuantityLimitResultForShow(partCheckResult.getCheckVOList());
        }
        // 若所有都命中例外规则，则直接返回
        if (Boolean.TRUE.equals(partCheckResult.allHitOtherRule)) {
            return orderQuantityLimitResultForShow(partCheckResult.getCheckVOList());
        }

        // 处理全商城条件
        OrderQuantityRuleCheckVO orderQuantityRuleCheckVO = ruleHitResult(ruleManageVO.getAllMallRule(), partCheckResult.getTotalQuantity(), partCheckResult.getTotalAmount());
        List<OrderQuantityRuleCheckVO> checkVOList = partCheckResult.getCheckVOList();
        checkVOList.add(orderQuantityRuleCheckVO);
        return orderQuantityLimitResultForShow(checkVOList);
    }

    @Override
    public List<OrderQuantityRuleCheckVO> orderQuantityLimitResultForShow(List<OrderQuantityRuleCheckVO> orderQuantityRuleCheckVOS) {
        // 处理展示，一个仓只展示一条，当存在金额时取金额大的一条展示，当不存在金额时取数量大的展示
        Map<String, List<OrderQuantityRuleCheckVO>> groupByTarget = orderQuantityRuleCheckVOS.stream().collect(Collectors.groupingBy(rule -> rule.getWarehouseType()+ "" + rule.getTarget()));
        List<OrderQuantityRuleCheckVO> result = new ArrayList<>();
        for (Map.Entry<String, List<OrderQuantityRuleCheckVO>> ruleEntry : groupByTarget.entrySet()) {
            Optional<OrderQuantityRuleCheckVO> ruleOptional = ruleEntry.getValue().stream()
                    .max(Comparator.comparing(OrderQuantityRuleCheckVO::getAmount, Comparator.nullsLast(BigDecimal::compareTo))
                            .thenComparing(OrderQuantityRuleCheckVO::getQuantity, Comparator.nullsLast(Integer::compareTo)));

            if (!ruleOptional.isPresent()) {
                continue;
            }
            OrderQuantityRuleCheckVO orderQuantityRuleCheckVO = ruleOptional.get();
            if (Boolean.FALSE.equals(orderQuantityRuleCheckVO.getRulePass())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (orderQuantityRuleCheckVO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                    stringBuilder.append(orderQuantityRuleCheckVO.getAmount()).append(XianmuI18nUtil.getI18nValue("元"));
                }
                if (orderQuantityRuleCheckVO.getQuantity() > 0) {
                    if (stringBuilder.length() != 0) {
                        stringBuilder.append("and".equals(orderQuantityRuleCheckVO.getOp()) ? "," : XianmuI18nUtil.getI18nValue("或"));
                    }
                    stringBuilder.append(orderQuantityRuleCheckVO.getQuantity()).append(XianmuI18nUtil.getI18nValue("件"));
                }
                orderQuantityRuleCheckVO.setMsg(XianmuI18nUtil.getI18nValue("还差")+stringBuilder);
                result.add(orderQuantityRuleCheckVO);
            }
        }
        return result;
    }

    /**
     * 获取商品销售方式
     * @param itemIds
     * @param tenantId
     * @return
     */
    @Override
    public Map<Long, Integer> getItemSaleMode(List<Long> itemIds, Long tenantId) {
        List<List<Long>> itemIdList = Lists.partition(itemIds, NumberConstant.ONE_THOUSAND);
        List<MarketItemVO> marketItemVOList = Lists.newArrayList();
        for (List<Long> ids : itemIdList) {
            List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(ids, tenantId);
            if (CollectionUtil.isEmpty(marketItemVOS)) {
                continue;
            }
            marketItemVOList.addAll(marketItemVOS);
        }
        Map<Long, Integer> itemSaleModeMap = marketItemVOList.stream().collect(Collectors.toMap(MarketItemVO::getItemId, MarketItemVO::getItemSaleMode));
        return itemSaleModeMap;
    }

    /**
     * 全商城or例外规则起订量检查结果
     */
    @Data
    @Builder
    public static class PartCheckResult {

        private BigDecimal totalAmount;

        private int totalQuantity;

        private boolean earlyReturn;

        private List<OrderQuantityRuleCheckVO> checkVOList;

        /**
         * 是否全部命中例外规则
         */
        private Boolean allHitOtherRule;
    }


    @Override
    public PartCheckResult handleOtherRule(OrderRuleCheckDTO orderRuleCheckDTO, List<OrderQuantityRuleVO> otherRule, Map<Long, Integer> itemSaleModeMap) {
        List<OrderQuantityRuleCheckVO> checkVOList = new ArrayList<>();

        // 过滤不可凑单商品
        List<OrderRuleCheckItemDTO> needCheckItemList = orderRuleCheckDTO.getItemList().stream().filter(item -> !ItemSaleModeEnum.TYING_CANNOT_ADD_ON_SALE.getMode().equals(itemSaleModeMap.get(item.getItemId()))).collect(Collectors.toList());
        log.info("需要校验的商品项:{}", needCheckItemList);
        boolean rulePass = true;
        int totalQuantity = needCheckItemList.stream()
                .mapToInt(OrderRuleCheckItemDTO::getQuantity)
                .sum();
        BigDecimal totalPrice = needCheckItemList.stream()
                .reduce(BigDecimal.ZERO, (sum, item) ->
                                sum.add(item.getAmount().multiply(BigDecimal.valueOf(item.getQuantity()))),
                        BigDecimal::add);
        if (CollectionUtils.isEmpty(otherRule)) {
            return PartCheckResult.builder()
                    .allHitOtherRule(false)
                    .totalQuantity(totalQuantity)
                    .totalAmount(totalPrice)
                    .checkVOList(checkVOList)
                    .build();
        }
        for (OrderQuantityRuleVO orderQuantityRuleVO : otherRule) {
            int quantity = 0;
            BigDecimal priceCnt = BigDecimal.ZERO;
            Iterator<OrderRuleCheckItemDTO> itemIterator = needCheckItemList.iterator();
            boolean anyHit = false;
            log.info("当前待校验规则:{}", orderQuantityRuleVO);
            while (itemIterator.hasNext()) {
                OrderRuleCheckItemDTO next = itemIterator.next();
                log.info("ruleId={},当前校验商品:{}", orderQuantityRuleVO.getId(), next);
                BigDecimal itemTotalPrice = next.getAmount().multiply(BigDecimal.valueOf(next.getQuantity()));
                // 先判断仓
                if (!orderQuantityRuleVO.getRuleTarget().equals(next.getWarehouseNo()) || !orderQuantityRuleVO.getWarehouseType().equals(next.getWarehouseType())) {
                    log.info("ruleId={},当前商品{}仓规则未命中", orderQuantityRuleVO.getId(), next.getItemId());
                    continue;
                }
                //命中货源
                if (orderQuantityRuleVO.getHitGoodsSource() != null
                        && (orderQuantityRuleVO.getHitGoodsSource().equals(GoodsSourceEnum.ALL.getCode())
                        || orderQuantityRuleVO.getHitGoodsSource().equals(goodsSourceMap(next.getWarehouseType(), next.getGoodsType())))) {
                    quantity += next.getQuantity();
                    priceCnt = priceCnt.add(itemTotalPrice);
                    log.info("ruleId={},当前校验商品{}命中货源规则", orderQuantityRuleVO.getId(), next.getItemId());
                    anyHit = true;
                    itemIterator.remove();
                    continue;
                }
                // 判断是否包含商品
                if ((orderQuantityRuleVO.getHitGoodsSource() == null && orderQuantityRuleVO.getHitItemIds() == null)
                        || (orderQuantityRuleVO.getHitItemIds() != null && orderQuantityRuleVO.getHitItemIds().contains(next.getItemId()))
                        || Boolean.TRUE.equals(orderQuantityRuleVO.getIncludeNewFlag())) {
                    quantity += next.getQuantity();
                    priceCnt = priceCnt.add(itemTotalPrice);
                    anyHit = true;
                    log.info("ruleId={},当前校验商品{}命中hitItem规则", orderQuantityRuleVO.getId(), next.getItemId());
                    itemIterator.remove();
                }
            }
            log.info("rule={}, hitQuantity={}, hitPrice={}", orderQuantityRuleVO, quantity, priceCnt);
            if (anyHit) {
                OrderQuantityRuleCheckVO checkResult = ruleHitResult(orderQuantityRuleVO, quantity, priceCnt);
                rulePass = rulePass && checkResult.getRulePass();
                if (Boolean.FALSE.equals(checkResult.getRulePass())) {
                    checkVOList.add(checkResult);
                }
            }
            // 判断当前规则是否满足
        }
        log.info("未命中规则商品:{}", needCheckItemList);

        return PartCheckResult.builder()
                .allHitOtherRule(needCheckItemList.isEmpty())
                .totalQuantity(totalQuantity)
                .totalAmount(totalPrice)
                .checkVOList(checkVOList)
                .build();
    }

    @Override
    public OrderQuantityRuleCheckVO ruleHitResult(OrderQuantityRuleVO rule, Integer quantity, BigDecimal price) {
        OrderQuantityRuleCheckVO vo = new OrderQuantityRuleCheckVO();
        vo.setRulePass(true);
        vo.setWarehouseType(rule.getWarehouseType());
        vo.setTarget(rule.getRuleTarget());
        if (quantity == 0 && price.equals(BigDecimal.ZERO)) {
            return vo;
        }
        vo.setOp(rule.getOperator());
        boolean opAndJudge = "and".equals(rule.getOperator());
        for (OrderQuantityRuleDetailDTO detailDTO : rule.getRuleDetailList()) {
            boolean inlineCheckResult = true;
            // ruleDetailType 枚举
            if (detailDTO.getRuleDetailType() == 0) {
                //金额判断
                BigDecimal subAmount = detailDTO.getAmount().subtract(price);
                if (subAmount.compareTo(BigDecimal.ZERO) > 0) {
                    log.debug("金额不满足");
                    vo.setAmount(subAmount);
                    inlineCheckResult = false;
                }
            } else {
                //数量判断
                int subQuantity = detailDTO.getQuantity() - quantity;
                if (subQuantity > 0) {
                    log.debug("数量不满足");
                    vo.setQuantity(subQuantity);
                    inlineCheckResult = false;
                }
            }

            // 当为或时命中一条就跳出
            if (!opAndJudge) {
                if (inlineCheckResult) {
                    vo.setRulePass(true);
                    return vo;
                }
            }
            vo.setRulePass(vo.getRulePass() && inlineCheckResult);
        }
        return vo;
    }

    private Integer goodsSourceMap(Integer warehouseType, Integer goodsType) {
        if(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
            return GoodsSourceEnum.SUPPLIER.getCode();
        }
        if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
            if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(goodsType)) {
                return GoodsSourceEnum.AGENT.getCode();
            } else {
                return GoodsSourceEnum.SUPPLIER.getCode();
            }
        }
        if (OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(warehouseType)) {
            return GoodsSourceEnum.SELF.getCode();
        }
        return null;
    }


    @Override
    public OrderQuantityRuleManageVO queryOrderQuantityRule(Long tenantId) {
        List<MerchantOrderQuantityRule> list = merchantOrderQuantityRuleDao.listOrderRule(tenantId);
        OrderQuantityRuleManageVO manageVO = new OrderQuantityRuleManageVO();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Predicate<MerchantOrderQuantityRule> allMallPredicate = (rule) -> rule.getRuleTarget().equals(0) && OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(rule.getWarehouseType());
        Optional<MerchantOrderQuantityRule> allMallRuleOption = list.stream().filter(allMallPredicate).findAny();
        allMallRuleOption.ifPresent(rule -> manageVO.setAllMallRule(OrderQuantityRuleMapper.INSTANCE.ruleToVo(rule)));
        List<MerchantOrderQuantityRule> otherRuleList = list.stream().filter(allMallPredicate.negate()).sorted(Comparator.comparing(MerchantOrderQuantityRule::getRuleSort)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(otherRuleList)) {
            manageVO.setOtherRule(OrderQuantityRuleMapper.INSTANCE.toRuleVOList(otherRuleList));
        }
        return manageVO;
    }

    /**
     * 补充详情页item仓库信息
     *
     * @param orderRuleCheckDTO
     * @param loginContextInfoDTO
     * @return
     */
    private void handleItemWarehouseInfo(OrderRuleCheckDTO orderRuleCheckDTO, LoginContextInfoDTO loginContextInfoDTO) {

        // 购物车直接返回
        if (orderRuleCheckDTO.getCheckSource() == PageSourceEnum.TROLLEY.getCode()) {
            for (OrderRuleCheckItemDTO orderRuleCheckItemDTO : orderRuleCheckDTO.getItemList()) {
                switch (orderRuleCheckItemDTO.getWarehouseType()) {
                    //无仓
                    case 0:
                        orderRuleCheckItemDTO.setWarehouseNo(-1L);
                        break;
                    // 三方
                    case 1:
                        orderRuleCheckItemDTO.setWarehouseNo(-2L);
                        break;
                }
            }
            return;
        }
        List<OrderRuleCheckItemDTO> itemList = orderRuleCheckDTO.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            throw new BizException("商品不能为空");
        }

        // 获取地址信息
        MerchantAddressDTO merchantAddressDTO = handleAddressInfo(loginContextInfoDTO);
        if (StringUtils.isEmpty(merchantAddressDTO.getPoiNote())) {
            throw new BizException("您的下单地址坐标不准确，请前往修改地址");
        }
        log.debug("merchantAddressDTO = {}", JSON.toJSON(merchantAddressDTO));

        OrderRuleCheckItemDTO orderRuleCheckItemDTO = itemList.get(0);
        List<Long> itemIds = Lists.newArrayList(orderRuleCheckItemDTO.getItemId());
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());

        if (CollectionUtils.isEmpty(marketItemVOList)) {
            throw new BizException("商品信息错误");
        }
        log.debug("marketItemVOList = {}", JSON.toJSONString(marketItemVOList));
        MarketItemVO marketItemVO = marketItemVOList.get(0);

        StockDTO stockDTO = queryStockInfo(loginContextInfoDTO, orderRuleCheckItemDTO, merchantAddressDTO, marketItemVO);

        log.debug("StockDTO = {}", JSON.toJSONString(stockDTO));
        handleWarehouseTypeAndTarget(orderRuleCheckItemDTO, marketItemVO, stockDTO);
    }

    private static void handleWarehouseTypeAndTarget(OrderRuleCheckItemDTO orderRuleCheckItemDTO, MarketItemVO marketItemVO, StockDTO stockDTO) {
        MarketItemEnums.GoodsType goodsType = MarketItemEnums.GoodsType.getByCode(marketItemVO.getGoodsType());
        orderRuleCheckItemDTO.setGoodsType(marketItemVO.getGoodsType());
        switch (goodsType) {
            // 无货商品
            case VIRTUAL:
                orderRuleCheckItemDTO.setWarehouseType(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode());
                orderRuleCheckItemDTO.setWarehouseNo(-1L);
                break;
            // 报价货品
            case QUOTATION:
                orderRuleCheckItemDTO.setWarehouseType(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
                orderRuleCheckItemDTO.setWarehouseNo(-2L);
                break;
            // 自营货品
            case SELF_SUPPORT:
                // 判断是否是自营仓,自营仓根据仓库编号拆单
                if (marketItemVO.getTenantId().equals(stockDTO.getWarehouseTenantId())) {
                    orderRuleCheckItemDTO.setWarehouseType(OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode());
                    orderRuleCheckItemDTO.setWarehouseNo(stockDTO.getWarehouseNo());
                } else {
                    // 代仓获取取代仓商城价
                    orderRuleCheckItemDTO.setWarehouseType(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
                    orderRuleCheckItemDTO.setWarehouseNo(-2L);
                }
                break;
            default:
        }
    }

    /**
     * 查询库存信息
     *
     * @param loginContextInfoDTO
     * @param orderRuleCheckItemDTO
     * @param merchantAddressDTO
     * @param marketItemVO
     * @return
     */
    private StockDTO queryStockInfo(LoginContextInfoDTO loginContextInfoDTO, OrderRuleCheckItemDTO orderRuleCheckItemDTO, MerchantAddressDTO merchantAddressDTO, MarketItemVO marketItemVO) {

        StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setMerchantAddressDTO(merchantAddressDTO);
        stockQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        PreDistributionOrderItemDTO orderItemDTO = new PreDistributionOrderItemDTO();
        orderItemDTO.setQuantity(orderRuleCheckItemDTO.getQuantity());
        orderItemDTO.setItemId(orderRuleCheckItemDTO.getItemId());
        orderItemDTO.setGoodsType(marketItemVO.getGoodsType());
        if (!MarketItemEnums.GoodsType.VIRTUAL.getCode().equals(marketItemVO.getGoodsType())) {
            boolean isSelfSupport = MarketItemEnums.GoodsType.SELF_SUPPORT.getCode().equals(marketItemVO.getGoodsType());
            ProductAgentSkuDTO productAgentSkuDTO = productQueryFacade.selectBySkuIdAndTenantId(marketItemVO.getSkuId(), isSelfSupport ? loginContextInfoDTO.getTenantId() : XianmuSupplyTenant.TENANT_ID);
            orderItemDTO.setSkuId(marketItemVO.getSkuId());
            orderItemDTO.setAgentTenantId(productAgentSkuDTO.getAgentTenantId());
            orderItemDTO.setAgentSku(productAgentSkuDTO.getAgentSkuCode());
            orderItemDTO.setAgentSkuId(productAgentSkuDTO.getAgentSkuId());
        }
        stockQueryDTO.setPreDistributionOrderItemDTOList(Lists.newArrayList(orderItemDTO));
        Map<Long, StockDTO> stockMap = stockService.preDistributionOrderOccupy(stockQueryDTO);
        StockDTO stockDTO = stockMap.get(orderRuleCheckItemDTO.getItemId());
        return stockDTO;
    }

    /**
     * 查询收货地址
     *
     * @param loginContextInfoDTO
     * @return
     */
    private MerchantAddressDTO handleAddressInfo(LoginContextInfoDTO loginContextInfoDTO) {
        MerchantAddress merchantAddress = merchantAddressService.queryDefaultAddress(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId());
        MerchantAddressDTO merchantAddressDTO = new MerchantAddressDTO();
        BeanUtils.copyProperties(merchantAddress, merchantAddressDTO);
        CommonLocationCityDTO commonLocationCityDTO = areaService.selectByCityName(merchantAddress.getCity());
        merchantAddressDTO.setCityId(commonLocationCityDTO.getId());
        return merchantAddressDTO;
    }


}
