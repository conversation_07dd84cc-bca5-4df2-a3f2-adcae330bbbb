package com.cosfo.mall.order.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.cosfo.mall.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.BillProfitSharingSnapshotTypeEnum;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @description: 无仓商品分账计算
 * @author: George
 * @date: 2024-01-08
 **/
@Slf4j
@Service
public class NoWarehouseProductProfitSharingCalculate implements ProfitSharingCalculate {

    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    @Override
    public boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        return ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType().equals(deliveryType) && ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode().equals(profitSharingRuleType);
    }

    @Override
    public void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        String profitSharingNo = billProfitSharingSnapshotDtos.get(0).getProfitSharingNo();
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderMapper.selectByProfitSharingNo(profitSharingNo);
        if (Objects.isNull(billProfitSharingOrder)) {
            throw new ProviderException("未查询到分账信息");
        }

        Long orderId = billProfitSharingSnapshotDtos.get(0).getOrderId();
        Long supplierId = billProfitSharingOrder.getSupplierId();
        Long tenantId = billProfitSharingOrder.getTenantId();

        // 1、查询订单快照信息
        List<OrderItemAndSnapshotResp> orderItemSnapshots = getOrderItemSnapshots(tenantId, orderId, supplierId);
        List<Long> orderItemIds = orderItemSnapshots.stream().map(OrderItemAndSnapshotResp::getOrderItemId).collect(Collectors.toList());

        // 2、查询售后信息
        List<OrderAfterSaleResp> orderAfterSales = getOrderAfterSales(orderItemIds);
        Map<Long, BigDecimal> orderAfterSaleMap = orderAfterSales.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId, Collectors.reducing(BigDecimal.ZERO, el -> NumberUtil.sub(el.getTotalPrice(), el.getDeliveryFee()), BigDecimal::add)));

        // 3、计算各方分账金额
        Pair<BigDecimal, BigDecimal> eachPartyPair = calculateSharingEachParty(billProfitSharingSnapshotDtos, orderItemSnapshots, orderAfterSaleMap);
        BigDecimal supplierPrice = eachPartyPair.getKey();
        BigDecimal brandPrice = eachPartyPair.getValue();

        // 4、更新快照
        Integer type = billProfitSharingSnapshotDtos.size() == 1 ? BillProfitSharingSnapshotTypeEnum.ALL.getCode() : BillProfitSharingSnapshotTypeEnum.PART.getCode();
        for (BillProfitSharingSnapshotDTO dto : billProfitSharingSnapshotDtos) {
            dto.setOriginPrice(NumberUtil.add(supplierPrice, brandPrice));
            dto.setProfitSharingPrice(
                    Objects.equals(dto.getAccountType(), AccountTypeEnum.SUPPLIER.getType())
                            ? supplierPrice
                            : brandPrice
            );
            dto.setType(type);
            billProfitSharingService.updateBillProfitSharingSnapshot(dto);
        }
    }

    private Pair<BigDecimal, BigDecimal> calculateSharingEachParty(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, List<OrderItemAndSnapshotResp> orderItemSnapshots, Map<Long, BigDecimal> orderAfterSaleMap) {
        BigDecimal totalPriceSum = BigDecimal.ZERO;
        BigDecimal supplyPriceSum = BigDecimal.ZERO;

        // 遍历订单明细
        for (OrderItemAndSnapshotResp orderItemSnapshot : orderItemSnapshots) {
            // 订单金额
            BigDecimal totalPrice = orderItemSnapshot.getTotalPrice();
            BigDecimal supplyPrice = NumberUtil.mul(orderItemSnapshot.getSupplyPrice(), orderItemSnapshot.getAmount());

            // 售后金额
            BigDecimal refundPrice = orderAfterSaleMap.getOrDefault(orderItemSnapshot.getOrderItemId(), BigDecimal.ZERO);
            BigDecimal rate = NumberUtil.div(refundPrice, orderItemSnapshot.getTotalPrice());
            supplyPrice = NumberUtil.mul(supplyPrice, NumberUtil.sub(BigDecimal.ONE, rate)).setScale(NumberConstant.TWO, ROUND_HALF_UP);

            // 合计
            totalPriceSum = NumberUtil.add(totalPriceSum, totalPrice, refundPrice.negate());
            supplyPriceSum = NumberUtil.add(supplyPriceSum, supplyPrice);
        }

        if (billProfitSharingSnapshotDtos.size() == 1) {
            // 只有一方分账参与分账的情况下
            BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = billProfitSharingSnapshotDtos.get(0);
            Integer accountType = billProfitSharingSnapshotDTO.getAccountType();
            return Objects.equals(accountType, AccountTypeEnum.TENANT.getType())
                    ? Pair.of(BigDecimal.ZERO, totalPriceSum)
                    : Pair.of(totalPriceSum, BigDecimal.ZERO);
        }
        // supplyPriceSum 分账给供应商 剩下的给品牌方
        BigDecimal brandPrice = NumberUtil.sub(totalPriceSum, supplyPriceSum);
        return Pair.of(supplyPriceSum, brandPrice);
    }

    /**
     * 获取售后单信息
     *
     * @param orderItemIds
     * @return
     */
    private List<OrderAfterSaleResp> getOrderAfterSales(List<Long> orderItemIds) {
        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setOrderItemIds(orderItemIds);
        orderAfterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        return RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(orderAfterSaleQueryReq));
    }

    /**
     * 获取订单明细快照信息
     *
     * @param tenantId
     * @param orderId
     * @param supplierId
     * @return
     */
    private List<OrderItemAndSnapshotResp> getOrderItemSnapshots(Long tenantId, Long orderId, Long supplierId) {
        OrderItemQueryReq queryReq = new OrderItemQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setOrderIds(Collections.singletonList(orderId));
        queryReq.setSupplierIds(Collections.singletonList(supplierId));
        return RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(queryReq));
    }
}
