package com.cosfo.mall.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProductAgentSkuFeeRuleTypeEnum;
import com.cosfo.mall.common.constants.ProfitSharingMerberCodeEnum;
import com.cosfo.mall.common.context.TenantTypeEnum;
import com.cosfo.mall.order.mapper.OrderAgentSkuFeeRuleSnapshotMapper;
import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import com.cosfo.mall.order.model.po.OrderItem;
import com.cosfo.mall.order.service.OrderAgentSkuFeeRuleService;
import com.cosfo.mall.order.service.OrderItemService;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Service
public class OrderAgentSkuFeeRuleServiceImpl implements OrderAgentSkuFeeRuleService {
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private TenantService tenantService;
    @Resource
    private OrderAgentSkuFeeRuleSnapshotMapper orderAgentSkuFeeRuleSnapshotMapper;
    @Resource
    private OrderItemService orderItemService;
    @Lazy
    @Resource
    private OrderService orderService;

    @Override
    public void saveOrderAgentSkuFeeRule(Long tenantId, Long orderId) {
        ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDTO = productAgentSkuFeeRuleService.queryByTenantId(tenantId);
        if (Objects.isNull(productAgentSkuFeeRuleDTO)) {
            return;
        }
        List<OrderAgentSkuFeeRuleSnapshot> list = new ArrayList<>(NumberConstant.THREE);
        // TODO 代仓商品收费规则记录 帆台、鲜沐、品牌方
        /** 查询帆台账号*/
        TenantDTO tenantDTO = tenantService.selectByType(TenantTypeEnum.FANTAI.getType());
        OrderAgentSkuFeeRuleSnapshot fanTaiOrderAgentSkuFeeRuleSnapshot = new OrderAgentSkuFeeRuleSnapshot();
        BeanUtils.copyProperties(productAgentSkuFeeRuleDTO, fanTaiOrderAgentSkuFeeRuleSnapshot);
        fanTaiOrderAgentSkuFeeRuleSnapshot.setAccountId(tenantDTO.getId());
        fanTaiOrderAgentSkuFeeRuleSnapshot.setOrderId(orderId);
        fanTaiOrderAgentSkuFeeRuleSnapshot.setFeeRuleType(productAgentSkuFeeRuleDTO.getType());
        list.add(fanTaiOrderAgentSkuFeeRuleSnapshot);
        /** 查询鲜沐供应商账号*/
        TenantDTO supplierTenantDTO= tenantService.selectByType(TenantTypeEnum.SUPPLIER.getType());
        OrderAgentSkuFeeRuleSnapshot supplierOrderAgentSkuFeeRuleSnapshot = new OrderAgentSkuFeeRuleSnapshot();
        BeanUtils.copyProperties(productAgentSkuFeeRuleDTO, supplierOrderAgentSkuFeeRuleSnapshot);
        supplierOrderAgentSkuFeeRuleSnapshot.setAccountId(supplierTenantDTO.getId());
        supplierOrderAgentSkuFeeRuleSnapshot.setOrderId(orderId);
        supplierOrderAgentSkuFeeRuleSnapshot.setFeeRuleType(productAgentSkuFeeRuleDTO.getType());
        list.add(supplierOrderAgentSkuFeeRuleSnapshot);
        /** 品牌方*/
        OrderAgentSkuFeeRuleSnapshot brandOrderAgentSkuFeeRuleSnapshot = new OrderAgentSkuFeeRuleSnapshot();
        BeanUtils.copyProperties(productAgentSkuFeeRuleDTO, brandOrderAgentSkuFeeRuleSnapshot);
        brandOrderAgentSkuFeeRuleSnapshot.setAccountId(tenantId);
        brandOrderAgentSkuFeeRuleSnapshot.setOrderId(orderId);
        brandOrderAgentSkuFeeRuleSnapshot.setFeeRuleType(productAgentSkuFeeRuleDTO.getType());
        list.add(brandOrderAgentSkuFeeRuleSnapshot);
        orderAgentSkuFeeRuleSnapshotMapper.batchInsert(list);
    }

    @Override
    public void updateOrderHitRule(Long tenantId, Long orderId, ProductAgentSkuFeeRuleDetailDTO hitRule) {
        orderAgentSkuFeeRuleSnapshotMapper.updateHitRule(tenantId, orderId, JSON.toJSONString(hitRule));
    }

    /**
     * 返回该订单项需要扣除的代仓费用金额
     * @param orderAfterSale
     * @return
     */
    @Override
    public BigDecimal calculateAgentFeeByOrder(OrderAfterSaleResp orderAfterSale) {
        Long orderItemId = orderAfterSale.getOrderItemId();
        BigDecimal deliveryFee = Optional.ofNullable(orderAfterSale.getDeliveryFee()).orElse(BigDecimal.ZERO);
        BigDecimal totalRefundPrice = NumberUtil.sub(orderAfterSale.getTotalPrice(), deliveryFee);
        OrderItem orderItem = orderItemService.queryById(orderItemId);
        Integer amount = orderItem.getAmount();
        Integer orderAgentCount = orderService.countAgentInOrder(orderItem.getTenantId(), orderItem.getOrderId());
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper.queryByTenantIdAndOrderId(orderItem.getTenantId(), orderItem.getOrderId());
        if (CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)) {
            return BigDecimal.ZERO;
        }
        OrderAgentSkuFeeRuleSnapshot orderAgentSkuFeeRuleSnapshot = orderAgentSkuFeeRuleSnapshots.get(NumberConstant.ZERO);
        String rule = orderAgentSkuFeeRuleSnapshots.get(NumberConstant.ZERO).getRule();
        List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS = JSONObject.parseArray(rule, ProductAgentSkuFeeRuleDetailDTO.class);

        if (CollectionUtil.isEmpty(productAgentSkuFeeRuleDetailDTOS)) {
            return BigDecimal.ZERO;
        }
        if (Objects.equals(orderAgentSkuFeeRuleSnapshot.getFeeRuleType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
            ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO = productAgentSkuFeeRuleDetailDTOS.stream().filter(el -> Objects.equals(el.getMemberCode(), ProfitSharingMerberCodeEnum.SUPPLIER.getCode())).findFirst().get();
            BigDecimal agentFee = NumberUtil.mul(totalRefundPrice, NumberUtil.div(productAgentSkuFeeRuleDetailDTO.getPercentage(), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            return agentFee;
        }

        if (Objects.equals(orderAgentSkuFeeRuleSnapshot.getFeeRuleType(), ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode())) {
            // 临界点
            ProductAgentSkuFeeRuleDetailDTO recentProductAgentSkuFeeRule = null;
            Integer maxCount = NumberConstant.ZERO;
            for (ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO : productAgentSkuFeeRuleDetailDTOS) {
                if (productAgentSkuFeeRuleDetailDTO.getCount().compareTo(orderAgentCount) <= NumberConstant.ZERO && productAgentSkuFeeRuleDetailDTO.getCount().compareTo(maxCount) > NumberConstant.ZERO) {
                    maxCount = productAgentSkuFeeRuleDetailDTO.getCount();
                    recentProductAgentSkuFeeRule = productAgentSkuFeeRuleDetailDTO;
                }
            }
            if (Objects.isNull(recentProductAgentSkuFeeRule)) {
                return BigDecimal.ZERO;
            }

            BigDecimal agentPreAmount = NumberUtil.mul(recentProductAgentSkuFeeRule.getAmount(), amount);
            // 代仓按件数售后公式 = 每件 * 售后金额 / 总售价
            BigDecimal rate = NumberUtil.div(totalRefundPrice, orderItem.getTotalPrice());
            BigDecimal agentFee = NumberUtil.mul(agentPreAmount, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            return agentFee;
        }
        return BigDecimal.ZERO;
    }

    @Override
    public List<OrderAgentSkuFeeRuleSnapshot> queryByTenantIdAndOrderId(Long tenantId, Long orderId) {
        return orderAgentSkuFeeRuleSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
    }
}
