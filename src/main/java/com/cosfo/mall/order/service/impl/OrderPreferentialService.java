package com.cosfo.mall.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constants.OrderRuleTypeEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.dto.OrderPreferentialDTO;
import com.cosfo.mall.facade.input.OrderPreferentialQueryInput;
import com.cosfo.mall.facade.marketcenter.OrderPreferentialFacade;
import com.cosfo.mall.market.model.dto.SkuMallPriceDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemPriceService;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.order.model.bo.OrderRuleBO;
import com.cosfo.mall.order.model.bo.RuleLevelValueBO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.model.dto.OrderPreferentialThresholdQueryDTO;
import com.cosfo.mall.order.model.vo.OrderPreferentialRuleVO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/10/14 15:39
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderPreferentialService {

    @Autowired
    private OrderPreferentialFacade orderPreferentialFacade;

    @Autowired
    private MerchantAddressService merchantAddressService;

    @Autowired
    private MarketItemService marketItemService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private MarketItemPriceService marketItemPriceService;

    /**
     * 查询订单优惠金额信息
     *
     * @param itemBuyAmount      商品购买数量
     * @param skuMallPriceDTOMap 商品价格信息
     * @return 订单明细项优惠信息
     */
    public Map<Long, BigDecimal> queryOrderPreferentialDiscount(Map<Long, Integer> itemBuyAmount,
                                                                Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap,
                                                                Long tenantId, Long storeId) {
        if (CollectionUtils.isEmpty(itemBuyAmount) || CollectionUtils.isEmpty(skuMallPriceDTOMap)) {
            return Collections.emptyMap();
        }
        List<OrderPreferentialQueryInput> orderPreferentialQueryInputs = new ArrayList<>();
        itemBuyAmount.forEach((itemId, amount) -> {
            SkuMallPriceDTO skuMallPriceDTO = skuMallPriceDTOMap.get(itemId);
            if (Objects.isNull(skuMallPriceDTO) || Objects.isNull(skuMallPriceDTO.getPrice())) {
                BizException exception = new BizException(itemId + "商品价格不存在");
                log.error("itemId:{}获取商城价失败", itemId, exception);
                throw exception;
            }
            orderPreferentialQueryInputs.add(new OrderPreferentialQueryInput(itemId, skuMallPriceDTO.getPrice(), amount));
        });
        final Map<Long, OrderPreferentialDTO> longOrderPreferentialDTOMap = orderPreferentialFacade.queryOrderDiscountCalculateResult(orderPreferentialQueryInputs, tenantId, storeId);
        Map<Long, BigDecimal> orderPreferentialInfoMap = new HashMap<>();
        longOrderPreferentialDTOMap.forEach((itemId, orderPreferentialDTO) -> {
            orderPreferentialInfoMap.put(itemId, orderPreferentialDTO.getPreferentialAmount());
        });
        return orderPreferentialInfoMap;
    }

    /**
     * 查询订单优惠信息
     *
     * @param itemBuyAmount      商品购买数量
     * @param skuMallPriceDTOMap 商品价格信息
     * @return 订单明细项优惠信息
     */
    public Map<Long, OrderPreferentialDTO> queryOrderPreferential(Map<Long, Integer> itemBuyAmount,
                                                                  Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap,
                                                                  Long tenantId, Long storeId) {
        if (CollectionUtils.isEmpty(itemBuyAmount) || CollectionUtils.isEmpty(skuMallPriceDTOMap)) {
            return Collections.emptyMap();
        }
        List<OrderPreferentialQueryInput> orderPreferentialQueryInputs = new ArrayList<>();
        itemBuyAmount.forEach((itemId, amount) -> {
            SkuMallPriceDTO skuMallPriceDTO = skuMallPriceDTOMap.get(itemId);
            if (Objects.isNull(skuMallPriceDTO) || Objects.isNull(skuMallPriceDTO.getPrice())) {
                BizException exception = new BizException(itemId + "商品价格不存在");
                log.error("itemId:{}获取商城价失败", itemId, exception);
                throw exception;
            }
            orderPreferentialQueryInputs.add(new OrderPreferentialQueryInput(itemId, skuMallPriceDTO.getPrice(), amount));
        });
        return orderPreferentialFacade.queryOrderDiscountCalculateResult(orderPreferentialQueryInputs, tenantId, storeId);
    }

    /**
     * 查询订单优惠门槛规则信息。
     *
     * @param orderPreferentialThresholdQueryDTO 订单优惠门槛查询参数，包含订单项列表等信息
     * @param loginContextInfoDTO                登录上下文信息，包含租户ID、门店ID等
     * @return 返回订单优惠规则视图对象，包括优惠类型、已优惠金额、距离下一优惠门槛差额及对应优惠值
     */
    public OrderPreferentialRuleVO queryOrderPreferentialThreshold(OrderPreferentialThresholdQueryDTO orderPreferentialThresholdQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (CollectionUtils.isEmpty(orderPreferentialThresholdQueryDTO.getOrderItemDTOList())) {
            throw new ProviderException("下单商品不能为空");
        }

        MerchantAddress merchantAddress = merchantAddressService.queryDefaultAddress(loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getTenantId());
        if (merchantAddress == null) {
            throw new ProviderException("请选择收货地址");
        }

        List<OrderItemDTO> orderItemDTOS = orderPreferentialThresholdQueryDTO.getOrderItemDTOList();

        List<Long> itemIds = orderItemDTOS.stream().map(OrderItemDTO::getItemId).collect(Collectors.toList());
        // 查询商品信息
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());

        MerchantAddressDTO merchantAddressDTO = MerchantAddressMapperConvert.INSTANCE.address2Dto(merchantAddress);

        ResultDTO<TenantDTO> resultDTO = tenantService.selectTenantInfo(loginContextInfoDTO.getTenantId());
        TenantDTO tenantDTO = resultDTO.getData();
        // 下单数量
        Map<Long, Integer> itemBuyAmount = orderItemDTOS.stream()
                .collect(Collectors.toMap(OrderItemDTO::getItemId, OrderItemDTO::getAmount));
        // 获取sku价格信息
        Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOList, itemBuyAmount, tenantDTO,
                merchantAddressDTO, true);

        // 获取订单优惠相关信息
        Map<Long, OrderPreferentialDTO> orderPreferentialDTOMap = this.queryOrderPreferential(itemBuyAmount, skuMallPriceDTOMap, loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId());

        if (CollectionUtils.isEmpty(orderPreferentialDTOMap)) {
            return new OrderPreferentialRuleVO();
        }

        BigDecimal totalOriginalAmount = BigDecimal.ZERO;
        BigDecimal totalPreferentialAmount = BigDecimal.ZERO;

        for (OrderPreferentialDTO dto : orderPreferentialDTOMap.values()) {
            if (dto == null || dto.getPreferentialAmount() == null || dto.getPreferentialAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            totalOriginalAmount = totalOriginalAmount.add(dto.getOriginalAmount().multiply(BigDecimal.valueOf(dto.getQuantity())));
            totalPreferentialAmount = totalPreferentialAmount.add(dto.getPreferentialAmount());
        }

        OrderPreferentialDTO orderPreferentialDTO = orderPreferentialDTOMap.get(orderItemDTOS.get(0).getItemId());
        if (orderPreferentialDTO == null || StringUtils.isBlank(orderPreferentialDTO.getDiscountsDetailSnapshot())) {
            return new OrderPreferentialRuleVO();
        }

        final String discountsDetailSnapshot = orderPreferentialDTO.getDiscountsDetailSnapshot();
        OrderRuleBO orderRuleBO;
        try {
            orderRuleBO = JSON.parseObject(discountsDetailSnapshot, OrderRuleBO.class);
        } catch (Exception e) {
            throw new ProviderException("折扣详情快照格式异常", e);
        }

        if (orderRuleBO == null || CollectionUtils.isEmpty(orderRuleBO.getValue())) {
            return new OrderPreferentialRuleVO();
        }

        if (OrderRuleTypeEnum.LADDER.getCode().equals(orderRuleBO.getType())) {
            return buildLadderRuleVO(orderRuleBO, totalOriginalAmount, totalPreferentialAmount, orderPreferentialDTO);
        }

        if (OrderRuleTypeEnum.EVERY.getCode().equals(orderRuleBO.getType())) {
            return buildEveryRuleVO(orderRuleBO, totalOriginalAmount, totalPreferentialAmount, orderPreferentialDTO);
        }

        return new OrderPreferentialRuleVO();
    }

    private OrderPreferentialRuleVO buildLadderRuleVO(OrderRuleBO orderRuleBO, BigDecimal totalOriginalAmount, BigDecimal totalPreferentialAmount, OrderPreferentialDTO orderPreferentialDTO) {
        orderRuleBO.getValue().sort(Comparator.comparing(RuleLevelValueBO::getRuleLevel));

        List<RuleLevelValueBO> sortedLevels = orderRuleBO.getValue();

        for (RuleLevelValueBO currentLevel : sortedLevels) {
            // 总价没满足当前阶梯，则说明当前阶梯是下一个即将满足的档次
            if (totalOriginalAmount.compareTo(currentLevel.getRuleLevel()) < 0) {
                log.info("下一个阶梯信息 >>> {}", JSON.toJSONString(currentLevel));
                return new OrderPreferentialRuleVO(orderPreferentialDTO.getRelatedId(),
                        orderPreferentialDTO.getPreferentialType(),
                        totalPreferentialAmount,
                        currentLevel.getRuleLevel().subtract(totalOriginalAmount),
                        currentLevel.getValue());
            }
        }

        log.info("没有下一个阶梯价了 >>> {}", totalOriginalAmount);
        return new OrderPreferentialRuleVO(orderPreferentialDTO.getRelatedId(), orderPreferentialDTO.getPreferentialType(), totalPreferentialAmount, null, null);
    }

    private OrderPreferentialRuleVO buildEveryRuleVO(OrderRuleBO orderRuleBO, BigDecimal totalOriginalAmount, BigDecimal totalPreferentialAmount, OrderPreferentialDTO orderPreferentialDTO) {
        RuleLevelValueBO ruleLevelValueBO = orderRuleBO.getValue().get(0);
        if (BigDecimal.ZERO.compareTo(ruleLevelValueBO.getRuleLevel()) == 0) {
            throw new ProviderException("满减门槛设置无效，请联系管理员");
        }

        BigDecimal remainder = totalOriginalAmount.remainder(ruleLevelValueBO.getRuleLevel());
        BigDecimal gapToNext = ruleLevelValueBO.getRuleLevel().subtract(remainder);
        BigDecimal nextValue = totalPreferentialAmount.add(ruleLevelValueBO.getValue());

        return new OrderPreferentialRuleVO(orderPreferentialDTO.getRelatedId(),
                orderPreferentialDTO.getPreferentialType(),
                totalOriginalAmount, gapToNext, nextValue);
    }

}
