package com.cosfo.mall.order.service;

import com.cosfo.mall.common.result.PageResultDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.model.dto.OrderQueryDTO;
import com.cosfo.mall.order.model.dto.PlaceOrderDTO;
import com.cosfo.mall.order.model.vo.*;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import net.summerfarm.ofc.client.common.message.cosfo.FulfillmentOrderResultMessageDTO;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/28
 */
public interface OrderService {
    /**
     * 预下单
     *
     * @param preOrderDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<List<OrderVO>> preOrder(PlaceOrderDTO preOrderDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 下单
     *
     * @param loginContextInfoDTO
     * @param preOrderDTOList
     * @return
     */
    ResultDTO<List<String>> newPlaceOrder(List<PlaceOrderDTO> preOrderDTOList, LoginContextInfoDTO loginContextInfoDTO);


    List<MarketItemVO> checkOrderStock(List<PlaceOrderDTO> preOrderDTOList, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询订单状态
     * <p>
     * 前端轮询支付用
     *
     * @param orderNos
     * @return
     */
    List<OrderVO> queryOrderStatus(List<String> orderNos);

    /**
     * 订单列表
     *
     * @param loginContextInfoDTO
     * @return
     * @Param orderQueryDTO
     */
    PageResultDTO<List<OrderVO>> list(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 获取订单详情
     *
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<OrderVO> detail(Long orderId, LoginContextInfoDTO loginContextInfoDTO);


    OrderAmountVO queryNewOrderAmount(OrderItemVO orderItemVO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 确认收货
     *
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    Boolean confirm(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 取消订单
     *
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     * @see com.cosfo.mall.common.constants.OrderCancelTypeEnum 0、手动取消 1、延时消息 2、超时延时消息
     */
    Boolean cancel(Long orderId, LoginContextInfoDTO loginContextInfoDTO, Integer cancelType);

    /**
     * 取消订单
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Boolean batchCancelOrder(Long tenantId, List<Long> orderIds);


    /**
     * 处理三方仓履约单创建成功消息
     *
     * @param dto
     */
    void dealFulfillmentOrderCreate(FulfillmentOrderResultMessageDTO dto);

    /**
     * 生成订单费用明细
     *
     * @param orderId
     * @return
     */
    void saveOrderItemFeeTransaction(Long orderId);


    /**
     * 首页
     *
     * @return
     */
    ResultDTO<OrderHomeVO> home(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 对外提供获取订单信息
     *
     * @param orderNo
     * @param tenantId
     * @return
     */
    ResultDTO<OrderVO> getOrderInfo(String orderNo, Long tenantId);

    /**
     * 查询订单信息
     *
     * @param queryDTO
     * @return
     */
    List<OrderResp> querySameDeliveryOrders(OrderQueryDTO queryDTO);

    /**
     * 根据条件查询订单信息
     *
     * @param queryDTO
     * @return
     */
    ResultDTO<OrderResp> queryOne(OrderQueryDTO queryDTO);

    /**
     * 关单
     *
     * @param orderId
     * @param contextInfoDTO
     */
    Boolean closeOrder(Long orderId, LoginContextInfoDTO contextInfoDTO);

    /**
     * 配送清单
     *
     * @param orderId
     * @return
     */
    List<OrderDeliveryVO> deliveredDetails(Long orderId);

    /**
     * 待配送清单
     * @param orderId
     * @return
     */
    List<OrderDeliveryItemVO> pendingDeliveryDetails(Long orderId);

    /**
     * 再来一单
     *
     * @param orderId
     * @param loginContextInfoDTO
     */
    List<Long> again(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询订单里有多少个代仓商品
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    Integer countAgentInOrder(Long tenantId, Long orderId);


    /**
     * 获取商品已够数量
     * @param marketItemVOList
     * @param orderItemDTOMap
     * @param tenantId
     * @param storeId
     */
    void merchantStoreItemSaleLimitCheck(List<MarketItemVO> marketItemVOList, Map<Long, OrderItemDTO> orderItemDTOMap, Long tenantId, Long storeId);

    /**
     * 更新订单金额
     * @param orderNoList
     * @return
     */
    OrderAmountRefreshVO refreshOrderAmount(List<String> orderNoList);
}

