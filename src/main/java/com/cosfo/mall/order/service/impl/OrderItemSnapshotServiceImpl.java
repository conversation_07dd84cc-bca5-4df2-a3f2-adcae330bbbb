package com.cosfo.mall.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.order.mapper.OrderItemMapper;
import com.cosfo.mall.order.mapper.OrderItemSnapshotMapper;
import com.cosfo.mall.order.model.po.OrderItem;
import com.cosfo.mall.order.model.po.OrderItemSnapshot;
import com.cosfo.mall.order.service.OrderItemSnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 11:27
 */
@Service
@Slf4j
public class OrderItemSnapshotServiceImpl implements OrderItemSnapshotService {

    @Resource
    private OrderItemSnapshotMapper orderItemSnapshotMapper;
    @Resource
    private OrderItemMapper orderItemMapper;

    @Override
    public OrderItemSnapshot selectByOrderItemId(Long tenantId, Long orderItemId) {
        return orderItemSnapshotMapper.selectByItemId(tenantId, orderItemId);
    }


    @Override
    public List<OrderItemSnapshot> selectByOrderIds(Long tenantId, Set<Long> orderIds) {
        List<OrderItem> orderItems = orderItemMapper.selectByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderItems)) {
            log.warn("订单未找到OrderItem:{}", JSON.toJSONString(orderIds));
            return Collections.EMPTY_LIST;
        }
        Set<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toSet());
        log.info("租户:{} 订单:{} 的orderItemIds:{}", tenantId, JSON.toJSONString(orderIds), JSON.toJSONString(orderItemIds));
        return orderItemSnapshotMapper.batchQuery(tenantId, orderItemIds);
    }
}
