package com.cosfo.mall.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.FulfillmentFinishMessageTypeEnum;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.model.dto.PageDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.QiNiuUtils;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.delivery.model.vo.FulfillmentDeliveryVO;
import com.cosfo.mall.facade.OfcServiceFacade;
import com.cosfo.mall.facade.WmsServiceFacade;
import com.cosfo.mall.facade.WncServiceFacade;
import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
import com.cosfo.mall.facade.dto.OrderItemAfterSaleDetailInfoDTO;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.converter.OrderAfterSaleConverter;
import com.cosfo.mall.order.converter.OrderItemConverter;
import com.cosfo.mall.order.model.dto.*;
import com.cosfo.mall.order.model.vo.OrderConsultationVO;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.system.service.SystemParameterService;
import com.cosfo.mall.warehouse.model.vo.FastMallVO;
import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import com.cosfo.manage.client.tenant.TenantReturnAddressProvider;
import com.cosfo.manage.client.tenant.resp.TenantReturnAddressResp;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.*;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.client.resp.order.*;
import com.cosfo.summerfarm.model.dto.order.AfterOrderItemVO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessageDetail;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessageDetailRecycle;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述: 订单服务类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/21
 */
@Slf4j
@Service
public class OrderAfterSaleServiceImpl implements OrderAfterSaleService {
    @Resource
    @Lazy
    private RefundService refundService;
    @Resource
    private OfcServiceFacade ofcServiceFacade;
    @Resource
    private WmsServiceFacade wmsServiceFacade;
    @Resource
    private WncServiceFacade wncServiceFacade;
    @Resource
    private SystemParameterService systemParameterService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    @DubboReference
    private TenantReturnAddressProvider tenantReturnAddressProvider;


    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;
    @DubboReference
    private OrderItemCommandProvider orderItemCommandProvider;


    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderAddressQueryProvider orderAddressQueryProvider;
    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;


    @Override
    public Long save(OrderAfterSaleCreateDTO createDTO) {
        Long orderItemId = createDTO.getOrderItemId();
        OrderItemResp orderItemResp = RpcResultUtil.handle(orderItemQueryProvider.queryById(orderItemId));
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderItemResp.getOrderId()));
        OrderAfterSaleAddReq afterSaleDTO = OrderAfterSaleConverter.INSTANCE.toAfterSaleReq(createDTO);

        if (Objects.equals(orderDTO.getPayType(), PayTypeEnum.COMBINED_PAY.getCode())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderDTO.getTenantId(), orderDTO.getId());
            List<Integer> combinedPayTypes = combinedDetails.stream().map(detail -> {
                return com.cosfo.mall.common.constants.TradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
            }).collect(Collectors.toList());
            afterSaleDTO.setCombinedPayTypes(combinedPayTypes);
        }
//        afterSaleDTO.setWarehouseType(orderDTO.getWarehouseType());
        afterSaleDTO.setOrderId(ObjectUtil.isNotNull(afterSaleDTO.getOrderId()) ? afterSaleDTO.getOrderId() : orderItemResp.getOrderId());
//        afterSaleDTO.setReason(ObjectUtil.isNotNull(afterSaleDTO.getReason()) ? afterSaleDTO.getReason() : "拍多/拍错/不想要");
//        if (!StringUtils.isEmpty(orderDTO.getWarehouseNo())) {
//            afterSaleDTO.setReturnWarehouseNo(String.valueOf(orderDTO.getWarehouseNo()));
//        }
        return RpcResultUtil.handle(orderAfterSaleCommandProvider.createAfterDeliveryAfterSale(afterSaleDTO));

    }

    @Override
    public List<Long> saveBatch(List<OrderAfterSaleCreateDTO> afterSaleCreateDTOS) {
        Long orderItemId = afterSaleCreateDTOS.get(0).getOrderItemId();
        OrderItemResp orderItemDTO = RpcResultUtil.handle(orderItemQueryProvider.queryById(orderItemId));
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderItemDTO.getOrderId()));
        // 如果是组合支付 查询组合明细 后续确认退款服务类型
        List<Integer> combinedPayTypes;
        if (Objects.equals(orderDTO.getPayType(), PayTypeEnum.COMBINED_PAY.getCode())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderDTO.getTenantId(), orderDTO.getId());
            combinedPayTypes = combinedDetails.stream().map(detail -> {
                return com.cosfo.mall.common.constants.TradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
            }).collect(Collectors.toList());
        } else {
            combinedPayTypes = Lists.newArrayList();
        }
        List<OrderAfterSaleAddReq> createReqList = afterSaleCreateDTOS.stream().map(orderAfterSaleCreateDTO -> {
            OrderAfterSaleAddReq input = OrderAfterSaleConverter.INSTANCE.toAfterSaleReq(orderAfterSaleCreateDTO);
            input.setWarehouseType(orderDTO.getWarehouseType());
            input.setOrderId(ObjectUtil.isNotNull(input.getOrderId()) ? input.getOrderId() : orderItemDTO.getOrderId());
            input.setReason(ObjectUtil.isNotNull(input.getReason()) ? input.getReason() : "拍多/拍错/不想要");
            input.setCombinedPayTypes(combinedPayTypes);
            return input;
        }).collect(Collectors.toList());
        List<Long> afterSaleNos = RpcResultUtil.handle(orderAfterSaleCommandProvider.createPreDeliveryAfterSale(createReqList));
        return afterSaleNos;
    }

    @Override
    public Boolean cancel(Long orderAfterSaleId) {
        Boolean result = RpcResultUtil.handle(orderAfterSaleCommandProvider.cancel(orderAfterSaleId));
        return result;
    }

    @Override
    public ResultDTO<List<OrderAfterSaleResultDTO>> detail(Long orderItemId, LoginContextInfoDTO loginContextInfoDTO) {
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setOrderItemIds(Lists.newArrayList(orderItemId));
        queryReq.setTenantId(loginContextInfoDTO.getTenantId());
        queryReq.setStoreId(loginContextInfoDTO.getStoreId());
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(queryReq));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            ResultDTO.success(new ArrayList<>());
        }
        List<String> aftersaleNos = afterSaleDTOList.stream().filter(e -> StringUtil.isNotEmpty(e.getAfterSaleOrderNo())).map(OrderAfterSaleResp::getAfterSaleOrderNo).collect(Collectors.toList());
        List<FulfillmentDeliveryVO> fulfillmentDeliveryVOS = ofcServiceFacade.queryDelivery(aftersaleNos);

        List<Long> accountIds = afterSaleDTOList.stream().map(OrderAfterSaleResp::getAccountId).collect(Collectors.toList());
        List<MerchantStoreAccountResultResp> merchantStoreAccountDTOS = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountIds);
        Map<Long, MerchantStoreAccountResultResp> accountMap = merchantStoreAccountDTOS.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, item -> item));
        Map<String, List<FulfillmentDeliveryVO>> fulfillmentMap = fulfillmentDeliveryVOS.stream().collect(Collectors.groupingBy(FulfillmentDeliveryVO::getOrderNo));

        OrderItemAndSnapshotResp orderItemAndSnapshotDTO = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById(orderItemId));

        List<OrderAfterSaleResultDTO> result = afterSaleDTOList.stream().map(dto -> {
            OrderAfterSaleResultDTO resultDTO = OrderAfterSaleConverter.INSTANCE.toResultDTO(dto);
            // 补充门店信息
            MerchantStoreAccountResultResp merchantStoreAccountDTO = accountMap.get(dto.getAccountId());

            resultDTO.setApplyAccount(merchantStoreAccountDTO.getAccountName() + merchantStoreAccountDTO.getPhone());
            resultDTO.setTitle(orderItemAndSnapshotDTO.getTitle());
            resultDTO.setMainPicture(orderItemAndSnapshotDTO.getMainPicture());
            resultDTO.setSpecification(orderItemAndSnapshotDTO.getSpecification());
            resultDTO.setSpecificationUnit(orderItemAndSnapshotDTO.getSpecificationUnit());
            resultDTO.setPresaleSwitch(orderItemAndSnapshotDTO.getPresaleSwitch());
            resultDTO.setOrderAmount(orderItemAndSnapshotDTO.getAmount());
            resultDTO.setOrderPrice(orderItemAndSnapshotDTO.getTotalPrice());
            resultDTO.setPrice(orderItemAndSnapshotDTO.getPayablePrice());
            resultDTO.setAfterSaleUnit(com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(dto.getServiceType(), dto.getAfterSaleType())
                    ? orderItemAndSnapshotDTO.getAfterSaleUnit() : orderItemAndSnapshotDTO.getSpecificationUnit());

            resultDTO.setStatus(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.getShowStatus(dto.getStatus(), dto.getServiceType()));

            List<FulfillmentDeliveryVO> fulfillmentDeliveryVO = fulfillmentMap.get(dto.getAfterSaleOrderNo());
            if (CollectionUtil.isNotEmpty(fulfillmentDeliveryVO)) {
                if (Objects.equals(OrderAfterSaleServiceTypeEnum.RESEND.getValue(), dto.getServiceType())) {
                    resultDTO.setFulfillmentDeliveryVOList(fulfillmentDeliveryVO);
                } else {
                    resultDTO.setFulfillmentDeliveryVO(fulfillmentDeliveryVO.get(0));
                }
            }

            return resultDTO;
        }).collect(Collectors.toList());
        return ResultDTO.success(result);
    }


    private void updateAfterSaleStatus(Long afterSaleId, Integer sourceStatus, Integer targetStatus, String handleRemark) {
        OrderAfterSaleStatusUpdateReq statusUpdateReq = new OrderAfterSaleStatusUpdateReq();
        statusUpdateReq.setAfterSaleId(afterSaleId);
        statusUpdateReq.setSourceStatus(sourceStatus);
        statusUpdateReq.setTargetStatus(targetStatus);
        statusUpdateReq.setHandleRemark(handleRemark);
        RpcResultUtil.handle(orderAfterSaleCommandProvider.updateStatus(statusUpdateReq));
    }

    @Override
    public void payRefundSuccessDeal(Long afterSaleId) {
        if (afterSaleId == null) {
            log.info("售后id为空，售后退款成功流程处理结束");
            return;
        }
        log.info("更新售后单{}成功状态", afterSaleId);
        // 更新售后单状态
//        OrderAfterSaleInput orderAfterSaleInput = new OrderAfterSaleInput();
//        orderAfterSaleInput.setId(afterSaleId);
//        orderAfterSaleInput.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode());
//        orderAfterSaleInput.setFinishedTime(LocalDateTime.now());
//        orderAfterSaleServiceFacade.updateByPrimaryKey(orderAfterSaleInput);

        updateAfterSaleStatus(afterSaleId, null, OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), null);

        // 查询所有成功售后单
//        List<OrderAfterSaleDTO> orderAfterSales = orderAfterSaleServiceFacade.querySuccessOrderAfterSaleById(afterSaleId);
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(afterSaleId)));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            throw new BizException("售后单不存在");
        }
        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setOrderIds(Lists.newArrayList(afterSaleDTO.getOrderId()));
        queryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> successAfterSaleList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(queryReq));
        // 过滤掉换货、补发
        Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = successAfterSaleList.stream().filter(el ->
                (!el.getServiceType().equals(OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue()) && !el.getServiceType().equals(OrderAfterSaleServiceTypeEnum.RESEND.getValue()))).collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));

        if (!CollectionUtils.isEmpty(orderAfterSaleMap)) {
            // 获取订单号
            Long orderId = successAfterSaleList.get(0).getOrderId();
            Long tenantId = successAfterSaleList.get(0).getTenantId();
            // 查询所有订单项
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
//            List<OrderItemDTO> orderItems = orderItemMapper.queryByOrderId(tenantId, orderId);
            boolean isUpdateOrderStatus = true;
            // 判断是否所有订单项已退款
            for (OrderItemAndSnapshotResp orderItem : orderItemAndSnapshotDTOList) {
                List<OrderAfterSaleResp> orderAfterSaleDTOS = orderAfterSaleMap.get(orderItem.getOrderItemId());
                if (CollectionUtils.isEmpty(orderAfterSaleDTOS)) {
                    isUpdateOrderStatus = false;
                    continue;
                }
                Integer refundReceivedAmount = 0;
                Integer otherAmount = 0;
                for (OrderAfterSaleResp orderAfterSaleDTO : orderAfterSaleDTOS) {
                    Boolean receivedRefundFlag = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(orderAfterSaleDTO.getServiceType(), orderAfterSaleDTO.getAfterSaleType());
                    if (receivedRefundFlag) {
                        refundReceivedAmount += orderAfterSaleDTO.getAmount();
                        continue;
                    }
                    otherAmount += orderAfterSaleDTO.getAmount();
                }
                Integer totalApplyAmount = refundReceivedAmount + otherAmount * orderItem.getMaxAfterSaleAmount();
                if (totalApplyAmount.compareTo(orderItem.getMaxAfterSaleAmount() * orderItem.getAmount()) == 0) {
                    // 更新订单状态
                    OrderItemStatusUpdateReq updateReq = new OrderItemStatusUpdateReq();
                    updateReq.setOrderItemId(orderItem.getOrderItemId());
                    updateReq.setStatus(OrderItemStatusEnum.REFUND.getCode());
                    RpcResultUtil.handle(orderItemCommandProvider.updateStatus(updateReq));
//                    orderItemMapper.updateOrderItemStatus(orderItem.getTenantId(), orderItem.getOrderId(), orderItem.getOrderItemId(), OrderItemStatusEnum.REFUND.getCode(), null);
                } else {
                    isUpdateOrderStatus = false;
                }
            }

            // 如果订单项全部退款更新订单状态
            if (isUpdateOrderStatus) {
//                Order order = orderMapper.selectByPrimaryKey(orderId);
                OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
                log.info("更新订单{}已退款状态", orderId);
                Integer status = OrderStatusEnum.CLOSING.getCode().equals(orderDTO.getStatus()) ? OrderStatusEnum.CLOSED.getCode() : OrderStatusEnum.REFUNDED.getCode();
//                orderMapper.updateOrderStatus(orderId, null, tenantId, null, status, null);
                OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                updateReq.setOrderId(orderId);
                updateReq.setOriginStatus(orderDTO.getStatus());
                updateReq.setStatus(status);
                orderCommandProvider.updateStatus(updateReq);
            }
        }
    }


    @Override
    public ResultDTO<List<OrderAfterSaleResp>> queryAllAfterSale(String afterSaleOrderNo) {
//        OrderAfterSaleDTO orderAfterSale = orderAfterSaleServiceFacade.selectByAfterSaleOrderNo(afterSaleOrderNo);
        OrderAfterSaleResp afterSaleDTO = queryAfterSaleByNo(afterSaleOrderNo);
        if (afterSaleDTO == null) {
            return ResultDTO.fail(ResultDTOEnum.ORDER_AFTER_SALE_NOT_FOUND);
        }
        Long orderId = afterSaleDTO.getOrderId();
//        List<OrderAfterSaleDTO> afterSaleList = orderAfterSaleServiceFacade.querySuccessOrderAfterSaleByOrderId(orderId);
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setOrderIds(Lists.newArrayList(orderId));
        req.setStatusList(Lists.newArrayList(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> result = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(req));
        return ResultDTO.success(result);
    }

    private OrderAfterSaleResp queryAfterSaleByNo(String orderAfterSaleNo) {
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(orderAfterSaleNo)));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            return null;
        }
        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);
        return afterSaleDTO;
    }

    @Override
    public void afterSaleProcessFinish(List<AfterSaleFinishDeliveryDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, List<AfterSaleFinishDeliveryDTO>> afterSaleOrderDeliveryMap = list.stream().collect(Collectors.groupingBy(AfterSaleFinishDeliveryDTO::getOrderNo));
        afterSaleProcessFinishByAfterSaleNo(afterSaleOrderDeliveryMap);
    }

    private void afterSaleProcessFinishByAfterSaleNo(Map<String, List<AfterSaleFinishDeliveryDTO>> afterSaleOrderDeliveryMap) {
        afterSaleOrderDeliveryMap.forEach((afterSaleNo, dtos) -> {
//            OrderAfterSale orderAfterSale = afterSaleService.selectByAfterSaleOrderNo(afterSaleNo);
            List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(afterSaleNo)));
            if (CollectionUtils.isEmpty(afterSaleDTOList)) {
                log.error("未查询到该售后单:{}", afterSaleNo);
                return;
            }
            OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);

            if (!Objects.equals(afterSaleDTO.getStatus(), OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue())) {
                log.error("该售后单:{}状态不为三方处理中", afterSaleNo);
                return;
            }
            if (CollectionUtils.isEmpty(dtos)) {
                log.info("该售后单:{}回收信息为空", afterSaleNo);
                return;
            }

            // 是否是回收类型
            Integer serviceType = afterSaleDTO.getServiceType();
            boolean recycleType = (OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType) ||
                    Objects.equals(OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue(), serviceType));

            List<OrderAfterSaleProcessFinishReq> collect = dtos.stream().map(dto -> {
                OrderAfterSaleProcessFinishReq processFinishReq = new OrderAfterSaleProcessFinishReq();
                processFinishReq.setOrderAfterSaleNo(afterSaleNo);
                processFinishReq.setSku(dto.getSku());
                processFinishReq.setShouldCount(dto.getShouldCount());
                processFinishReq.setDeliveryType(dto.getDeliveryType());
                processFinishReq.setShortCount(dto.getShortCount());
                processFinishReq.setState(dto.getState());
                processFinishReq.setRemark(dto.getRemark());
                processFinishReq.setItemFinishType(dto.getType());
                // 构建回收信息
                builderRecycleMessageIfNeed(dto, processFinishReq, recycleType);
                return processFinishReq;
            }).collect(Collectors.toList());
            Boolean result = RpcResultUtil.handle(orderAfterSaleCommandProvider.processFinish(collect));

        });
    }

    /**
     * 构建回收信息
     *
     * @param dto
     * @param processFinishReq
     * @param recycleType                   是否是回收类型
     */
    private void builderRecycleMessageIfNeed(AfterSaleFinishDeliveryDTO dto, OrderAfterSaleProcessFinishReq processFinishReq, boolean recycleType) {
        if (!recycleType) {
            return;
        }
        // 消息內容类型为空、非回收，return
        if (Objects.isNull(dto.getType()) || !FulfillmentFinishMessageTypeEnum.RECYCLE.getType().equals(dto.getType())) {
            return;
        }
        CommonFulfillmentFinishMessageDetailRecycle detailRecycle = dto.getDetailRecycle();
        if (Objects.isNull(detailRecycle)) {
            log.info("回收类型不存在回收明细 AfterSaleFinishDeliveryDTO:{}", JSON.toJSONString(dto));
            return;
        }

        // 图片复制到saas
        if (Objects.nonNull(detailRecycle.getRecyclePics())) {
            pictureUpload(Lists.newArrayList(detailRecycle.getRecyclePics().split(",")));
            processFinishReq.setRecyclePicture(detailRecycle.getRecyclePics());
        }

        OrderAfterSaleRecycleDataDTO recycleDataDTO = OrderAfterSaleRecycleDataDTO.builder().shouldCount(dto.getShouldCount())
                .specificationQuantity(detailRecycle.getSpecificationQuantity()).basicSpecQuantity(detailRecycle.getBasicSpecQuantity())
                .specificationUnit(detailRecycle.getSpecificationUnit()).basicSpecUnit(detailRecycle.getBasicSpecUnit()).build();
        processFinishReq.setRecycleQuantityDetail(JSON.toJSONString(recycleDataDTO));
        String recycleDetails = Constants.NORMAL_RECYCLE_TEMPLATE;
        boolean isNormal = Objects.equals(dto.getState(), com.cosfo.ordercenter.client.common.DeliveryStateEnum.NORMAL.getState());
        if (!isNormal) {
            recycleDetails = Constants.ABNORMAL_RECYCLE_TEMPLATE;
        }

        // 回收数量明细
        String recycleQuantityDetail = org.apache.commons.lang3.StringUtils.join("应收", recycleDataDTO.getShouldCount(), recycleDataDTO.getSpecificationUnit(),
                "，实收", recycleDataDTO.getSpecificationQuantity(), recycleDataDTO.getSpecificationUnit(),
                recycleDataDTO.getBasicSpecQuantity(), recycleDataDTO.getBasicSpecUnit());

        String recycleRemark = org.apache.commons.lang3.StringUtils.join(detailRecycle.getReasonTypeDesc(), "。", detailRecycle.getRemark());
        boolean reasonTypeEmpty = StringUtils.isEmpty(detailRecycle.getReasonTypeDesc());
        if (reasonTypeEmpty) {
            recycleRemark = org.apache.commons.lang3.StringUtils.join(detailRecycle.getReasonTypeDesc(), detailRecycle.getRemark());
        }
        // 回收详细描述
        recycleDetails = org.apache.commons.lang3.StringUtils.join(recycleDetails,  recycleQuantityDetail, "；", recycleRemark);
        processFinishReq.setRecycleDetailMessage(recycleDetails);
    }

    private void pictureUpload(List<String> pictureList) {
        if (CollectionUtil.isEmpty(pictureList)) {
            return;
        }
        List<String> pictures = Lists.newArrayList();
        for (String originPicture : pictureList) {
            if (!StringUtils.isEmpty(originPicture)) {
                String[] detailPictures = originPicture.split(Constants.COMMA);
                pictures.addAll(Lists.newArrayList(detailPictures));
            }
        }

        List<String> result = pictures.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(result)) {
            String[] strings = new String[result.size()];
            QiNiuUtils.copyFileBatch(result.toArray(strings));
        }
    }

    @Override
    public void afterSaleProcessFinishByMessage(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage) {
        if (Objects.isNull(commonFulfillmentFinishMessage)) {
            return;
        }
        List<AfterSaleFinishDeliveryDTO> cosfoAfterSaleFinishDeliveryDTOS = transferAfterSaleFinishDeliveryDTOList(commonFulfillmentFinishMessage);
        afterSaleProcessFinish(cosfoAfterSaleFinishDeliveryDTOS);
    }

    public List<AfterSaleFinishDeliveryDTO> transferAfterSaleFinishDeliveryDTOList(CommonFulfillmentFinishMessage dto) {
        List<AfterSaleFinishDeliveryDTO> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dto.getItemList())) {
            return list;
        }
        for (CommonFulfillmentFinishMessageDetail commonFulfillmentFinishMessageDetail : dto.getItemList()) {
            AfterSaleFinishDeliveryDTO afterSaleFinishDeliveryDTO = new AfterSaleFinishDeliveryDTO();
            afterSaleFinishDeliveryDTO.setOrderNo(dto.getSourceOrderNo());
            afterSaleFinishDeliveryDTO.setState(commonFulfillmentFinishMessageDetail.getStatus());
            afterSaleFinishDeliveryDTO.setShouldCount(commonFulfillmentFinishMessageDetail.getQuantity());
            afterSaleFinishDeliveryDTO.setShortCount(commonFulfillmentFinishMessageDetail.getShortQuantity());
            afterSaleFinishDeliveryDTO.setDeliveryType(dto.getDeliveryType());
            afterSaleFinishDeliveryDTO.setRemark(dto.getMessage());
            afterSaleFinishDeliveryDTO.setType(commonFulfillmentFinishMessageDetail.getType());
            afterSaleFinishDeliveryDTO.setDetailRecycle(commonFulfillmentFinishMessageDetail.getDetailRecycle());
            list.add(afterSaleFinishDeliveryDTO);
        }
        return list;
    }


    @Override
    public ResultDTO<AfterOrderItemVO> queryItemInfo(String afterSaleOrderNo) {
//        OrderAfterSaleItemDTO orderAfterSaleItemDTO = orderAfterSaleServiceFacade.queryOrderAfterSaleItemInfo(afterSaleOrderNo);
        OrderAfterSaleResp afterSaleDTO = queryAfterSaleByNo(afterSaleOrderNo);
        if (afterSaleDTO == null) {
            throw new BizException("售后单不存在");
        }
        OrderItemSnapshotResp itemSnapshotDTO = RpcResultUtil.handle(orderItemSnapshotQueryProvider.queryByOrderItemId(afterSaleDTO.getOrderItemId()));
        OrderItemResp orderItemDTO = RpcResultUtil.handle(orderItemQueryProvider.queryById(afterSaleDTO.getOrderItemId()));
        AfterOrderItemVO data = OrderItemConverter.INSTANCE.toItemVO(itemSnapshotDTO);
        data.setAmount(orderItemDTO.getAmount());
        data.setItemId(orderItemDTO.getItemId());
        data.setAmount(orderItemDTO.getAmount());
        data.setTotalPrice(orderItemDTO.getTotalPrice());
        return ResultDTO.success(data);
    }

    @Override
    public void orderAfterSaleAutoFinished() {
//        orderAfterSaleServiceFacade.orderAfterSaleAutoFinished();
        RpcResultUtil.handle(orderAfterSaleCommandProvider.autoFinished());
    }


    @Override
    public BigDecimal calculateRefundPrice(Long orderItemId, Integer amount) {
//        OrderAfterSaleInput input = new OrderAfterSaleInput();
//        input.setOrderItemId(orderItemId);
//        input.setAmount(amount);
//        BigDecimal refundPrice = orderAfterSaleServiceFacade.calculateRefundPrice(input);
        OrderAfterSaleCalRefundPriceReq calRefundPriceReq = new OrderAfterSaleCalRefundPriceReq();
        calRefundPriceReq.setOrderItemId(orderItemId);
        calRefundPriceReq.setQuantity(amount);
        BigDecimal refundPrice = RpcResultUtil.handle(orderAfterSaleQueryProvider.calculateRefundPrice(calRefundPriceReq));
        return refundPrice;
    }

    @Override
    public Boolean verifyLastOrderItemOrderAfterSaleFlag(Long id) {
//        OrderAfterSaleResultDTO orderAfterSale = orderAfterSaleServiceFacade.selectByPrimaryKey(id);
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(id)));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            throw new BizException("售后单不存在");
        }
        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);
//        OrderItem orderItem = orderItemMapper.selectByPrimaryKey(orderAfterSale.getOrderItemId());
//        OrderItemDTO orderItemDTO = RpcResultUtil.handle(orderItemQueryProvider.queryById(orderAfterSale.getOrderItemId()));
//        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotService.selectByOrderItemId(orderAfterSale.getTenantId(), orderAfterSale.getOrderItemId());
        OrderItemAndSnapshotResp orderItemAndSnapshotDTO = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById(afterSaleDTO.getOrderItemId()));
//        OrderAfterSaleInput input = OrderAfterSaleInput.builder().tenantId(afterSaleDTO.getTenantId()).orderItemId(afterSaleDTO.getOrderItemId()).statusList(Arrays.asList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode(), OrderAfterSaleStatusEnum.REFUNDING.getCode())).build();
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setTenantId(afterSaleDTO.getTenantId());
        queryReq.setOrderItemIds(Lists.newArrayList(afterSaleDTO.getOrderItemId()));
        queryReq.setStatusList(Lists.newArrayList(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.REFUNDING.getValue()));
        List<OrderAfterSaleResp> afterSaleList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(queryReq));

//        List<OrderAfterSaleResultDTO> successOrderAfterSales = orderAfterSaleServiceFacade.queryAfterSaleOrderByOrderId(input);
        //successOrderAfterSales.add(orderAfterSale);

        Integer totalAfterSaleAmount = 0;
        Integer maxAfterSaleAmount = orderItemAndSnapshotDTO.getMaxAfterSaleAmount();
        for (OrderAfterSaleResp successOrderAfterSale : afterSaleList) {
            boolean refundReceivedFlag = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(successOrderAfterSale.getServiceType(), successOrderAfterSale.getAfterSaleType());
            Integer orderAfterSaleAmount = successOrderAfterSale.getAmount();
            if (refundReceivedFlag) {
                totalAfterSaleAmount += orderAfterSaleAmount;
                continue;
            }
            totalAfterSaleAmount = maxAfterSaleAmount * orderAfterSaleAmount;
        }
        Boolean allAmountAfterSaleFlag = Objects.equals(totalAfterSaleAmount, orderItemAndSnapshotDTO.getAmount() * orderItemAndSnapshotDTO.getMaxAfterSaleAmount());
        if (!allAmountAfterSaleFlag) {
            return Boolean.FALSE;
        }
        // 金额判断
        BigDecimal totalAfterSalePrice = afterSaleList.stream().reduce(BigDecimal.ZERO, (x, y) -> x.add(NumberUtil.sub(y.getTotalPrice(), y.getDeliveryFee())), BigDecimal::add);
        return totalAfterSalePrice.compareTo(orderItemAndSnapshotDTO.getTotalPrice()) == 0;
    }

    @Override
    public WarehouseStorageVO queryOneWarehouseStorage(String afterSaleOrderNo) {
        WarehouseStorageVO warehouseStorageVO = null;
//        OrderAfterSaleDTO orderAfterSale = orderAfterSaleServiceFacade.selectByAfterSaleOrderNo(afterSaleOrderNo);
        OrderAfterSaleResp afterSaleDTO = queryAfterSaleByNo(afterSaleOrderNo);
//        if (!ObjectUtil.isNull(afterSaleDTO)) {
        if (afterSaleDTO != null) {
            if (OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode().equals(afterSaleDTO.getWarehouseType())) {
                DubboResponse<TenantReturnAddressResp> resp = tenantReturnAddressProvider.getTenantReturnAddress(afterSaleDTO.getReturnAddressId());
                if (!resp.isSuccess() || resp.getData() == null) {
                    log.error("获取退回地址失败, afterSaleOrderNo={}, resp={}", afterSaleOrderNo, resp);
                    return warehouseStorageVO;
                }

                TenantReturnAddressResp data = resp.getData();
                warehouseStorageVO = new WarehouseStorageVO();
                StringBuilder address = new StringBuilder()
                        .append(data.getProvince())
                        .append(data.getCity())
                        .append(data.getArea())
                        .append(data.getAddress())
                        .append(data.getHouseNo() == null ? "" : data.getHouseNo());
                warehouseStorageVO.setAddress(address.toString());
                warehouseStorageVO.setPersonContact(data.getContactName());
                warehouseStorageVO.setPhone(data.getContactPhone());

            } else {
                String returnWarehouseNo = afterSaleDTO.getReturnWarehouseNo();
                if (StringUtils.isEmpty(returnWarehouseNo)) {
                    Long orderId = afterSaleDTO.getOrderId();
//                    Order order = orderMapper.selectByPrimaryKey(orderId);
                    OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(afterSaleDTO.getOrderId()));
                    if (!ObjectUtil.isNull(orderDTO) && !Objects.isNull(orderDTO.getWarehouseNo())) {
                        returnWarehouseNo = String.valueOf(orderDTO.getWarehouseNo());
                    }
                }

                if (!StringUtils.isEmpty(returnWarehouseNo)) {
                    warehouseStorageVO = wncServiceFacade.queryOneWarehouseStorage(Integer.valueOf(returnWarehouseNo));
                }
            }
        }
        log.info("查询退货地址queryOneWarehouseStorage，afterSaleOrderNo={},response={}", afterSaleOrderNo, JSON.toJSONString(warehouseStorageVO));
        return warehouseStorageVO;
    }

    @Override
    public void addSaleLogistics(LogisticsDTO dto, LoginContextInfoDTO loginContextInfoDTO) {

//        OrderAfterSaleDTO orderAfterSale = orderAfterSaleServiceFacade.selectByAfterSaleOrderNo(dto.getAfterSaleOrderNo());
        OrderAfterSaleResp afterSaleDTO = queryAfterSaleByNo(dto.getAfterSaleOrderNo());
        if (Objects.isNull(afterSaleDTO)) {
            throw new BizException("未查询到售后订单");
        }
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(afterSaleDTO.getOrderId()));
//        OrderAfterSaleInput orderAfterSaleInput = new OrderAfterSaleInput();
//        orderAfterSaleInput.setId(afterSaleDTO.getId());
//        orderAfterSaleInput.setStatus(OrderAfterSaleStatusEnum.REFUNDDDING_GOODS.getCode());
//        orderAfterSaleServiceFacade.updateByPrimaryKey(orderAfterSaleInput);
        updateAfterSaleStatus(afterSaleDTO.getId(), afterSaleDTO.getStatus(), com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue(), null);
        // 兼容自营仓历史数据，在售后单上没有退货仓的取订单发货仓
        Long returnWarehouseNo = null;
        if (StringUtils.isEmpty(afterSaleDTO.getReturnWarehouseNo())) {
            if (!StringUtils.isEmpty(orderDTO.getWarehouseNo())) {
                returnWarehouseNo = Long.valueOf(orderDTO.getWarehouseNo());
            }
        } else {
            returnWarehouseNo = Long.valueOf(afterSaleDTO.getReturnWarehouseNo());
        }
//        Long returnWarehouseNo = org.springframework.util.StringUtils.isEmpty(afterSaleDTO.getReturnWarehouseNo()) ? Long.valueOf(orderDTO.getWarehouseNo()) : Long.valueOf(afterSaleDTO.getReturnWarehouseNo());
        try {
            ofcServiceFacade.insertAfterSaleLogistics(dto, loginContextInfoDTO.getAccountName(), returnWarehouseNo);
        } catch (Exception e) {
//            orderAfterSaleInput.setStatus(OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getCode());
//            orderAfterSaleServiceFacade.updateByPrimaryKey(orderAfterSaleInput);
            updateAfterSaleStatus(afterSaleDTO.getId(), null, com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue(), null);
            throw new BizException("保存物流信息错误");
        }
    }

    @Override
    public void upadteSaleLogistics(LogisticsDTO dto, LoginContextInfoDTO loginContextInfoDTO) {
        ofcServiceFacade.upadteSaleLogistics(dto, loginContextInfoDTO.getAccountName());
    }

    @Override
    public List<FastMallVO> queryFastMallList() {
        List<FastMallVO> fastMallVOS = wmsServiceFacade.queryFastMallList();
        return fastMallVOS;
    }

    @Override
    public ResultDTO<OrderConsultationVO> consultationDetail(Long orderItemId, LoginContextInfoDTO requestContextInfoDTO) {
        OrderConsultationVO orderConsultationVO = new OrderConsultationVO();
        Long orderId = null;
//        OrderItem orderItem = orderItemMapper.selectByPrimaryKey(orderItemId);
        OrderItemResp orderItemDTO = RpcResultUtil.handle(orderItemQueryProvider.queryById(orderItemId));
        if (Objects.nonNull(orderItemDTO)) {
//            Order order = orderMapper.selectByPrimaryKey(orderItem.getOrderId());
            OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderItemDTO.getOrderId()));
            orderConsultationVO.setOrderNo(Optional.ofNullable(orderDTO).map(OrderResp::getOrderNo).orElse(null));
            orderId = Optional.ofNullable(orderDTO).map(OrderResp::getId).orElse(null);
        }
        if (Objects.nonNull(orderId)) {

            // 查询订单地址信息
//            OrderAddressDTO orderAddressDTO = orderAddressMapper.selectByOrderId(orderId, requestContextInfoDTO.getTenantId());
            OrderAddressResp orderAddressDTO = RpcResultUtil.handle(orderAddressQueryProvider.queryByOrderId(requestContextInfoDTO.getTenantId(), orderId));
            orderConsultationVO.setOrderAddressDTO(orderAddressDTO);
        }
        return ResultDTO.success(orderConsultationVO);
    }


    @Override
    public ResultDTO<OrderItemAfterSaleDetailInfoDTO> getOrderItemAfterSale(Long orderItemId, LoginContextInfoDTO requestContextInfoDTO) {
        OrderItemAfterSaleDetailInfoDTO result = new OrderItemAfterSaleDetailInfoDTO();
        OrderItemResp orderItem = RpcResultUtil.handle(orderItemQueryProvider.queryById(orderItemId));
        // 判断售后到期时间
        // 校验售后时间
        if (Objects.nonNull(orderItem.getAfterSaleExpiryTime()) && orderItem.getAfterSaleExpiryTime().compareTo(LocalDateTime.now()) < NumberConstant.ZERO) {
            throw new BizException("当前时间已经超过售后时间，不可进行售后");
        }
        OrderItemSnapshotResp orderItemSnapshotDTO = RpcResultUtil.handle(orderItemSnapshotQueryProvider.queryByOrderItemId(orderItemId));
        result.setOrderItemId(orderItemId);
        result.setAmount(orderItem.getAmount());
        result.setPrice(orderItem.getPayablePrice());
        result.setTotalPrice(orderItem.getTotalPrice());
        result.setTitle(orderItemSnapshotDTO.getTitle());
        result.setMainPicture(orderItemSnapshotDTO.getMainPicture());
        result.setSpecification(orderItemSnapshotDTO.getSpecification());
        result.setAfterSaleUnit(orderItemSnapshotDTO.getAfterSaleUnit());
        OrderAfterSaleEnableApplyReq orderAfterSaleEnableApplyReq = new OrderAfterSaleEnableApplyReq();
        orderAfterSaleEnableApplyReq.setOrderId(orderItem.getOrderId());
        orderAfterSaleEnableApplyReq.setTenantId(orderItem.getTenantId());
        orderAfterSaleEnableApplyReq.setOrderItemId(orderItem.getId());
        Map<Long, OrderAfterSaleEnableResp> enableApplyMap = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryEnableApply(orderAfterSaleEnableApplyReq));
        OrderAfterSaleEnableResp orderAfterSaleEnableDTO = enableApplyMap.get(orderItemId);
        result.setEnableApplyAmount(orderAfterSaleEnableDTO.getEnableApplyAmount());
        result.setEnableApplyQuantity(orderAfterSaleEnableDTO.getEnableApplyQuantity());

        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderItem.getOrderId()));
        result.setAfterSaleType(
                Objects.equals(com.cosfo.ordercenter.client.common.OrderStatusEnum.WAIT_DELIVERY.getCode(), orderDTO.getStatus())
                        || com.cosfo.ordercenter.client.common.OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderDTO.getStatus())
                        ? OrderAfterSaleTypeEnum.NOT_SEND.getType() : OrderAfterSaleTypeEnum.DELIVERED.getType());
        result.setPayType(orderDTO.getPayType());
        return ResultDTO.success(result);
    }

    @Override
    public ResultDTO<PageDTO<OrderAfterSaleResultDTO>> list(OrderAfterSaleQueryDTO orderAfterSaleDTO, LoginContextInfoDTO loginContextInfoDTO) {
//        OrderAfterSaleInput input = OrderAfterSaleInput.builder().tenantId(loginContextInfoDTO.getTenantId()).storeId(loginContextInfoDTO.getStoreId()).processFlag(Objects.nonNull(orderAfterSaleDTO.getStatus())).pageIndex(orderAfterSaleDTO.getPageNum()).pageSize(orderAfterSaleDTO.getPageSize()).build();
        OrderAfterSalePageQueryReq pageQueryReq = new OrderAfterSalePageQueryReq();
        pageQueryReq.setTenantId(loginContextInfoDTO.getTenantId());
        pageQueryReq.setStoreIds(Lists.newArrayList(loginContextInfoDTO.getStoreId()));
        if (orderAfterSaleDTO.getStatus() != null) {
            pageQueryReq.setStatusList(Arrays.asList(
                    OrderAfterSaleStatusEnum.UNAUDITED.getValue(),
                    OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
                    OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                    OrderAfterSaleStatusEnum.INVENTORY_FAIl.getValue(),
                    OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
                    OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(),
                    OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue()
            ));
        }

        pageQueryReq.setPageNum(orderAfterSaleDTO.getPageNum());
        pageQueryReq.setPageSize(orderAfterSaleDTO.getPageSize());
        PageInfo<OrderAfterSaleWithOrderResp> afterSalePage = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryPage(pageQueryReq));

        return ResultDTO.success(OrderAfterSaleConverter.INSTANCE.pageInfoToDTONew(afterSalePage));
    }

    @Override
    public List<OrderAfterSaleResp> queryAllFinishedAfterSaleOrderByOrderId(Long tenantId, Long orderId) {

        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setTenantId(tenantId);
        afterSaleQueryReq.setOrderIds(Lists.newArrayList(orderId));
        afterSaleQueryReq.setStatusList(Lists.newArrayList(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        return RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));
    }

    @Override
    public List<OrderAfterSaleResp> queryAfterSaleByOrderIdAndStatus(Long tenantId, Long orderId, List<Integer> status) {
        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setTenantId(tenantId);
        afterSaleQueryReq.setOrderIds(Lists.newArrayList(orderId));
        afterSaleQueryReq.setStatusList(status);
        return RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));
    }

    @Override
    public OrderAfterSaleResp queryById(Long orderAfterSaleId) {
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(orderAfterSaleId)));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            return null;
        }
        return afterSaleDTOList.get(0);
    }
}
