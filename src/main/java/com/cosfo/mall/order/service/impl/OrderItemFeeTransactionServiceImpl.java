package com.cosfo.mall.order.service.impl;

import com.cosfo.mall.order.dao.OrderItemFeeTransactionDao;
import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionQueryDTO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;
import com.cosfo.mall.order.service.OrderItemFeeTransactionService;
import com.cosfo.mall.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 订单明细费用交易服务层
 * @date 2023/4/11 11:19
 */
@Service
@Slf4j
public class OrderItemFeeTransactionServiceImpl implements OrderItemFeeTransactionService {

    @Resource
    private OrderItemFeeTransactionDao orderItemFeeTransactionDao;
    @Resource
    private OrderService orderService;

    @Override
    public boolean saveOrderItemFeeTransaction(List<OrderItemFeeTransaction> transactionList) {
        if (CollectionUtils.isEmpty(transactionList)) {
            return false;
        }
        return orderItemFeeTransactionDao.saveBatch(transactionList);
    }

    @Override
    public List<OrderItemFeeTransaction> queryByCondition(OrderItemFeeTransactionQueryDTO orderItemFeeTransactionQueryDTO) {
        return orderItemFeeTransactionDao.queryByCondition(orderItemFeeTransactionQueryDTO);
    }

    @Override
    public boolean existsByOrderIds(List<Long> orderIds) {
        return orderItemFeeTransactionDao.existsByOrderIds(orderIds);
    }

    @Override
    public void generateOrderItemFee(List<Long> orderIds) {
        boolean existed = checkIfOrderItemFeeExist(orderIds);
        if (existed) {
            log.info("订单:{}费用已存在，无需重复生成", orderIds);
            return;
        }
        for (Long orderId : orderIds) {
            orderService.saveOrderItemFeeTransaction(orderId);
        }
    }

    private boolean checkIfOrderItemFeeExist(List<Long> orderIds) {
        return existsByOrderIds(orderIds);
    }
}
