package com.cosfo.mall.order.service;

import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
public interface OrderAgentSkuFeeRuleService {
    /**
     * 保存下单时代仓商品收费规则
     *
     * @param tenantId
     * @param orderId
     */
    void saveOrderAgentSkuFeeRule(Long tenantId, Long orderId);

    /**
     * 更新命中规则
     * @param tenantId
     * @param orderId
     * @param hitRule
     */
    void updateOrderHitRule(Long tenantId, Long orderId, ProductAgentSkuFeeRuleDetailDTO hitRule);

    /**
     * 返回该订单项的售后需要扣除的代仓费用金额
     *
     * @param orderAfterSale
     * @return
     */
    BigDecimal calculateAgentFeeByOrder(OrderAfterSaleResp orderAfterSale);

    /**
     * 根据租户id和orderId查询
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    List<OrderAgentSkuFeeRuleSnapshot> queryByTenantIdAndOrderId(Long tenantId, Long orderId);
}
