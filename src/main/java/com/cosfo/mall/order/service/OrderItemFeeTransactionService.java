package com.cosfo.mall.order.service;

import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionQueryDTO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;

import java.util.List;

/**
 * @desc 订单明细费用交易服务层
 * <AUTHOR>
 * @date 2023/4/11 11:19
 */
public interface OrderItemFeeTransactionService {

    /**
     * 保存订单明细费用项
     * @param transactionList
     * @return
     */
    boolean saveOrderItemFeeTransaction(List<OrderItemFeeTransaction> transactionList);

    /**
     * 根据条件查询订单明细交易流水
     * @param orderItemFeeTransactionQueryDTO
     * @return
     */
    List<OrderItemFeeTransaction> queryByCondition(OrderItemFeeTransactionQueryDTO orderItemFeeTransactionQueryDTO);


    /**
     * 根据订单id查询订单明细费用是否存在
     *
     * @param orderIds
     * @return
     */
    boolean existsByOrderIds(List<Long> orderIds);

    /**
     * 生成订单明细费用
     *
     * @param orderIds
     */
    void generateOrderItemFee(List<Long> orderIds);
}
