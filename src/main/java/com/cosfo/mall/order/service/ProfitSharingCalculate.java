package com.cosfo.mall.order.service;

import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
public interface ProfitSharingCalculate {
    /**
     * 获取策略执行器
     *
     * @param deliveryType 配送方式0品牌方1三方仓2无仓
     * @param profitSharingRuleType 分账金额类型分账金额类型 1,自营商品金额2，供应商商品金额3，代仓商品金额4运费5订单手续费
     * @param type  0部分分账给品牌方1全部分账给品牌方
     * @return
     */
    boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type);

    /**
     * 分账计算
     *
     * @param billProfitSharingSnapshotDtos
     */
    void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap);
}
