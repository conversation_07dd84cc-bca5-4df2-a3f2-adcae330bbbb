package com.cosfo.mall.order.service.impl;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.service.AreaService;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.facade.ProductQueryFacade;
import com.cosfo.mall.market.model.dto.SkuMallPriceDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemPriceService;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.order.mapper.TrolleyMapper;
import com.cosfo.mall.order.model.po.Trolley;
import com.cosfo.mall.order.model.vo.TrolleyDetailVO;
import com.cosfo.mall.order.model.vo.TrolleyItemVO;
import com.cosfo.mall.order.service.TrolleyService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 购物车服务类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@Service
@Slf4j
public class TrolleyServiceImpl implements TrolleyService {
    @Resource
    private TrolleyMapper trolleyMapper;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private TenantService tenantService;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private AreaService areaService;
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private MarketItemPriceService marketItemPriceService;
    @Resource
    private ProductQueryFacade productQueryFacade;


    @Override
    public ResultDTO<List<TrolleyDetailVO>> get(LoginContextInfoDTO loginContextInfoDTO) {
        // 获取购物车中商品信息
        List<Trolley> trolleys = trolleyMapper.selectByAccountId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId());
        // 返回对象
        List<TrolleyDetailVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trolleys)) {
            // 获取所有商品项
            List<Long> itemIds = trolleys.stream().map(Trolley::getItemId).collect(Collectors.toList());
            List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());
            Map<Long, MarketItemVO> marketItemVOMap = marketItemVOS.stream().collect(Collectors.toMap(MarketItemVO::getItemId, item -> item));

            // 查询下单地址
            MerchantAddress merchantAddress = merchantAddressService.queryDefaultAddress(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId());

            // 不检查poi
//            if (StringUtils.isBlank(merchantAddress.getPoiNote())) {
//                return ResultDTO.fail(ResultDTOEnum.ADDRESS_POI_MISSING.getCode(), ResultDTOEnum.ADDRESS_POI_MISSING.getMessage());
//            }

            MerchantAddressDTO merchantAddressDTO = MerchantAddressMapperConvert.INSTANCE.address2Dto(merchantAddress);
//            CommonLocationCityDTO commonLocationCityDTO = areaService.selectByCityName(merchantAddress.getCity());
//            merchantAddressDTO.setCityId(commonLocationCityDTO.getId());


            // 报价货品ID
            List<Long> quotationSkuIds = marketItemVOS.stream().filter(marketItemVO -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemVO.getGoodsType())).map(MarketItemVO::getSkuId).collect(Collectors.toList());
            // 查询货品和鲜沐商品的映射关系
            Map<Long, ProductAgentSkuDTO> quotationProductAgentSkuDTOMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(quotationSkuIds)) {
                List<ProductAgentSkuDTO> quotationProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(quotationSkuIds, XianmuSupplyTenant.TENANT_ID);
                quotationProductAgentSkuDTOMap = quotationProductAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
            }

            // 自营货品ID
            List<Long> selfSupportSkuIds = marketItemVOS.stream().filter(marketItemVO -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType())).map(MarketItemVO::getSkuId).collect(Collectors.toList());
            // 查询货品和鲜沐商品的映射关系
            Map<Long, ProductAgentSkuDTO> selfSupportProductAgentSkuDTOMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(selfSupportSkuIds)) {
                List<ProductAgentSkuDTO> selfSupportProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(selfSupportSkuIds, loginContextInfoDTO.getTenantId());
                selfSupportProductAgentSkuDTOMap = selfSupportProductAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
            }

            // 获取sku价格信息
            ResultDTO resultDTO = tenantService.selectTenantInfo(loginContextInfoDTO.getTenantId());
            TenantDTO tenantDTO = (TenantDTO) resultDTO.getData();
            // 购买数量
            Map<Long, Integer> itemBuyAmount = trolleys.stream().collect(Collectors.toMap(Trolley::getItemId, Trolley::getAmount));
            // 获取商品商城价
            Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOS, itemBuyAmount, tenantDTO, merchantAddressDTO,false);
            // 数据整合
            list = convertToMarketItemVO(trolleys, marketItemVOMap, merchantAddressDTO, skuMallPriceDTOMap);
        }

        return ResultDTO.success(list);
    }

    private List<TrolleyDetailVO> convertToMarketItemVO(List<Trolley> trolleys, Map<Long, MarketItemVO> marketItemVOMap, MerchantAddressDTO merchantAddressDTO, Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap) {
        if (CollectionUtils.isEmpty(trolleys)) {
            return new ArrayList<>();
        }

        // 购物车
        List<TrolleyDetailVO> list = new ArrayList<>();
        // 失效商品
        List<MarketItemVO> failureGoods = new ArrayList<>();
        // 无货订单
        TrolleyDetailVO proprietaryDelivery = new TrolleyDetailVO();
        proprietaryDelivery.setSupplierName(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getName());
        proprietaryDelivery.setValidFlag(Boolean.TRUE);
        proprietaryDelivery.setGoods(new ArrayList<>());

        // 三方订单 -> 根据配送日期拆单
        Map<LocalDate, TrolleyDetailVO> threeDeliveryMap = new HashMap<>();

        // 预售商品拆单，一个预售品一单
        List<TrolleyDetailVO> presaleItemList = new ArrayList<>();

        // 自营仓订单 -> 根据仓编号拆单
        Map<Long, TrolleyDetailVO> trolleyDetailVOMap = new HashMap<>(NumberConstant.SIXTEEN);

        // 遍历购物车商品
        trolleys.forEach(trolley -> {
            if (marketItemVOMap.containsKey(trolley.getItemId())) {
                // 商品信息
                MarketItemVO marketItemVO = marketItemVOMap.get(trolley.getItemId());

                // 购物车商品数量
                marketItemVO.setAmount(trolley.getAmount());
                marketItemVO.setValidFlag(Boolean.TRUE);
                SkuMallPriceDTO skuMallPriceDTO = null;
                // 商城价
                skuMallPriceDTO = skuMallPriceDTOMap.get(marketItemVO.getItemId());
                // 未获取到鲜沐报价价格，商品项失效
                if (Objects.isNull(skuMallPriceDTO) || Objects.isNull(skuMallPriceDTO.getPrice())) {
                    marketItemVO.setValidFlag(false);
                    marketItemVO.setEnableAmount(NumberConstant.ZERO);
                    failureGoods.add(marketItemVO);
                    return;
                } else {
                    marketItemVO.setPrice(skuMallPriceDTO.getPrice());
                }

                marketItemVO.setLadderPrices (skuMallPriceDTO.getLadderPrices ());

                // 商品是否删除 或者下架，删除商品放入失效商品列表
                if (Objects.equals(marketItemVO.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag())
                        || Objects.equals(marketItemVO.getOnSale(), OnSaleTypeEnum.SOLD_OUT.getCode())) {
                    marketItemVO.setValidFlag(TrolleyValidFlagEnum.VALID.getFlag());
                    failureGoods.add(marketItemVO);
                    return;
                }

                proprietaryDelivery.setWarehouseType(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode());
                proprietaryDelivery.getGoods().add(marketItemVO);
            }
        });

        if (!CollectionUtils.isEmpty(proprietaryDelivery.getGoods())) {
            list.add(proprietaryDelivery);
        }

        if (!CollectionUtils.isEmpty(presaleItemList)) {
            list.addAll(presaleItemList);
        }

        if (!CollectionUtils.isEmpty(threeDeliveryMap)) {
            list.addAll(threeDeliveryMap.values());
        }

        if (!CollectionUtils.isEmpty(trolleyDetailVOMap)) {
            List<TrolleyDetailVO> trolleyDetailVOS = trolleyDetailVOMap.values().stream().collect(Collectors.toList());
            list.addAll(trolleyDetailVOS);
        }

        if (failureGoods.size() > 0) {
            // 失效商品
            TrolleyDetailVO failureTrolley = new TrolleyDetailVO();
            failureTrolley.setValidFlag(false);
            failureTrolley.setSupplierName("失效商品");
            failureTrolley.setGoods(failureGoods);
            list.add(failureTrolley);
        }

        return list;
    }

    private TrolleyDetailVO buildThreeDelivery(LocalDate deliveryDate, Integer fulfillmentType) {
        TrolleyDetailVO threeDelivery = new TrolleyDetailVO();
        threeDelivery.setValidFlag(Boolean.TRUE);
        threeDelivery.setSupplierName(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getName());
        threeDelivery.setWarehouseType(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
        threeDelivery.setGoods(new ArrayList<>());
        threeDelivery.setDeliveryTime(deliveryDate);
        threeDelivery.setFulfillmentType(fulfillmentType);
        return threeDelivery;
    }

    @Override
    public ResultDTO clear(LoginContextInfoDTO loginContextInfoDTO) {
        Trolley trolley = new Trolley();
        trolley.setTenantId(loginContextInfoDTO.getTenantId());
        trolley.setStoreId(loginContextInfoDTO.getStoreId());
        trolley.setAccountId(loginContextInfoDTO.getAccountId());
        trolleyMapper.delete(trolley);
        return ResultDTO.success();
    }

    @Override
    public ResultDTO<Integer> add(Long itemId, Integer quantity, Integer type, LoginContextInfoDTO loginContextInfoDTO) {
        // 校验商品信息
        AssertCheckParams.notNull(itemId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "商品Id不能为空");
        // 判断
        if (quantity == null || quantity < 1) {
            return ResultDTO.fail("添加商品数量有误");
        }
        List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(Collections.singletonList(itemId), loginContextInfoDTO.getTenantId());
        Integer miniOrderQuantity = marketItemVOS.stream().findFirst().orElseGet(() -> new MarketItemVO()).getMiniOrderQuantity();


        if (type.equals(TrolleyAddTypeEnum.TROLLEY_PAGE.getCode())) {//购物车直接更新
            Trolley trolley = trolleyMapper.selectByItemId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId(), itemId);
            if (trolley == null) {
                trolley = new Trolley();
                List<Trolley> trolleys = trolleyMapper.selectByAccountId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId());
                if (trolleys.size() > TrolleyConstants.MAX_NUM) {
                    throw new DefaultServiceException(ResultDTOEnum.TROLLEY_ERROR.getCode(), "当前购物车中商品种类数量超过最大可添加数量，不能继续加购");
                }
                quantity = quantity < miniOrderQuantity ? miniOrderQuantity : quantity;
                // 添加商品
                trolley.setTenantId(loginContextInfoDTO.getTenantId());
                trolley.setStoreId(loginContextInfoDTO.getStoreId());
                trolley.setAccountId(loginContextInfoDTO.getAccountId());
                trolley.setItemId(itemId);
                trolley.setAmount(quantity);
                trolleyMapper.merge(trolley);
            } else {
                trolley.setAmount(quantity < miniOrderQuantity ? miniOrderQuantity : quantity);
                trolleyMapper.updateByPrimaryKeySelective(trolley);
            }
        } else {//其他累加传入数量
            Trolley trolley = trolleyMapper.selectByItemId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId(), itemId);
            if (trolley == null) {
                trolley = new Trolley();
                List<Trolley> trolleys = trolleyMapper.selectByAccountId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId());
                if (trolleys.size() > TrolleyConstants.MAX_NUM) {
                    throw new DefaultServiceException(ResultDTOEnum.TROLLEY_ERROR.getCode(), "当前购物车中商品种类数量超过最大可添加数量，不能继续加购");
                }
                quantity = quantity < miniOrderQuantity ? miniOrderQuantity : quantity;
            } else {
                quantity = quantity + trolley.getAmount() < miniOrderQuantity ? miniOrderQuantity - trolley.getAmount() : quantity;
            }

            // 添加商品
            trolley.setTenantId(loginContextInfoDTO.getTenantId());
            trolley.setStoreId(loginContextInfoDTO.getStoreId());
            trolley.setAccountId(loginContextInfoDTO.getAccountId());
            trolley.setItemId(itemId);
            trolley.setAmount(quantity);
            trolleyMapper.merge(trolley);
        }

        Trolley trolley = trolleyMapper.selectByItemId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId(), itemId);
        if (trolley == null) {
            throw new BizException(ResultDTOEnum.TROLLEY_ERROR.getCode(), "当前购物车中商品不存在");
        }

        return ResultDTO.success(trolley.getAmount());
    }

    @Override
    public List<TrolleyItemVO> getByItemIds(List<Long> itemIds, LoginContextInfoDTO loginContextInfoDTO) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyList();
        }
        List<Trolley> trolleyList = trolleyMapper.selectByItemIds(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId(), itemIds);

        List<TrolleyItemVO> resultList = Optional.ofNullable(trolleyList)
                .orElse(Collections.emptyList())
                .stream()
                .map(e -> {
                    TrolleyItemVO trolleyItemVO = new TrolleyItemVO();
                    trolleyItemVO.setItemId(e.getItemId());
                    trolleyItemVO.setQuantity(e.getAmount());
                    return trolleyItemVO;
                }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public ResultDTO remove(List<Long> itemIds, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.isTrue(!CollectionUtils.isEmpty(itemIds), ResultDTOEnum.PARAMETER_MISSING.getCode(), "商品Id不能为空");
        Trolley trolley = new Trolley();
        trolley.setTenantId(loginContextInfoDTO.getTenantId());
        trolley.setStoreId(loginContextInfoDTO.getStoreId());
        trolley.setAccountId(loginContextInfoDTO.getAccountId());
        for (Long itemId : itemIds) {
            trolley.setItemId(itemId);
            trolleyMapper.deleteByItemId(trolley);
        }
        return ResultDTO.success();
    }
}
