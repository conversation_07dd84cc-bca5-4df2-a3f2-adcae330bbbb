package com.cosfo.mall.order.service.impl;

import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.order.mapper.OrderItemMapper;
import com.cosfo.mall.order.model.po.OrderItem;
import com.cosfo.mall.order.service.OrderItemService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/3/17 18:35
 */
@Service
public class OrderItemServiceImpl implements OrderItemService {

    @Resource
    private OrderItemMapper orderItemMapper;

    @Override
    public OrderItem queryById(Long id) {
        List<OrderItem> orderItems = orderItemMapper.queryByIds(Arrays.asList(id));
        return CollectionUtils.isEmpty(orderItems) ? null : orderItems.get(NumberConstant.ZERO);
    }

}
