package com.cosfo.mall.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;
import com.cosfo.mall.common.context.DeliveryTypeEnum;
import com.cosfo.mall.common.context.TenantTypeEnum;
import com.cosfo.mall.common.utils.AssertCheckDefault;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Service
public class SupplyProductCalculate implements ProfitSharingCalculate {
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private TenantService tenantService;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;


    @Override
    public boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        return DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(deliveryType) && ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode().equals(profitSharingRuleType);
    }

    @Override
    public void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        // 查询订单信息
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getOrderId()));
        // 查询订单项信息
        List<OrderItemAndSnapshotResp> orderItemList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderDTO.getId()));
        // 查询已经完成的售后单
        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setOrderIds(Lists.newArrayList(orderDTO.getId()));
        orderAfterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(orderAfterSaleQueryReq));
        // 计算商品价格
        Map<Long, List<OrderAfterSaleResp>> map = afterSaleDTOList.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));

        BigDecimal productTotalPrice = BigDecimal.ZERO, supplierProductTotalPrice = BigDecimal.ZERO;
        for (OrderItemAndSnapshotResp orderItem : orderItemList) {
            // 鲜沐直供商品
            if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType()) && GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItem.getGoodsType())) {
                productTotalPrice = NumberUtil.add(productTotalPrice, orderItem.getTotalPrice());
                BigDecimal supplierProductPrice = NumberUtil.mul(orderItem.getSupplyPrice(), orderItem.getAmount());
                BigDecimal refundTotalPrice = BigDecimal.ZERO;
                if (map.containsKey(orderItem.getOrderItemId())) {
                    List<OrderAfterSaleResp> orderAfterSales = map.get(orderItem.getOrderItemId());
                    for (OrderAfterSaleResp orderAfterSaleDto : orderAfterSales) {
                        BigDecimal refundPrice = NumberUtil.sub(orderAfterSaleDto.getTotalPrice(), Objects.isNull(orderAfterSaleDto.getDeliveryFee()) ? BigDecimal.ZERO : orderAfterSaleDto.getDeliveryFee());
                        refundTotalPrice = NumberUtil.add(refundPrice, refundTotalPrice);
                    }
                }

                // 商品退回比例 退款/商品价格
                BigDecimal rate = NumberUtil.div(refundTotalPrice, orderItem.getTotalPrice());
                productTotalPrice = NumberUtil.sub(productTotalPrice, refundTotalPrice);
                supplierProductPrice = NumberUtil.mul(supplierProductPrice, NumberUtil.sub(BigDecimal.ONE, rate)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                supplierProductTotalPrice = NumberUtil.add(supplierProductTotalPrice, supplierProductPrice);
            }
        }

        TenantDTO tenantDTO = tenantService.selectByType(TenantTypeEnum.FANTAI.getType());
        BillProfitSharingSnapshotDTO fantaiBillProfitSharingSnapShotDto = null;
        BigDecimal residuePrice = productTotalPrice;
        // 供应商
        TenantDTO supplierTenant = tenantService.selectByType(TenantTypeEnum.SUPPLIER.getType());
        // 鲜沐直供商品金额
        for(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto:billProfitSharingSnapshotDtos){
            // 供应商
            if(supplierTenant.getId().equals(billProfitSharingSnapshotDto.getAccountId())){
                billProfitSharingSnapshotDto.setOriginPrice(productTotalPrice);
                billProfitSharingSnapshotDto.setProfitSharingPrice(supplierProductTotalPrice);
                residuePrice = NumberUtil.sub(residuePrice, supplierProductTotalPrice);
                billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
            }else if(tenantDTO.getId().equals(billProfitSharingSnapshotDto.getAccountId())){
                // 帆台
                fantaiBillProfitSharingSnapShotDto = billProfitSharingSnapshotDto;
            }else {
                billProfitSharingSnapshotDto.setOriginPrice(productTotalPrice);
                BigDecimal brandProductPrice = NumberUtil.sub(productTotalPrice, supplierProductTotalPrice);
                billProfitSharingSnapshotDto.setProfitSharingPrice(brandProductPrice);
                residuePrice = NumberUtil.sub(residuePrice, brandProductPrice);
                billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
            }
        }
        AssertCheckDefault.expectNotNull(fantaiBillProfitSharingSnapShotDto, "帆台分账明细不存在:" + JSON.toJSONString(billProfitSharingSnapshotDtos));
        // 帆台
        fantaiBillProfitSharingSnapShotDto.setOriginPrice(productTotalPrice);
        fantaiBillProfitSharingSnapShotDto.setProfitSharingPrice(residuePrice);
        billProfitSharingService.updateBillProfitSharingSnapshot(fantaiBillProfitSharingSnapShotDto);
    }

}
