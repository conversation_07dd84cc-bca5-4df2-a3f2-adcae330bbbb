package com.cosfo.mall.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.context.OrderItemFeeEnum;
import com.cosfo.mall.order.model.bo.OrderItemFeeBO;
import com.cosfo.mall.order.model.dto.OrderItemFeeTransactionQueryDTO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.order.service.OrderAgentSkuFeeRuleService;
import com.cosfo.mall.order.service.OrderItemFeeCalculateService;
import com.cosfo.mall.order.service.OrderItemFeeTransactionService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderItemFeeCalculateServiceImpl implements OrderItemFeeCalculateService {

    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private OrderAgentSkuFeeRuleService orderAgentSkuFeeRuleService;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private OrderItemFeeTransactionService orderItemFeeTransactionService;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;

    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initAgentFee(List<Long> orderIds) {
        log.info("start init agent fee, orderIds = {}", orderIds);
//        List<Order> orders = orderService.selectOnlyByOrderIds(orderIds);
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByIds(orderIds));
        orderList.forEach(order -> {
            log.info("init order = {} agent fee", order.getId());
//            List<OrderItem> orderItemList = orderItemService.queryByOrderId(order.getTenantId(), order.getId());
//            List<OrderItemSnapshot> itemSnapshotList = orderItemSnapshotService.selectByOrderId(order.getTenantId(), order.getId());
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(order.getId()));
            List<OrderItemFeeTransaction> orderItemFeeTransactionList = this.calculateAgentFee(orderItemAndSnapshotDTOList, true, order.getWarehouseType());
            orderItemFeeTransactionService.saveOrderItemFeeTransaction(orderItemFeeTransactionList);
            log.info("init order = {} agent fee finish", order.getId());
        });
    }


    @Override
    public List<OrderItemFeeTransaction> calculateAgentFee(List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOS, Boolean initFlag, Integer warehouseType) {
        // 判断是否有代仓品
        List<OrderItemAndSnapshotResp> agentItemSnapshots = orderItemAndSnapshotDTOS.stream().filter(el -> Objects.equals(warehouseType, OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(el.getGoodsType(), GoodsTypeEnum.SELF_GOOD_TYPE.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentItemSnapshots)) {
            return Collections.emptyList();
        }
        List<OrderItemFeeTransaction> orderItemFeeTransactionList = new ArrayList<>(orderItemAndSnapshotDTOS.size());
        Map<Long, OrderItemAndSnapshotResp> orderItemMap = orderItemAndSnapshotDTOS.stream().collect(Collectors.toMap(OrderItemAndSnapshotResp::getOrderItemId, item -> item));
        OrderItemAndSnapshotResp orderItemSnapshot = agentItemSnapshots.get(NumberConstant.ZERO);
        Long tenantId = orderItemSnapshot.getTenantId();
//        OrderItem item = orderItemMap.get(orderItemSnapshot.getOrderItemId());
        if (!initFlag) {
            // 保存计费规则镜像
            orderAgentSkuFeeRuleService.saveOrderAgentSkuFeeRule(tenantId, orderItemSnapshot.getOrderId());
        }
        Integer totalAmount = 0;
        for (OrderItemAndSnapshotResp agentItemSnapshot : agentItemSnapshots) {
            OrderItemAndSnapshotResp orderItem = orderItemMap.get(agentItemSnapshot.getOrderItemId());
            totalAmount += orderItem.getAmount();
        }

        // 获取命中规则
        ProductAgentSkuFeeRuleDetailDTO hitAgentRule = productAgentSkuFeeRuleService.getHitAgentRule(tenantId, totalAmount);
        // 更新命中规则
        if (hitAgentRule == null) {
            return Lists.newArrayList();
        }

        orderAgentSkuFeeRuleService.updateOrderHitRule(tenantId, orderItemSnapshot.getOrderId(), hitAgentRule);

        for (OrderItemAndSnapshotResp agentItemSnapshot : agentItemSnapshots) {
            OrderItemAndSnapshotResp orderItem = orderItemMap.get(agentItemSnapshot.getOrderItemId());
            BigDecimal agentAmount = productAgentSkuFeeRuleService.calculateAgentAmountByTenant(tenantId, orderItem.getPayablePrice(), totalAmount);
            OrderItemFeeTransaction orderItemFeeTransaction = new OrderItemFeeTransaction();
            orderItemFeeTransaction.setOrderId(orderItem.getOrderId());
            orderItemFeeTransaction.setOrderItemId(agentItemSnapshot.getOrderItemId());
            orderItemFeeTransaction.setTenantId(orderItem.getTenantId());
            orderItemFeeTransaction.setTransactionType(OrderItemFeeEnum.TransactionType.PAY.getCode());
            orderItemFeeTransaction.setFeeType(OrderItemFeeEnum.FeeType.AGENT_FEE.getCode());
            orderItemFeeTransaction.setFee(NumberUtil.mul(agentAmount, orderItem.getAmount()));
            orderItemFeeTransactionList.add(orderItemFeeTransaction);
        }
        return orderItemFeeTransactionList;
    }


    @Override
    public OrderItemFeeBO buildOrderItemRefundBO(Long orderAfterSaleId) {
        OrderAfterSaleResp orderAfterSaleResultDTO = orderAfterSaleService.queryById(orderAfterSaleId);
        BigDecimal agentFee = calculateAgentRefund(orderAfterSaleResultDTO);

        OrderItemFeeBO orderItemAfterSaleBO = new OrderItemFeeBO();
        orderItemAfterSaleBO.setOrderItemId(orderAfterSaleResultDTO.getOrderItemId());
        orderItemAfterSaleBO.setFee(agentFee);
        orderItemAfterSaleBO.setFeeType(OrderItemFeeEnum.FeeType.AGENT_FEE.getCode());
        return orderItemAfterSaleBO;
    }

    private BigDecimal calculateAgentRefund(OrderAfterSaleResp orderAfterSale) {
        Long orderItemId = orderAfterSale.getOrderItemId();
//        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotService.selectByOrderItemId(orderAfterSale.getTenantId(), orderAfterSale.getOrderItemId());
        OrderItemAndSnapshotResp orderItemAndSnapshotDTO = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById(orderItemId));
//        Order order = orderMapper.selectByPrimaryKey(orderAfterSale.getOrderId());
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderItemAndSnapshotDTO.getOrderId()));
        // 代仓品继续往下，其他返回0
        if (!(Objects.equals(orderDTO.getWarehouseType(), OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(orderItemAndSnapshotDTO.getGoodsType(), GoodsTypeEnum.SELF_GOOD_TYPE.getCode()))) {
            return BigDecimal.ZERO;
        }
        // 如果不是供应商责任 则不处理
        if (!ResponsibilityTypeEnum.SUPPLIER.getType().equals(orderAfterSale.getResponsibilityType()) && Objects.equals(orderAfterSale.getAfterSaleType(), OrderAfterSaleTypeEnum.DELIVERED.getType())) {
            return BigDecimal.ZERO;
        }
        // 换货补发不需要
        if (Objects.equals(orderAfterSale.getServiceType(), OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue()) || Objects.equals(orderAfterSale.getServiceType(), OrderAfterSaleServiceTypeEnum.RESEND.getValue())) {
            return BigDecimal.ZERO;
        }

        // 查询是否是最后一次售后
        Boolean lastOrderAfterSaleFlag = orderAfterSaleService.verifyLastOrderItemOrderAfterSaleFlag(orderAfterSale.getId());
        if (!lastOrderAfterSaleFlag) {
            // 根据售后单计算代仓费
            return orderAgentSkuFeeRuleService.calculateAgentFeeByOrder(orderAfterSale);
        }

        // 查询订单明细-代仓的交易流水
        OrderItemFeeTransactionQueryDTO queryDTO = OrderItemFeeTransactionQueryDTO.builder()
                .orderItemId(orderItemId)
                .feeType(OrderItemFeeEnum.FeeType.AGENT_FEE.getCode()).build();
        List<OrderItemFeeTransaction> orderItemFeeTransactionList = orderItemFeeTransactionService.queryByCondition(queryDTO);
        if (CollectionUtils.isEmpty(orderItemFeeTransactionList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal transactionFee = orderItemFeeTransactionList.stream().map(OrderItemFeeTransaction::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        return transactionFee;
    }
}
