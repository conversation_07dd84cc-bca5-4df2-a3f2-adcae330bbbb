package com.cosfo.mall.order.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.cosfo.mall.common.utils.MallDateUtil;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.marketing.model.ItemSaleLimitRuleEnum;
import com.cosfo.mall.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.mall.marketing.service.ItemSaleLimitConfigService;
import com.cosfo.mall.order.model.dto.OrderItemCheckDTO;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderStatisticsQueryProvider;
import com.cosfo.ordercenter.client.req.ItemSaleQuantityReq;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @date: 2024/2/26 下午3:14
 */
@Service
@Slf4j
public class OrderItemCheckService {

    @Resource
    private ItemSaleLimitConfigService itemSaleLimitConfigService;

    @DubboReference
    private OrderStatisticsQueryProvider orderStatisticsQueryProvider;

    /**
     * 商品限购数量检查
     *
     * @param marketItemVOList
     * @param itemQuantityMap
     * @param tenantId
     * @param storeId
     */
    public List<OrderItemCheckDTO> merchantStoreItemSaleLimitCheck(List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemQuantityMap, Long tenantId, Long storeId) {
        List<OrderItemCheckDTO> resultList = new ArrayList<>();

        Set<Long> itemIds = marketItemVOList.stream().map(MarketItemVO::getItemId).collect(Collectors.toSet());
        Map<Long, ItemSaleLimitConfigDTO> itemSaleLimitConfigMap = itemSaleLimitConfigService.queryItemSaleLimitConfigMap(tenantId, itemIds);

        for (MarketItemVO marketItemVO : marketItemVOList) {
            ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = itemSaleLimitConfigMap.get(marketItemVO.getItemId());
            // 不存在配置或者无限制
            if (itemSaleLimitConfigDTO == null || ItemSaleLimitRuleEnum.NO_LIMIT.getCode().equals(itemSaleLimitConfigDTO.getSaleLimitRule())) {
                continue;
            }
            ItemSaleLimitRuleEnum itemSaleLimitRuleEnum = ItemSaleLimitRuleEnum.fromCode(itemSaleLimitConfigDTO.getSaleLimitRule());
            int orderQuantitySum = 0;
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime beginDay = null;
            switch (itemSaleLimitRuleEnum) {
                case EVERY_TIME:
                    break;
                case DAILY:
                    beginDay = LocalDateTimeUtil.beginOfDay(now);
                    break;
                case WEEKLY:
                    beginDay = MallDateUtil.startOfWeek(now);
                    break;
                case MONTHLY:
                    beginDay = MallDateUtil.startOfMonth(now);
                    break;
                default:
            }
            orderQuantitySum = itemQuantityMap.get(marketItemVO.getItemId()) + sumOrderQuantity(tenantId, storeId, beginDay, now, marketItemVO.getItemId());

            if (orderQuantitySum > itemSaleLimitConfigDTO.getSaleLimitQuantity()) {
                resultList.add(OrderItemCheckDTO.checkFail(marketItemVO.getItemId(), itemQuantityMap.get(marketItemVO.getItemId()), String.format("商品超过[%s]最大可购买数量件[%s]", itemSaleLimitRuleEnum.getDesc(), itemSaleLimitConfigDTO.getSaleLimitQuantity())));
            } else {
                resultList.add(OrderItemCheckDTO.checkOk(marketItemVO.getItemId(), itemQuantityMap.get(marketItemVO.getItemId())));
            }
        }

        return resultList;
    }

    private Integer sumOrderQuantity(Long tenantId, Long merchantStoreId, LocalDateTime startDay, LocalDateTime endDay, Long itemId) {
        if (startDay == null) {
            return 0;
        }
        Map<Long, Integer> itemQuantity = sumOrderByMerchantStoreId(tenantId, merchantStoreId, startDay, endDay, Lists.newArrayList(itemId));
        return itemQuantity.getOrDefault(itemId, 0);
    }

    private Map<Long, Integer> sumOrderByMerchantStoreId(Long tenantId, Long merchantStoreId, LocalDateTime startDay, LocalDateTime endDay, List<Long> itemIds) {
        // 统计时间范围内未取消的gl
        ItemSaleQuantityReq itemSaleQuantityReq = new ItemSaleQuantityReq();
        itemSaleQuantityReq.setTenantId(tenantId);
        itemSaleQuantityReq.setMerchantStoreId(merchantStoreId);
        itemSaleQuantityReq.setItemIds(itemIds);
        itemSaleQuantityReq.setStartDay(startDay);
        itemSaleQuantityReq.setEndDay(endDay);

        Map<Long, Integer> result = RpcResultUtil.handle(orderStatisticsQueryProvider.countItemSaleQuantity(itemSaleQuantityReq));
        return result;
    }


    /**
     * 倍数下单校验
     *
     * @param marketItemVOList
     * @param itemQuantityMap
     */
    public List<OrderItemCheckDTO> merchantStoreItemBuyMultipleMuCheck(List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemQuantityMap) {
        List<OrderItemCheckDTO> resultList = new ArrayList<>();
        for (MarketItemVO marketItemVO : marketItemVOList) {
            Integer itemQuantity = itemQuantityMap.get(marketItemVO.getItemId());
            if (marketItemVO.getBuyMultipleSwitch()) {
                if (itemQuantity % marketItemVO.getBuyMultiple() != 0) {
                    resultList.add(OrderItemCheckDTO.checkFail(marketItemVO.getItemId(), itemQuantity, String.format("商品未按照正确的订货倍数%s下单", marketItemVO.getBuyMultiple())));
                    continue;
                }
            }
            resultList.add(OrderItemCheckDTO.checkOk(marketItemVO.getItemId(), itemQuantity));
        }

        return resultList;
    }


    /**
     * 起订量校验
     *
     * @param marketItemVOList
     * @param itemQuantityMap
     */
    public List<OrderItemCheckDTO> miniOrderQuantityCheck(List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemQuantityMap) {
        List<OrderItemCheckDTO> resultList = new ArrayList<>();
        for (MarketItemVO marketItemVO : marketItemVOList) {
            Integer itemQuantity = itemQuantityMap.get(marketItemVO.getItemId());
            if (itemQuantity < marketItemVO.getMiniOrderQuantity()) {
                resultList.add(OrderItemCheckDTO.checkFail(marketItemVO.getItemId(), itemQuantity, String.format("商品数量不足起订量%s", marketItemVO.getMiniOrderQuantity())));
                continue;
            }
            resultList.add(OrderItemCheckDTO.checkOk(marketItemVO.getItemId(), itemQuantity));
        }

        return resultList;
    }

}
