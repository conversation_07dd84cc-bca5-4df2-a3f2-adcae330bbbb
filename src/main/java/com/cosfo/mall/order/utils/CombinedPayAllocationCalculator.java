package com.cosfo.mall.order.utils;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 组合支付分摊计算工具类
 * 用于计算组合支付中非现金和余额的分摊逻辑
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Component
public class CombinedPayAllocationCalculator {

    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;

    /**
     * 计算组合支付的分摊结果
     *
     * @param order 订单信息
     * @return 分摊结果，包含商品和运费的调整金额
     */
    public CombinedPayAllocationResult calculateAllocation(OrderResp order) {
        // 非组合支付直接返回零调整
        if (!Objects.equals(order.getPayType(), PayTypeEnum.COMBINED_PAY.getType())) {
            return new CombinedPayAllocationResult(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 获取组合支付明细
        List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(
                order.getTenantId(), order.getId());

        if (CollectionUtils.isEmpty(combinedDetails)) {
            log.warn("订单[{}]为组合支付但未找到组合支付明细", order.getId());
            return new CombinedPayAllocationResult(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 分离非现金和余额支付
        BigDecimal nonCashAmount = BigDecimal.ZERO;
        BigDecimal balanceAmount = BigDecimal.ZERO;

        for (PaymentCombinedDetail detail : combinedDetails) {
            Integer payType = TradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
            if (Objects.equals(payType, PayTypeEnum.NON_CASH_PAY.getType())) {
                nonCashAmount = NumberUtil.add(nonCashAmount, detail.getTotalPrice());
            } else if (Objects.equals(payType, PayTypeEnum.BALANCE.getType())) {
                balanceAmount = NumberUtil.add(balanceAmount, detail.getTotalPrice());
            }
        }

        // 计算商品总价
        BigDecimal productTotalPrice = calculateProductTotalPrice(order);
        BigDecimal deliveryFee = Objects.isNull(order.getDeliveryFee()) ? BigDecimal.ZERO : order.getDeliveryFee();

        log.info("订单[{}]组合支付分摊计算: 非现金金额={}, 余额金额={}, 商品总价={}, 运费={}",
                order.getId(), nonCashAmount, balanceAmount, productTotalPrice, deliveryFee);

        // 计算分摊金额 - 使用倒减逻辑
        BigDecimal productAdjustment = calculateProductAdjustment(productTotalPrice, deliveryFee, nonCashAmount, balanceAmount);
        BigDecimal deliveryAdjustment = calculateDeliveryAdjustment(productTotalPrice, deliveryFee, balanceAmount);

        log.info("订单[{}]组合支付分摊结果: 商品调整金额={}, 运费调整金额={}",
                order.getId(), productAdjustment, deliveryAdjustment);

        return new CombinedPayAllocationResult(productAdjustment, deliveryAdjustment);
    }

    /**
     * 计算商品总价
     *
     * @param order 订单信息
     * @return 商品总价
     */
    private BigDecimal calculateProductTotalPrice(OrderResp order) {
        List<OrderItemAndSnapshotResp> orderItemList = RpcResultUtil.handle(
                orderItemQueryProvider.queryByOrderId(order.getId()));

        BigDecimal productTotalPrice = BigDecimal.ZERO;
        for (OrderItemAndSnapshotResp orderItem : orderItemList) {
            productTotalPrice = NumberUtil.add(productTotalPrice, orderItem.getTotalPrice());
        }

        return productTotalPrice;
    }

    /**
     * 计算商品的调整金额
     * 非现金全部分摊到商品，余额使用倒减逻辑
     *
     * @param productTotalPrice 商品总价
     * @param deliveryFee       运费
     * @param nonCashAmount     非现金金额
     * @param balanceAmount     余额金额
     * @return 商品调整金额
     */
    private BigDecimal calculateProductAdjustment(BigDecimal productTotalPrice, BigDecimal deliveryFee,
                                                  BigDecimal nonCashAmount, BigDecimal balanceAmount) {
        // 非现金全部分摊到商品
        BigDecimal nonCashToProduct = nonCashAmount;

        // 余额使用倒减逻辑：先计算运费分摊，剩余的分摊到商品
        BigDecimal balanceToDelivery = calculateDeliveryAdjustment(productTotalPrice, deliveryFee, balanceAmount);
        BigDecimal balanceToProduct = NumberUtil.sub(balanceAmount, balanceToDelivery);

        return NumberUtil.add(nonCashToProduct, balanceToProduct);
    }

    /**
     * 计算运费的调整金额
     * 只有余额按比例分摊到运费，非现金不分摊到运费
     *
     * @param productTotalPrice 商品总价
     * @param deliveryFee       运费
     * @param balanceAmount     余额金额
     * @return 运费调整金额
     */
    private BigDecimal calculateDeliveryAdjustment(BigDecimal productTotalPrice, BigDecimal deliveryFee, BigDecimal balanceAmount) {
        BigDecimal totalAmount = NumberUtil.add(productTotalPrice, deliveryFee);

        // 余额按运费占比分摊
        if (totalAmount.compareTo(BigDecimal.ZERO) > 0 && balanceAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal deliveryRatio = NumberUtil.div(deliveryFee, totalAmount);
            return NumberUtil.mul(balanceAmount, deliveryRatio).setScale(2, RoundingMode.HALF_UP);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 组合支付分摊结果
     */
    @Data
    public static class CombinedPayAllocationResult {
        /**
         * 商品调整金额（需要从商品总价中减去的金额）
         */
        private final BigDecimal productAdjustment;

        /**
         * 运费调整金额（需要从运费中减去的金额）
         */
        private final BigDecimal deliveryAdjustment;

        public CombinedPayAllocationResult(BigDecimal productAdjustment, BigDecimal deliveryAdjustment) {
            this.productAdjustment = productAdjustment;
            this.deliveryAdjustment = deliveryAdjustment;
        }
    }
}
