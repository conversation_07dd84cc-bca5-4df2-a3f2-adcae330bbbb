package com.cosfo.mall.market.convert;

import com.cosfo.mall.market.model.dto.CombineMarketItemDTO;
import com.cosfo.mall.market.model.vo.CombineItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 10:42
 * @Description:
 */
@Mapper
public interface CombineMarketConvert {
    CombineMarketConvert INSTANCE = Mappers.getMapper(CombineMarketConvert.class);

    List<CombineItemVO> convert2CombineItemVOs(List<CombineMarketItemDTO> dtos);

    @Mapping(source = "marketItemId", target = "itemId")
    CombineItemVO convert2CombineItemVO(CombineMarketItemDTO dto);

}
