package com.cosfo.mall.market.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.enums.MarketItemUnitTypeEnum;
import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketItemUnitResp;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/17
 */
public class MarketItemConvert {

    /**
     * 转化为
     *
     * @return
     */
    public static List<MarketItemVO> convertMarketItemVOList(List<MarketItemInfoResp> marketItemInfoResps){

        if (marketItemInfoResps == null) {
            return Collections.emptyList();
        }
        List<MarketItemVO> marketItemVOList = new ArrayList<>();
        for (MarketItemInfoResp marketItemInfoResp : marketItemInfoResps) {
            marketItemVOList.add(toMarketItemVO(marketItemInfoResp));
        }
        return marketItemVOList;
    }

    public static MarketItemVO toMarketItemVO(MarketItemInfoResp marketItemInfoResp) {
        if (marketItemInfoResp == null) {
            return null;
        }
        MarketItemVO marketItemVO = new MarketItemVO();
        marketItemVO.setMarketId(marketItemInfoResp.getMarketId());
        marketItemVO.setItemId(marketItemInfoResp.getItemId());
        marketItemVO.setTenantId(marketItemInfoResp.getTenantId());
        marketItemVO.setSkuId(marketItemInfoResp.getSkuId());
        marketItemVO.setSupplierName(marketItemInfoResp.getSupplierName());
        marketItemVO.setMainPicture(marketItemInfoResp.getMainPicture());
        marketItemVO.setSpecification(marketItemInfoResp.getSpecification());
        marketItemVO.setSpecificationUnit(marketItemInfoResp.getSpecificationUnit());
        marketItemVO.setOnSale(marketItemInfoResp.getOnSale());
        marketItemVO.setMiniOrderQuantity(marketItemInfoResp.getMiniOrderQuantity());
        marketItemVO.setAfterSaleUnit(marketItemInfoResp.getAfterSaleUnit());
        marketItemVO.setMaxAfterSaleAmount(marketItemInfoResp.getMaxAfterSaleAmount());
        marketItemVO.setDeleteFlag(marketItemInfoResp.getDeleteFlag());
        marketItemVO.setGoodsType(marketItemInfoResp.getGoodsType());
        marketItemVO.setTitle(marketItemInfoResp.getItemTitle());
        // TODO 供应商上线后需要加
        marketItemVO.setSupplierId(marketItemInfoResp.getSupplierId());
        marketItemVO.setItemSaleMode(marketItemInfoResp.getItemSaleMode());
        marketItemVO.setItemCode(marketItemInfoResp.getItemCode());
        marketItemVO.setBuyMultiple (marketItemInfoResp.getBuyMultiple());
        marketItemVO.setBuyMultipleSwitch (marketItemInfoResp.getBuyMultipleSwitch());
        marketItemVO.setPresaleSwitch(marketItemInfoResp.getPresaleSwitch());
        marketItemVO.setWeight(marketItemInfoResp.getWeight());
        marketItemVO.setClassificationId(Optional.ofNullable(marketItemInfoResp.getMarketItemClassificationResp())
                .map(MarketItemClassificationResp::getSecondClassificationId)
                .orElse(null));
        return marketItemVO;
    }


    @Named("getUnitByEnum")
    public static MarketItemUnitResp getUnitByEnum(List<MarketItemUnitResp> marketItemUnitList, MarketItemUnitTypeEnum storeOrderingUnit) {
        if(CollectionUtil.isEmpty (marketItemUnitList)){
            return null;
        }
        Optional<MarketItemUnitResp> first = marketItemUnitList.stream ().filter (e -> storeOrderingUnit.getCode ().equals (e.getUnitType ())).findFirst ();
        if(first.isPresent ()){
            return first.get ();
        }
        return null;
    }

    @Named("getStoreInventoryCostUnitMultiple")
    public static BigDecimal getStoreInventoryCostUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp costUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_COST_UNIT);
        if(inventoryUnit !=null && costUnit != null){
            return costUnit.getStoreOrderingUnitMultiple ().divide (inventoryUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }
    @Named("getStoreOrderingInventoryUnitMultiple")
    public static BigDecimal getStoreOrderingInventoryUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp orderingUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_ORDERING_UNIT);
        if(inventoryUnit!=null && orderingUnit!=null){
            return inventoryUnit.getStoreOrderingUnitMultiple ().divide (orderingUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }
}
