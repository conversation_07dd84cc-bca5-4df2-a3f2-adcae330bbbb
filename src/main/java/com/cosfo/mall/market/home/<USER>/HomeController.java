package com.cosfo.mall.market.home.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.home.service.HomeService;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.query.MarketItemQuery;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description 首页 Controller层
 * <AUTHOR>
 * @date 2022/5/13 9:16
 */
@RestController
@RequestMapping("/home")
public class HomeController extends BaseController {

    @Resource
    private HomeService homeService;
    @Resource
    private MerchantStoreService merchantStoreService;

    /**
     * 分类首页查询商品信息
     * @param pageIndex
     * @param pageSize
     * @param marketItemQuery
     * @return
     */
    @RequestMapping(value = "/listAll/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public ResultDTO<PageInfo<MarketItemDTO>> listAll(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, MarketItemQuery marketItemQuery) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return homeService.listAll(pageIndex, pageSize, marketItemQuery, contextInfoDTO);
    }

    /**
     * 查询商品详情
     * @param itemId
     * @return
     */
    @RequestMapping(value = "/selectDetail", method = RequestMethod.GET)
    public ResultDTO<MarketItemDTO> selectDetail(Long itemId) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return homeService.selectDetail(contextInfoDTO, itemId);
    }

    /**
     * 分类首页查询商品信息，未登录
     * @param pageIndex
     * @param pageSize
     * @param marketItemQuery
     * @return
     */
    @RequestMapping(value = "/unLogin/listAll/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public ResultDTO unLoginListAll(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, MarketItemQuery marketItemQuery) {
        return homeService.unLoginListAll(pageIndex, pageSize, marketItemQuery);
    }
    /**
     * 返回门店下单有效期，是临期才返回
     */
    @RequestMapping(value = "/isExpire", method = RequestMethod.POST)
    public CommonResult<String> getPlaceOrderPermissionExpiryTime() {
        return CommonResult.ok(merchantStoreService.getPlaceOrderPermissionExpiryTime(getRequestContextInfoDTO().getStoreId ()));
    }
}
