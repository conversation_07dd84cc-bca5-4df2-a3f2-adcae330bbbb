package com.cosfo.mall.market.home.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.query.MarketItemQuery;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Set;

/**
 * 首页服务层
 * 接入商品中心的service
 * @author: xiaowk
 * @date: 2023/5/25 下午9:36
 */
public interface HomeService {

    /**
     * 分类首页商品
     * @param pageIndex
     * @param pageSize
     * @param marketItemQuery
     * @param requestContextInfoDTO
     * @return
     */
    ResultDTO<PageInfo<MarketItemDTO>> listAll(Integer pageIndex, Integer pageSize, MarketItemQuery marketItemQuery, LoginContextInfoDTO requestContextInfoDTO);

    /**
     * 查询商品详情
     * @param contextInfoDTO
     * @param itemId
     * @return
     */
    ResultDTO<MarketItemDTO> selectDetail(LoginContextInfoDTO contextInfoDTO, Long itemId);

    /**
     * 未登录状态的首页
     * @param pageIndex
     * @param pageSize
     * @param marketItemQuery
     * @return
     */
    ResultDTO<List<MarketItemDTO>> unLoginListAll(Integer pageIndex, Integer pageSize, MarketItemQuery marketItemQuery);

    Set<String> getBrands(Long tenantId);
}
