package com.cosfo.mall.market.mapper;

import com.cosfo.mall.market.model.po.MarketAreaItemStorePriceMapping;

public interface MarketAreaItemStorePriceMappingMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MarketAreaItemStorePriceMapping record);

    int insertSelective(MarketAreaItemStorePriceMapping record);

    MarketAreaItemStorePriceMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MarketAreaItemStorePriceMapping record);

    int updateByPrimaryKey(MarketAreaItemStorePriceMapping record);
}