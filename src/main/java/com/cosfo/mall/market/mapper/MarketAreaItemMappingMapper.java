package com.cosfo.mall.market.mapper;

import com.cosfo.mall.market.model.po.MarketAreaItem;
import com.cosfo.mall.market.model.po.MarketAreaItemMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 定价策略表
 */
@Mapper
public interface MarketAreaItemMappingMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(MarketAreaItemMapping record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(MarketAreaItemMapping record);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    MarketAreaItemMapping selectByPrimaryKey(Long id);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(MarketAreaItemMapping record);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(MarketAreaItemMapping record);

    /**
     * 查询统一价
     *
     * @param tenantId
     * @return
     */
    List<MarketAreaItemMapping> queryUnificationPriceByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 查询其他价
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MarketAreaItemMapping> queryOtherPriceByTenantIdAndStoreId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId);

    /**
     * 查询门店固定商品统一价
     *
     * @param tenantId
     * @param storeId
     * @param areaItemId
     * @return
     */
    MarketAreaItemMapping queryUnificationPriceByTenantIdAndStoreIdAndAreaItemId(@Param("tenantId") Long tenantId,
                                                                                 @Param("storeId") Long storeId,
                                                                                 @Param("areaItemId") Long areaItemId);

    /**
     * 查询门店固定商品其他价
     *
     * @param tenantId
     * @param storeId
     * @param areaItemId
     * @return
     */
    MarketAreaItemMapping queryOtherPriceByTenantIdAndStoreIdAndAreaItemId(@Param("tenantId") Long tenantId,
                                                                           @Param("storeId") Long storeId,
                                                                           @Param("areaItemId") Long areaItemId);
}