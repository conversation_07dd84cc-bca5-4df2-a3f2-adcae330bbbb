package com.cosfo.mall.market.mapper;

import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.po.MarketItem;
import com.cosfo.mall.market.model.query.MarketItemQuery;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @discription 商品持久层
 * <AUTHOR>
 * @date 2022/5/13 9:41
 */
@Mapper
public interface MarketItemMapper {

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    MarketItem selectByPrimaryKey(Long id);

    /**
     * 批量查询商品信息
     * @param itemIds
     * @param tenantId
     * @return
     */
    // TODO Sql是否要修改
    List<MarketItemVO> batchByItemIds(@Param("itemIds") List<Long> itemIds,@Param("tenantId") Long tenantId);

    /**
     * 查询商品
     * @param query
     * @return
     */
    List<MarketItemDTO> listAll(MarketItemQuery query);

    /**
     * 根据商品id查询映射的skuId
     * @param itemId
     * @return
     */
    Long selectSupplySkuId(Long itemId);

    /**
     * 根据marketIds查询
     * @param tenantId
     * @param marketIds
     * @return
     */
    List<MarketItemDTO> batchQueryOnSaleByMarketIds(@Param("tenantId") Long tenantId, @Param("marketIds") List<Long> marketIds);
}
