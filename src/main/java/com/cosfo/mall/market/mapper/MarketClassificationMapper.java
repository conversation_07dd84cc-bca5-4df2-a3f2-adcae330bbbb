package com.cosfo.mall.market.mapper;

import com.cosfo.mall.market.model.dto.MarketClassificationDTO;
import com.cosfo.mall.market.model.po.MarketClassification;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Discription 分类持久层
 * <AUTHOR>
 * @date 2022/5/10
 */
@Mapper
public interface MarketClassificationMapper {

    /**
     * 根据条件查询分类
     * @param record
     * @return
     */
    List<MarketClassification> listAll(MarketClassificationDTO record);
}
