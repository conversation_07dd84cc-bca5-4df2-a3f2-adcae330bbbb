package com.cosfo.mall.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.market.model.po.Market;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Mapper
public interface MarketMapper extends BaseMapper<Market> {
    /**
     * 列表查询
     *
     * @param classificationId
     * @param title
     * @return
     */
    List<Market> selectByTitleAndClassificationId(@Param("classificationId") Long classificationId,@Param("title") String title);
}
