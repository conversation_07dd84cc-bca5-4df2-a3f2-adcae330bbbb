package com.cosfo.mall.market.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.model.dto.MarketItemQuerySaleInfoDTO;
import com.cosfo.mall.market.model.dto.MarketItemSaleInfoDTO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 分类 Controller层
 * @date 2022/5/17 9:49
 */
@RestController
@RequestMapping("/marketItem")
public class MarketItemController extends BaseController {

    @Resource
    private MarketItemService marketItemService;

    /**
     * 根据名称联想搜索
     * @return
     */
    @RequestMapping(value = "/selectByTitle", method = RequestMethod.GET)
    public ResultDTO selectByTitle(String title) {
        LoginContextInfoDTO contextInfoDTO = getRequestContextInfoDTO();
        return marketItemService.selectByTitle(contextInfoDTO, title);
    }

    /**
     * 查询商品售卖信息
     * @param marketItemQuerySaleInfoDTO
     * @return
     */
    @RequestMapping(value = "/query-item-sale-info", method = RequestMethod.POST)
    public CommonResult<MarketItemSaleInfoDTO> queryItemSaleInfo(@RequestBody MarketItemQuerySaleInfoDTO marketItemQuerySaleInfoDTO) {
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        return marketItemService.queryItemSaleInfo(marketItemQuerySaleInfoDTO, loginContextInfoDTO);
    }
}
