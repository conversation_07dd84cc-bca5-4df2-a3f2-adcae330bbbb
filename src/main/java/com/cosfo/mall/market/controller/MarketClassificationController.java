package com.cosfo.mall.market.controller;

import com.cosfo.mall.common.config.SystemTenantProperties;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.service.MarketClassificationService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 分类 Controller层
 * @date 2022/5/10 9:49
 */
@RestController
@RequestMapping("/marketClassification")
public class MarketClassificationController extends BaseController {

    @Resource
    private MarketClassificationService marketClassificationService;

    @Resource
    private SystemTenantProperties systemTenantProperties;

    /**
     * 分类树
     *
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.GET)
    public ResultDTO selectClassificationTree() {
        return marketClassificationService.selectClassificationTree(systemTenantProperties.getWurthTenantId());
    }

    /**
     * 未登录状态分类树【无需鉴权】
     *
     * @return
     */
    @RequestMapping(value = "/unLoginListAll", method = RequestMethod.GET)
    public ResultDTO selectUnLoginClassificationTree() {
        return marketClassificationService.noLoginSelectClassificationTree(systemTenantProperties.getWurthTenantId());
    }
}
