package com.cosfo.mall.market.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * market_area_item_store_price_mapping
 * <AUTHOR>
@Data
public class MarketAreaItemStorePriceMapping implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 价格定价方式Id
     */
    private Long areaItemMappingId;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}