package com.cosfo.mall.market.model.dto;

import com.cosfo.mall.market.model.vo.CombineItemVO;
import com.cosfo.mall.market.model.vo.LadderPriceVO;
import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description 商品DTO实体
 * <AUTHOR>
 * @date 2022/5/13 9:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketItemDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 库存
     */
    private Integer amount;

    /**
     * 分类id
     */
    private Long classificationId;

    /**
     * 过期时间
     */
    private String expirationTime;

    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 基础价/原价（加价之前）
     */
    private BigDecimal basePrice;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名
     */
    private String brandName;

    /**
     * 0 自营 1 三方
     */
    @Deprecated
    private Integer warehouseType;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 购物车数量
     */
    private Integer trolleyAmount;

    /**
     * 配送方式
     */
    @Deprecated
    private Integer deliveryType;
    /**
     * 按件数价格规则
     */
    private List<ProductAgentSkuFeeCountRuleDTO> ruleList;

    /**
     * 货源类型 0-无货 1-报价 2-自营
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;

    /**
     * 商品类型  0=实物商品,1=虚拟商品,2=组合品
     */
    private Integer itemType;

    /**
     * 子商品列表
     */
    private List<CombineItemVO> subItemList;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;
    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;
    /**
     * 预计送达时间
     */
    private LocalDate deliveryTime;
    /**
     * 截单时间
     */
    private LocalDateTime deliveryCloseTime;

    /**
     * 履约类型，0：城配履约，1：快递履约
     * 1快递履约 文案固定为【今日16:00前下单，最晚48小时内发货】
     */
    private Integer fulfillmentType;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;
    /**
     * 商品描述
     */
    private String descriptionString;

    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private List<LadderPriceVO> ladderPrices;
    /**
     * sap sku编码
     */
    private String sapSkuCode;
    /**
     * sap 物料编码
     */
    private String sapMaterialCode;
    /**
     * 包装数量
     */
    private Integer packageQuantity;

    /**
     * 活动信息
     */
    private List<ActivityInfoVO> activityInfoVO;
}
