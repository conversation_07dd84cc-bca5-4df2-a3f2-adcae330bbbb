package com.cosfo.mall.market.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 城市销售商品实体类
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketAreaItem implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 所属租户id
     */
    private Long tenantId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * item表id
     */
    private Long itemId;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;

    /**
     * 0 自营仓 1 第三方仓
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 配送方式 0品牌方配送 1三方配送
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity=1;

    private static final long serialVersionUID = 1L;
}
