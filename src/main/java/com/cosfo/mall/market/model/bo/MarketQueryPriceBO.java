package com.cosfo.mall.market.model.bo;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询商城价 bo
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketQueryPriceBO {

    /**
     * 登录上下文对象
     */
    private LoginContextInfoDTO loginContextInfoDTO;

    /**
     * 门店地址
     */
    private MerchantAddressDTO merchantAddressDTO;

    /**
     * skuList
     */
    private List<Long> skuIdList;

    /**
     * itemId和数量 map容器
     */
    private Map<Long, Integer> itemAmountMap;
}
