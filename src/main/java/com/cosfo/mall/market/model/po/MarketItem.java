package com.cosfo.mall.market.model.po;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description 商品实体
 * <AUTHOR>
 * @date 2022/5/13 9:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketItem implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * marketId
     */
    private Long marketId;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 货源类型 0无货 1报价货品 2自营货品
     */
    private Integer goodsType;

    private static final long serialVersionUID = 1L;
}
