package com.cosfo.mall.market.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * market_area_item_mapping
 * <AUTHOR>
@Data
public class MarketAreaItemMapping implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 映射区域item id
     */
    private Long areaItemId;

    /**
     * 定价方式0统一价1其他价
     */
    private Integer storePriceType;

    /**
     * 0、百分比上浮 1、定额上浮 2、固定价
     */
    private Integer type;

    /**
     * 价格配置数额
     */
    private BigDecimal mappingNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}