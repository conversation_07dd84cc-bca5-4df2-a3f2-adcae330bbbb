package com.cosfo.mall.market.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/16 16:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketItemQuery {

    /**
     * 名称
     */
    private String title;

    /**
     * 分类id
     */
    private Long classificationId;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 店铺Id
     */
    private Long storeId;

    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 分页大小
     */
    private Integer pageSize;
}
