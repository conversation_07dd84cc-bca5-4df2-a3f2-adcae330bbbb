package com.cosfo.mall.market.model.dto;

import com.cosfo.mall.market.model.vo.LadderPriceVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/25
 */
@Data
public class SkuMallPriceDTO {
    /**
     * itemId
     */
    private Long itemId;
    /**
     * 供应商skuId
     */
    private Long supplySkuId;
    /**
     * 基础价/原价（加价之前）
     */
    private BigDecimal basePrice;
    /**
     * 商城价
     */
    private BigDecimal price;
    /**
     * 供应价
     */
    private BigDecimal supplyPrice;
    /**
     * 定价方式0百分比上浮1定额上浮2固定价
     */
    private Integer pricingType;
    /**
     * 定价数值
     */
    private BigDecimal pricingNumber;
    /**
     * 代仓商城价
     */
    private BigDecimal agentMallPrice;
    /**
     * 原销售单价
     */
    private BigDecimal marketItemPrice;

    private List<LadderPriceVO> ladderPrices;
}
