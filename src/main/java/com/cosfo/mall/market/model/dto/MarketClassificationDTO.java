package com.cosfo.mall.market.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 分类 DTO 传输对象
 * @date 2022/5/10 9:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketClassificationDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * icon
     */
    private String icon;

    /**
     * 排序值
     */
    private Integer sort;
}
