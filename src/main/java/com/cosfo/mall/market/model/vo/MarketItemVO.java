package com.cosfo.mall.market.model.vo;

import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class MarketItemVO {
    /**
     * marketId
     */
    private Long marketId;
    /**
     * ItemId
     */
    private Long ItemId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 供应商skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 分类id
     */
    private Long classificationId;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 售价
     */
    private BigDecimal price;
    /**
     * 是否在售
     */
    private Integer onSale;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 配送方式 0品牌方配送 1三方配送
     */
    @Deprecated
    private Integer deliveryType;
    /**
     * 购物车数量
     */
    private Integer amount;
    /**
     * 可用库存
     */
    private Integer enableAmount;
    /**
     * 是否有效
     */
    private Boolean validFlag;
    /**
     * 有效期
     */
    private LocalDate expirationTime;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity=0;

    /**
     * 下一个价格配置
     */
    private ProductAgentSkuFeeCountRuleDTO nextStepRule;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * @see com.cosfo.mall.common.constants.MarketDeleteFlagEnum
     */
    private Integer deleteFlag;

    /**
     * 货源类型 0无货 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 供应商id
     */
    private String supplierId;


    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 限购数量
     */
    private Integer saleLimitQuantity;

    /**
     * 限购规则 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    private Integer saleLimitRule;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private List<LadderPriceVO> ladderPrices;


    /**
     * 无货商品重量, 单位kg
     */
    private BigDecimal weight;

    /**
     * sap sku编码
     */
    private String sapSkuCode;
    /**
     * sap 物料编码
     */
    private String sapMaterialCode;
}
