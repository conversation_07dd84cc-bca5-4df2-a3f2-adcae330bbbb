package com.cosfo.mall.market.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.dto.MarketItemQuerySaleInfoDTO;
import com.cosfo.mall.market.model.dto.MarketItemSaleInfoDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import net.xianmu.common.result.CommonResult;

import java.math.BigDecimal;
import java.util.List;

/**
 *  接入商品中心的service
 * @author: xiaowk
 * @date: 2023/5/25 下午9:35
 */
public interface MarketItemService {

    /**
     * 批量查询商品信息
     *
     * @param itemIds
     * @param tenantId
     * @return
     */
    List<MarketItemVO> batchByItemIds(List<Long> itemIds, Long tenantId);

    /**
     * 根据名称联想
     *
     * @param contextInfoDTO
     * @param title
     * @return
     */
    ResultDTO selectByTitle(LoginContextInfoDTO contextInfoDTO, String title);

    List<MarketItemDTO> selectSimpleItemByItemIds(Long tenantId,Long storeId,List<Long> itemIds,Integer onSale);

    /**
     * 查询商品售卖信息
     *
     * @param marketItemQuerySaleInfoDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<MarketItemSaleInfoDTO> queryItemSaleInfo(MarketItemQuerySaleInfoDTO marketItemQuerySaleInfoDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 计算代仓货品加价
     * @param tenantId
     * @param price
     * @param miniOrderQuantity
     * @param reqItemCount
     * @return
     */
    BigDecimal calSelfSupplyMallPrice(Long tenantId, BigDecimal price, Integer miniOrderQuantity, Integer reqItemCount);

}
