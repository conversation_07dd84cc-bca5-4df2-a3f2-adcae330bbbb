package com.cosfo.mall.market.service;

import com.cosfo.mall.market.model.dto.SkuMallPriceDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/12
 */
public interface MarketItemPriceService {
    /**
     * 查询普通商品商城售价
     *
     * @param storeId
     * @param loginContextInfoDTO
     * @param marketItemVOList
     * @param itemBuyAmount
     * @return
     */
    Map<Long, SkuMallPriceDTO> queryItemMallPrice(Long storeId, LoginContextInfoDTO loginContextInfoDTO, List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemBuyAmount, TenantDTO tenantDTO, MerchantAddressDTO merchantAddressDTO,boolean throwExceptionFlag);

    /**
     * 查询组合商品商城售价
     *
     * @param storeId
     * @param tenantId
     * @param combineItemId
     * @return
     */
    Map<Long, SkuMallPriceDTO> queryCombineItemMallPrice(Long storeId, Long tenantId,Integer amount, Long combineItemId,boolean throwExceptionFlag);

    /**
     * 代仓品计算代仓费用加价之后价格
     *
     * @param tenantId 租户Id
     * @param price 加价前商城价
     * @param itemCount 购买数量
     * @return
     */
    BigDecimal calculateProductAgentSkuFee(Long tenantId, BigDecimal price, Integer itemCount);
}
