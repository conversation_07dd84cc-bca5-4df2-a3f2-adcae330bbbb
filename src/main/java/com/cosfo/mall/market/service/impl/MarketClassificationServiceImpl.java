package com.cosfo.mall.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.facade.MarketClassificationFacade;
import com.cosfo.mall.market.model.dto.MarketClassificationTreeDTO;
import com.cosfo.mall.market.service.MarketClassificationService;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/10 9:51
 */
@Service
public class MarketClassificationServiceImpl implements MarketClassificationService {

    @Resource
    private MarketClassificationFacade marketClassificationFacade;

    /**
     * 一级分类父类ID
     */
    private static final Long DEFAULT_PRIMARY_ID = 0L;

    @Override
    public ResultDTO<List<MarketClassificationTreeDTO>> selectClassificationTree(Long tenantId) {
        if(Objects.isNull(tenantId)){
            return ResultDTO.success(Collections.emptyList());
        }

        List<MarketClassificationTreeDTO> marketClassificationTreeDTOS = marketClassificationFacade.selectClassificationTree(tenantId);
        if(!CollectionUtils.isEmpty(marketClassificationTreeDTOS)) {
            List<MarketClassificationTreeDTO> classificationTreeDTOS = marketClassificationTreeDTOS.stream().filter(e -> !CollectionUtils.isEmpty(e.getChildList())).collect(Collectors.toList());
            return ResultDTO.success(classificationTreeDTOS);
        }

        return ResultDTO.success(marketClassificationTreeDTOS);
    }

    @Override
    public ResultDTO<List<MarketClassificationTreeDTO>> noLoginSelectClassificationTree(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new ParamsException("租户参数不能为空");
        }
        ResultDTO<List<MarketClassificationTreeDTO>> classificationTreeResult = selectClassificationTree(tenantId);
        List<MarketClassificationTreeDTO> classificationTree = classificationTreeResult.getData();
        if (!CollectionUtils.isEmpty(classificationTree)) {
            // 未登录状态下只能查看第一个分类
            List<MarketClassificationTreeDTO> noLoginClassification = new ArrayList<>();
            noLoginClassification.add(classificationTree.get(0));
            MarketClassificationTreeDTO secClassification = noLoginClassification.get(0).getChildList().get(0);
            secClassification.setChildList(null);
            noLoginClassification.get(0).setChildList(Collections.singletonList(secClassification));
            return ResultDTO.success(noLoginClassification);
        }

        //String treeStr = "[{\"childList\":[{\"icon\":\"\",\"id\":123,\"name\":\"黄油\",\"parentId\":115},{\"icon\":\"\",\"id\":127,\"name\":\"炼乳\",\"parentId\":115},{\"icon\":\"\",\"id\":131,\"name\":\"奶粉\",\"parentId\":115},{\"icon\":\"\",\"id\":133,\"name\":\"奶盖\",\"parentId\":115},{\"icon\":\"\",\"id\":138,\"name\":\"稀奶油\",\"parentId\":115},{\"icon\":\"\",\"id\":140,\"name\":\"液体乳\",\"parentId\":115},{\"icon\":\"\",\"id\":148,\"name\":\"植脂制品\",\"parentId\":115},{\"icon\":\"\",\"id\":152,\"name\":\"奶酪丨芝士\",\"parentId\":115}],\"icon\":\"static/category-8.png\",\"id\":115,\"name\":\"乳制品\",\"sort\":1},{\"childList\":[{\"icon\":\"\",\"id\":134,\"name\":\"莓果\",\"parentId\":107},{\"icon\":\"\",\"id\":135,\"name\":\"应季水果\",\"parentId\":107},{\"icon\":\"\",\"id\":137,\"name\":\"芒果\",\"parentId\":107},{\"icon\":\"\",\"id\":142,\"name\":\"牛油果\",\"parentId\":107},{\"icon\":\"\",\"id\":144,\"name\":\"瓜类\",\"parentId\":107},{\"icon\":\"\",\"id\":147,\"name\":\"火龙果\",\"parentId\":107},{\"icon\":\"\",\"id\":151,\"name\":\"柑橘橙\",\"parentId\":107},{\"icon\":\"\",\"id\":154,\"name\":\"柠檬丨金桔\",\"parentId\":107},{\"icon\":\"\",\"id\":161,\"name\":\"苹果丨梨丨桃\",\"parentId\":107},{\"icon\":\"\",\"id\":157,\"name\":\"柚子丨百香果\",\"parentId\":107},{\"icon\":\"\",\"id\":164,\"name\":\"椰子丨香蕉\",\"parentId\":107},{\"icon\":\"\",\"id\":168,\"name\":\"葡提丨番茄\",\"parentId\":107},{\"icon\":\"\",\"id\":174,\"name\":\"奇异果丨猕猴桃\",\"parentId\":107},{\"icon\":\"\",\"id\":215,\"name\":\"凤梨丨菠萝\",\"parentId\":107},{\"icon\":\"\",\"id\":178,\"name\":\"水果制品\",\"parentId\":107},{\"icon\":\"\",\"id\":221,\"name\":\"蔬菜\",\"parentId\":107}],\"icon\":\"static/category-4.png\",\"id\":107,\"name\":\"鲜果\",\"sort\":2}]";
        //if(tenantId.equals(13L)){
            String treeStr = "[{\n" +
                    "\t\"childList\": [{\n" +
                    "\t\t\t\"icon\": \"\",\n" +
                    "\t\t\t\"id\": 224,\n" +
                    "\t\t\t\"name\": \"草莓\",\n" +
                    "\t\t\t\"parentId\": 223,\n" +
                    "\t\t\t\"sort\": 2,\n" +
                    "\t\t\t\"tenantId\": 13,\n" +
                    "\t\t}],\n" +
                    "\t\"icon\": \"static/category-18.png\",\n" +
                    "\t\"id\": 223,\n" +
                    "\t\"name\": \"鲜果\",\n" +
                    "\t\"sort\": 51\n" +
                    "}]";

        List<MarketClassificationTreeDTO> treeList = JSONObject.parseArray(treeStr, MarketClassificationTreeDTO.class);

        return ResultDTO.success(treeList);
    }
}
