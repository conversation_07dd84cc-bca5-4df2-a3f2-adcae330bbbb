package com.cosfo.mall.market.service.impl;

import cn.hutool.core.map.MapUtil;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProductAgentSkuFeeRuleTypeEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.facade.MarketFacade;
import com.cosfo.mall.facade.PriceFacade;
import com.cosfo.mall.facade.dto.PriceDetailDTO;
import com.cosfo.mall.market.convert.MarketItemConvert;
import com.cosfo.mall.market.mapper.MarketAreaItemMappingMapper;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.dto.MarketItemQuerySaleInfoDTO;
import com.cosfo.mall.market.model.dto.MarketItemSaleInfoDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
@Slf4j
@Service
public class MarketItemServiceImpl implements MarketItemService {

    @Resource
    private MarketAreaItemMappingMapper marketAreaItemMappingMapper;
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private PriceFacade priceFacade;

    @Override
    public List<MarketItemVO> batchByItemIds(List<Long> itemIds, Long tenantId) {

        MarketItemCommonQueryReq marketItemCommonQueryReq = new MarketItemCommonQueryReq();
        marketItemCommonQueryReq.setTenantId(tenantId);
        marketItemCommonQueryReq.setItemIds(itemIds);
        marketItemCommonQueryReq.setPageNum(NumberConstant.ONE);
        marketItemCommonQueryReq.setPageSize(NumberConstant.ONE_THOUSAND);
        List<MarketItemInfoResp> marketItemInfoResps = marketFacade.queryMarketItemList(marketItemCommonQueryReq);
        List<MarketItemVO> marketItemVos = MarketItemConvert.convertMarketItemVOList(marketItemInfoResps);
        return marketItemVos;
    }

    @Override
    public ResultDTO selectByTitle(LoginContextInfoDTO contextInfoDTO, String title) {
        List<MarketItemDTO> marketItemDTOList = marketFacade.querySimpleMarketItemInfo(contextInfoDTO.getTenantId(), contextInfoDTO.getStoreId(), title);
        return ResultDTO.success(marketItemDTOList);
    }

    @Override
    public List<MarketItemDTO> selectSimpleItemByItemIds(Long tenantId, Long storeId, List<Long> itemIds, Integer onSale) {
        return marketFacade.querySimpleMarketItemInfo(tenantId, storeId,itemIds,onSale, NumberConstant.ONE, NumberConstant.ONE_THOUSAND);
    }


    @Override
    public BigDecimal calSelfSupplyMallPrice(Long tenantId, BigDecimal price, Integer miniOrderQuantity, Integer reqItemCount) {
        Integer itemCnt = miniOrderQuantity;
        if (reqItemCount != null) {
            itemCnt = reqItemCount;
        }
        return calculateProductAgentSkuFee(tenantId, price, itemCnt);
    }

    private BigDecimal calculateProductAgentSkuFee(Long tenantId, BigDecimal price, Integer itemCount) {
        ProductAgentSkuFeeDTO productAgentSkuFeeDTO = productAgentSkuFeeRuleService.buildAgentSkuFeeRuleList(tenantId, price);
        if (Objects.isNull(productAgentSkuFeeDTO)) {
            return price;
        }
        // 按比例金额
        if (Objects.equals(productAgentSkuFeeDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
            return productAgentSkuFeeDTO.getAfterPercentPrice();
        }
        // 按件数金额
        List<ProductAgentSkuFeeCountRuleDTO> countRuleDTOList = productAgentSkuFeeDTO.getCountRuleDTOList();
        Optional<ProductAgentSkuFeeCountRuleDTO> ruleOptional = countRuleDTOList.stream().sorted(Comparator.comparing(ProductAgentSkuFeeCountRuleDTO::getCount).reversed())
                .filter(el -> el.getCount() <= itemCount).findFirst();
        // 没有找到对应的加价规则区间
        if (Objects.isNull(ruleOptional) || !ruleOptional.isPresent()) {
            return price;
        }
        ProductAgentSkuFeeCountRuleDTO productAgentSkuFeeCountRuleDTO = ruleOptional.get();
        return productAgentSkuFeeCountRuleDTO.getPrice();
    }

    @Override
    public CommonResult<MarketItemSaleInfoDTO> queryItemSaleInfo(MarketItemQuerySaleInfoDTO marketItemQuerySaleInfoDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long itemId = marketItemQuerySaleInfoDTO.getItemId();
        Integer amount = marketItemQuerySaleInfoDTO.getAmount();
        Long tenantId = loginContextInfoDTO.getTenantId();
        Long storeId = loginContextInfoDTO.getStoreId();

        Assert.notNull(itemId, "商品ID不能为空");
        Assert.notNull(amount, "商品数量不能为空");

        MarketItemDTO marketItemDTO = marketFacade.getMarketItemDetail(tenantId, storeId, itemId);
         if (marketItemDTO == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到该商品价格信息");
        }

        Map<Long, Integer> itemBuyAmount = new HashMap<> ();
        itemBuyAmount.put(itemId, amount);
        Map<Long, PriceDetailDTO> priceDetailDTOMap = priceFacade.listItemPriceDetailByItemIds(tenantId, storeId, PriceTargetTypeEnum.STORE.getCode(), itemBuyAmount,false);

        MarketItemSaleInfoDTO marketItemSaleInfoDTO = new MarketItemSaleInfoDTO();
        marketItemSaleInfoDTO.setItemId(itemId);
        if(MapUtil.isNotEmpty (priceDetailDTOMap)&&priceDetailDTOMap.containsKey (itemId)) {
            marketItemSaleInfoDTO.setPrice (priceDetailDTOMap.get (itemId).getPrice());
        }else{
            marketItemSaleInfoDTO.setPrice (marketItemDTO.getPrice ());
        }

        if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemDTO.getGoodsType())) {
            // TODO 是否加价，下一个加价规则金额判断
            marketItemSaleInfoDTO.setPrice(calSelfSupplyMallPrice(tenantId, marketItemSaleInfoDTO.getPrice(), marketItemDTO.getMiniOrderQuantity(), amount));
            // 计算基于amount的下一个价格配置
            ProductAgentSkuFeeCountRuleDTO productAgentSkuFeeCountRuleDTO = productAgentSkuFeeRuleService.buildNextStepSkuFeeRule(loginContextInfoDTO.getTenantId(), marketItemDTO.getPrice(), amount);
            marketItemSaleInfoDTO.setNextStepRule(productAgentSkuFeeCountRuleDTO);
        }
        return CommonResult.ok(marketItemSaleInfoDTO);
    }
}
