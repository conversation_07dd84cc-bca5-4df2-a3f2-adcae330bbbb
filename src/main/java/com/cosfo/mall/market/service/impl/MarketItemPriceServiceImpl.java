package com.cosfo.mall.market.service.impl;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProductAgentSkuFeeRuleTypeEnum;
import com.cosfo.mall.facade.PriceFacade;
import com.cosfo.mall.facade.dto.ItemPriceDetailDTO;
import com.cosfo.mall.facade.dto.PriceDetailDTO;
import com.cosfo.mall.market.model.dto.SkuMallPriceDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemPriceService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/12
 */
@Service
public class MarketItemPriceServiceImpl implements MarketItemPriceService {
    @Resource
    private PriceFacade priceFacade;
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;

    @Override
    public Map<Long, SkuMallPriceDTO> queryItemMallPrice(Long storeId, LoginContextInfoDTO loginContextInfoDTO, List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemBuyAmount, TenantDTO tenantDTO, MerchantAddressDTO merchantAddressDTO,boolean throwExceptionFlag) {
        Long tenantId = loginContextInfoDTO.getTenantId();

        if(CollectionUtils.isEmpty(marketItemVOList)){
            return Collections.emptyMap();
        }

        Map<Long, MarketItemVO> marketItemVOMap = marketItemVOList.stream().collect(Collectors.toMap(MarketItemVO::getItemId, item -> item));
//        Set<Long> itemIds = marketItemVOMap.keySet();
        Map<Long, PriceDetailDTO> priceDetailDTOMap = priceFacade.listItemPriceDetailByItemIds(tenantId, storeId, PriceTargetTypeEnum.STORE.getCode(), itemBuyAmount, throwExceptionFlag);
        if(CollectionUtils.isEmpty(priceDetailDTOMap)){
            return Collections.emptyMap();
        }

        Map<Long, SkuMallPriceDTO> map = new HashMap<>();
        Set<Long> itemIdSet = priceDetailDTOMap.keySet();
        itemIdSet.forEach(itemId -> {
            PriceDetailDTO priceDetailDTO = priceDetailDTOMap.get(itemId);
            SkuMallPriceDTO skuMallPriceDTO = new SkuMallPriceDTO();
            if(Objects.nonNull(priceDetailDTO)) {
                skuMallPriceDTO = convertToPriceDetailDTO(priceDetailDTO);
                skuMallPriceDTO.setItemId(itemId);
                skuMallPriceDTO.setLadderPrices (priceDetailDTO.getLadderPrices());
                MarketItemVO marketItemVO = marketItemVOMap.get(itemId);
                if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType())) {
                    Integer quantity = itemBuyAmount.get(itemId);
                    // 获取代仓费用
                    BigDecimal agentMallPrice = calculateProductAgentSkuFee(tenantId, skuMallPriceDTO.getPrice(), quantity);
                    skuMallPriceDTO.setAgentMallPrice(agentMallPrice);
                }
            }

            map.put(itemId, skuMallPriceDTO);
        });

        return map;
    }

    /**
     * 转化为SkuMallPriceDTO
     *
     * @param priceDetailDTO
     * @return
     */
    public static SkuMallPriceDTO convertToPriceDetailDTO(PriceDetailDTO priceDetailDTO){
        if (priceDetailDTO == null) {
            return null;
        }

        SkuMallPriceDTO skuMallPriceDTO = new SkuMallPriceDTO();
        skuMallPriceDTO.setPrice(priceDetailDTO.getPrice());
        // 供应价
        skuMallPriceDTO.setSupplyPrice(priceDetailDTO.getCostPrice());
        skuMallPriceDTO.setMarketItemPrice(priceDetailDTO.getMarketItemPrice());
        skuMallPriceDTO.setBasePrice(priceDetailDTO.getPrice());
        return skuMallPriceDTO;
    }

    @Override
    public Map<Long, SkuMallPriceDTO> queryCombineItemMallPrice(Long storeId, Long tenantId,Integer amount, Long combineItemId,boolean throwExceptionFlag) {
        if(Objects.isNull(combineItemId)){
            return new HashMap<>(NumberConstant.SIXTEEN);
        }
        Map<Long, Integer> itemBuyAmount = new HashMap<> ();
        itemBuyAmount.put(combineItemId, amount);
        List<ItemPriceDetailDTO> itemPriceDetailDTOS = priceFacade.listItemPriceDetailByItemIds4CombineItem(tenantId, storeId, PriceTargetTypeEnum.STORE.getCode(), itemBuyAmount,throwExceptionFlag);
        if(CollectionUtils.isEmpty(itemPriceDetailDTOS)){
            return new HashMap<>(NumberConstant.SIXTEEN);
        }

        // 获取商品子项的价格
        ItemPriceDetailDTO itemPriceDetailDTO = itemPriceDetailDTOS.get(NumberConstant.ZERO);
        List<ItemPriceDetailDTO> subItemPriceDetails = itemPriceDetailDTO.getSubItemPriceDetails();
        Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap = subItemPriceDetails.stream().collect(Collectors.toMap(ItemPriceDetailDTO::getItemId, e -> convertToSkuMallPriceDTO(e)));
        return skuMallPriceDTOMap;
    }

    private SkuMallPriceDTO convertToSkuMallPriceDTO(ItemPriceDetailDTO itemPriceDetailDTO){
        SkuMallPriceDTO skuMallPriceDTO = new SkuMallPriceDTO();
        skuMallPriceDTO.setItemId(itemPriceDetailDTO.getItemId());
        skuMallPriceDTO.setPrice(itemPriceDetailDTO.getPrice());
        skuMallPriceDTO.setSupplyPrice(itemPriceDetailDTO.getCostPrice());
        skuMallPriceDTO.setMarketItemPrice(itemPriceDetailDTO.getMarketItemPrice());
        return skuMallPriceDTO;
    }

    @Override
    public BigDecimal calculateProductAgentSkuFee(Long tenantId, BigDecimal price, Integer itemCount) {
        ProductAgentSkuFeeDTO productAgentSkuFeeDTO = productAgentSkuFeeRuleService.buildAgentSkuFeeRuleList(tenantId, price);
        if (Objects.isNull(productAgentSkuFeeDTO)) {
            return price;
        }
        // 按比例金额
        if (Objects.equals(productAgentSkuFeeDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
            return productAgentSkuFeeDTO.getAfterPercentPrice();
        }
        // 按件数金额
        List<ProductAgentSkuFeeCountRuleDTO> countRuleDTOList = productAgentSkuFeeDTO.getCountRuleDTOList();
        Optional<ProductAgentSkuFeeCountRuleDTO> ruleOptional = countRuleDTOList.stream().sorted(Comparator.comparing(ProductAgentSkuFeeCountRuleDTO::getCount).reversed()).filter(el -> el.getCount() <= itemCount).findFirst();
        // 没有找到对应的加价规则区间
        if (Objects.isNull(ruleOptional) || !ruleOptional.isPresent()) {
            return price;
        }
        ProductAgentSkuFeeCountRuleDTO productAgentSkuFeeCountRuleDTO = ruleOptional.get();
        return productAgentSkuFeeCountRuleDTO.getPrice();
    }
}
