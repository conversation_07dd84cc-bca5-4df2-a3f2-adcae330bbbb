package com.cosfo.mall.market.service;


import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.model.dto.MarketClassificationTreeDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/10 9:52
 */
public interface MarketClassificationService {


    /**
     * 分类树
     *
     * @param tenantId
     * @return
     */
    ResultDTO<List<MarketClassificationTreeDTO>> selectClassificationTree(Long tenantId);

    /**
     * 未登录获取分类数
     *
     * @param tenantId
     * @return
     */
    ResultDTO<List<MarketClassificationTreeDTO>> noLoginSelectClassificationTree(Long tenantId);
}
