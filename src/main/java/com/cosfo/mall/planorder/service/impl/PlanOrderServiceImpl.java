package com.cosfo.mall.planorder.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.common.constants.ItemSaleModeEnum;
import com.cosfo.mall.common.constants.MarketDeleteFlagEnum;
import com.cosfo.mall.common.constants.MarketItemSaleStatusEnum;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.service.AreaService;
import com.cosfo.mall.common.utils.PageInfoConverter;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.ProductQueryFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.market.model.dto.SkuMallPriceDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemPriceService;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.order.converter.OrderItemConverter;
import com.cosfo.mall.order.mapper.TrolleyMapper;
import com.cosfo.mall.order.model.dto.OrderItemCheckDTO;
import com.cosfo.mall.order.model.po.Trolley;
import com.cosfo.mall.order.model.vo.OrderItemVO;
import com.cosfo.mall.order.model.vo.TrolleyDetailVO;
import com.cosfo.mall.order.service.impl.OrderItemCheckService;
import com.cosfo.mall.planorder.model.dto.PlanOrderDetailItemDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemCheckDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemSnapshotDTO;
import com.cosfo.mall.planorder.model.input.PlanOrderListQueryInput;
import com.cosfo.mall.planorder.model.input.PlanOrderQueryInput;
import com.cosfo.mall.planorder.model.vo.PlanOrderDetailVO;
import com.cosfo.mall.planorder.model.vo.PlanOrderOrderVO;
import com.cosfo.mall.planorder.model.vo.UnfinishedPlanOrderVO;
import com.cosfo.mall.planorder.service.PlanOrderService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.stock.model.dto.PreDistributionOrderItemDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.dto.StockQueryDTO;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.manage.client.enums.AgentOrderEnum;
import com.cosfo.manage.client.planorder.PlanOrderQueryProvider;
import com.cosfo.manage.client.planorder.req.CountPlanOrderStatusNumReq;
import com.cosfo.manage.client.planorder.req.PlanOrderQueryReq;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import com.cosfo.manage.client.planorder.resp.PlanOrderItemResp;
import com.cosfo.manage.client.planorder.resp.PlanOrderResp;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.i18n.util.XianmuI18nUtil;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2024/2/19 下午6:16
 */
@Service
@Slf4j
public class PlanOrderServiceImpl implements PlanOrderService {

    @Resource
    private MarketItemService marketItemService;
    @Resource
    private TrolleyMapper trolleyMapper;
    @Resource
    private MarketItemPriceService marketItemPriceService;
    @Resource
    private OrderItemCheckService orderItemCheckService;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private AreaService areaService;
    @Resource
    private ProductQueryFacade productQueryFacade;
    @Resource
    private StockService stockService;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;

    @DubboReference
    private PlanOrderQueryProvider planOrderQueryProvider;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;

    @Override
    public List<Long> again(String planOrderNo, LoginContextInfoDTO loginContextInfoDTO) {
        PlanOrderDetailResp planOrderDetailResp = RpcResultUtil.handle(planOrderQueryProvider.queryByPlanOrderNo(planOrderNo));
        if (planOrderDetailResp == null || CollectionUtils.isEmpty(planOrderDetailResp.getPlanOrderItems())) {
            throw new BizException("计划单不存在");
        }
        // 查询计划单的商品信息
        List<Long> itemIds = planOrderDetailResp.getPlanOrderItems().stream().map(PlanOrderItemResp::getItemId).distinct().collect(Collectors.toList());

        // 商品加购物车的数量
        Map<Long, Integer> itemAmountMap = planOrderDetailResp.getPlanOrderItems().stream().collect(Collectors.toMap(PlanOrderItemResp::getItemId, PlanOrderItemResp::getItemAmount, (v1, v2) -> v1));

        // 查询商品信息
        List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());

        // 只新增上架的商品
        // 有效商品
        List<MarketItemVO> itemVOS = marketItemVOS.stream().filter(marketItemVO -> !Objects.equals(marketItemVO.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag()) && MarketItemSaleStatusEnum.ON_SALE.getCode().equals(marketItemVO.getOnSale())).collect(Collectors.toList());

        // 剔除购物车已经有的商品
        List<Trolley> trolleysList = trolleyMapper.selectByAccountId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getAccountId());
        Map<Long, Trolley> trolleyMap = trolleysList.stream().collect(Collectors.toMap(Trolley::getItemId, item -> item));

        List<Trolley> trolleys = itemVOS.stream().map(item -> {
            if (!trolleyMap.containsKey(item.getItemId())) {
                Trolley trolley = new Trolley();
                trolley.setAccountId(loginContextInfoDTO.getAccountId());
                trolley.setStoreId(loginContextInfoDTO.getStoreId());
                trolley.setItemId(item.getItemId());
                trolley.setTenantId(loginContextInfoDTO.getTenantId());
                trolley.setAmount(itemAmountMap.get(item.getItemId()));
                return trolley;

            } else {
                // 有就update
                Trolley trolley = trolleyMap.get(item.getItemId());
                trolley.setAmount(trolley.getAmount() + itemAmountMap.get(item.getItemId()));
                trolleyMapper.updateByPrimaryKeySelective(trolley);
            }

            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(trolleys)) {
            trolleyMapper.batchInsert(trolleys);
        }

        return itemVOS.stream().map(MarketItemVO::getItemId).collect(Collectors.toList());
    }

    @Override
    public Integer countWaitConfirmPlanOrderNum(Long tenantId, Long storeId) {
        CountPlanOrderStatusNumReq req = new CountPlanOrderStatusNumReq();
        req.setTenantId(tenantId);
        req.setStoreIds(Collections.singletonList(storeId));
        req.setPlanOrderStatusList(Collections.singletonList(AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue()));
        return RpcResponseUtil.handler(planOrderQueryProvider.countPlanOrderStatusNum(req));
    }

    @Override
    public PageInfo<PlanOrderDetailVO> listPlanOrder(PlanOrderListQueryInput input, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();

        PlanOrderQueryReq req = new PlanOrderQueryReq();
        req.setTenantId(loginContextInfoDTO.getTenantId());
        req.setStoreIds(Collections.singletonList(loginContextInfoDTO.getStoreId()));
        if (input.getPlanOrderStatus() != null) {
            req.setPlanOrderStatusList(Collections.singletonList(input.getPlanOrderStatus()));
        } else {
            req.setPlanOrderStatusList(Lists.newArrayList(AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue(), AgentOrderEnum.Status.CREATE_ORDER_SUCCESS.getValue(), AgentOrderEnum.Status.PLAN_ORDER_CANCEL.getValue()));
        }
        req.setPageNum(input.getPageIndex());
        req.setPageSize(input.getPageSize());
        PageInfo<PlanOrderResp> planOrderRespPageInfo = RpcResultUtil.handle(planOrderQueryProvider.queryPlanOrderPage(req));
        if (planOrderRespPageInfo == null || CollectionUtils.isEmpty(planOrderRespPageInfo.getList())) {
            return PageInfo.emptyPageInfo();
        }

        List<Long> itemIds = planOrderRespPageInfo.getList().stream().flatMap(planOrderResp -> planOrderResp.getPlanOrderItems().stream()).map(PlanOrderItemResp::getItemId).distinct().collect(Collectors.toList());

        List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(itemIds, tenantId);
        Map<Long, MarketItemVO> marketItemMap = marketItemVOS.stream().collect(Collectors.toMap(MarketItemVO::getItemId, Function.identity(), (v1, v2) -> v1));

        PageInfo<PlanOrderDetailVO> pageInfo = PageInfoConverter.toPageInfo(planOrderRespPageInfo, e -> convertPlanOrderDetailVOPage(e, loginContextInfoDTO, marketItemMap));
        return pageInfo;
    }

    private PlanOrderDetailVO convertPlanOrderDetailVOPage(PlanOrderResp planOrderResp, LoginContextInfoDTO loginContextInfoDTO, Map<Long, MarketItemVO> marketItemMap) {
        PlanOrderDetailVO planOrderDetailVO = new PlanOrderDetailVO();
        planOrderDetailVO.setPlanOrderId(planOrderResp.getPlanOrderId());
        planOrderDetailVO.setPlanOrderNo(planOrderResp.getPlanOrderNo());
        planOrderDetailVO.setPlanOrderStatus(planOrderResp.getPlanOrderStatus());
        planOrderDetailVO.setPlanType(planOrderResp.getPlanType());
        planOrderDetailVO.setRecommendReason(planOrderResp.getRecommendReason());
        planOrderDetailVO.setPlanOrderCreateTime(planOrderResp.getPlanOrderCreateTime());
        planOrderDetailVO.setPlanOrderCreateName(XianmuI18nUtil.getI18nValue("品牌总部"));

        List<PlanOrderItemResp> itemResps = planOrderResp.getPlanOrderItems();

        if (AgentOrderEnum.Status.CREATE_ORDER_SUCCESS.getValue().equals(planOrderDetailVO.getPlanOrderStatus())) {
            buildPlanOrderDetailItemDTO(planOrderDetailVO, planOrderResp.getItemInfoSnapshot(), itemResps);
        } else {
            // 计划单商品总价格
            BigDecimal itemTotalPrice = null;
            // 计划单商品
            List<PlanOrderDetailItemDTO> itemDTOList = new ArrayList<>(itemResps.size());

            // 下单数量
            Map<Long, Integer> itemBuyAmount = itemResps.stream()
                    .collect(Collectors.toMap(PlanOrderItemResp::getItemId, PlanOrderItemResp::getItemAmount));
            // 下单商品
            List<MarketItemVO> marketItemVOS = itemBuyAmount.keySet().stream().filter(e -> marketItemMap.containsKey(e)).map(e -> marketItemMap.get(e)).collect(Collectors.toList());
            // 获取商品商城价
            Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOS, itemBuyAmount, null, null,true);

            for (PlanOrderItemResp itemResp : itemResps) {
                Long itemId = itemResp.getItemId();
                MarketItemVO marketItemVO = marketItemMap.get(itemId);
                if (marketItemVO == null) {
                    continue;
                }

                PlanOrderDetailItemDTO planOrderDetailItemDTO = getPlanOrderDetailItemDTO(marketItemVO, itemResp, skuMallPriceDTOMap.get(itemId));

                if (planOrderDetailItemDTO.getItemTotalPrice() != null) {
                    itemTotalPrice = planOrderDetailItemDTO.getItemTotalPrice().add(itemTotalPrice == null ? BigDecimal.ZERO : itemTotalPrice);
                }

                itemDTOList.add(planOrderDetailItemDTO);
            }

            planOrderDetailVO.setItemDTOList(itemDTOList);
            planOrderDetailVO.setItemCount(itemDTOList.size());
            planOrderDetailVO.setItemTotalPrice(itemTotalPrice);
        }

        return planOrderDetailVO;
    }

    private PlanOrderDetailItemDTO getPlanOrderDetailItemDTO(MarketItemVO marketItemVO, PlanOrderItemResp itemResp, SkuMallPriceDTO skuMallPriceDTO) {
        PlanOrderDetailItemDTO planOrderDetailItemDTO = new PlanOrderDetailItemDTO();
        planOrderDetailItemDTO.setItemId(itemResp.getItemId());
        planOrderDetailItemDTO.setItemAmount(itemResp.getItemAmount());
        planOrderDetailItemDTO.setSpuTitle(marketItemVO.getTitle());
        planOrderDetailItemDTO.setSpecification(marketItemVO.getSpecification());
        planOrderDetailItemDTO.setSpecificationUnit(marketItemVO.getSpecificationUnit());
        planOrderDetailItemDTO.setMainPicture(marketItemVO.getMainPicture());
        planOrderDetailItemDTO.setItemCode(marketItemVO.getItemCode());
        planOrderDetailItemDTO.setGoodsType(marketItemVO.getGoodsType());
        planOrderDetailItemDTO.setOnSale(marketItemVO.getOnSale());
        planOrderDetailItemDTO.setMiniOrderQuantity(marketItemVO.getMiniOrderQuantity());
        planOrderDetailItemDTO.setBuyMultipleSwitch(marketItemVO.getBuyMultipleSwitch());
        planOrderDetailItemDTO.setBuyMultiple(marketItemVO.getBuyMultiple());
        if (skuMallPriceDTO != null && skuMallPriceDTO.getPrice() != null) {
            planOrderDetailItemDTO.setItemPrice(skuMallPriceDTO.getPrice());
            planOrderDetailItemDTO.setItemTotalPrice(NumberUtil.mul(skuMallPriceDTO.getPrice(), itemResp.getItemAmount()));
        }
        return planOrderDetailItemDTO;
    }

    private Map<Long, PlanOrderItemSnapshotDTO> getPlanOrderItemSnapshotMap(String planOrderNo, String itemInfoSnapshot) {
        Map<Long, PlanOrderItemSnapshotDTO> itemSnapshotDTOMap = Collections.emptyMap();
        if (!StringUtils.isEmpty(itemInfoSnapshot)) {
            try {
                List<PlanOrderItemSnapshotDTO> itemSnapshotDTOS = JSONArray.parseArray(itemInfoSnapshot, PlanOrderItemSnapshotDTO.class);
                itemSnapshotDTOMap = itemSnapshotDTOS.stream().collect(Collectors.toMap(PlanOrderItemSnapshotDTO::getItemId, Function.identity(), (v1, v2) -> v1));
            } catch (Exception e) {
                log.error("解析计划单商品快照异常,planOrderNo={}", planOrderNo, e);
            }
        }
        return itemSnapshotDTOMap;
    }

    @Override
    public PlanOrderDetailVO getPlanOrderDetail(PlanOrderQueryInput planOrderQueryInput, LoginContextInfoDTO loginContextInfoDTO) {
        PlanOrderDetailResp planOrderDetailResp = RpcResultUtil.handle(planOrderQueryProvider.queryByPlanOrderNo(planOrderQueryInput.getPlanOrderNo()));
        if (planOrderDetailResp == null) {
            throw new BizException("计划单不存在");
        }

        PlanOrderDetailVO planOrderDetailVO = new PlanOrderDetailVO();
        planOrderDetailVO.setPlanOrderId(planOrderDetailResp.getPlanOrderId());
        planOrderDetailVO.setPlanOrderNo(planOrderDetailResp.getPlanOrderNo());
        planOrderDetailVO.setPlanOrderStatus(planOrderDetailResp.getPlanOrderStatus());
        planOrderDetailVO.setPlanType(planOrderDetailResp.getPlanType());
        planOrderDetailVO.setRecommendReason(planOrderDetailResp.getRecommendReason());
        planOrderDetailVO.setPlanOrderCreateTime(planOrderDetailResp.getPlanOrderCreateTime());
        planOrderDetailVO.setPlanOrderCreateName(XianmuI18nUtil.getI18nValue("品牌总部"));
        planOrderDetailVO.setAutoCancelTime(planOrderDetailResp.getAutoCancelTime());
        planOrderDetailVO.setCancelType(planOrderDetailResp.getCancelType());
        planOrderDetailVO.setCancelTime(planOrderDetailResp.getCancelTime());
        if (AgentOrderEnum.CancelOrderRoleEnum.STORE.name().equalsIgnoreCase(planOrderDetailResp.getCancelType())) {
            planOrderDetailVO.setCancelUserName(planOrderDetailResp.getCancelUserName());
            planOrderDetailVO.setCancelUserPhone(planOrderDetailResp.getCancelUserPhone());
        } else if (AgentOrderEnum.CancelOrderRoleEnum.TENANT.name().equalsIgnoreCase(planOrderDetailResp.getCancelType())) {
            planOrderDetailVO.setCancelUserName(XianmuI18nUtil.getI18nValue("品牌总部"));

        } else if (AgentOrderEnum.CancelOrderRoleEnum.SYSTEM.name().equalsIgnoreCase(planOrderDetailResp.getCancelType())) {
            planOrderDetailVO.setCancelUserName("超时系统自动取消");
        }

        planOrderDetailVO.setOrderCreateTime(planOrderDetailResp.getOrderCreateTime());
        planOrderDetailVO.setOrderNoList(planOrderDetailResp.getOrderNoList());

        // 下单操作人信息
        buildCreateOrderUser(planOrderDetailVO, loginContextInfoDTO);

        convertPlanOrderDetailVO(planOrderDetailVO, planOrderDetailResp.getPlanOrderItems(), planOrderDetailResp.getItemInfoSnapshot(), loginContextInfoDTO);

        return planOrderDetailVO;
    }

    private void buildCreateOrderUser(PlanOrderDetailVO planOrderDetailVO, LoginContextInfoDTO loginContextInfoDTO) {

        if (AgentOrderEnum.Status.CREATE_ORDER_SUCCESS.getValue().equals(planOrderDetailVO.getPlanOrderStatus())) {
            if (AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(planOrderDetailVO.getPlanType())) {
                OrderQueryReq queryReq = new OrderQueryReq();
                queryReq.setTenantId(loginContextInfoDTO.getTenantId());
                queryReq.setPlanOrderNo(planOrderDetailVO.getPlanOrderNo());
                List<OrderResp> orderDTOS = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
                if (!CollectionUtils.isEmpty(orderDTOS)) {
                    Long accountId = orderDTOS.get(0).getAccountId();
                    MerchantStoreAccountResultResp accountResultResp = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountId);
                    if (accountResultResp != null) {
                        planOrderDetailVO.setAgentOperatorName(accountResultResp.getAccountName());
                        planOrderDetailVO.setAgentOperatorPhone(accountResultResp.getPhone());
                    }
                }
            } else {
                planOrderDetailVO.setAgentOperatorName(XianmuI18nUtil.getI18nValue("品牌总部"));
            }
        }

    }

    private void convertPlanOrderDetailVO(PlanOrderDetailVO planOrderDetailVO, List<PlanOrderItemResp> itemResps, String itemInfoSnapshot, LoginContextInfoDTO loginContextInfoDTO) {
        if (AgentOrderEnum.Status.CREATE_ORDER_SUCCESS.getValue().equals(planOrderDetailVO.getPlanOrderStatus())) {
            buildPlanOrderDetailItemDTO(planOrderDetailVO, itemInfoSnapshot, itemResps);

        } else {
            // 计划单商品总价格
            BigDecimal itemTotalPrice = null;
            // 计划单商品
            List<PlanOrderDetailItemDTO> itemDTOList = new ArrayList<>(itemResps.size());

            List<Long> itemIds = itemResps.stream().map(PlanOrderItemResp::getItemId).distinct().collect(Collectors.toList());

            // 下单商品
            List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());
            Map<Long, MarketItemVO> marketItemMap = marketItemVOS.stream().collect(Collectors.toMap(MarketItemVO::getItemId, Function.identity(), (v1, v2) -> v1));

            // 下单数量
            Map<Long, Integer> itemBuyAmount = itemResps.stream()
                    .collect(Collectors.toMap(PlanOrderItemResp::getItemId, PlanOrderItemResp::getItemAmount));

            // 获取商品商城价
            Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOS, itemBuyAmount, null, null,true);

            // 库存
            MerchantAddressDTO addressDto = merchantAddressService.queryDefaultAddressDTO(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId());
            Map<Long, StockDTO> stockDtoMap = stockService.queryStockAmount(loginContextInfoDTO, addressDto, itemIds, null);

            for (PlanOrderItemResp itemResp : itemResps) {
                Long itemId = itemResp.getItemId();
                MarketItemVO marketItemVO = marketItemMap.get(itemId);
                if (marketItemVO == null) {
                    continue;
                }

                PlanOrderDetailItemDTO planOrderDetailItemDTO = getPlanOrderDetailItemDTO(marketItemVO, itemResp, skuMallPriceDTOMap.get(itemId));

                StockDTO stockDTO = stockDtoMap.get(itemId);
                planOrderDetailItemDTO.setStockAmount(Objects.isNull(stockDTO) ? NumberConstant.ZERO : stockDTO.getAmount());

                if (planOrderDetailItemDTO.getItemTotalPrice() != null) {
                    itemTotalPrice = planOrderDetailItemDTO.getItemTotalPrice().add(itemTotalPrice == null ? BigDecimal.ZERO : itemTotalPrice);
                }

                itemDTOList.add(planOrderDetailItemDTO);
            }

            planOrderDetailVO.setItemDTOList(itemDTOList);
            planOrderDetailVO.setItemCount(itemDTOList.size());
            planOrderDetailVO.setItemTotalPrice(itemTotalPrice);
        }
    }

    private void buildPlanOrderDetailItemDTO(PlanOrderDetailVO planOrderDetailVO, String itemInfoSnapshot, List<PlanOrderItemResp> itemResps) {
        // 计划单商品总价格
        BigDecimal itemTotalPrice = null;
        // 计划单商品
        List<PlanOrderDetailItemDTO> itemDTOList = new ArrayList<>(itemResps.size());

        Map<Long, PlanOrderItemSnapshotDTO> itemSnapshotDTOMap = getPlanOrderItemSnapshotMap(planOrderDetailVO.getPlanOrderNo(), itemInfoSnapshot);

        for (PlanOrderItemResp itemResp : itemResps) {
            Long itemId = itemResp.getItemId();
            PlanOrderItemSnapshotDTO planOrderItemSnapshotDTO = itemSnapshotDTOMap.get(itemId);
            if (planOrderItemSnapshotDTO == null) {
                continue;
            }
            PlanOrderDetailItemDTO planOrderDetailItemDTO = convertPlanOrderDetailItemDTO(planOrderItemSnapshotDTO);
            if (planOrderDetailItemDTO.getItemTotalPrice() != null) {
                itemTotalPrice = planOrderDetailItemDTO.getItemTotalPrice().add(itemTotalPrice == null ? BigDecimal.ZERO : itemTotalPrice);
            }
            itemDTOList.add(planOrderDetailItemDTO);
        }

        planOrderDetailVO.setItemDTOList(itemDTOList);
        planOrderDetailVO.setItemCount(itemDTOList.size());
        planOrderDetailVO.setItemTotalPrice(itemTotalPrice);
    }

    private PlanOrderDetailItemDTO convertPlanOrderDetailItemDTO(PlanOrderItemSnapshotDTO planOrderItemSnapshotDTO) {
        if (planOrderItemSnapshotDTO == null) {
            return null;
        }
        PlanOrderDetailItemDTO planOrderDetailItemDTO = new PlanOrderDetailItemDTO();
        planOrderDetailItemDTO.setItemId(planOrderItemSnapshotDTO.getItemId());
        planOrderDetailItemDTO.setSpuTitle(planOrderItemSnapshotDTO.getSpuTitle());
        planOrderDetailItemDTO.setSpecification(planOrderItemSnapshotDTO.getSpecification());
        planOrderDetailItemDTO.setSpecificationUnit(planOrderItemSnapshotDTO.getSpecificationUnit());
        planOrderDetailItemDTO.setMainPicture(planOrderItemSnapshotDTO.getMainPicture());
        planOrderDetailItemDTO.setItemCode(planOrderItemSnapshotDTO.getItemCode());
        planOrderDetailItemDTO.setGoodsType(planOrderItemSnapshotDTO.getGoodsType());
        planOrderDetailItemDTO.setOnSale(planOrderItemSnapshotDTO.getOnSale());
        planOrderDetailItemDTO.setItemAmount(planOrderItemSnapshotDTO.getItemAmount());
        planOrderDetailItemDTO.setStockAmount(planOrderItemSnapshotDTO.getStockAmount());
        planOrderDetailItemDTO.setItemPrice(planOrderItemSnapshotDTO.getItemPrice());
        planOrderDetailItemDTO.setItemTotalPrice(planOrderItemSnapshotDTO.getItemTotalAmount());
        return planOrderDetailItemDTO;
    }

    @Override
    public PlanOrderItemCheckDTO planOrderConfirmCheck(List<PlanOrderItemDTO> planOrderItemDTOS, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        Long storeId = loginContextInfoDTO.getStoreId();
        Map<Long, Integer> itemBuyMap = planOrderItemDTOS.stream().collect(Collectors.toMap(PlanOrderItemDTO::getItemId, PlanOrderItemDTO::getItemAmount, (v1, v2) -> v1));

        // 校验失败商品项
        List<OrderItemCheckDTO> failItemList = new ArrayList<>();

        List<Long> itemIds = planOrderItemDTOS.stream().map(PlanOrderItemDTO::getItemId).collect(Collectors.toList());
        // 查询商品信息
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, loginContextInfoDTO.getTenantId());

        // 上下架校验
        marketItemVOList = checkMarketItemOnSale(marketItemVOList, failItemList);

        // 限购校验
        marketItemVOList = checkItemSaleLimit(marketItemVOList, itemBuyMap, tenantId, storeId, failItemList);

        // 倍数订货
        marketItemVOList = checkBuyMultiple(marketItemVOList, itemBuyMap, failItemList);

        // 起订量
        marketItemVOList = checkminiOrderQuantity(marketItemVOList, itemBuyMap, failItemList);


        // 拆单校验库存、搭售
        marketItemVOList = planOrderSplitOrderAndCheck(marketItemVOList, itemBuyMap, loginContextInfoDTO, failItemList);

        List<OrderItemCheckDTO> successCheckList = marketItemVOList.stream().map(e -> {
            return OrderItemCheckDTO.checkOk(e.getItemId(), itemBuyMap.get(e.getItemId()));
        }).collect(Collectors.toList());

        PlanOrderItemCheckDTO planOrderItemCheckDTO = new PlanOrderItemCheckDTO();
        planOrderItemCheckDTO.setSuccessCheckList(successCheckList);
        planOrderItemCheckDTO.setFailItemList(failItemList);

        log.info("计划单商品检查结果，result={}", JSON.toJSONString(planOrderItemCheckDTO));
        return planOrderItemCheckDTO;
    }

    private List<MarketItemVO> planOrderSplitOrderAndCheck(List<MarketItemVO> marketItemVOS, Map<Long, Integer> itemBuyMap, LoginContextInfoDTO loginContextInfoDTO, List<OrderItemCheckDTO> failItemList) {
        if (CollectionUtils.isEmpty(marketItemVOS)) {
            return Collections.emptyList();
        }

        Map<Long, MarketItemVO> marketItemVOMap = marketItemVOS.stream().collect(Collectors.toMap(MarketItemVO::getItemId, item -> item));

        // 查询下单地址
        MerchantAddressDTO merchantAddressDTO = merchantAddressService.queryDefaultAddressDTO(loginContextInfoDTO.getStoreId(), loginContextInfoDTO.getTenantId());

        if (merchantAddressDTO == null) {
            throw new BizException("门店地址不存在，不能下单");
        }
        if (StringUtils.isEmpty(merchantAddressDTO.getPoiNote())) {
            throw new BizException("门店poi为空，不能下单");
        }

        // 报价货品ID
        List<Long> quotationSkuIds = marketItemVOS.stream().filter(marketItemVO -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemVO.getGoodsType())).map(MarketItemVO::getSkuId).collect(Collectors.toList());
        // 查询货品和鲜沐商品的映射关系
        Map<Long, ProductAgentSkuDTO> quotationProductAgentSkuDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(quotationSkuIds)) {
            List<ProductAgentSkuDTO> quotationProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(quotationSkuIds, XianmuSupplyTenant.TENANT_ID);
            quotationProductAgentSkuDTOMap = quotationProductAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        }

        // 自营货品ID
        List<Long> selfSupportSkuIds = marketItemVOS.stream().filter(marketItemVO -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType())).map(MarketItemVO::getSkuId).collect(Collectors.toList());
        // 查询货品和鲜沐商品的映射关系
        Map<Long, ProductAgentSkuDTO> selfSupportProductAgentSkuDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(selfSupportSkuIds)) {
            List<ProductAgentSkuDTO> selfSupportProductAgentSkuDTOS = productQueryFacade.queryAgentSkuInfo(selfSupportSkuIds, loginContextInfoDTO.getTenantId());
            selfSupportProductAgentSkuDTOMap = selfSupportProductAgentSkuDTOS.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, item -> item));
        }

        // 查询可用库存
        Map<Long, StockDTO> stockDTOMap = new HashMap<>();
        try {
            StockQueryDTO stockQueryDTO = new StockQueryDTO();
            stockQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
            stockQueryDTO.setMerchantAddressDTO(merchantAddressDTO);
            Map<Long, ProductAgentSkuDTO> finalQuotationProductAgentSkuDTOMap = quotationProductAgentSkuDTOMap;
            Map<Long, ProductAgentSkuDTO> finalSelfSupportProductAgentSkuDTOMap = selfSupportProductAgentSkuDTOMap;
            List<PreDistributionOrderItemDTO> preDistributionOrderItemDTOS = marketItemVOS.stream().map(e -> {
                Long itemId = e.getItemId();
                Integer quantity = itemBuyMap.get(e.getItemId());
                MarketItemVO marketItemVO = marketItemVOMap.get(itemId);
                PreDistributionOrderItemDTO preDistributionOrderItemDTO = new PreDistributionOrderItemDTO();
                preDistributionOrderItemDTO.setItemId(itemId);
                preDistributionOrderItemDTO.setQuantity(quantity);
                preDistributionOrderItemDTO.setGoodsType(marketItemVO.getGoodsType());
                if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemVO.getGoodsType())) {
                    ProductAgentSkuDTO productAgentSkuDTO = finalSelfSupportProductAgentSkuDTOMap.get(marketItemVO.getSkuId());
                    preDistributionOrderItemDTO.setSkuId(marketItemVO.getSkuId());
                    preDistributionOrderItemDTO.setAgentSku(productAgentSkuDTO.getAgentSkuCode());
                    preDistributionOrderItemDTO.setAgentSkuId(preDistributionOrderItemDTO.getAgentSkuId());
                } else if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemVO.getGoodsType())) {
                    ProductAgentSkuDTO productAgentSkuDTO = finalQuotationProductAgentSkuDTOMap.get(marketItemVO.getSkuId());
                    preDistributionOrderItemDTO.setSkuId(marketItemVO.getSkuId());
                    preDistributionOrderItemDTO.setAgentSku(productAgentSkuDTO.getAgentSkuCode());
                    preDistributionOrderItemDTO.setAgentSkuId(preDistributionOrderItemDTO.getAgentSkuId());
                }

                return preDistributionOrderItemDTO;
            }).collect(Collectors.toList());
            stockQueryDTO.setPreDistributionOrderItemDTOList(preDistributionOrderItemDTOS);
            stockDTOMap = stockService.preDistributionOrderOccupy(stockQueryDTO);
        } catch (Exception e) {
            log.error("异常信息：{}", e.getMessage(), e);
            log.error("{}获取库存数量失败", loginContextInfoDTO.getAccountId());
        }

        // 获取商品商城价
        Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap = marketItemPriceService.queryItemMallPrice(loginContextInfoDTO.getStoreId(), loginContextInfoDTO, marketItemVOS, itemBuyMap, null, merchantAddressDTO,true);
        // 数据整合
        return splitOrderAndCheck(marketItemVOMap, stockDTOMap, skuMallPriceDTOMap, itemBuyMap, failItemList);
    }


    private List<MarketItemVO> splitOrderAndCheck(Map<Long, MarketItemVO> marketItemVOMap, Map<Long, StockDTO> stockDTOMap, Map<Long, SkuMallPriceDTO> skuMallPriceDTOMap, Map<Long, Integer> itemBuyMap, List<OrderItemCheckDTO> failItemList) {

        // 购物车
        List<TrolleyDetailVO> list = new ArrayList<>();
        // 失效商品
        List<MarketItemVO> failureGoods = new ArrayList<>();
        // 无货订单
        TrolleyDetailVO proprietaryDelivery = new TrolleyDetailVO();
        proprietaryDelivery.setSupplierName(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getName());
        proprietaryDelivery.setValidFlag(Boolean.TRUE);
        proprietaryDelivery.setGoods(new ArrayList<>());
        // 三方订单
        TrolleyDetailVO threeDelivery = new TrolleyDetailVO();
        threeDelivery.setValidFlag(Boolean.TRUE);
        threeDelivery.setSupplierName(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getName());
        threeDelivery.setGoods(new ArrayList<>());
        // 自营仓订单 -> 根据仓编号拆单
        Map<Long, TrolleyDetailVO> trolleyDetailVOMap = new HashMap<>(NumberConstant.SIXTEEN);

        // 遍历购物车商品
        marketItemVOMap.values().forEach(e -> {

            Integer buyQuantity = itemBuyMap.get(e.getItemId());

            // 商品信息
            MarketItemVO marketItemVO = marketItemVOMap.get(e.getItemId());
            // 商品是否删除，删除商品放入失效商品列表
            if (Objects.equals(marketItemVO.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag())) {
                failItemList.add(OrderItemCheckDTO.checkFail(e.getItemId(), null, "商品已删除"));
                return;
            }

            if (!marketItemVO.getOnSale().equals(MarketItemSaleStatusEnum.ON_SALE.getCode())) {
                failItemList.add(OrderItemCheckDTO.checkFail(e.getItemId(), null, "商品已下架"));
                return;
            }

            SkuMallPriceDTO skuMallPriceDTO = null;
            // 商城价
            skuMallPriceDTO = skuMallPriceDTOMap.get(marketItemVO.getItemId());
            // 未获取到鲜沐报价价格，商品项失效
            if (Objects.isNull(skuMallPriceDTO) || Objects.isNull(skuMallPriceDTO.getPrice())) {
                failItemList.add(OrderItemCheckDTO.checkFail(e.getItemId(), null, "商品价格不存在"));
                return;
            } else {
                marketItemVO.setPrice(skuMallPriceDTO.getPrice());
            }

            // 可用库存调取库存服务
            // 获取库存
            StockDTO stockDTO = stockDTOMap.get(marketItemVO.getItemId());
            if (!Objects.isNull(stockDTO) && !Objects.isNull(stockDTO.getAmount())) {
                marketItemVO.setEnableAmount(stockDTO.getAmount() != null ? stockDTO.getAmount() : NumberConstant.ZERO);
                marketItemVO.setExpirationTime(stockDTO.getQuantityDate());

                // 判断库存信息是否大于可用库存
                if (stockDTO.getAmount() == null || stockDTO.getAmount() <= 0 || buyQuantity > stockDTO.getAmount()) {
                    failItemList.add(OrderItemCheckDTO.checkFail(e.getItemId(), null, "商品库存不足"));
                    return;
                }

                GoodsTypeEnum goodsType = GoodsTypeEnum.getTypeByCode(marketItemVO.getGoodsType());
                switch (goodsType) {
                    // 无货商品
                    case NO_GOOD_TYPE:
                        proprietaryDelivery.setWarehouseType(OrderEnums.WarehouseTypeEnum.PROPRIETARY.getCode());
                        proprietaryDelivery.getGoods().add(marketItemVO);
                        break;
                    // 报价货品
                    case QUOTATION_TYPE:
                        threeDelivery.getGoods().add(marketItemVO);
                        threeDelivery.setWarehouseType(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
                        break;
                    // 自营货品
                    case SELF_GOOD_TYPE:
                        // 判断是否是自营仓,自营仓根据仓库编号拆单
                        if (marketItemVO.getTenantId().equals(stockDTO.getWarehouseTenantId())) {
                            if (trolleyDetailVOMap.containsKey(stockDTO.getWarehouseNo())) {
                                TrolleyDetailVO trolleyDetailVO = trolleyDetailVOMap.get(stockDTO.getWarehouseNo());
                                List<MarketItemVO> goods = trolleyDetailVO.getGoods();
                                goods.add(marketItemVO);
                                trolleyDetailVO.setGoods(goods);
                            } else {
                                TrolleyDetailVO trolleyDetailVO = new TrolleyDetailVO();
                                trolleyDetailVO.setValidFlag(Boolean.TRUE);
                                trolleyDetailVO.setSupplierName(OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getName());
                                List<MarketItemVO> marketItemVOS = new ArrayList<>(Arrays.asList(marketItemVO));
                                trolleyDetailVO.setGoods(marketItemVOS);
                                trolleyDetailVO.setWarehouseNo(stockDTO.getWarehouseNo());
                                trolleyDetailVO.setWarehouseType(OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode());
                                trolleyDetailVOMap.put(stockDTO.getWarehouseNo(), trolleyDetailVO);
                            }
                            // 鲜沐三方仓
                        } else {
                            // 代仓获取取代仓商城价
                            marketItemVO.setPrice(skuMallPriceDTO.getAgentMallPrice());
                            threeDelivery.getGoods().add(marketItemVO);
                            threeDelivery.setWarehouseType(OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
                        }

                        break;
                }
            } else {
                failItemList.add(OrderItemCheckDTO.checkFail(e.getItemId(), null, "商品库存不足"));
                return;
            }
        });

        if (!CollectionUtils.isEmpty(proprietaryDelivery.getGoods())) {
            list.add(proprietaryDelivery);
        }

        if (!CollectionUtils.isEmpty(threeDelivery.getGoods())) {
            list.add(threeDelivery);
        }

        if (!CollectionUtils.isEmpty(trolleyDetailVOMap)) {
            List<TrolleyDetailVO> trolleyDetailVOS = trolleyDetailVOMap.values().stream().collect(Collectors.toList());
            list.addAll(trolleyDetailVOS);
        }

        // 校验搭售、独售
        checkOrderItemSaleMode(list, failItemList);

        Set<Long> checkFailItemIdSet = failItemList.stream().map(OrderItemCheckDTO::getItemId).collect(Collectors.toSet());
        // 返回校验通过的商品
        return marketItemVOMap.values().stream().filter(e -> !checkFailItemIdSet.contains(e.getItemId())).collect(Collectors.toList());
    }

    private void checkOrderItemSaleMode(List<TrolleyDetailVO> list, List<OrderItemCheckDTO> failItemList) {

        for (TrolleyDetailVO trolleyDetailVO : list) {
            // 若不存在可独售商品，则异常提示返回
            boolean normalSale = trolleyDetailVO.getGoods().stream().anyMatch(e -> ItemSaleModeEnum.NORMAL_SALE.getMode().equals(e.getItemSaleMode()));
            if (!normalSale) {
                for (MarketItemVO item : trolleyDetailVO.getGoods()) {
                    failItemList.add(OrderItemCheckDTO.checkFail(item.getItemId(), null, "商品搭售需要包含可独售商品"));
                }
            }
        }
    }


    /**
     * 限购校验
     *
     * @param marketItemVOList
     * @param itemMap
     * @param tenantId
     * @param storeId
     * @param failItemList
     * @return
     */
    private List<MarketItemVO> checkItemSaleLimit(List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemMap, Long tenantId, Long storeId, List<OrderItemCheckDTO> failItemList) {
        if (CollectionUtils.isEmpty(marketItemVOList)) {
            return Collections.emptyList();
        }
        // 限购校验
        List<OrderItemCheckDTO> orderItemCheckDTOS = orderItemCheckService.merchantStoreItemSaleLimitCheck(marketItemVOList, itemMap, tenantId, storeId);

        failItemList.addAll(orderItemCheckDTOS.stream().filter(e -> !e.isCheckFlag()).collect(Collectors.toList()));
        Set<Long> checkFailItemIdSet = failItemList.stream().map(OrderItemCheckDTO::getItemId).collect(Collectors.toSet());

        // 返回校验通过的商品
        return marketItemVOList.stream().filter(e -> !checkFailItemIdSet.contains(e.getItemId())).collect(Collectors.toList());
    }

    /**
     * 倍数订货校验
     *
     * @param marketItemVOList
     * @param itemMap
     * @param failItemList
     * @return
     */
    private List<MarketItemVO> checkBuyMultiple(List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemMap, List<OrderItemCheckDTO> failItemList) {
        if (CollectionUtils.isEmpty(marketItemVOList)) {
            return Collections.emptyList();
        }
        List<OrderItemCheckDTO> orderItemCheckDTOS = orderItemCheckService.merchantStoreItemBuyMultipleMuCheck(marketItemVOList, itemMap);

        failItemList.addAll(orderItemCheckDTOS.stream().filter(e -> !e.isCheckFlag()).collect(Collectors.toList()));
        Set<Long> checkFailItemIdSet = failItemList.stream().map(OrderItemCheckDTO::getItemId).collect(Collectors.toSet());

        // 返回校验通过的商品
        return marketItemVOList.stream().filter(e -> !checkFailItemIdSet.contains(e.getItemId())).collect(Collectors.toList());
    }

    /**
     * 起订量校验
     *
     * @param marketItemVOList
     * @param itemMap
     * @param failItemList
     * @return
     */
    private List<MarketItemVO> checkminiOrderQuantity(List<MarketItemVO> marketItemVOList, Map<Long, Integer> itemMap, List<OrderItemCheckDTO> failItemList) {
        if (CollectionUtils.isEmpty(marketItemVOList)) {
            return Collections.emptyList();
        }
        List<OrderItemCheckDTO> orderItemCheckDTOS = orderItemCheckService.miniOrderQuantityCheck(marketItemVOList, itemMap);

        failItemList.addAll(orderItemCheckDTOS.stream().filter(e -> !e.isCheckFlag()).collect(Collectors.toList()));
        Set<Long> checkFailItemIdSet = failItemList.stream().map(OrderItemCheckDTO::getItemId).collect(Collectors.toSet());

        // 返回校验通过的商品
        return marketItemVOList.stream().filter(e -> !checkFailItemIdSet.contains(e.getItemId())).collect(Collectors.toList());
    }

    /**
     * 上下架校验
     *
     * @param marketItemVOList
     * @param failItemList
     * @return
     */
    private List<MarketItemVO> checkMarketItemOnSale(List<MarketItemVO> marketItemVOList, List<OrderItemCheckDTO> failItemList) {
        if (CollectionUtils.isEmpty(marketItemVOList)) {
            return Collections.emptyList();
        }
        return marketItemVOList.stream()
                .filter(el -> {

                    if (Objects.equals(el.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag())) {
                        failItemList.add(OrderItemCheckDTO.checkFail(el.getItemId(), null, "商品已删除"));
                    } else if (!Objects.equals(OnSaleTypeEnum.ON_SALE.getCode(), el.getOnSale())) {
                        failItemList.add(OrderItemCheckDTO.checkFail(el.getItemId(), null, "商品已下架"));
                    }

                    return !Objects.equals(el.getDeleteFlag(), MarketDeleteFlagEnum.DELETED.getFlag())
                            && Objects.equals(OnSaleTypeEnum.ON_SALE.getCode(), el.getOnSale());
                }).collect(Collectors.toList());
    }


    @Override
    public List<PlanOrderOrderVO> orderDetailList(String planOrderNo, LoginContextInfoDTO loginContextInfoDTO) {
        PlanOrderDetailResp planOrderDetailResp = RpcResultUtil.handle(planOrderQueryProvider.queryByPlanOrderNo(planOrderNo));
        if (planOrderDetailResp == null) {
            throw new BizException("计划单不存在");
        }

        if (CollectionUtils.isEmpty(planOrderDetailResp.getOrderNoList())) {
            return Collections.emptyList();
        }

        List<OrderResp> orderDTOS = RpcResultUtil.handle(orderQueryProvider.queryByNos(planOrderDetailResp.getOrderNoList()));
        if (CollectionUtils.isEmpty(orderDTOS)) {
            return Collections.emptyList();
        }

        List<Long> orderIds = orderDTOS.stream().map(OrderResp::getId).collect(Collectors.toList());
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
        // 订单快照信息
        Map<Long, List<OrderItemAndSnapshotResp>> snapshotMap = orderItemAndSnapshotList.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getOrderId));

        List<PlanOrderOrderVO> planOrderOrderVOS = orderDTOS.stream().map(order -> {
            PlanOrderOrderVO planOrderOrderVO = new PlanOrderOrderVO();
            planOrderOrderVO.setOrderId(order.getId());
            planOrderOrderVO.setOrderNo(order.getOrderNo());
            // 查询商品信息
            List<OrderItemVO> orderItemVOS = OrderItemConverter.INSTANCE.dtoToVOList(snapshotMap.get(order.getId()));
            planOrderOrderVO.setOrderItemVOS(orderItemVOS);
            planOrderOrderVO.setTotalAmount(orderItemVOS.size());

            return planOrderOrderVO;
        }).collect(Collectors.toList());

        return planOrderOrderVOS;
    }

    @Override
    public UnfinishedPlanOrderVO unfinishedForcePlanOrder(LoginContextInfoDTO loginContextInfoDTO) {
        UnfinishedPlanOrderVO unfinishedPlanOrderVO = new UnfinishedPlanOrderVO();
        unfinishedPlanOrderVO.setExistUnfinishedPlanOrder(false);

        PlanOrderQueryReq req = new PlanOrderQueryReq();
        req.setTenantId(loginContextInfoDTO.getTenantId());
        req.setStoreIds(Collections.singletonList(loginContextInfoDTO.getStoreId()));
        req.setPlanOrderStatusList(Lists.newArrayList(AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue()));
        req.setPlanType(AgentOrderEnum.PlanTypeEnum.CREATE_FORCE_PLAN_ORDER.name());
        req.setPageNum(1);
        req.setPageSize(1);
        PageInfo<PlanOrderResp> planOrderRespPageInfo = RpcResultUtil.handle(planOrderQueryProvider.queryPlanOrderPage(req));
        if (planOrderRespPageInfo != null && !CollectionUtils.isEmpty(planOrderRespPageInfo.getList())) {
            unfinishedPlanOrderVO.setExistUnfinishedPlanOrder(true);
            unfinishedPlanOrderVO.setPlanOrderNo(planOrderRespPageInfo.getList().get(0).getPlanOrderNo());
        }

        return unfinishedPlanOrderVO;
    }

}
