package com.cosfo.mall.planorder.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemCheckDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemDTO;
import com.cosfo.mall.planorder.model.input.PlanOrderListQueryInput;
import com.cosfo.mall.planorder.model.input.PlanOrderQueryInput;
import com.cosfo.mall.planorder.model.vo.PlanOrderDetailVO;
import com.cosfo.mall.planorder.model.vo.PlanOrderOrderVO;
import com.cosfo.mall.planorder.model.vo.UnfinishedPlanOrderVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface PlanOrderService {


    /**
     * 再来一单, 计划单商品加购物车
     *
     * @param planOrderNo
     * @param loginContextInfoDTO
     */
    List<Long> again(String planOrderNo, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 个人中心统计计划单待确认数量
     * @param tenantId
     * @param storeId
     * @return
     */
    Integer countWaitConfirmPlanOrderNum(Long tenantId, Long storeId);


    /**
     * 分页查询计划单
     * @param input
     * @param loginContextInfoDTO
     * @return
     */
    PageInfo<PlanOrderDetailVO> listPlanOrder(PlanOrderListQueryInput input, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询计划单详情
     * @param planOrderQueryInput
     * @param loginContextInfoDTO
     * @return
     */
    PlanOrderDetailVO getPlanOrderDetail(PlanOrderQueryInput planOrderQueryInput, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 计划单下单确认检查
     * @param planOrderItemDTOS
     * @param loginContextInfoDTO
     * @return
     */
    PlanOrderItemCheckDTO planOrderConfirmCheck(List<PlanOrderItemDTO> planOrderItemDTOS, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询计划单下单成功的订单列表详情
     * @param planOrderNo
     * @param loginContextInfoDTO
     * @return
     */
    List<PlanOrderOrderVO> orderDetailList(String planOrderNo, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询未完成的铺货单
     * @param loginContextInfoDTO
     * @return
     */
    UnfinishedPlanOrderVO unfinishedForcePlanOrder(LoginContextInfoDTO loginContextInfoDTO);

}
