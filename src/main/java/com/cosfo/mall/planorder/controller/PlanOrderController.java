package com.cosfo.mall.planorder.controller;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemCheckDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemDTO;
import com.cosfo.mall.planorder.model.input.PlanOrderCancelInput;
import com.cosfo.mall.planorder.model.input.PlanOrderConfirmCheckInput;
import com.cosfo.mall.planorder.model.input.PlanOrderListQueryInput;
import com.cosfo.mall.planorder.model.input.PlanOrderQueryInput;
import com.cosfo.mall.planorder.model.vo.PlanOrderConfirmCheckVO;
import com.cosfo.mall.planorder.model.vo.PlanOrderDetailVO;
import com.cosfo.mall.planorder.model.vo.PlanOrderOrderVO;
import com.cosfo.mall.planorder.model.vo.UnfinishedPlanOrderVO;
import com.cosfo.mall.planorder.service.PlanOrderService;
import com.cosfo.manage.client.enums.AgentOrderEnum;
import com.cosfo.manage.client.planorder.PlanOrderCommandProvider;
import com.cosfo.manage.client.planorder.req.CancelPlanOrderReq;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * 计划单管理
 *
 * @author: xiaowk
 * @time: 2024/2/18 上午11:17
 */
@RestController
@RequestMapping("/plan-order")
@Slf4j
public class PlanOrderController extends BaseController {

    @Resource
    private PlanOrderService planOrderService;

    @DubboReference
    private PlanOrderCommandProvider planOrderCommandProvider;

    /**
     * 计划单列表
     * @param planOrderListQueryInput
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<PlanOrderDetailVO>> list(@RequestBody PlanOrderListQueryInput planOrderListQueryInput) {
        PageInfo<PlanOrderDetailVO> pageInfo = planOrderService.listPlanOrder(planOrderListQueryInput, getRequestContextInfoDTO());
        return CommonResult.ok(pageInfo);
    }

    /**
     * 计划单详情
     * @param planOrderQueryInput
     * @return
     */
    @PostMapping("/query/detail")
    public CommonResult<PlanOrderDetailVO> detail(@RequestBody PlanOrderQueryInput planOrderQueryInput) {
        return CommonResult.ok(planOrderService.getPlanOrderDetail(planOrderQueryInput, getRequestContextInfoDTO()));
    }

    /**
     * 查看计划单关联订单详情列表
     * @param planOrderQueryInput
     * @return
     */
    @PostMapping("/query/order-detail-list")
    public CommonResult<List<PlanOrderOrderVO>> orderDetailList(@RequestBody PlanOrderQueryInput planOrderQueryInput) {
        return CommonResult.ok(planOrderService.orderDetailList(planOrderQueryInput.getPlanOrderNo(), getRequestContextInfoDTO()));
    }

    /**
     * 取消计划单
     * @param planOrderCancelInput
     * @return
     */
    @PostMapping("/upsert/cancel")
    public CommonResult<Void> cancel(@Valid @RequestBody PlanOrderCancelInput planOrderCancelInput) {
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        CancelPlanOrderReq req = new CancelPlanOrderReq();
        req.setTenantId(loginContextInfoDTO.getTenantId());
        req.setPlanOrderId(planOrderCancelInput.getPlanOrderId());
        req.setOperatorId(loginContextInfoDTO.getAccountId());
        req.setOperatorSource(AgentOrderEnum.CancelOrderRoleEnum.STORE.name());
        req.setCancelRemark(planOrderCancelInput.getCancelRemark());
        RpcResponseUtil.handler(planOrderCommandProvider.cancelPlanOrder(req));
        return CommonResult.ok();
    }

    /**
     * 确认计划单检查
     * @param planOrderConfirmCheckInput
     * @return
     */
    @PostMapping("/query/confirm-check")
    public CommonResult<PlanOrderConfirmCheckVO> confirmCheck(@RequestBody PlanOrderConfirmCheckInput planOrderConfirmCheckInput) {
        PlanOrderItemCheckDTO checkDTO = planOrderService.planOrderConfirmCheck(planOrderConfirmCheckInput.getPlanOrderItemDTOS(), getRequestContextInfoDTO());
        if(CollectionUtils.isEmpty(checkDTO.getSuccessCheckList())){
            return CommonResult.ok(new PlanOrderConfirmCheckVO(null));
        }

        List<PlanOrderItemDTO> planOrderItemDTOS = checkDTO.getSuccessCheckList().stream().map(e -> {
            PlanOrderItemDTO planOrderItemDTO = new PlanOrderItemDTO();
            planOrderItemDTO.setItemId(e.getItemId());
            planOrderItemDTO.setItemAmount(e.getItemQuantity());
            return planOrderItemDTO;
        }).collect(Collectors.toList());

        return CommonResult.ok(new PlanOrderConfirmCheckVO(planOrderItemDTOS));
    }


    /**
     * 计划单加购物车
     *
     * @param planOrderQueryInput
     * @return itemIdList
     */
    @PostMapping("/upsert/again")
    public CommonResult<List<Long>> again(@Valid @RequestBody PlanOrderQueryInput planOrderQueryInput) {
        List<Long> itemIds = planOrderService.again(planOrderQueryInput.getPlanOrderNo(), getRequestContextInfoDTO());
        return CommonResult.ok(itemIds);
    }


    /**
     * 查询未完成的强制计划单（铺货单）
     */
    @PostMapping("/query/unfinished-force-planorder")
    public CommonResult<UnfinishedPlanOrderVO> unfinishedForcePlanOrder() {
        UnfinishedPlanOrderVO vo = planOrderService.unfinishedForcePlanOrder(getRequestContextInfoDTO());
        return CommonResult.ok(vo);
    }

}
