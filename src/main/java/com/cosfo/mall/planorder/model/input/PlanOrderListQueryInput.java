package com.cosfo.mall.planorder.model.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2024/2/4 17:45
 * @Description:
 */
@Data
public class PlanOrderListQueryInput extends BasePageInput implements Serializable {
    private static final long serialVersionUID = 1818576460513550074L;


    /**
     * 计划单状态 100-待门店确认; 105-下单中; 200-下单成功; 300-下单失败; 400-已取消
     */
    private Integer planOrderStatus;

}
