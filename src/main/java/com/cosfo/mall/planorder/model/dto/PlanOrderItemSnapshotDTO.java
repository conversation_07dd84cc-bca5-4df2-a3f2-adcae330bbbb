package com.cosfo.mall.planorder.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: xiaowk
 * @time: 2024/3/1 下午1:03
 */
@Data
public class PlanOrderItemSnapshotDTO {
    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 商品标题
     */
    private String spuTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品单价
     */
    private BigDecimal itemPrice;

    /**
     * 数量
     */
    private Integer itemAmount;

    /**
     * 商品总价
     */
    private BigDecimal itemTotalAmount;

    /**
     * 门店端可见状态,可能为空
     */
    private String storeShowStatus;

    /**
     * 失败原因，可能为空
     */
    private String failReason;

    /**
     * 上下架状态
     */
    private Integer onSale;

    /**
     * 库存
     */
    private Integer stockAmount;
}
