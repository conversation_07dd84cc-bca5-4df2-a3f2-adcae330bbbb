package com.cosfo.mall.planorder.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.mall.planorder.model.dto.PlanOrderDetailItemDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * 计划单列表详情
 * @author: xiaowk
 * @date: 2024/2/18 上午11:59
 */
@Data
public class PlanOrderDetailVO implements Serializable {
    private static final long serialVersionUID = -9061812591590177917L;

    /**
     * 计划单ID
     */
    private Long planOrderId;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 计划单状态 100-待门店确认; 105-下单中; 200-下单成功; 300-下单失败; 400-已取消
     */
    private Integer planOrderStatus;


    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 商品件数（不同商品个数*每件商品下单件数）
     */
    private Integer itemTotalAmount;

    /**
     * 商品数量（不同商品的个数）
     */
    private Integer itemCount;

    /**
     * 商品总价（不含运费）
     */
    private BigDecimal itemTotalPrice;

    /**
     * 计划单创建单
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planOrderCreateTime;

    /**
     * 计划单创建人，默认品牌总部
     */
    private String planOrderCreateName;

    /**
     * 系统自动取消截止时间
     */
    private LocalDateTime autoCancelTime;

    /**
     * 取消类型 store-门店 tenant-品牌总部 system-系统自动取消
     */
    private String cancelType;

    /**
     * 取消人名称
     */
    private String cancelUserName;

    /**
     * 取消人手机号
     */
    private String cancelUserPhone;
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;


    /**
     * 下单人ID
     */
    private Long agentOperatorAuthId;

    /**
     * 下单人名称
     */
    private String agentOperatorName;

    /**
     * 下单人手机号
     */
    private String agentOperatorPhone;

    /**
     * 成功下单订单编号列表
     */
    private List<String> orderNoList;

    /**
     * 计划单商品信息
     */
    private List<PlanOrderDetailItemDTO> itemDTOList;
}
