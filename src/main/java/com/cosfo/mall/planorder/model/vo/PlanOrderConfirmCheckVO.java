package com.cosfo.mall.planorder.model.vo;

import com.cosfo.mall.planorder.model.dto.PlanOrderItemDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2024/2/18 下午1:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlanOrderConfirmCheckVO implements Serializable {
    private static final long serialVersionUID = 3624662689644394246L;

    /**
     * 计划单商品列表
     */
    private List<PlanOrderItemDTO> planOrderItemDTOS;
}
