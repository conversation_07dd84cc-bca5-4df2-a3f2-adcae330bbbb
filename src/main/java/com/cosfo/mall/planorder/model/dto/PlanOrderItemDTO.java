package com.cosfo.mall.planorder.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 *
 * @author: xiaowk
 * @date: 2024/2/18 下午1:38
 */
@Data
public class PlanOrderItemDTO implements Serializable {
    private static final long serialVersionUID = 936195741622865691L;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不可为空")
    private Long itemId;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不可为空")
    private Integer itemAmount;
}
