package com.cosfo.mall.planorder.model.vo;

import com.cosfo.mall.order.model.vo.OrderItemVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/2/18 下午5:54
 */
@Data
public class PlanOrderOrderVO implements Serializable {

    private static final long serialVersionUID = -127267556189552871L;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 总件数
     */
    private Integer totalAmount;

    /**
     * 订单项
     */
    private List<OrderItemVO> orderItemVOS;

}
