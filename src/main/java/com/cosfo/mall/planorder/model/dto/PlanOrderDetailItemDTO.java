package com.cosfo.mall.planorder.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 17:19
 * @Description:
 */
@Data
public class PlanOrderDetailItemDTO implements Serializable {
    private static final long serialVersionUID = 8547079556119797133L;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 商品标题
     */
    private String spuTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品单价
     */
    private BigDecimal itemPrice;

    /**
     * 数量
     */
    private Integer itemAmount;

    /**
     * 商品总价
     */
    private BigDecimal itemTotalPrice;

    /**
     * 上下架状态 0-下架 1-上架
     */
    private Integer onSale;

    /**
     * 剩余库存
     */
    private Integer stockAmount;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

}
