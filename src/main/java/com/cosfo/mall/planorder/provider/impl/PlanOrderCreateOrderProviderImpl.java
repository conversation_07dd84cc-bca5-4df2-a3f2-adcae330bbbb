package com.cosfo.mall.planorder.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.client.planorder.provider.PlanOrderCreateOrderProvider;
import com.cosfo.mall.client.planorder.req.PlanOrderCreateOrderReq;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.model.po.MerchantContact;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.merchant.service.MerchantContactService;
import com.cosfo.mall.order.model.dto.OrderItemCheckDTO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.model.dto.PlaceOrderDTO;
import com.cosfo.mall.order.model.vo.OrderVO;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.planorder.model.dto.FailPlanOrderItemDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemCheckDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemDTO;
import com.cosfo.mall.planorder.service.PlanOrderService;
import com.cosfo.manage.client.enums.AgentOrderEnum;
import com.cosfo.manage.client.planorder.PlanOrderCommandProvider;
import com.cosfo.manage.client.planorder.PlanOrderQueryProvider;
import com.cosfo.manage.client.planorder.req.CreateOrderFailReq;
import com.cosfo.manage.client.planorder.req.CreateOrderSuccessReq;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.OrderTypeEnum;
import com.cosfo.ordercenter.client.common.PayTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.exception.I18nBizException;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 计划单生成订单
 *
 * @author: xiaowk
 * @time: 2024/2/20 下午4:59
 */
@Slf4j
@DubboService
public class PlanOrderCreateOrderProviderImpl implements PlanOrderCreateOrderProvider {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OrderService orderService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private MerchantContactService merchantContactService;
    @Resource
    private PlanOrderService planOrderService;
    @DubboReference
    private PlanOrderCommandProvider planOrderCommandProvider;
    @DubboReference
    private PlanOrderQueryProvider planOrderQueryProvider;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;

    @Override
    public DubboResponse<Boolean> createOrder(@Valid PlanOrderCreateOrderReq req) {
        long startTime = System.currentTimeMillis();

        // 失败结果
        List<FailPlanOrderItemDTO> failPlanOrderItemList = new ArrayList<>();

        String planOrderNo = req.getPlanOrderNo();

        List<String> keys = Lists.newArrayList();

        Long tenantId = req.getTenantId();

        try {
            // planOrderNo加锁
            boolean lockFlag = lockOrder(keys, Collections.singletonList(planOrderNo));
            if (!lockFlag) {
                log.error("planOrderNo={} 正在下单，稍后重试", planOrderNo);
                return DubboResponse.getOK(false);
            }

            // 组装登录用户信息
            LoginContextInfoDTO loginContextInfoDTO = buildLoginContextInfoDTO(req);

            PlanOrderDetailResp planOrderDetailResp = RpcResultUtil.handle(planOrderQueryProvider.queryByPlanOrderNo(planOrderNo));
            if (planOrderDetailResp == null || CollectionUtils.isEmpty(planOrderDetailResp.getPlanOrderItems())) {
                throw new BizException("计划单不存在");
            }

            // 非下单中状态，已处理过，不执行
            if (!AgentOrderEnum.Status.SYSTEM_CREATING_ORDER.getValue().equals(planOrderDetailResp.getPlanOrderStatus())) {
                log.warn("计划单已处理，planOrderNo={}", planOrderNo);
                return DubboResponse.getOK(false);
            }

            // 下单商品校验项
            List<PlanOrderItemDTO> planOrderItemDTOS = req.getOrderItemList().stream().map(e -> {
                PlanOrderItemDTO planOrderItemDTO = new PlanOrderItemDTO();
                planOrderItemDTO.setItemId(e.getItemId());
                planOrderItemDTO.setItemAmount(e.getQuantity());
                return planOrderItemDTO;
            }).collect(Collectors.toList());
            PlanOrderItemCheckDTO checkDTO = planOrderService.planOrderConfirmCheck(planOrderItemDTOS, loginContextInfoDTO);

            log.info("计划单创建订单, [计划单下单确认检查], planOrderNo={}, 执行耗时costtime={}ms", planOrderNo, System.currentTimeMillis() - startTime);


            // 校验不通过的保存失败信息
            for (OrderItemCheckDTO orderItemCheckDTO : checkDTO.getFailItemList()) {
                FailPlanOrderItemDTO failPlanOrderItemDTO = new FailPlanOrderItemDTO();
                failPlanOrderItemDTO.setFailType(AgentOrderEnum.CreateOrderFailTypeEnum.ITEM.name());
                failPlanOrderItemDTO.setFailReason(orderItemCheckDTO.getFailMsg());
                failPlanOrderItemDTO.setItemId(orderItemCheckDTO.getItemId());
                failPlanOrderItemList.add(failPlanOrderItemDTO);
            }

            // 全部校验不通过，计划单下单失败
            if (CollectionUtils.isEmpty(checkDTO.getSuccessCheckList())) {
                log.error("计划单商品全部校验不通过，planOrderNo={}", planOrderNo);
                createOrderFail(planOrderNo, failPlanOrderItemList);
                return DubboResponse.getOK(false);
            }

            // 预下单
            ResultDTO<List<OrderVO>> preOrderResultDTO = orderService.preOrder(buildPreOrderDTO(checkDTO.getSuccessCheckList()), loginContextInfoDTO);
            if (!preOrderResultDTO.isSuccess()) {
                throw new BizException(preOrderResultDTO.getMessage());
            }

            log.info("计划单创建订单, [预下单], planOrderNo={}, 执行耗时costtime={}ms", planOrderNo, System.currentTimeMillis() - startTime);


            // 预下单拆单信息
            List<OrderVO> splitOrderVOS = preOrderResultDTO.getData();

            // 下单
            ResultDTO<List<String>> placeOrderResultDTO = orderService.newPlaceOrder(buildPlaceOrderDTOList(splitOrderVOS, planOrderNo, loginContextInfoDTO), loginContextInfoDTO);
            if (!placeOrderResultDTO.isSuccess()) {
                throw new BizException(placeOrderResultDTO.getMessage());
            }

            log.info("计划单创建订单, [下单], planOrderNo={}, 执行耗时costtime={}ms", planOrderNo, System.currentTimeMillis() - startTime);

            List<String> orderNoList = placeOrderResultDTO.getData();
            // 轮训查看订单状态
            List<OrderResp> orderDTOList = pollOrderStatus(orderNoList);

            log.info("计划单创建订单, [轮训下单状态], planOrderNo={}, 执行耗时costtime={}ms", planOrderNo, System.currentTimeMillis() - startTime);

            List<FailPlanOrderItemDTO> orderFailItemList = orderDTOList.stream().filter(e -> OrderStatusEnum.CANCELED.getCode().equals(e.getStatus())).map(e -> {
                FailPlanOrderItemDTO failPlanOrderItem = new FailPlanOrderItemDTO();
                failPlanOrderItem.setFailType(AgentOrderEnum.CreateOrderFailTypeEnum.ORDER.name());
                failPlanOrderItem.setFailReason("下单失败，订单已取消");
                failPlanOrderItem.setOrderNo(e.getOrderNo());
                return failPlanOrderItem;
            }).collect(Collectors.toList());

            failPlanOrderItemList.addAll(orderFailItemList);

            // 占用库存成功订单
            List<String> lockOrderNos = orderDTOList.stream().filter(e -> OrderStatusEnum.NO_PAYMENT.getCode().equals(e.getStatus())).map(OrderResp::getOrderNo).collect(Collectors.toList());
            List<Long> lockOrderIds = orderDTOList.stream().filter(e -> OrderStatusEnum.NO_PAYMENT.getCode().equals(e.getStatus())).map(OrderResp::getId).collect(Collectors.toList());
            Map<String, Long> lockOrderNo2IdMap = orderDTOList.stream().filter(e -> OrderStatusEnum.NO_PAYMENT.getCode().equals(e.getStatus())).collect(Collectors.toMap(OrderResp::getOrderNo, OrderResp::getId, (v1, v2) -> v1));

            // 没有待支付的订单，下单失败
            if (CollectionUtils.isEmpty(lockOrderNos)) {
                log.error("计划单成功下单订单为空，planOrderNo={}", planOrderNo);
                createOrderFail(planOrderNo, failPlanOrderItemList);
                return DubboResponse.getOK(false);
            }

            // 订单支付是否成功map
            Map<String, Boolean> paySuccessMap = orderPay(lockOrderNos, planOrderNo);

            log.info("计划单创建订单, [账单支付], planOrderNo={}, 执行耗时costtime={}ms", planOrderNo, System.currentTimeMillis() - startTime);

            List<String> successOrderNos = Lists.newArrayList();
            for (Map.Entry<String, Boolean> entry : paySuccessMap.entrySet()) {
                boolean payFlag = entry.getValue();
                String orderNo = entry.getKey();
                if(payFlag){
                    // 支付成功，记录订单
                    successOrderNos.add(orderNo);
                }else{
                    // 支付失败，取消订单
                    try {
                        orderService.cancel(lockOrderNo2IdMap.get(orderNo), null, OrderCancelTypeEnum.MANUALLY_CANCEL.getType());
                        FailPlanOrderItemDTO failPlanOrderItem = new FailPlanOrderItemDTO();
                        failPlanOrderItem.setFailType(AgentOrderEnum.CreateOrderFailTypeEnum.ORDER.name());
                        failPlanOrderItem.setFailReason("账期支付失败，订单已取消");
                        failPlanOrderItem.setOrderNo(orderNo);
                        failPlanOrderItemList.add(failPlanOrderItem);
                    } catch (Exception e) {
                        log.error("取消支付失败订单异常， orderNo={}", orderNo, e);
                    }
                }
            }

            // 全部失败，计划单下单失败
            if(CollectionUtils.isEmpty(successOrderNos)){
                createOrderFail(planOrderNo, failPlanOrderItemList);
                return DubboResponse.getOK(false);
            }

            // 成功订单
            createOrderSuccess(planOrderNo, failPlanOrderItemList, successOrderNos);

        } catch (RpcException e) {
            // RPC网络异常排除
            log.error("创建订单RPC异常，planOrderNo={}", planOrderNo, e);
            return DubboResponse.getOK(false);
        } catch (ParamsException | BizException e) {
            log.error("创建订单异常，planOrderNo={}", planOrderNo, e);
            createOrderException(planOrderNo, e.getMessage(), tenantId);
            return DubboResponse.getOK(false);
        } catch (Exception e) {
            log.error("创建订单系统异常，planOrderNo={}", planOrderNo, e);
            createOrderException(planOrderNo, "系统异常", tenantId);
            return DubboResponse.getOK(false);
        } finally {
            unlockOrder(keys);
        }

        log.info("计划单创建订单, [完成], planOrderNo={}, 执行耗时costtime={}ms", planOrderNo, System.currentTimeMillis() - startTime);

        return DubboResponse.getOK(true);
    }


    private Map<String, Boolean> orderPay(List<String> lockOrderNos, String planOrderNo) {
        if (CollectionUtils.isEmpty(lockOrderNos)) {
            return Collections.emptyMap();
        }

        Map<String, Boolean> paySuccessMap = new HashMap<>(lockOrderNos.size());
        for (String orderNo : lockOrderNos) {
            try {
                // 调用 账期支付接口，更新订单为【10-待出库】
                PaymentRequest paymentRequest = new PaymentRequest();
                paymentRequest.setOrderNos(Collections.singletonList(orderNo));
                paymentRequest.setPayType(PayTypeEnum.BILL.getCode());
                paymentRequest.setH5Request(false);
                PaymentResult result = paymentService.pay(paymentRequest);

                boolean paySuccessFlag = result.isSuccess();
                paySuccessMap.put(orderNo, paySuccessFlag);
            } catch (Exception e) {
                log.error("计划单下单账期支付失败，planOrderNo={}, orderNo={}", planOrderNo, orderNo, e);
                paySuccessMap.put(orderNo, false);
            }
        }
        return paySuccessMap;
    }

    private void createOrderSuccess(String planOrderNo, List<FailPlanOrderItemDTO> failPlanOrderItemList, List<String> orderNoList) {
        try {
            CreateOrderSuccessReq createOrderSuccessReq = new CreateOrderSuccessReq();
            createOrderSuccessReq.setPlanOrderNo(planOrderNo);
            createOrderSuccessReq.setFailReason(JSON.toJSONString(failPlanOrderItemList));
            createOrderSuccessReq.setOrderNoList(orderNoList);

            RpcResponseUtil.handler(planOrderCommandProvider.updateCreateOrderSuccess(createOrderSuccessReq));
        } catch (Exception e) {
            log.error("更新计划单下单成功异常，planOrderNo={}", planOrderNo, e);
        }
    }

    private void createOrderException(String planOrderNo, String errorMsg, Long tenantId) {
        try {
            List<FailPlanOrderItemDTO> failPlanOrderItemList = new ArrayList<>();
            FailPlanOrderItemDTO failPlanOrderItemDTO = new FailPlanOrderItemDTO();
            failPlanOrderItemDTO.setFailType(AgentOrderEnum.CreateOrderFailTypeEnum.SYSTEM.name());
            failPlanOrderItemDTO.setFailReason(errorMsg);
            failPlanOrderItemList.add(failPlanOrderItemDTO);

            cancelOrderForSystemException(planOrderNo, tenantId, failPlanOrderItemList);

            CreateOrderFailReq createOrderFailReq = new CreateOrderFailReq();
            createOrderFailReq.setPlanOrderNo(planOrderNo);
            createOrderFailReq.setFailReason(JSON.toJSONString(failPlanOrderItemList));

            RpcResponseUtil.handler(planOrderCommandProvider.updateCreateOrderFail(createOrderFailReq));
        } catch (Exception e) {
            log.error("更新计划单下单失败异常，planOrderNo={}", planOrderNo, e);
        }
    }

    /**
     * 计划单创建订单发生系统异常时，检查是否有订单，取消订单
     * @param planOrderNo
     * @param tenantId
     */
    private void cancelOrderForSystemException(String planOrderNo, Long tenantId, List<FailPlanOrderItemDTO> failPlanOrderItemList){
        try {
            OrderQueryReq queryReq = new OrderQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setPlanOrderNo(planOrderNo);
            List<OrderResp> orderDTOS = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
            if(CollectionUtils.isEmpty(orderDTOS)){
                return;
            }

            for (OrderResp orderDTO : orderDTOS) {
                // 系统异常，取消订单
                try {
                    orderService.cancel(orderDTO.getId(), null, OrderCancelTypeEnum.MANUALLY_CANCEL.getType());
                } catch (Exception e) {
                    log.error("系统异常取消订单异常， orderNo={}", orderDTO.getOrderNo(), e);
                }
                FailPlanOrderItemDTO failPlanOrderItem = new FailPlanOrderItemDTO();
                failPlanOrderItem.setFailType(AgentOrderEnum.CreateOrderFailTypeEnum.ORDER.name());
                failPlanOrderItem.setFailReason("系统异常，订单已取消");
                failPlanOrderItem.setOrderNo(orderDTO.getOrderNo());
                failPlanOrderItemList.add(failPlanOrderItem);
            }
        } catch (Exception e) {
            log.error("计划单下单系统异常取消订单异常，planOrderNo={}", planOrderNo, e);
        }
    }

    private void createOrderFail(String planOrderNo, List<FailPlanOrderItemDTO> failPlanOrderItemList) {
        try {
            CreateOrderFailReq createOrderFailReq = new CreateOrderFailReq();
            createOrderFailReq.setPlanOrderNo(planOrderNo);
            createOrderFailReq.setFailReason(JSON.toJSONString(failPlanOrderItemList));

            RpcResponseUtil.handler(planOrderCommandProvider.updateCreateOrderFail(createOrderFailReq));
        } catch (Exception e) {
            log.error("更新计划单下单失败异常，planOrderNo={}", planOrderNo, e);
        }
    }


    private List<OrderResp> pollOrderStatus(List<String> orderNoList) {
        long startTime = System.currentTimeMillis();
        // 有[下单中]状态的订单
        boolean hasCreateOrderFlag = true;
        List<OrderResp> orderList = Collections.emptyList();

        while (hasCreateOrderFlag && (System.currentTimeMillis() - startTime) <= 30000L) {
            orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNoList));
            if (CollectionUtils.isEmpty(orderList)) {
                throw new BizException("下单订单信息为空");
            }

            hasCreateOrderFlag = orderList.stream().anyMatch(e -> OrderStatusEnum.CREATING_ORDER.getCode().equals(e.getStatus()));
            // 有[下单中]状态的订单, 休眠等待50ms
            if (hasCreateOrderFlag) {
                try {
                    Thread.sleep(50L);
                } catch (InterruptedException e) {
                    log.error("代下单轮训订单状态休眠异常 orderNoList={}", orderNoList, e);
                }
            }
        }

        return orderList;
    }

    private LoginContextInfoDTO buildLoginContextInfoDTO(PlanOrderCreateOrderReq req) {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(req.getTenantId());
        loginContextInfoDTO.setStoreId(req.getStoreId());

        MerchantStoreAccountResultResp merchantStoreAccountResultResp = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountByStoreId(req.getTenantId(), req.getStoreId());
        if (merchantStoreAccountResultResp == null) {
            throw new I18nBizException("门店账号不存在, storeId={0}", req.getStoreId());
        }
        // 门店账号
        loginContextInfoDTO.setAccountId(merchantStoreAccountResultResp.getId());

        // 设置用户信息缓存，支付使用
        ThreadTokenHolder.setToken(loginContextInfoDTO);
        return loginContextInfoDTO;
    }

    private PlaceOrderDTO buildPreOrderDTO(List<OrderItemCheckDTO> successCheckList) {
        PlaceOrderDTO preOrderDTO = new PlaceOrderDTO();
        List<OrderItemDTO> orderItemDTOS = successCheckList.stream().map(e -> {
            OrderItemDTO orderItemDTO = new OrderItemDTO();
            orderItemDTO.setItemId(e.getItemId());
            orderItemDTO.setAmount(e.getItemQuantity());
            return orderItemDTO;
        }).collect(Collectors.toList());
        preOrderDTO.setOrderItemDTOS(orderItemDTOS);
        return preOrderDTO;
    }

    private List<PlaceOrderDTO> buildPlaceOrderDTOList(List<OrderVO> orderVOS, String planOrderNo, LoginContextInfoDTO loginContextInfoDTO) {
        if (CollectionUtils.isEmpty(orderVOS)) {
            return null;
        }

        Long tenantId = loginContextInfoDTO.getTenantId();
        Long storeId = loginContextInfoDTO.getStoreId();
        MerchantAddress merchantAddress = merchantAddressService.selectByStoreId(storeId, tenantId);
        if (Objects.isNull(merchantAddress)) {
            throw new BizException("门店地址不存在, storeId=" + storeId);
        }

        List<MerchantContact> merchantContacts = merchantContactService.queryMerchantContact(loginContextInfoDTO.getTenantId(), merchantAddress.getId());
        if (CollectionUtils.isEmpty(merchantContacts)) {
            throw new BizException("门店联系人不存在, storeId=" + storeId);
        }

        MerchantContact merchantContact = merchantContacts.stream()
                .filter(e -> e.getDefaultFlag() == 1)
                .findFirst()
                .orElse(merchantContacts.get(0));

        List<PlaceOrderDTO> placeOrderDTOList = new ArrayList<>(orderVOS.size());

        for (OrderVO orderVO : orderVOS) {
            PlaceOrderDTO placeOrderDTO = new PlaceOrderDTO();
            List<OrderItemDTO> orderItemDTOS = orderVO.getOrderItemVOS().stream().map(e -> {
                OrderItemDTO orderItemDTO = new OrderItemDTO();
                orderItemDTO.setItemId(e.getItemId());
                orderItemDTO.setAmount(e.getAmount());
                return orderItemDTO;
            }).collect(Collectors.toList());
            placeOrderDTO.setOrderItemDTOS(orderItemDTOS);
            placeOrderDTO.setMerchantAddressId(merchantAddress.getId());
            placeOrderDTO.setMerchantContactId(merchantContact.getId());
            placeOrderDTO.setWarehouseNo(orderVO.getWarehouseNo());
            placeOrderDTO.setWarehouseType(orderVO.getWarehouseType());
            placeOrderDTO.setPayType(PayTypeEnum.BILL.getCode());
            placeOrderDTO.setOrderType(Optional.ofNullable(orderVO.getOrderType()).orElse(OrderTypeEnum.ORDINARY_ORDER.getValue()));
            placeOrderDTO.setPlanOrderNo(planOrderNo);
            placeOrderDTO.setAutoPlanOrderCreateOrder(true);

            placeOrderDTOList.add(placeOrderDTO);
        }

        return placeOrderDTOList;
    }

    /**
     * 计划单号加锁
     *
     * @param keys
     * @param lockObjectList
     */
    private boolean lockOrder(List<String> keys, List<String> lockObjectList) {
        try {
            for (String lockObject : lockObjectList) {
                String redisKey = RedisKeyEnum.C00016.join(lockObject);
                RLock lock = redissonClient.getLock(redisKey);
                // 未获取到锁，退出
                if (!lock.tryLock()) {
                    return false;
                }
                keys.add(redisKey);
            }
        } catch (Exception e) {
            log.error("计划单号加锁异常 lockObjectList={}", lockObjectList, e);
            return false;
        }
        return true;
    }

    /**
     * 外部订单号解锁
     *
     * @param keys
     */
    private void unlockOrder(List<String> keys) {
        for (String key : keys) {
            RLock lock = redissonClient.getLock(key);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

}
