package com.cosfo.mall.planorder.provider.impl;

import com.cosfo.mall.client.planorder.provider.PlanOrderCheckProvider;
import com.cosfo.mall.client.planorder.req.PlanOrderCheckReq;
import com.cosfo.mall.client.planorder.resp.PlanOrderCheckResp;
import com.cosfo.mall.client.planorder.resp.PlanOrderCheckResp.ItemCheckResult;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemCheckDTO;
import com.cosfo.mall.planorder.model.dto.PlanOrderItemDTO;
import com.cosfo.mall.planorder.service.PlanOrderService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2024/10/17 下午3:45
 */
@Slf4j
@DubboService
public class PlanOrderCheckProviderImpl implements PlanOrderCheckProvider {
    @Resource
    private PlanOrderService planOrderService;


    @Override
    public DubboResponse<List<PlanOrderCheckResp>> checkPlanOrder(@Valid PlanOrderCheckReq planOrderCheckReq) {

        List<PlanOrderItemDTO> planOrderItemDTOS = planOrderCheckReq.getAgentOrderItemList().stream().map(e -> {
            PlanOrderItemDTO planOrderItemDTO = new PlanOrderItemDTO();
            planOrderItemDTO.setItemId(e.getItemId());
            planOrderItemDTO.setItemAmount(e.getItemAmount());
            return planOrderItemDTO;
        }).collect(Collectors.toList());

        List<PlanOrderCheckResp> resultList = new ArrayList<>();

        for (Long storeId : planOrderCheckReq.getStoreIdList()) {
            LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
            loginContextInfoDTO.setTenantId(planOrderCheckReq.getTenantId());
            loginContextInfoDTO.setStoreId(storeId);
            PlanOrderItemCheckDTO checkDTO = planOrderService.planOrderConfirmCheck(planOrderItemDTOS, loginContextInfoDTO);
            if (checkDTO != null && !CollectionUtils.isEmpty(checkDTO.getFailItemList())) {

                List<PlanOrderCheckResp.ItemCheckResult> itemCheckResultList = checkDTO.getFailItemList().stream().map(e -> {
                    ItemCheckResult itemCheckResult = new ItemCheckResult();
                    itemCheckResult.setItemId(e.getItemId());
                    itemCheckResult.setItemQuantity(e.getItemQuantity());
                    itemCheckResult.setCheckFlag(e.isCheckFlag());
                    itemCheckResult.setFailMsg(e.getFailMsg());
                    return itemCheckResult;
                }).collect(Collectors.toList());

                PlanOrderCheckResp planOrderCheckResp = new PlanOrderCheckResp();
                planOrderCheckResp.setStoreId(storeId);
                planOrderCheckResp.setItemCheckResultList(itemCheckResultList);

                resultList.add(planOrderCheckResp);
            }

        }

        return DubboResponse.getOK(resultList);
    }
}
