package com.cosfo.mall.cache;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.lang.reflect.Parameter;
import java.time.Duration;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicLong;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class LocalCacheProcessor implements InitializingBean {

    private static final AtomicLong COUNTER = new AtomicLong(0L);

    private static final Cache<String, Object> LOADING_CACHE = CacheBuilder.newBuilder()
      .maximumSize(1000)
      .recordStats()
      .expireAfterWrite(Duration.ofHours(4L))//240 minutes
      .build();

    @Around("@annotation(com.cosfo.mall.cache.LocalCache)")
    public Object getFromLocalCacheFirst(ProceedingJoinPoint joinPoint) throws Exception {
        if (COUNTER.getAndIncrement() % 200 == 0) {
            logCacheStats();
        }
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        return LOADING_CACHE.get(buildCacheKey(methodSignature), new Callable<Object>() {
            @SneakyThrows
            @Override
            public Object call() throws Exception {
                return joinPoint.proceed();
            }
        });
    }

    private static String buildCacheKey(MethodSignature methodSignature) {
        Parameter[] parameters = methodSignature.getMethod().getParameters();
        if (null == parameters || parameters.length == 0) {
            throw new RuntimeException("Unexpected methodSignature:" + methodSignature);
        }
        String typeName = methodSignature.getDeclaringTypeName(), methodName = methodSignature.getMethod().getName();
        return String.format("%s_%s_%s", typeName, methodName, JSON.toJSONString(parameters));
    }

    private void logCacheStats() {
        log.info("cache stats:{}", LOADING_CACHE.stats());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        logCacheStats();
    }
}
