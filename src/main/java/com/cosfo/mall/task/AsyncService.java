//package com.cosfo.mall.task;
//
//import com.cosfo.aftersale.model.dto.OrderAfterSaleDTO;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/3 17:42
// */
//public interface AsyncService {
//
//
//    /**
//     * 异步钉钉通知运营处理异常的售后单
//     * @param orderAfterSale
//     */
//    void sendNotifyExceptionMessage(OrderAfterSale orderAfterSale);
//
//    /**
//     * 异步钉钉通知运营处理派送时缺货自动生成的售后单
//     * @param orderAfterSale
//     */
//    void sendAutoCreateAfterSaleMessage(OrderAfterSale orderAfterSale);
//}
