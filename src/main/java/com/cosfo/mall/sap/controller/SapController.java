package com.cosfo.mall.sap.controller;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.facade.sap.SapApiFacade;
import com.cosfo.mall.sap.model.dto.SapOrderNotifyDTO;
import com.cosfo.mall.sap.service.SapOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/sap")
public class SapController {

    @Resource
    private SapApiFacade sapApiFacade;

    @Resource
    private SapOrderService sapOrderService;

    @RequestMapping(value = "/queryAtp", method = RequestMethod.POST)
    public ResultDTO queryAtp(@RequestParam("productId") String productId) {
        return ResultDTO.success(sapApiFacade.queryAtp(productId));
    }


    @RequestMapping(value = "/order/notify", method = RequestMethod.POST)
    public ResultDTO orderNotify(@RequestBody String body) {
        log.info("接收sap订单状态回告: {}", body);


        SapOrderNotifyDTO sapOrderNotifyDTO = JSON.parseObject(body, SapOrderNotifyDTO.class);

        sapOrderService.orderNotify(sapOrderNotifyDTO);

        return ResultDTO.success();
    }
}
