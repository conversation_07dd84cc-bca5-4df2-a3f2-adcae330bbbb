package com.cosfo.mall.sap.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *  伍尔特订单回告信息
 *
 * @author: xiaowk
 * @date: 2025/5/23 下午2:23
 */
@Data
public class SapOrderNotifyDTO {

    @JSONField(name = "orderList")
    private OrderNotifyPO orderNotifyPO;

    @Data
    public static class OrderNotifyPO {

        /**
         * 鲜沐订单号
         */
        private String orderNo;

        /**
         * sap订单号
         */
        @JSONField(name = "orderNo_SAP")
        private String sapOrderNo;

        /**
         * sap订单类型
         */
        @JSONField(name = "order_type")
        private String orderType;

        /**
         * 售达方
         */
        private String soldTo;

        /**
         * 送达方
         */
        private String shipTo;

        /**
         * 订单日期 2025-04-16传20250416
         */
        private String orderDate;

        /**
         * 订单时间 09:59:59传095959
         */
        private String orderTime;

        /**
         * 税额 2位小数
         */
        private BigDecimal taxAmount;

        /**
         * 备注
         */
        private String remark;

        /**
         * 订单项商品信息
         * 包含运费项（指定物料号，如果免运费，则不需要传）
         */
        private List<OrderItemNotifyPO> itemList;

    }

    @Data
    public static class OrderItemNotifyPO {
        /**
         * 行项目号
         */
        private Integer itemNo;

        /**
         * 物料号（18位）
         */
        private String material;

        /**
         * 数量（运费项固定为1）
         */
        private Integer quantity;

        /**
         * 单价（未税）2位小数
         */
        private BigDecimal netPrice;

        /**
         * 价格单位（运费项固定为1）（本次对接固定为1）
         */
        private Integer unit;

        /**
         * 总价（未税）2位小数
         */
        private BigDecimal netValue;

        /**
         * 发票编号
         */
        private String billing;

        /**
         * 发票号码
         */
        @JSONField(name = "FI_document")
        private String fIDocument;

        private String delivery;
        /**
         * 物流单号
         */
        @JSONField(name = "Carrier")
        private String carrier;

        /**
         * 物流链接
         */
        @JSONField(name = "URL")
        private String deliveryUrl;
    }

}
