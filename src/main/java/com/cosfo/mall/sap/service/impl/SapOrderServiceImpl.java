package com.cosfo.mall.sap.service.impl;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.arms.sdk.v1.async.TraceExecutors;
import com.alibaba.fastjson.JSON;
import com.cosfo.mall.facade.sap.SapApiFacade;
import com.cosfo.mall.order.dao.OrderDeliveryInfoDao;
import com.cosfo.mall.order.model.po.OrderDeliveryInfo;
import com.cosfo.mall.sap.model.dto.OrderItemUpdateNotifyDTO;
import com.cosfo.mall.sap.model.dto.SapOrderNotifyDTO;
import com.cosfo.mall.sap.service.SapOrderService;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemUpdateNotifyReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.SapOrderNotifyReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2025/5/23 下午3:12
 */
@Slf4j
@Service
public class SapOrderServiceImpl implements SapOrderService {

    /**
     * 查询库存线程池
     */
    private static ExecutorService SAP_QUERY_STOCK_SERVICE0 = new ThreadPoolExecutor(10, 10,
            60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new NamedThreadFactory("SAP_QUERY_STOCK_SERVICE", false),
            new ThreadPoolExecutor.AbortPolicy());

    // 日志打印traceId
    private static ExecutorService SAP_QUERY_STOCK_SERVICE = TraceExecutors.wrapExecutorService(SAP_QUERY_STOCK_SERVICE0, true);


    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @Resource
    private OrderDeliveryInfoDao orderDeliveryInfoDao;
    @Resource
    private SapApiFacade sapApiFacade;

    @Override
    public void orderNotify(SapOrderNotifyDTO sapOrderNotifyDTO) {
        if (sapOrderNotifyDTO == null || sapOrderNotifyDTO.getOrderNotifyPO() == null) {
            return;
        }

        SapOrderNotifyDTO.OrderNotifyPO orderNotifyPO = sapOrderNotifyDTO.getOrderNotifyPO();

        Long orderId = null;
        String orderNo = null;
        // 需要更新的sapOrderNo
        String sapOrderNo = null;
        if (StringUtils.isNotBlank(orderNotifyPO.getOrderNo())
                && StringUtils.isNotBlank(orderNotifyPO.getSapOrderNo())) {

            OrderResp orderResp = RpcResultUtil.handle(orderQueryProvider.queryByNo(orderNotifyPO.getOrderNo()));
            if (orderResp == null) {
                log.error("sap订单回告信息，orderNo={} 订单不存在", orderNotifyPO.getOrderNo());
                return;
            }

            orderId = orderResp.getId();
            orderNo = orderResp.getOrderNo();

            if (StringUtils.isNotBlank(orderResp.getSapOrderNo())) {
                log.warn("sap订单回告信息，orderNo={} 订单已存在sapOrderNo={}", orderNotifyPO.getOrderNo(), orderResp.getSapOrderNo());
            } else {
                sapOrderNo = orderNotifyPO.getSapOrderNo();
            }

        } else if (StringUtils.isNotBlank(orderNotifyPO.getSapOrderNo())) {
            OrderQueryReq req = new OrderQueryReq();
            req.setSapOrderNo(orderNotifyPO.getSapOrderNo());
            List<OrderResp> orderResps = RpcResultUtil.handle(orderQueryProvider.queryOrderList(req));

            if (CollectionUtils.isEmpty(orderResps) || orderResps.size() > 1) {
                log.error("sap订单回告信息，sapOrderNo={} 订单不存在或有多条数据。orderResps={}", orderNotifyPO.getSapOrderNo(), JSON.toJSONString(orderResps));
                return;
            }

            orderId = orderResps.get(0).getId();
            orderNo = orderResps.get(0).getOrderNo();
        } else {
            log.error("sap订单回告信息，orderNo={}, sapOrderNo={}, 没有标识订单号", orderNotifyPO.getOrderNo(), orderNotifyPO.getSapOrderNo());
            return;
        }

        SapOrderNotifyReq sapOrderNotifyReq = new SapOrderNotifyReq();
        sapOrderNotifyReq.setOrderId(orderId);
        sapOrderNotifyReq.setSapOrderNo(sapOrderNo);
        // 状态  1-待确认 2 待确认-推送中 3-待配送 4-配送中 5-已完成 6-已取消
        sapOrderNotifyReq.setSourceStatusList(Lists.newArrayList(OrderStatusEnum.NO_PAYMENT.getCode()));
        sapOrderNotifyReq.setTargetStatus(OrderStatusEnum.WAIT_DELIVERY.getCode());


        List<SapOrderNotifyDTO.OrderItemNotifyPO> itemList = orderNotifyPO.getItemList();

        if (!CollectionUtils.isEmpty(itemList)) {
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
            Map<String, OrderItemAndSnapshotResp> sapSkucode2OrderItemMap = orderItemAndSnapshotRespList.stream().filter(e -> StringUtils.isNotBlank(e.getSapSkuCode())).collect(Collectors.toMap(OrderItemAndSnapshotResp::getSapSkuCode, Function.identity(), (v1, v2) -> v1));

            List<OrderItemUpdateNotifyDTO> orderItemUpdateNotifyDTOS = itemList.stream().map(e -> {
                OrderItemAndSnapshotResp orderItemAndSnapshotResp = sapSkucode2OrderItemMap.get(e.getMaterial());
                if (orderItemAndSnapshotResp == null) {
                    return null;
                }

                OrderItemUpdateNotifyDTO orderItemUpdateNotifyDTO = new OrderItemUpdateNotifyDTO();
                orderItemUpdateNotifyDTO.setOrderItemId(orderItemAndSnapshotResp.getOrderItemId());
                orderItemUpdateNotifyDTO.setItemId(orderItemAndSnapshotResp.getItemId());
                orderItemUpdateNotifyDTO.setQuantity(orderItemAndSnapshotResp.getAmount());
                orderItemUpdateNotifyDTO.setBilling(e.getBilling());
                orderItemUpdateNotifyDTO.setFIDocument(e.getFIDocument());
                orderItemUpdateNotifyDTO.setCarrier(e.getCarrier());
                orderItemUpdateNotifyDTO.setDeliveryUrl(e.getDeliveryUrl());
                return orderItemUpdateNotifyDTO;
            }).filter(Objects::nonNull).collect(Collectors.toList());


            // 组装发票更新请求
            List<OrderItemUpdateNotifyReq> orderItemUpdateNotifyReqList = orderItemUpdateNotifyDTOS.stream().map(e -> {
                if (StringUtils.isBlank(e.getBilling()) && StringUtils.isBlank(e.getFIDocument())) {
                    return null;
                }

                OrderItemUpdateNotifyReq req = new OrderItemUpdateNotifyReq();
                req.setId(e.getOrderItemId());
                req.setSapBilling(e.getBilling());
                req.setSapFiDocument(e.getFIDocument());
                return req;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            sapOrderNotifyReq.setOrderItemUpdateNotifyReqList(orderItemUpdateNotifyReqList);

            // 查询是否有物流更新
            String finalOrderNo = orderNo;
            List<OrderDeliveryInfo> orderDeliveryInfoList = orderItemUpdateNotifyDTOS.stream().map(e -> {
                if (StringUtils.isBlank(e.getCarrier()) && StringUtils.isBlank(e.getDeliveryUrl())) {
                    return null;
                }

                OrderDeliveryInfo orderDeliveryInfo = new OrderDeliveryInfo();
                orderDeliveryInfo.setOrderNo(finalOrderNo);
                orderDeliveryInfo.setItemId(e.getItemId());
                orderDeliveryInfo.setQuantity(e.getQuantity());
//                orderDeliveryInfo.setLogisticsCompany();
                orderDeliveryInfo.setLogisticsNo(e.getCarrier());
                orderDeliveryInfo.setLogisticsUrl(e.getDeliveryUrl());
                orderDeliveryInfo.setDeliveryType(1);

                return orderDeliveryInfo;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(orderDeliveryInfoList)) {
                sapOrderNotifyReq.setSourceStatusList(Lists.newArrayList(OrderStatusEnum.NO_PAYMENT.getCode(), OrderStatusEnum.WAIT_DELIVERY.getCode()));
                sapOrderNotifyReq.setTargetStatus(OrderStatusEnum.DELIVERING.getCode());
                orderDeliveryInfoDao.batchSave(orderDeliveryInfoList);
            }
        }


        RpcResultUtil.handle(orderCommandProvider.updateOrderForSapOrderNotify(sapOrderNotifyReq));
    }

    @Override
    public Map<String, Integer> queryItemStock(List<String> sapSkuCodeList) {
        Map<String, Integer> resultMap = new HashMap<>();

        List<Future<Pair<String, BigDecimal>>> futures = new ArrayList<>();

        for (String sapSkuCode : sapSkuCodeList) {
            Callable<Pair<String, BigDecimal>> task = () -> {
                BigDecimal stock = sapApiFacade.queryAtp(sapSkuCode);
                return Pair.of(sapSkuCode, stock);
            };

            futures.add(SAP_QUERY_STOCK_SERVICE.submit(task));
        }

        for (Future<Pair<String, BigDecimal>> future : futures) {
            try {
                Pair<String, BigDecimal> pair = future.get();
                resultMap.put(pair.getKey(), pair.getValue() == null ? 0 : pair.getValue().intValue());
            } catch (Exception e) {
                log.error("线程池查询库存异常，", e);
            }
        }

        for (String sapSkuCode : sapSkuCodeList) {
            // 库存查询异常，默认返回0
            if(!resultMap.containsKey(sapSkuCode)){
                resultMap.put(sapSkuCode, 0);
            }
        }

        return resultMap;
    }


}
