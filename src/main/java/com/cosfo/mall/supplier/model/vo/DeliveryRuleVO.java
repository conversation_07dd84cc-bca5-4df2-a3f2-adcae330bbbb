package com.cosfo.mall.supplier.model.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class DeliveryRuleVO implements Serializable{

    private BigDecimal fruitPrice; //非乳制品总价

    private BigDecimal dairyPrice; //乳制品总价

    private BigDecimal totalPrice; //订单总价

    @Deprecated
    private List<Integer> categoryList; // 免邮类目列表

    /** 固定星期免配送费 1-7 用","分隔*/
    private String freeDeliveryWeek;

    public String getFreeDeliveryWeek() {
        return this.freeDeliveryWeek;
    }

    public void setFreeDeliveryWeek(String freeDeliveryWeek) {
        this.freeDeliveryWeek = freeDeliveryWeek;
    }

    public List<Integer> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<Integer> categoryList) {
        this.categoryList = categoryList;
    }

    public BigDecimal getFruitPrice() {
        return fruitPrice;
    }

    public void setFruitPrice(BigDecimal fruitPrice) {
        this.fruitPrice = fruitPrice;
    }

    public BigDecimal getDairyPrice() {
        return dairyPrice;
    }

    public void setDairyPrice(BigDecimal dairyPrice) {
        this.dairyPrice = dairyPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
}
