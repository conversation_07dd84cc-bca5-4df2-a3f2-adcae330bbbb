package com.cosfo.mall.supplier;

import com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 13:50
 */
public interface SupplierDeliveryInfoService {

    /**
     * 保存配送信息
     * @param orderNo
     * @param deliveryFee
     * @param deliveryFeeRule
     * @param type 0跟随供应商 1免运费
     */
    void saveSupplierDeliveryInfo(String orderNo, BigDecimal deliveryFee, String deliveryFeeRule, Integer type);

    /**
     * 查询供应商运费
     *
     * @param orderNo
     * @return
     */
    SupplierDeliveryInfo querySupplierDeliveryInfo(String orderNo);

}
