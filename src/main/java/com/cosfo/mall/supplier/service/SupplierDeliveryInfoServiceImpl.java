package com.cosfo.mall.supplier.service;

import com.cosfo.mall.supplier.SupplierDeliveryInfoService;
import com.cosfo.mall.supplier.mapper.SupplierDeliveryInfoMapper;
import com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 13:50
 */
@Slf4j
@Service
public class SupplierDeliveryInfoServiceImpl implements SupplierDeliveryInfoService {

    @Resource
    private SupplierDeliveryInfoMapper supplierDeliveryInfoMapper;

    @Override
    public void saveSupplierDeliveryInfo(String orderNo, BigDecimal deliveryFee, String deliveryFeeRule, Integer type) {
        SupplierDeliveryInfo supplierDeliveryInfo = new SupplierDeliveryInfo();
        supplierDeliveryInfo.setOrderNo(orderNo);
        supplierDeliveryInfo.setSupplierDeliveryFee(deliveryFee);
        supplierDeliveryInfo.setSupplierDeliveryFeeRule(deliveryFeeRule);
        supplierDeliveryInfo.setType(type);
        supplierDeliveryInfoMapper.insert(supplierDeliveryInfo);
    }

    @Override
    public SupplierDeliveryInfo querySupplierDeliveryInfo(String orderNo) {
        return supplierDeliveryInfoMapper.queryByOrderNo(orderNo);
    }

//    @Override
//    public ResultDTO<Boolean> handlerHavingOrder() {
//        List<SupplierDeliveryInfo> supplierDeliveryInfos = supplierDeliveryInfoMapper.listAll();
//        if (CollectionUtils.isEmpty(supplierDeliveryInfos)) {
//            return ResultDTO.success(false);
//        }
//        List<String> orderNoList = supplierDeliveryInfos.stream().map(SupplierDeliveryInfo::getOrderNo).distinct().collect(Collectors.toList());
//        List<com.cosfo.ordercenter.client.resp.OrderDTO> orderDTOList = RpcResultUtil.handle(orderQueryService.queryByNos(orderNoList));
//        if (CollectionUtils.isEmpty(orderDTOList)) {
//            return ResultDTO.success(false);
//        }
//        Map<String, com.cosfo.ordercenter.client.resp.OrderDTO> orderMap = orderDTOList.stream().collect(Collectors.toMap(com.cosfo.ordercenter.client.resp.OrderDTO::getOrderNo, o -> o));
//        List<Long> orderIds = orderDTOList.stream().map(com.cosfo.ordercenter.client.resp.OrderDTO::getId).collect(Collectors.toList());
//
//        List<OrderAddressDTO> orderAddressList = RpcResultUtil.handle(orderAddressQueryService.queryByOrderIds(null, orderIds));
//        Map<Long, OrderAddressDTO> addressMap = orderAddressList.stream().collect(Collectors.toMap(OrderAddressDTO::getOrderId, address -> address));
//
//        OrderItemQueryReq itemQueryReq = new OrderItemQueryReq();
//        itemQueryReq.setOrderIds(orderIds);
//        List<OrderItemAndSnapshotDTO> orderItemList = RpcResultUtil.handle(orderItemQueryService.queryOrderItemList(itemQueryReq));
//        Map<Long, List<OrderItemAndSnapshotDTO>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotDTO::getOrderId));
//        supplierDeliveryInfos.forEach(supplierDeliveryInfo -> {
////            Order order = orderService.selectByOrderNo(supplierDeliveryInfo.getOrderNo());
//            com.cosfo.ordercenter.client.resp.OrderDTO order = orderMap.get(supplierDeliveryInfo.getOrderNo());
//            // 是否有已经有同一个配送日的订单
//            OrderQueryDTO queryDTO = OrderQueryDTO.builder().id(order.getId()).tenantId(order.getTenantId()).storeId(order.getStoreId()).deliveryTime(Objects.isNull(order.getDeliveryTime()) ? null : TimeUtils.convertTOLocalDate(order.getDeliveryTime())).supplierTenantId(order.getSupplierTenantId()).build();
//            List<OrderResp> orderList = orderService.querySameDeliveryOrders(queryDTO);
//            if (!CollectionUtils.isEmpty(orderList)) {
//                return;
//            }
//            // 参数集合
//            List<OrderItemDTO> orderItemDTOS = new ArrayList<>();
//            OrderDTO orderDTO = new OrderDTO();
//            // 查询地址
////            OrderAddressDTO orderAddressDTO = orderAddressMapper.selectByOrderId(order.getId(), order.getTenantId());
//            OrderAddressDTO orderAddressDTO = addressMap.get(order.getId());
////            List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(order.getTenantId(), order.getId());
//            List<OrderItemAndSnapshotDTO> orderItemAndSnapshotDTOS = orderItemMap.get(order.getId());
//            for (OrderItemAndSnapshotDTO orderItem : orderItemAndSnapshotDTOS) {
//                OrderItemDTO itemDTO = new OrderItemDTO();
////                OrderItemSnapshot itemSnapshot = orderItemSnapshotMapper.selectByItemId(order.getTenantId(), orderItem.getOrderItemId());
//                itemDTO.setSupplySkuId(orderItem.getSupplierSkuId());
//                if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(order.getWarehouseType()) && GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(orderItem.getGoodsType())) {
//                    itemDTO.setCalcPartDeliveryFee(orderItem.getTotalPrice());
//                } else if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(order.getWarehouseType()) && GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItem.getGoodsType())) {
//                    itemDTO.setCalcPartDeliveryFee(NumberUtil.mul(orderItem.getSupplyPrice(), orderItem.getAmount()));
//                }
//                orderItemDTOS.add(itemDTO);
//            }
//
//            //订单信息参数
//            orderDTO.setCity(orderAddressDTO.getCity());
//            orderDTO.setArea(orderAddressDTO.getArea());
//            orderDTO.setOrderItemDTOList(orderItemDTOS);
//
//            // 调用供应商接口
//            BigDecimal deliveryFee = BigDecimal.TEN;
//            String deliveryFeeRule = "";
//            try {
//                String params = JSON.toJSONString(orderDTO);
//                String result = HttpUtil.createPost(mallApiHost + DELIVERY_INTERFACE).body(params, CharsetUtil.UTF_8).contentType(Constants.JSON_CONTENT_TYPE).execute().body();
//                SummerfarmResult summerfarmResult = JSONObject.parseObject(result, SummerfarmResult.class);
//                if (summerfarmResult.isSuccess()) {
//                    SummerfarmDeliveryDTO summerfarmDeliveryDTO = JSONObject.parseObject(JSON.toJSONString(summerfarmResult.getData()), SummerfarmDeliveryDTO.class);
//                    deliveryFee = summerfarmDeliveryDTO.getDeliveryFee();
//                    deliveryFeeRule = summerfarmDeliveryDTO.getDeliveryFeeRule();
//                }
//            } catch (Exception e) {
//                log.error("订单编号：{} 获取第三方运费失败", order.getOrderNo(), e);
//            }
//
//            supplierDeliveryInfo.setSupplierDeliveryFee(deliveryFee);
//            supplierDeliveryInfo.setSupplierDeliveryFeeRule(deliveryFeeRule);
//            supplierDeliveryInfoMapper.updateByPrimaryKeySelective(supplierDeliveryInfo);
//        });
//
//        return ResultDTO.success();
//    }
//
//    /**
//     * 是否免邮日
//     *
//     * @param freeDeliveryWeek 免邮日期
//     * @return t、免邮日 f、非免邮日
//     */
//    private boolean isFreeDeliveryDay(String freeDeliveryWeek, LocalDate orderDate) {
//        String[] freeDay = freeDeliveryWeek.split(",");
//        Arrays.sort(freeDay);
//        //是否为免运费日期
//        if ("0".equals(freeDay[0])) {
//            return true;
//        } else {
//            int dayOfWeek = orderDate.getDayOfWeek().getValue();
//            for (String s : freeDay) {
//                int free = Integer.parseInt(s);
//                if (dayOfWeek == free) {
//                    return true;
//                }
//            }
//        }
//
//        return false;
//    }
}
