package com.cosfo.mall.supplier.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SupplierDeliveryInfoMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(SupplierDeliveryInfo record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(SupplierDeliveryInfo record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    SupplierDeliveryInfo selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SupplierDeliveryInfo record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(SupplierDeliveryInfo record);

    /**
     * 根据订单编号查询供应商配送费
     *
     * @param orderNo
     * @return
     */
    SupplierDeliveryInfo queryByOrderNo(String orderNo);

    /**
     * 查询全部订单
     *
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<SupplierDeliveryInfo> listAll();
}
