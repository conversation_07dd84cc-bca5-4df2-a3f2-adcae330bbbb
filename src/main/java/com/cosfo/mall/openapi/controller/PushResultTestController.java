package com.cosfo.mall.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-10-31
 * @Description:模拟第三方推送回调URL
 */
@Slf4j
@RestController
@RequestMapping("/push/result")
public class PushResultTestController {

    @PostMapping(value = "receive")
    public String receive(@RequestBody Map<String, Object> paramMap) {
        log.info("测试推送信息 param:{}", JSON.toJSONString(paramMap));
        JSONObject responseObject = new JSONObject();
        responseObject.put("challenge", paramMap.get("challenge"));
        return responseObject.toJSONString();
    }
}
