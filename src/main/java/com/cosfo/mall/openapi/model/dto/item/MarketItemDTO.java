package com.cosfo.mall.openapi.model.dto.item;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商品基础信息
 *
 * @author: xiaowk
 * @time: 2023/9/19 下午3:03
 */
@Data
public class MarketItemDTO {
    /**
     * marketId
     */
    private Long marketId;
    /**
     * ItemId
     */
    private Long itemId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 供应商skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 分类id
     */
    private Long classificationId;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 售价
     */
    private BigDecimal price;
    /**
     * 是否在售
     */
    private Integer onSale;
    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 购物车数量
     */
    private Integer amount;
    /**
     * 可用库存
     */
    private Integer enableAmount;
    /**
     * 是否有效
     */
    private Boolean validFlag;
    /**
     * 有效期
     */
    private LocalDate expirationTime;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity=0;

    /**
     * 下一个价格配置
     */
//    private ProductAgentSkuFeeCountRuleDTO nextStepRule;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;

    /**
     * 货源类型 0无货 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 最大可占用库存
     */
    private Integer maxAmount;

    // --------------------- goodsType 1报价货品 2自营货品 有以下信息 ----------------
    /**
     * 代仓供应商Id
     */
    private Long agentTenantId;
    /**
     * 代仓商品Id
     */
    private Long agentSkuId;
    /**
     * 代仓sku编码
     */
    private String agentSkuCode;
    // ---------------------------

}
