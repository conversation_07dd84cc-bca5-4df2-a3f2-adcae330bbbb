package com.cosfo.mall.openapi.model.dto;

import com.cosfo.mall.common.constants.PlaceOrderItemKeyEnum;
import com.cosfo.mall.openapi.model.dto.item.MarketItemDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-12-22
 * @Description:api下单商品信息
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class PlaceOrderItemDetailDTO {

    /**
     * marketItemDTOMap的key值所属类型
     */
    private PlaceOrderItemKeyEnum itemKeyEnum;

    /**
     * 下单品项map
     * 注意：key值数据是鲜沐sku还是itemCode，请根据placeOrderMarketItemKeyEnum来判断
     */
    private Map<String, MarketItemDTO> marketItemDTOMap;
}
