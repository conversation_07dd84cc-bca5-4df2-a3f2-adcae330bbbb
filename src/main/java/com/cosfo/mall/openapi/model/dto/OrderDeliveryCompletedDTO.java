package com.cosfo.mall.openapi.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-10-30
 * @Description:配送完成响应数据格式
 */
@Data
public class OrderDeliveryCompletedDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部单号（订单号）
     */
    private String customerOrderId;

    /**
     * 三方门店编码
     */
    private String storeNo;

    /**
     * 出库的仓库号
     */
    private String warehouseNo;

    /**
     * 备注信息
     */
    private String message;

    /**
     * 履约明细
     */
    private List<OrderDeliveryCompletedDTO.OrderDeliveryItem> itemList;

    @Data
    public static class OrderDeliveryItem implements Serializable  {

        private static final long serialVersionUID = 1L;

        /**
         * 外部订单子项ID
         */
        private String customerOrderItemId;

        /**
         * 订单计划履约数量
         */
        private Integer quantity;

        /**
         * 实际履约数量
         */
        private Integer actualQuantity;

        /**
         * 缺货数量
         */
        private Integer shortQuantity;

        /**
         * 状态 0:正常 1:异常
         */
        private Integer status;

        /**
         * 鲜沐sku编码
         */
        private String skuCode;

        /**
         * 外部货源skuCode
         */
        private String customerSkuCode;

        /**
         * 外部货源商品规格
         */
        private String customerSkuSpecification;

        /**
         * 外部货源商品规格单位
         */
        private String customerSkuSpecificationUnit;
    }
}
