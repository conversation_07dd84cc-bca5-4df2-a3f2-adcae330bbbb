package com.cosfo.mall.openapi.model.dto;

import com.cosfo.mall.client.openapi.req.PlaceOrderReq;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-19
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ItemRouteDTO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 入参品项code列表
     */
    private List<String> codeList;

    /**
     * 店铺ID
     */
    private Long storeId;

    /**
     * 地址信息
     */
    private MerchantAddressDTO merchantAddressDTO;
}
