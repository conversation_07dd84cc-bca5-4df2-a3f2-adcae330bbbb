package com.cosfo.mall.openapi.model.dto.item;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品价格信息
 *
 * @author: xiaowk
 * @date: 2023/9/20 上午9:51
 */
@Data
public class ItemPriceDTO {

    /**
     * itemId
     */
    private Long itemId;

    /**
     * 基础价/原价（加价之前）
     */
    private BigDecimal basePrice;
    /**
     * 商城价
     */
    private BigDecimal price;
    /**
     * 供应价
     */
    private BigDecimal supplyPrice;
    /**
     * 原销售单价
     */
    private BigDecimal marketItemPrice;

    /**
     * 代仓商城价
     */
    private BigDecimal agentMallPrice;
}
