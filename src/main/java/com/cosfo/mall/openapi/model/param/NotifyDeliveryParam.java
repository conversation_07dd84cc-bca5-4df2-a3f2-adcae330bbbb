package com.cosfo.mall.openapi.model.param;

import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-21
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class NotifyDeliveryParam {

    /**
     * 外部单号
     */
    private String customerOrderId;

    /**
     * 三方门店编码
     */
    private String storeNo;

    /**
     * 订单子项记录
     */
    private List<OrderItemResp> orderItemList;

    /**
     * 订单子项扩展记录
     */
    private List<OrderItemExtraResp> orderItemExtraList;

}
