package com.cosfo.mall.openapi.model.bo;

import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-10-30
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderDeliveryNotifyBO {

    /**
     * 订单信息
     */
    private OrderResp orderDTO;

    /**
     * 履约信息
     */
    private CommonFulfillmentFinishMessage commonFulfillmentFinishMessage;

    /**
     * 外部订单子项扩展
     */
    private List<OrderItemExtraResp> orderItemExtraList;

    /**
     * 门店信息
     */
    private MerchantStoreResultResp merchantStoreResultResp;

    /**
     * 订单商品子项
     */
    private List<OrderItemResp> orderItemList;

}
