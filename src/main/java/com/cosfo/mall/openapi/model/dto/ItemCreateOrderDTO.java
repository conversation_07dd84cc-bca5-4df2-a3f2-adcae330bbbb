package com.cosfo.mall.openapi.model.dto;

import com.cosfo.mall.client.openapi.req.PlaceOrderReq;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.openapi.model.bo.OrderBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-12-19
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ItemCreateOrderDTO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * sku列表
     */
    private List<String> skuCodes;

    /**
     * 店铺ID
     */
    private Long storeId;

    /**
     * 订单信息
     */
    private OrderBO orderBO;

    /**
     * 外部单号
     */
    private String customerOrderId;

    /**
     * 下单商品项
     */
    private List<PlaceOrderReq.PlaceOrderItem> orderItemList;

    /**
     * 下单商品项map
     */
    private Map<String, PlaceOrderReq.PlaceOrderItem> sku2PlaceOrderItemMap;

    /**
     * 地址信息
     */
    private MerchantAddressDTO merchantAddressDTO;

}
