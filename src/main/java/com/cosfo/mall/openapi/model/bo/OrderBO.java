package com.cosfo.mall.openapi.model.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @time: 2023/9/20 上午11:49
 */
@Data
public class OrderBO {
    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 租户编码
     */
    private Long tenantId;
    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 账号Id
     */
    private Long accountId;
    /**
     * 下单账号
     */
    private String accountName;

    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 配送仓类型 0无仓 1三方 2自营
     */
    private Integer warehouseType;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 应付金额
     */
    private BigDecimal payablePrice;
    /**
     * 配送费
     */
    private BigDecimal deliveryFee;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 订单创建时间
     */
    private LocalDateTime orderTime;

    /**
     * 配送时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDateTime deliveryTime;
    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;

    /**
     * 支付方式 1、微信支付 2、账期 3、余额支付 4、支付宝支付
     */
    private Integer payType;
    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 到期时间
     */
    private LocalDateTime expireTime;
    /**
     * 总件数
     */
    private Integer totalAmount;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 存在未关闭售后单标志
     */
    private Boolean saleAfterFlag;
    /**
     * 可申请售后时效
     */
    private Integer applyEndTime;
    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;
    /**
     * 库存仓编号
     */
    private Long warehouseNo;
    /**
     * 订单类型 0-普通订单,1=组合订单,2=预售订单
     */
    private Integer orderType;
    /**
     * 组合品item_id
     */
    private Long combineItemId;
    /**
     * 组合包订单Id
     */
    private Long combineOrderId;

    /**
     * 组合包marketId
     */
    private Long combineMarketId;

    /**
     * 订单记录版本，用来区分旧下单链路，旧链路为空，新链路为1
     */
    private Integer orderVersion;

    /**
     * 订单来源：0：内部系统;1：openapi调用
     */
    private Integer orderSource;

    /**
     * 外部系统订单号
     */
    private String customerOrderId;


    // -------------------------------------------------order外的参数对象

    /**
     * 收获地址信息
     */
    private OrderAddressDTO orderAddressDTO;

    /**
     * 订单项
     */
    private List<OrderItemBO> orderItemBOS;

    /**
     * 运费计算快照
     */
    private MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO;

    /**
     * 外部订单额外编码
     */
    private String customerOrderExtraNo;

    /**
     * 期望配送日期
     */
    private String expectedDeliveryDate;

    /**
     * 订单的支付时间（或者订单的下单时间，用于确定订单的最终配送日）
     * （格式：2025-01-01 01:10:25）
     */
    private String orderPaymentTime;

    /**
     * 是否加单
     */
    private Boolean overTimeOrder;

    /**
     * 订单优先级
     * 非必传 （0: 不优先， 1: 优先分配库存）
     */
    private Integer orderPriority;
}
