package com.cosfo.mall.openapi.model.dto;

import lombok.Data;

import java.util.List;

/**
 *
 * @author: xiaowk
 * @time: 2023/9/22 下午6:59
 */
@Data
public class OrderDeliveringDTO {

    /**
     * 外部单号（订单号/售后单号）
     */
    private String customerOrderId;

    /**
     * 三方门店编码
     */
    private String storeNo;

    /**
     * 出库的仓库号
     */
    private String warehouseNo;

    /**
     * 备注信息
     */
    private String message;

    /**
     * 履约明细
     */
    private List<OrderDeliveringItem> itemList;

    @Data
    public static class OrderDeliveringItem {

        /**
         * 外部订单子项ID
         */
        private String customerOrderItemId;

        /**
         * 订单计划履约数量
         */
        private Integer quantity;

        /**
         * 实际履约数量
         */
        private Integer actualQuantity;

        /**
         * 缺货数量
         */
        private Integer shortQuantity;

        /**
         * 状态 0:正常 1:异常
         */
        private Integer status;

        /**
         * 鲜沐sku编码
         */
        private String skuCode;

        /**
         * 外部货源skuCode
         */
        private String customerSkuCode;
    }
}
