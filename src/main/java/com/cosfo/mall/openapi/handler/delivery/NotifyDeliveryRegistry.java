package com.cosfo.mall.openapi.handler.delivery;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-21
 * @Description:
 */
@Component
public class NotifyDeliveryRegistry {

    @Autowired
    private List<NotifyDeliveryHandler> notifyDeliveryHandlerList;

    public NotifyDeliveryHandler getHandlerByTenantId(Long tenantId) {
        for (NotifyDeliveryHandler notifyDeliveryHandler : notifyDeliveryHandlerList) {
            if (notifyDeliveryHandler.support(tenantId)) {
                return notifyDeliveryHandler;
            }
        }
        return null;
    }
}
