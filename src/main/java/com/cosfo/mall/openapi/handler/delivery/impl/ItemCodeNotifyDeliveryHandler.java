package com.cosfo.mall.openapi.handler.delivery.impl;

import com.cosfo.mall.common.config.OpenApiConfig;
import com.cosfo.mall.openapi.handler.delivery.NotifyDeliveryHandler;
import com.cosfo.mall.openapi.model.dto.OrderDeliveringDTO;
import com.cosfo.mall.openapi.model.param.NotifyDeliveryParam;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-12-21
 * @Description:自有编码推送
 */
@Slf4j
@Component
public class ItemCodeNotifyDeliveryHandler implements NotifyDeliveryHandler {

    @Resource
    private OpenApiConfig openApiConfig;

    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;

    @Override
    public boolean support(Long tenantId) {
        return openApiConfig.getItemCodeOpenApiTenantIds().contains(tenantId);
    }

    @Override
    public OrderDeliveringDTO builderOrderDeliveringDTO(NotifyDeliveryParam notifyDeliveryParam) {
        log.info("发货回传推送自有编码策略,customerOrderId:{}", notifyDeliveryParam.getCustomerOrderId());

        List<OrderItemResp> orderItemList = notifyDeliveryParam.getOrderItemList();
        Map<Long, OrderItemResp> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));
        List<OrderItemExtraResp> orderItemExtraList = notifyDeliveryParam.getOrderItemExtraList();

        // 获取订单快照表信息
        List<Long> orderItemIds = orderItemList.stream().map(OrderItemResp::getId).collect(Collectors.toList());
        List<OrderItemSnapshotResp> snapshotList = RpcResultUtil.handle(orderItemSnapshotQueryProvider.queryByOrderItemIds(orderItemIds));
        Map<Long, OrderItemSnapshotResp> orderItemSnapshotMap = snapshotList.stream()
                .collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, item -> item, (v1, v2) -> v1));


        List<OrderDeliveringDTO.OrderDeliveringItem> itemList = orderItemExtraList.stream().map(e -> {
            OrderDeliveringDTO.OrderDeliveringItem orderDeliveringItem = new OrderDeliveringDTO.OrderDeliveringItem();
            orderDeliveringItem.setCustomerOrderItemId(e.getCustomerOrderItemId());
            Integer quantity = orderItemMap.get(e.getOrderItemId()).getAmount();
            orderDeliveringItem.setQuantity(quantity);
            orderDeliveringItem.setActualQuantity(quantity);
            orderDeliveringItem.setShortQuantity(0);
            orderDeliveringItem.setStatus(0);
            String itemCode = Optional.ofNullable(orderItemSnapshotMap.get(e.getOrderItemId()))
                    .map(OrderItemSnapshotResp::getItemCode).orElse(StringUtils.EMPTY);
            orderDeliveringItem.setSkuCode(itemCode);
            orderDeliveringItem.setCustomerSkuCode(e.getCustomerSkuCode());
            return orderDeliveringItem;
        }).collect(Collectors.toList());

        OrderDeliveringDTO orderDeliveringDTO = new OrderDeliveringDTO();
        orderDeliveringDTO.setCustomerOrderId(notifyDeliveryParam.getCustomerOrderId());
        orderDeliveringDTO.setStoreNo(notifyDeliveryParam.getStoreNo());
        orderDeliveringDTO.setItemList(itemList);
        return orderDeliveringDTO;
    }
}
