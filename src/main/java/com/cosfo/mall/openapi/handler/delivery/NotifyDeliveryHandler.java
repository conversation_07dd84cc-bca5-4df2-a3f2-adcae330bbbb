package com.cosfo.mall.openapi.handler.delivery;

import com.cosfo.mall.openapi.model.dto.OrderDeliveringDTO;
import com.cosfo.mall.openapi.model.param.NotifyDeliveryParam;

/**
 * @Author: fansongsong
 * @Date: 2023-12-21
 * @Description:
 */
public interface NotifyDeliveryHandler {

    /**
     * 是否支持tenantId
     *
     * @return
     */
    boolean support(Long tenantId);

    /**
     * 构建订单发货信息数据
     *
     * @param notifyDeliveryParam
     * @return
     */
    OrderDeliveringDTO builderOrderDeliveringDTO(NotifyDeliveryParam notifyDeliveryParam);
}
