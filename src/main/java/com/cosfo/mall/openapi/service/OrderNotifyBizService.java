package com.cosfo.mall.openapi.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.common.context.DeliveryStateEnum;
import com.cosfo.mall.common.utils.HttpUtil;
import com.cosfo.mall.facade.BizEventPushFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.mall.openapi.handler.delivery.NotifyDeliveryHandler;
import com.cosfo.mall.openapi.handler.delivery.NotifyDeliveryRegistry;
import com.cosfo.mall.openapi.model.bo.OrderDeliveryNotifyBO;
import com.cosfo.mall.openapi.model.dto.OrderDeliveringDTO;
import com.cosfo.mall.openapi.model.dto.OrderDeliveringDTO.OrderDeliveringItem;
import com.cosfo.mall.openapi.model.dto.OrderDeliveryCompletedDTO;
import com.cosfo.mall.openapi.model.param.NotifyDeliveryParam;
import com.cosfo.mall.order.mapper.OrderSelfLiftingMapper;
import com.cosfo.mall.order.model.dto.OrderSelfLiftingDTO;
import com.cosfo.ordercenter.client.common.OrderSourceEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemExtraQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.util.MessageExtUtil;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单发货信息回告通知
 *
 * @author: xiaowk
 * @date: 2023/10/10 下午7:01
 */
@Service
@Slf4j
public class OrderNotifyBizService {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderItemExtraQueryProvider orderItemExtraQueryProvider;


    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private OrderSelfLiftingMapper orderSelfLiftingMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BizEventPushFacade bizEventPushFacade;
    @Resource
    private NotifyDeliveryRegistry notifyDeliveryRegistry;

    // json tenantId - url
    @NacosValue(value = "${open.api.notify_order_delivering_urls_json:[{\"tenantId\":2,\"url\":\"http://www.bejson.com\"}]}", autoRefreshed = true)
    public String notify_order_delivering_urls;

    @NacosValue(value = "${open.api.notify_order.switch:false}", autoRefreshed = true)
    private boolean notifyOrderSwitch;


    public void notifyOrderDelivering(Long orderId) {
        if(!notifyOrderSwitch){
            log.info("订单发货信息回传关闭, orderId={}", orderId);
            return;
        }

        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        if (orderDTO == null) {
            log.error("订单不存在，orderId={}", orderId);
            return;
        }

        OrderItemExtraQueryReq param = new OrderItemExtraQueryReq();
        param.setTenantId(orderDTO.getTenantId());
        param.setOrderId(orderId);
        List<OrderItemExtraResp> orderItemExtraList = RpcResultUtil.handle(orderItemExtraQueryProvider.queryOrderItemExtraList(param));
        if (CollectionUtils.isEmpty(orderItemExtraList)) {
            log.error("外部订单项不存在，orderId={}", orderId);
            return;
        }

        List<OrderItemResp> orderItemList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderId));
        if (CollectionUtils.isEmpty(orderItemList)) {
            log.error("订单项不存在，orderId={}", orderId);
            return;
        }

        MerchantStoreResultResp merchantStoreResultResp = userCenterMerchantStoreFacade.getMerchantStoreById(orderDTO.getStoreId());
        if (merchantStoreResultResp == null) {
            log.error("门店信息不存在，storeId={}", orderDTO.getStoreId());
            return;
        }

        String url = null;
        try {
            List<OrderDeliveringUrl> urlList = JSON.parseArray(notify_order_delivering_urls, OrderDeliveringUrl.class);
            if(!CollectionUtils.isEmpty(urlList)){
                url = urlList.stream().filter(e -> orderDTO.getTenantId().equals(e.getTenantId())).map(e -> e.getUrl()).findFirst().orElse(null);
            }
        } catch (Exception e) {
            log.error("notify_order_delivering_urls={}解析报错", notify_order_delivering_urls, e);
        }

        // 配置了订单发货回传链接的才进行推送
        if (StringUtils.isBlank(url)) {
            log.error("订单发货信息回传链接为空，tenantId={}", orderDTO.getTenantId());
            return;
        }

        OrderDeliveringDTO orderDeliveringDTO = builderOrderDeliveringDTO(orderDTO, orderItemExtraList, orderItemList, merchantStoreResultResp);

        String res = null;
        try {
            log.info("请求数据 orderNo={}, reqbody={}", orderDTO.getOrderNo(), JSON.toJSONString(orderDeliveringDTO));
            res = HttpUtil.sendPost(url, JSON.toJSONString(orderDeliveringDTO));
            log.info("响应数据 reqbody={}, result={}", JSON.toJSONString(orderDeliveringDTO), res);

            JSONObject jsonObject = JSON.parseObject(res);
            if(!jsonObject.containsKey("code") || jsonObject.getIntValue("code") != 200){
                // 响应失败，抛异常使用mq重试
                BizException exception = new BizException("开放平台发货信息回传接口响应失败，orderNo=" + orderDTO.getOrderNo() + " 响应信息res=" + res);
//                log.error("开放平台发货信息回传接口响应失败", exception);
                threadSleepForMqException();
                throw exception;
            }
        } catch (Exception e) {
            log.warn("开放平台发货信息回传接口错误, url={}, reqbody={}, result={}", url, JSON.toJSONString(orderDeliveringDTO), res, e);
            threadSleepForMqException();
            throw new BizException("开放平台发货信息回传接口执行错误，orderNo=" + orderDTO.getOrderNo());
        }
    }

    private void threadSleepForMqException(){
        Long sleepTime = 0L;
        if(MessageExtUtil.getMessageExt() != null) {
            // 获取当前mq消息重试次数
            int reconsumeTimes = MessageExtUtil.getMessageExt().getReconsumeTimes();
            sleepTime = reconsumeTimes * 2L;
        }

        try {
            TimeUnit.SECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            log.error("sleep exception", e);
        }
    }

    /**
     * 构建订单发货回传数据信息
     */
    private OrderDeliveringDTO builderOrderDeliveringDTO(OrderResp orderDTO, List<OrderItemExtraResp> orderItemExtraList, List<OrderItemResp> orderItemList, MerchantStoreResultResp merchantStoreResultResp) {
        NotifyDeliveryHandler handler = notifyDeliveryRegistry.getHandlerByTenantId(orderDTO.getTenantId());
        if (Objects.nonNull(handler)) {
            NotifyDeliveryParam notifyDeliveryParam = NotifyDeliveryParam.builder().customerOrderId(orderDTO.getCustomerOrderId())
                    .storeNo(merchantStoreResultResp.getStoreNo())
                    .orderItemList(orderItemList)
                    .orderItemExtraList(orderItemExtraList).build();
            return handler.builderOrderDeliveringDTO(notifyDeliveryParam);
        }

        Map<Long, OrderItemResp> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));

        List<OrderDeliveringItem> itemList = orderItemExtraList.stream().map(e -> {
            OrderDeliveringItem orderDeliveringItem = new OrderDeliveringItem();
            orderDeliveringItem.setCustomerOrderItemId(e.getCustomerOrderItemId());
            Integer quantity = orderItemMap.get(e.getOrderItemId()).getAmount();
            orderDeliveringItem.setQuantity(quantity);
            orderDeliveringItem.setActualQuantity(quantity);
            orderDeliveringItem.setShortQuantity(0);
            orderDeliveringItem.setStatus(0);
            orderDeliveringItem.setSkuCode(e.getSkuCode());
            orderDeliveringItem.setCustomerSkuCode(e.getCustomerSkuCode());
            return orderDeliveringItem;
        }).collect(Collectors.toList());

        OrderDeliveringDTO orderDeliveringDTO = new OrderDeliveringDTO();
        orderDeliveringDTO.setCustomerOrderId(orderDTO.getCustomerOrderId());
        orderDeliveringDTO.setStoreNo(merchantStoreResultResp.getStoreNo());
//        orderDeliveringDTO.setWarehouseNo("");
//        orderDeliveringDTO.setMessage("");
        orderDeliveringDTO.setItemList(itemList);
        return orderDeliveringDTO;
    }


    @Data
    public static class OrderDeliveringUrl{
        /**
         * 租户id
         */
        private Long tenantId;
        /**
         * 订单发货通知http链接
         */
        private String url;
    }

    /**
     * 自提配送完成-外部系统单-通知第三方
     *
     * @param orderId
     */
    @Async
    public void selfLiftFinishedNotifyThirdPart(Long orderId) {
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        if (Objects.isNull(orderDTO)) {
            log.error("selfLiftFinishedNotifyThirdPart 订单不存在，orderId={}", orderId);
            return;
        }

        List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = orderSelfLiftingMapper.selectByOrderNo(orderDTO.getOrderNo());
        if (CollectionUtils.isEmpty(orderSelfLiftingDTOS)) {
            log.info("非自提订单，无需此时推送，orderId={}", orderId);
            return;
        }

        OrderDeliveryNotifyBO orderDeliveryNotifyBO = OrderDeliveryNotifyBO.builder().orderDTO(orderDTO).build();
        // 自提配送,推送通知
        deliveryNotify(selfLiftNotifyParamFunction, orderDeliveryNotifyBO);
    }

    /**
     * 配送通知
     *
     * @param deliveryNotifyParamFunction
     * @param orderDeliveryNotifyBO
     */
    private void deliveryNotify(Function<OrderDeliveryNotifyBO, OrderDeliveryCompletedDTO> deliveryNotifyParamFunction, OrderDeliveryNotifyBO orderDeliveryNotifyBO) {
        OrderResp orderDTO = orderDeliveryNotifyBO.getOrderDTO();
        Long orderId = orderDTO.getId();
        if (!OrderSourceEnum.OPENAPI.getValue().equals(orderDTO.getOrderSource())) {
            log.info("非openAPi订单，无需推送，orderId={}", orderId);
            return;
        }

        String cacheKey = RedisKeyEnum.C00008.join(orderId);
        Object orderResultCache = redisTemplate.opsForValue().get(cacheKey);
        if (Objects.nonNull(orderResultCache)) {
            log.info("deliveryNotify cacheKey已存在:{}", cacheKey);
            return;
        }

        OrderItemExtraQueryReq param = new OrderItemExtraQueryReq();
        param.setTenantId(orderDTO.getTenantId());
        param.setOrderId(orderId);
        List<OrderItemExtraResp> orderItemExtraList = RpcResultUtil.handle(orderItemExtraQueryProvider.queryOrderItemExtraList(param));
        if (CollectionUtils.isEmpty(orderItemExtraList)) {
            log.error("外部订单项不存在，orderId={}", orderId);
            return;
        }

        List<OrderItemResp> orderItemList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderId));
        if (CollectionUtils.isEmpty(orderItemList)) {
            log.error("订单项不存在，orderId={}", orderId);
            return;
        }

        MerchantStoreResultResp merchantStoreResultResp = userCenterMerchantStoreFacade.getMerchantStoreById(orderDTO.getStoreId());
        if (Objects.isNull(merchantStoreResultResp)) {
            log.error("门店信息不存在，storeId={}", orderDTO.getStoreId());
            return;
        }

        orderDeliveryNotifyBO.setOrderItemList(orderItemList);
        orderDeliveryNotifyBO.setOrderItemExtraList(orderItemExtraList);
        orderDeliveryNotifyBO.setMerchantStoreResultResp(merchantStoreResultResp);
        // 执行函数，组装推送参数
        OrderDeliveryCompletedDTO orderDeliveryCompletedDTO = deliveryNotifyParamFunction.apply(orderDeliveryNotifyBO);
        // 推送订单配送完成事件(推送URL能力由开放平台回调能力保证)
        log.info("推送订单配送完成事件,orderNo:{},orderDeliveryCompletedDTO:{}", orderDTO.getOrderNo(), JSON.toJSONString(orderDeliveryCompletedDTO));
        bizEventPushFacade.pushOrderDeliveryFinish(orderDTO.getTenantId(), orderDTO.getOrderNo(), orderDeliveryCompletedDTO);

        redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstant.ONE), NumberConstant.SEVEN, TimeUnit.DAYS);
    }

    /**
     * 自提配送,组装推送参数
     */
    Function<OrderDeliveryNotifyBO, OrderDeliveryCompletedDTO> selfLiftNotifyParamFunction = orderDeliveryNotifyBO -> {
        OrderResp orderDTO = orderDeliveryNotifyBO.getOrderDTO();
        MerchantStoreResultResp merchantStoreResultResp = orderDeliveryNotifyBO.getMerchantStoreResultResp();
        // 订单子项map
        Map<Long, OrderItemResp> orderItemIdMap = orderDeliveryNotifyBO.getOrderItemList().stream().collect(
                Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));

        // 通知参数组装
        List<OrderDeliveryCompletedDTO.OrderDeliveryItem> itemList = orderDeliveryNotifyBO.getOrderItemExtraList().stream().map(extra -> {
            OrderDeliveryCompletedDTO.OrderDeliveryItem orderDeliveringItem = new OrderDeliveryCompletedDTO.OrderDeliveryItem();
            Integer quantity = orderItemIdMap.get(extra.getOrderItemId()).getAmount();
            orderDeliveringItem.setQuantity(quantity);
            orderDeliveringItem.setActualQuantity(quantity);
            orderDeliveringItem.setShortQuantity(NumberConstant.ZERO);
            orderDeliveringItem.setStatus(DeliveryStateEnum.NORMAL.getState());
            orderDeliveringItem.setCustomerOrderItemId(extra.getCustomerOrderItemId());
            orderDeliveringItem.setSkuCode(extra.getSkuCode());
            orderDeliveringItem.setCustomerSkuCode(extra.getCustomerSkuCode());
            orderDeliveringItem.setCustomerSkuSpecification(extra.getCustomerSkuSpecification());
            orderDeliveringItem.setCustomerSkuSpecificationUnit(extra.getCustomerSkuSpecificationUnit());
            return orderDeliveringItem;
        }).collect(Collectors.toList());

        OrderDeliveryCompletedDTO orderDeliveryCompletedDTO = new OrderDeliveryCompletedDTO();
        orderDeliveryCompletedDTO.setCustomerOrderId(orderDTO.getCustomerOrderId());
        orderDeliveryCompletedDTO.setStoreNo(merchantStoreResultResp.getStoreNo());
        orderDeliveryCompletedDTO.setWarehouseNo(StringUtils.EMPTY);
        orderDeliveryCompletedDTO.setMessage(StringUtils.EMPTY);
        orderDeliveryCompletedDTO.setItemList(itemList);
        return orderDeliveryCompletedDTO;
    };

    /**
     * 司机配送,组装推送参数
     */
    Function<OrderDeliveryNotifyBO, OrderDeliveryCompletedDTO> deliveryNotifyParamFunction = orderDeliveryNotifyBO -> {
        OrderResp orderDTO = orderDeliveryNotifyBO.getOrderDTO();
        MerchantStoreResultResp merchantStoreResultResp = orderDeliveryNotifyBO.getMerchantStoreResultResp();
        // 订单子项id到扩展信息map
        Map<Long, OrderItemExtraResp> orderItemExtraDTOMap = orderDeliveryNotifyBO.getOrderItemExtraList().stream().collect(Collectors.toMap(
                OrderItemExtraResp::getOrderItemId, Function.identity(), (v1, v2) -> v1));
        // itemId到订单子项map
        Map<String, Long> itemIdOrderItemIdMap = orderDeliveryNotifyBO.getOrderItemList().stream().collect(Collectors.toMap(
                dto -> String.valueOf(dto.getItemId()), OrderItemResp::getId, (v1, v2) -> v1));

        // 履约明细
        CommonFulfillmentFinishMessage commonFulfillmentFinishMessage = orderDeliveryNotifyBO.getCommonFulfillmentFinishMessage();

        // 按照订单需履约数据进行填充
        List<OrderDeliveryCompletedDTO.OrderDeliveryItem> itemList = commonFulfillmentFinishMessage.getItemList().stream()
                .map(commonFulfillmentFinishMessageDetail -> {
                    String itemId = commonFulfillmentFinishMessageDetail.getItemId();
                    OrderItemExtraResp orderItemExtraDTO = Optional.ofNullable(itemIdOrderItemIdMap.get(itemId)).map(orderItemId -> orderItemExtraDTOMap.get(orderItemId)).orElse(null);
                    if (Objects.isNull(orderItemExtraDTO)) {
                        log.info("履约商品找不到对应订单子项扩展信息,commonFulfillmentFinishMessageDetail:{}", JSON.toJSONString(commonFulfillmentFinishMessageDetail));
                        return null;
                    }
                    OrderDeliveryCompletedDTO.OrderDeliveryItem orderDeliveringItem = new OrderDeliveryCompletedDTO.OrderDeliveryItem();
                    orderDeliveringItem.setCustomerOrderItemId(orderItemExtraDTO.getCustomerOrderItemId());

                    // 计划履约数量、实际履约数量、缺货数量、配送状态、sku编码
                    orderDeliveringItem.setQuantity(commonFulfillmentFinishMessageDetail.getQuantity());
                    orderDeliveringItem.setActualQuantity(commonFulfillmentFinishMessageDetail.getActualQuantity());
                    orderDeliveringItem.setShortQuantity(commonFulfillmentFinishMessageDetail.getShortQuantity());
                    orderDeliveringItem.setStatus(commonFulfillmentFinishMessageDetail.getStatus());
                    orderDeliveringItem.setSkuCode(commonFulfillmentFinishMessageDetail.getSkuCode());

                    orderDeliveringItem.setCustomerSkuCode(orderItemExtraDTO.getCustomerSkuCode());
                    orderDeliveringItem.setCustomerSkuSpecification(orderItemExtraDTO.getCustomerSkuSpecification());
                    orderDeliveringItem.setCustomerSkuSpecificationUnit(orderItemExtraDTO.getCustomerSkuSpecificationUnit());
                    return orderDeliveringItem;
                }).filter(dto -> Objects.nonNull(dto)).collect(Collectors.toList());


        OrderDeliveryCompletedDTO orderDeliveryCompletedDTO = new OrderDeliveryCompletedDTO();
        orderDeliveryCompletedDTO.setCustomerOrderId(orderDTO.getCustomerOrderId());
        orderDeliveryCompletedDTO.setStoreNo(merchantStoreResultResp.getStoreNo());
        orderDeliveryCompletedDTO.setWarehouseNo(Optional.ofNullable(commonFulfillmentFinishMessage.getWarehouseNo()).map(String::valueOf).orElse(StringUtils.EMPTY));
        orderDeliveryCompletedDTO.setMessage(Optional.ofNullable(commonFulfillmentFinishMessage.getMessage()).orElse(StringUtils.EMPTY));
        orderDeliveryCompletedDTO.setItemList(itemList);
        return orderDeliveryCompletedDTO;
    };

    /**
     * 配送完成-外部系统单-通知第三方
     *
     * @param commonFulfillmentFinishMessage
     */
    public void deliveryFinishedNotifyThirdPart(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage) {
        try {
            String sourceOrderNo = commonFulfillmentFinishMessage.getSourceOrderNo();
            OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryByNo(sourceOrderNo));
            if (Objects.isNull(orderDTO)) {
                log.error("finishedNotifyThirdpart 订单不存在，commonFulfillmentFinishMessage={}", JSON.toJSONString(commonFulfillmentFinishMessage));
                return;
            }

            OrderDeliveryNotifyBO orderDeliveryNotifyBO = OrderDeliveryNotifyBO.builder().orderDTO(orderDTO)
                    .commonFulfillmentFinishMessage(commonFulfillmentFinishMessage).build();
            // 司机配送订单配送完成
            deliveryNotify(deliveryNotifyParamFunction, orderDeliveryNotifyBO);
        } catch (Exception e) {
            log.error("finishedNotifyThirdPart e", e);
        }
    }
}
