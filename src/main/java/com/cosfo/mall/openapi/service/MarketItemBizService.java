package com.cosfo.mall.openapi.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.item.client.provider.MarketClassificationProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.provider.PriceProvider;
import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.common.constants.MarketDeleteFlagEnum;
import com.cosfo.mall.common.context.AgentTypeEnum;
import com.cosfo.mall.common.exception.OpenApiProviderException;
import com.cosfo.mall.common.exception.code.OpenApiErrorCode;
import com.cosfo.mall.facade.MarketFacade;
import com.cosfo.mall.facade.ProductQueryFacade;
import com.cosfo.mall.facade.SaleInventoryCenterQueryFacade;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.openapi.convert.MarketItemConvert;
import com.cosfo.mall.openapi.model.dto.ItemRouteDTO;
import com.cosfo.mall.openapi.model.dto.PlaceOrderItemDetailDTO;
import com.cosfo.mall.openapi.model.dto.item.ItemPriceDTO;
import com.cosfo.mall.openapi.model.dto.item.MarketItemDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.po.Stock;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.req.ProductSkuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2023/9/19 上午11:45
 */
@Service
@Slf4j
public class MarketItemBizService {

    @Resource
    private ProductQueryFacade productQueryFacade;
    @Resource
    private MarketFacade marketFacade;

    @DubboReference
    private MarketProvider marketProvider;
    @DubboReference
    private MarketClassificationProvider marketClassificationProvider;
    @DubboReference
    private PriceProvider priceProvider;
    @Resource
    private SaleInventoryCenterQueryFacade saleInventoryCenterQueryFacade;

    /**
     * 下单支持商品类型
     */
    public static Set<Integer> OPEN_ORDER_SUPPORT_GOODS_TYPE_SET = Sets.newHashSet(GoodsTypeEnum.QUOTATION_TYPE.getCode(), GoodsTypeEnum.SELF_GOOD_TYPE.getCode());

    /**
     * 根据鲜沐skucode查询鲜沐仓商品(报价、自营待仓)
     *
     * @param tenantId
     * @param skuCodes
     * @return
     */
    public Map<String, MarketItemDTO> queryXianmuWarehouseItemBySkuCodes(Long tenantId, List<String> skuCodes) {
        if (tenantId == null || CollectionUtils.isEmpty(skuCodes)) {
            return Collections.emptyMap();
        }
        // 查询自营待仓
        List<ProductAgentSkuDTO> mappingList = selfAgentListBySkuCodes(tenantId, skuCodes);
        Map<String, ProductAgentSkuDTO> skuCodeMap = mappingList.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getAgentSkuCode, Function.identity(), (v1, v2) -> v1));


        // 剩余去查询鲜沐自营
        Map<String, ProductAgentSkuDTO> finalSkuCodeMap = skuCodeMap;
        List<String> xianMuSkuList = skuCodes.stream().filter(skuCode -> Objects.isNull(finalSkuCodeMap.get(skuCode))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(xianMuSkuList)) {
            List<ProductAgentSkuDTO> productAgentSkuMappings = productQueryFacade.selectBySkuIdAndTenantId(XianmuSupplyTenant.TENANT_ID, xianMuSkuList);
            mappingList.addAll(productAgentSkuMappings);
        }

        skuCodeMap = mappingList.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getAgentSkuCode, Function.identity(), (v1, v2) -> v1));

        for (String skuCode : skuCodes) {
            if (skuCodeMap.get(skuCode) == null) {
                throw new BizException("商品信息不存在，skuCode=" + skuCode, OpenApiErrorCode.OR_ITEM_ABSENT);
            }
        }

        Set<Long> skuIdSet = mappingList.stream().map(ProductAgentSkuDTO::getSkuId).collect(Collectors.toSet());

        List<Long> marketIdList = null;

        Map<Long, List<MarketItemInfoResp>> marketItemRespMap = Collections.emptyMap();
        try {
            List<MarketItemInfoResp> marketItemInfoResps = RpcResultUtil.handle(marketProvider.queryMarketItemInfoBySkuIds(tenantId, skuIdSet));
            if (!CollectionUtils.isEmpty(marketItemInfoResps)) {
                // 过滤出上架、正常状态的商品
                marketItemRespMap = marketItemInfoResps.stream().filter(item ->
                        OnSaleTypeEnum.ON_SALE.getCode().equals(item.getOnSale()) &&
                                MarketDeleteFlagEnum.NORMAL.getFlag().equals(item.getDeleteFlag()))
                        .collect(Collectors.groupingBy(MarketItemInfoResp::getSkuId));
                marketIdList = marketItemInfoResps.stream().map(MarketItemInfoResp::getMarketId).distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("查询商品信息报错，tenantId={}, skuIds={}", tenantId, skuIdSet, e);
        }

        if (CollectionUtils.isEmpty(marketItemRespMap)) {
            throw new BizException("上架中的商品信息不存在, skuCodes=" + skuCodes, OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
        }

        Map<Long, MarketItemClassificationResp> classificationRespMap = Collections.emptyMap();
        try {
            if (!CollectionUtils.isEmpty(marketIdList)) {
                classificationRespMap = RpcResultUtil.handle(marketClassificationProvider.queryByMarketIds(tenantId, marketIdList));
            }
        } catch (Exception e) {
            log.error("查询商品分类信息报错，tenantId={}, skuIds={}", tenantId, skuIdSet, e);
        }

        Map<String, MarketItemDTO> resultMap = new HashMap<>(skuCodes.size());
        for (Entry<String, ProductAgentSkuDTO> entry : skuCodeMap.entrySet()) {
            String skuCode = entry.getKey();
            ProductAgentSkuDTO mapping = entry.getValue();
            Long skuId = mapping.getSkuId();

            List<MarketItemInfoResp> itemList = marketItemRespMap.get(skuId);
            if (CollectionUtils.isEmpty(itemList)) {
                throw new BizException("上架中的商品信息不存在，skuCode=" + skuCode, OpenApiErrorCode.OR_ITEM_ABSENT);
            }

            // skuId对应marketItem数量大于1, 告警
            if(itemList.size() > 1){
                log.error("开放平台下单 tenantId={}, 货品skuId={}, skuCode={}关联商品有多个, 请及时确认", tenantId, skuId, skuCode, new BizException("开放平台下单货品关联多个商品告警"));
            }

            MarketItemDTO marketItemDTO = MarketItemConvert.INSTANCE.respToDto(itemList.get(0));

            fillMarketItemDTO(classificationRespMap, mapping, marketItemDTO);

            resultMap.put(skuCode, marketItemDTO);
        }

        return resultMap;
    }

    private List<ProductAgentSkuDTO> selfAgentListBySkuCodes(Long tenantId, List<String> skuCodes) {
        List<ProductAgentSkuDTO> productAgentSkuMappings = productQueryFacade.selectBySkuIdAndTenantId(tenantId, skuCodes);
        return getProductAgentSkuDTOS(tenantId, productAgentSkuMappings);
    }

    /**
     * 查询商品显示价格
     *
     * @param tenantId
     * @param storeId
     * @param placeOrderItemDetailDTO
     * @return
     */
    public Map<Long, ItemPriceDTO> queryItemPrice(Long tenantId, Long storeId, PlaceOrderItemDetailDTO placeOrderItemDetailDTO) {
        Map<String, MarketItemDTO> marketItemDTOMap = placeOrderItemDetailDTO.getMarketItemDTOMap();
        if (CollectionUtils.isEmpty(marketItemDTOMap)) {
            return Collections.emptyMap();
        }

        List<Long> itemIds = marketItemDTOMap.values().stream().map(MarketItemDTO::getItemId).distinct().collect(Collectors.toList());

        DubboResponse<Map<Long, PriceDetailResp>> dubboResponse = priceProvider.listItemPriceDetailByItemIds(tenantId, storeId, PriceTargetTypeEnum.STORE.getCode(), itemIds);

        Map<Long, PriceDetailResp> priceDetailRespMap = null;
        try {
            priceDetailRespMap = RpcResultUtil.handle(dubboResponse);
        } catch (Exception e) {
            log.error("查询商品价格详情失败, itemIds={}", itemIds, e);
            throw new BizException("查询商品价格详情失败");
        }

        if (CollectionUtils.isEmpty(priceDetailRespMap)) {
            throw new BizException("商品报价信息不存在, skuCodes=" + marketItemDTOMap.keySet(), OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
        }

        Map<Long, ItemPriceDTO> resultMap = new HashMap<>(itemIds.size());

        for (Entry<String, MarketItemDTO> entry : marketItemDTOMap.entrySet()) {
            String apiCode = entry.getKey();
            MarketItemDTO marketItemDTO = entry.getValue();
            Long itemId = marketItemDTO.getItemId();
            PriceDetailResp priceDetailResp = priceDetailRespMap.get(itemId);
            if(priceDetailResp == null){
                throw new BizException("商品报价信息不存在，skucode=" + apiCode, OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
            }
            ItemPriceDTO itemPriceDTO = builderItemPriceDTO(itemId, priceDetailResp);

            resultMap.put(itemId, itemPriceDTO);
        }

        return resultMap;
    }

    /**
     * 查询商品售价 ，如果没有售价则返回0
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    public Map<Long, BigDecimal> queryItemPriceWithDefaultPrice(Long tenantId, Long storeId, List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }
        DubboResponse<Map<Long, PriceDetailResp>> dubboResponse = priceProvider.listItemPriceDetailByItemIds(tenantId, storeId, PriceTargetTypeEnum.STORE.getCode(), itemIds);

        Map<Long, PriceDetailResp> priceDetailRespMap = Collections.emptyMap ();
        try {
            priceDetailRespMap = RpcResultUtil.handle(dubboResponse);
        } catch (Exception e) {
            log.error("查询商品价格详情失败, itemIds={}", itemIds, e);
            throw new BizException("查询商品价格详情失败");
        }

        Map<Long, BigDecimal> resultMap = new HashMap<>(itemIds.size());

        for (Long itemId : itemIds) {
            PriceDetailResp priceDetailResp = priceDetailRespMap.get(itemId);
            if(priceDetailResp == null){
                resultMap.put(itemId, BigDecimal.ZERO);
            }else{
                resultMap.put(itemId, priceDetailResp.getPrice ());
            }
        }
        return resultMap;
    }

    /**
     * 构建商品价格dto
     * @param itemId
     * @param priceDetailResp
     * @return
     */
    private ItemPriceDTO builderItemPriceDTO(Long itemId, PriceDetailResp priceDetailResp) {
        ItemPriceDTO itemPriceDTO = new ItemPriceDTO();
        itemPriceDTO.setItemId(itemId);
        itemPriceDTO.setBasePrice(priceDetailResp.getPrice());
        itemPriceDTO.setPrice(priceDetailResp.getPrice());
        itemPriceDTO.setSupplyPrice(priceDetailResp.getCostPrice());
        itemPriceDTO.setMarketItemPrice(priceDetailResp.getMarketItemPrice());
        return itemPriceDTO;
    }

    /**
     * 自有编码路由规则
     * @param itemRouteDTO
     * @return
     */
    public Map<String, MarketItemDTO> queryXianmuWarehouseItemByItemCodes(ItemRouteDTO itemRouteDTO) {
        Long tenantId = itemRouteDTO.getTenantId();
        List<String> itemCodeList = itemRouteDTO.getCodeList();
        if (tenantId == null || CollectionUtils.isEmpty(itemCodeList)) {
            return Collections.emptyMap();
        }

        List<Long> marketIdList = null;
        List<Long> skuIds = null;
        List<MarketItemInfoResp> marketItemInfoResps = Collections.emptyList();
        Map<Long, List<MarketItemInfoResp>> marketItemRespMap = Collections.emptyMap();
        try {
            marketItemInfoResps = RpcResultUtil.handle(marketProvider.listMarketItemInfoByItemCodes(tenantId, itemCodeList));
            if (!CollectionUtils.isEmpty(marketItemInfoResps)) {
                // 过滤出上架、正常状态的商品
                marketItemInfoResps = marketItemInfoResps.stream().filter(item ->
                        OnSaleTypeEnum.ON_SALE.getCode().equals(item.getOnSale()) &&
                                MarketDeleteFlagEnum.NORMAL.getFlag().equals(item.getDeleteFlag())).
                        collect(Collectors.toList());
                marketItemRespMap = marketItemInfoResps.stream().collect(Collectors.groupingBy(MarketItemInfoResp::getSkuId));
                marketIdList = marketItemInfoResps.stream().map(MarketItemInfoResp::getMarketId).distinct().collect(Collectors.toList());
                skuIds = marketItemInfoResps.stream().map(MarketItemInfoResp::getSkuId).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("查询商品信息报错，tenantId={}, itemCodeList={}", tenantId, itemCodeList, e);
        }

        // 上架中的商品信息校验
        Set<String> currentItemCodeList = marketItemInfoResps.stream().map(MarketItemInfoResp::getItemCode).collect(Collectors.toSet());
        List<String> noExistItemCodeList = (List<String>) org.apache.commons.collections4.CollectionUtils.subtract(itemCodeList, currentItemCodeList);
        if (!CollectionUtils.isEmpty(noExistItemCodeList)) {
            throw new OpenApiProviderException("上架中的商品信息不存在, skuCode:" + StringUtils.join(noExistItemCodeList, ","),
                    OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
        }

        List<MarketItemInfoResp> notXianmuWarehouseItemList = marketItemInfoResps.stream().filter(resp -> !OPEN_ORDER_SUPPORT_GOODS_TYPE_SET.contains(resp.getGoodsType())).collect(Collectors.toList());
        // 有非直供、代仓商品
        if (!CollectionUtils.isEmpty(notXianmuWarehouseItemList)) {
            String errSkuCodes = notXianmuWarehouseItemList.stream().map(MarketItemInfoResp::getItemCode).collect(Collectors.joining(","));
            throw new OpenApiProviderException(errSkuCodes + "不是鲜沐直供/自营代仓商品，不支持下单", OpenApiErrorCode.OR_EXIST_NOT_QUOTATION_ITEM);
        }

        Map<Long, MarketItemClassificationResp> classificationRespMap = Collections.emptyMap();
        try {
            if (!CollectionUtils.isEmpty(marketIdList)) {
                classificationRespMap = RpcResultUtil.handle(marketClassificationProvider.queryByMarketIds(tenantId, marketIdList));
            }
        } catch (Exception e) {
            log.error("查询商品分类信息报错，tenantId={}, itemCodeList={}", tenantId, itemCodeList, e);
        }
        Map<Long, ProductAgentSkuDTO> skuIdMap = getProductAgentSkuMappingList(tenantId, skuIds);

        // 查询商品价格
        Map<String, List<MarketItemInfoResp>> marketItemMap = marketItemInfoResps.stream().collect(Collectors.groupingBy(MarketItemInfoResp::getItemCode));
        Map<Long, ItemPriceDTO> itemPriceDTOMap = queryItemPriceByItemCodeMap(itemRouteDTO, marketItemMap);
        log.info("商品价格过滤前 marketItemInfoResps:{},itemPriceDTOMap:{}", JSON.toJSONString(marketItemInfoResps), JSON.toJSONString(itemPriceDTOMap));
        // 过滤价格不存在的商品信息
        marketItemInfoResps = marketItemInfoResps.stream().filter(marketItemInfoResp -> Objects.nonNull(itemPriceDTOMap.get(marketItemInfoResp.getItemId()))).collect(Collectors.toList());
        currentItemCodeList = marketItemInfoResps.stream().map(MarketItemInfoResp::getItemCode).collect(Collectors.toSet());
        List<String> noPriceItemCodeList = (List<String>) org.apache.commons.collections4.CollectionUtils.subtract(itemCodeList, currentItemCodeList);
        if (!CollectionUtils.isEmpty(noPriceItemCodeList)) {
            throw new OpenApiProviderException("下单的商品报价不存在，不支持下单，skuCode:" + StringUtils.join(noPriceItemCodeList, ","),
                    OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
        }


        List<MarketItemDTO> marketItemDTOList = builderMarketItemDTOS(marketItemInfoResps, classificationRespMap, skuIdMap);
        MerchantAddressDTO merchantAddressDTO = itemRouteDTO.getMerchantAddressDTO();
        // 下单前查询库存，最大库存作为本次真实下单商品
        List<StockDTO> stockList = saleInventoryCenterQueryFacade.queryAddressSkuInventoryInfo(tenantId, merchantAddressDTO, marketItemDTOList);


        // 根据库存,路由出具体的商品信息
        Map<String, MarketItemDTO> routeMarketItemDTOMap = routeItemByStockDtoMap(marketItemDTOList, stockList);

        fillMarketInfo(tenantId, routeMarketItemDTOMap);
        return routeMarketItemDTOMap;
    }

    /**
     * 填充商品信息
     * @param tenantId
     * @param routeMarketItemDTOMap
     */
    private void fillMarketInfo(Long tenantId, Map<String, MarketItemDTO> routeMarketItemDTOMap) {
        List<Long> marketIds = routeMarketItemDTOMap.values().stream().map(MarketItemDTO::getMarketId).collect(Collectors.toList());
        List<MarketResp> markets = marketFacade.queryMarketListByMarketIds(tenantId, marketIds);
        Map<Long, MarketResp> marketMap = markets.stream().collect(Collectors.toMap(MarketResp::getId, Function.identity(), (v1, v2) -> v1));
        for (MarketItemDTO marketItemDTO : routeMarketItemDTOMap.values()) {
            MarketResp market = marketMap.get(marketItemDTO.getMarketId());
            if(market != null){
                marketItemDTO.setTitle(market.getTitle());
                marketItemDTO.setMainPicture(market.getMainPicture());
            }
        }
    }

    /**
     * 组装MarketItemDTO信息，用于库存查询
     * @param marketItemInfoResps
     * @param classificationRespMap
     * @param skuIdMap
     * @return
     */
    private List<MarketItemDTO> builderMarketItemDTOS(List<MarketItemInfoResp> marketItemInfoResps,
                                                      Map<Long, MarketItemClassificationResp> classificationRespMap,
                                                      Map<Long, ProductAgentSkuDTO> skuIdMap) {

        List<MarketItemDTO> marketItemDTOList = Lists.newArrayList();
        for (MarketItemInfoResp marketItemInfoResp : marketItemInfoResps) {
            Long skuId = marketItemInfoResp.getSkuId();
            ProductAgentSkuDTO mapping = skuIdMap.get(skuId);
            if (Objects.isNull(mapping)) {
                throw new OpenApiProviderException("商品不存在直供/代仓映射关系，请确认商品skuCode=" + marketItemInfoResp.getItemCode(), OpenApiErrorCode.OR_ITEM_ABSENT);
            }
            MarketItemDTO marketItemDTO = MarketItemConvert.INSTANCE.respToDto(marketItemInfoResp);

            fillMarketItemDTO(classificationRespMap, mapping, marketItemDTO);
            marketItemDTOList.add(marketItemDTO);
        }
        return marketItemDTOList;
    }

    /**
     * 填充marketItemDTO的属性信息
     * @param classificationRespMap
     * @param mapping
     * @param marketItemDTO
     */
    private void fillMarketItemDTO(Map<Long, MarketItemClassificationResp> classificationRespMap, ProductAgentSkuDTO mapping, MarketItemDTO marketItemDTO) {
        MarketItemClassificationResp classificationResp = classificationRespMap.get(marketItemDTO.getMarketId());
        if (classificationResp != null) {
            if (classificationResp.getFirstClassificationId() != null) {
                marketItemDTO.setClassificationId(classificationResp.getFirstClassificationId());
            }
            if (classificationResp.getSecondClassificationId() != null) {
                marketItemDTO.setClassificationId(classificationResp.getSecondClassificationId());
            }
        }

        marketItemDTO.setAgentTenantId(mapping.getAgentTenantId());
        marketItemDTO.setAgentSkuId(mapping.getAgentSkuId());
        marketItemDTO.setAgentSkuCode(mapping.getAgentSkuCode());
    }

    /**
     * 获取映射信息
     * @param tenantId
     * @param skuIds
     * @return
     */
    private Map<Long, ProductAgentSkuDTO> getProductAgentSkuMappingList(Long tenantId, List<Long> skuIds) {
        // 鲜沐直供映射
        List<ProductAgentSkuDTO> mappingList = productQueryFacade.queryAgentSkuInfo(skuIds, XianmuSupplyTenant.TENANT_ID);
        Map<Long, ProductAgentSkuDTO> skuIdMap = mappingList.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, Function.identity(), (v1, v2) -> v1));
        // 剩余去查询自营待仓
        Map<Long, ProductAgentSkuDTO> finalSkuIdMap = skuIdMap;
        List<Long> xianMuSkuList = skuIds.stream().filter(skuId -> Objects.isNull(finalSkuIdMap.get(skuId))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(xianMuSkuList)) {
            List<ProductAgentSkuDTO> productAgentSkuMappings = selfAgentListBySkuIds(tenantId,xianMuSkuList);
            mappingList.addAll(productAgentSkuMappings);
        }
        skuIdMap = mappingList.stream().collect(Collectors.toMap(ProductAgentSkuDTO::getSkuId, Function.identity(), (v1, v2) -> v1));
        return skuIdMap;
    }

    /**
     * 根据skuId查询代仓品的映射关系
     * @param tenantId
     * @param skuIds
     * @return
     */
    private List<ProductAgentSkuDTO> selfAgentListBySkuIds(Long tenantId, List<Long> skuIds) {
        List<ProductAgentSkuDTO> productAgentSkuMappings = productQueryFacade.queryAgentSkuInfo(skuIds, tenantId);
        return getProductAgentSkuDTOS(tenantId, productAgentSkuMappings);
    }

    private List<ProductAgentSkuDTO> getProductAgentSkuDTOS(Long tenantId, List<ProductAgentSkuDTO> productAgentSkuMappings) {
        if (CollectionUtil.isEmpty(productAgentSkuMappings)) {
            return Lists.newArrayList();
        }

        List<Long> skuIdList = productAgentSkuMappings.stream().map(ProductAgentSkuDTO::getSkuId).collect(Collectors.toList());

        // 回查货品是否代仓进行过滤
        ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
        productSkuPageQueryReq.setTenantId(tenantId);
        productSkuPageQueryReq.setSkuIds(skuIdList);
        productSkuPageQueryReq.setAgentType(AgentTypeEnum.AGENT.getType());
        productSkuPageQueryReq.setPageSize(skuIdList.size());
        List<ProductSkuDetailResp> productSkuDetailResps = productQueryFacade.selectSkuPage(productSkuPageQueryReq);
        Set<Long> skuIdSet = productSkuDetailResps.stream().map(ProductSkuDetailResp::getSkuId).collect(Collectors.toSet());
        return productAgentSkuMappings.stream().filter(mapping -> skuIdSet.contains(mapping.getSkuId())).collect(Collectors.toList());
    }

    /**
     * 根据库存信息路由命中到具体商品
     * @param marketItemDTOList
     * @param stockList
     */
    private Map<String, MarketItemDTO> routeItemByStockDtoMap(List<MarketItemDTO> marketItemDTOList, List<StockDTO> stockList) {
        Map<String, List<StockDTO>> skuStockMap = stockList.stream().collect(Collectors.groupingBy(StockDTO::getSupplySku));
        // 填充最大可占用库存
        for (MarketItemDTO marketItemDTO : marketItemDTOList) {
            Integer maxAmount = NumberConstant.ZERO;
            List<StockDTO> stockDTOS = skuStockMap.get(marketItemDTO.getAgentSkuCode());
            if (!CollectionUtils.isEmpty(stockDTOS)) {
                StockDTO stockDTO = stockDTOS.stream().max(Comparator.comparingInt(Stock::getMaxAmount)).get();
                maxAmount = stockDTO.getMaxAmount();
            }
            marketItemDTO.setMaxAmount(maxAmount);
        }
        log.info("商品路由前商品信息,marketItemDTOList:{}", JSON.toJSONString(marketItemDTOList));
        Map<String, List<MarketItemDTO>> itemCodeMap = marketItemDTOList.stream().collect(Collectors.groupingBy(MarketItemDTO::getItemCode));

        Map<String, MarketItemDTO> routeMarketItemDTOMap = Maps.newHashMap();
        for (Entry<String, List<MarketItemDTO>> entry : itemCodeMap.entrySet()) {
            String itemCode = entry.getKey();
            List<MarketItemDTO> marketItemDTOS = entry.getValue();
            List<MarketItemDTO> stockSortItemList = marketItemDTOS.stream()
                    .sorted(Comparator.comparing(MarketItemDTO::getMaxAmount).reversed()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(stockSortItemList)) {
                routeMarketItemDTOMap.put(itemCode, stockSortItemList.get(NumberConstant.ZERO));
            }
        }
        log.info("商品路由后选中商品信息,routeMarketItemDTOMap:{}", JSON.toJSONString(routeMarketItemDTOMap));
        return routeMarketItemDTOMap;
    }

//    /**
//     * 根据库存信息路由命中到具体商品
//     * @param marketItemDTOList
//     * @param stockDTOMap
//     */
//    private Map<String, MarketItemDTO> routeItemByStockDtoMap(List<MarketItemDTO> marketItemDTOList, Map<Long, StockDTO> stockDTOMap) {
//        // 填充最大可占用库存
//        for (MarketItemDTO marketItemDTO : marketItemDTOList) {
//            StockDTO stockDTO = stockDTOMap.get(marketItemDTO.getItemId());
//            marketItemDTO.setMaxAmount(Optional.ofNullable(stockDTO).map(StockDTO::getMaxAmount).orElse(NumberConstant.ZERO));
//        }
//        log.info("商品路由前商品信息,marketItemDTOList:{}", JSON.toJSONString(marketItemDTOList));
//        Map<String, List<MarketItemDTO>> itemCodeMap = marketItemDTOList.stream().collect(Collectors.groupingBy(MarketItemDTO::getItemCode));
//
//        Map<String, MarketItemDTO> routeMarketItemDTOMap = Maps.newHashMap();
//        for (Entry<String, List<MarketItemDTO>> entry : itemCodeMap.entrySet()) {
//            String itemCode = entry.getKey();
//            List<MarketItemDTO> marketItemDTOS = entry.getValue();
//            List<MarketItemDTO> stockSortItemList = marketItemDTOS.stream()
//                    .sorted(Comparator.comparing(MarketItemDTO::getMaxAmount).reversed()).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(stockSortItemList)) {
//                routeMarketItemDTOMap.put(itemCode, stockSortItemList.get(NumberConstant.ZERO));
//            }
//        }
//        log.info("商品路由后选中商品信息,routeMarketItemDTOMap:{}", JSON.toJSONString(routeMarketItemDTOMap));
//        return routeMarketItemDTOMap;
//    }

    private String getAddressInfo(MerchantAddressDTO merchantAddressDTO) {
        if (Objects.isNull(merchantAddressDTO)) {
            return StringUtils.EMPTY;
        }
        return StringUtils.join(merchantAddressDTO.getProvince(), merchantAddressDTO.getCity(), merchantAddressDTO.getArea(), merchantAddressDTO.getAddress());
    }
    /**
     * 查询自有编码的价格信息
     * @param itemRouteDTO
     * @param marketItemMap
     * @return
     */
    private Map<Long, ItemPriceDTO> queryItemPriceByItemCodeMap(ItemRouteDTO itemRouteDTO, Map<String, List<MarketItemInfoResp>> marketItemMap) {
        if (CollectionUtils.isEmpty(marketItemMap)) {
            return Collections.emptyMap();
        }
        Long tenantId = itemRouteDTO.getTenantId();
        Long storeId = itemRouteDTO.getStoreId();

        List<Long> itemIds = marketItemMap.values().stream().flatMap(itemList -> itemList.stream()).map(MarketItemInfoResp::getItemId).distinct().collect(Collectors.toList());
        DubboResponse<Map<Long, PriceDetailResp>> dubboResponse = priceProvider.listItemPriceDetailByItemIds(tenantId, storeId, PriceTargetTypeEnum.STORE.getCode(), itemIds);

        Map<Long, PriceDetailResp> priceDetailRespMap;
        try {
            priceDetailRespMap = RpcResultUtil.handle(dubboResponse);
        } catch (Exception e) {
            log.error("查询商品价格详情失败, itemIds={}", itemIds, e);
            throw new BizException("查询商品价格详情失败");
        }

        if (CollectionUtils.isEmpty(priceDetailRespMap)) {
            throw new ProviderException("商品报价信息不存在, skuCodes=" + itemRouteDTO.getCodeList() + ",门店地址信息=" +
                    getAddressInfo(itemRouteDTO.getMerchantAddressDTO()), OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
        }

        Map<Long, ItemPriceDTO> resultMap = new HashMap<>(itemIds.size());

        Map<Long, PriceDetailResp> finalPriceDetailRespMap = priceDetailRespMap;
        for (Entry<String, List<MarketItemInfoResp>> entry : marketItemMap.entrySet()) {
            String itemCode = entry.getKey();
            List<MarketItemInfoResp> marketItemInfoResps = entry.getValue();
            boolean itemCodeNoPrice = marketItemInfoResps.stream().allMatch(marketItemInfoResp -> Objects.isNull(finalPriceDetailRespMap.get(marketItemInfoResp.getItemId())));
            if (itemCodeNoPrice) {
                throw new ProviderException("商品报价信息不存在，skuCode=" + itemCode + ",门店地址信息=" +
                        getAddressInfo(itemRouteDTO.getMerchantAddressDTO()), OpenApiErrorCode.OR_ITEM_PRICE_ABSENT);
            }

            for (MarketItemInfoResp marketItemInfoResp : marketItemInfoResps) {
                Long itemId = marketItemInfoResp.getItemId();
                PriceDetailResp priceDetailResp = finalPriceDetailRespMap.get(itemId);
                if (Objects.isNull(priceDetailResp)) {
                    continue;
                }
                ItemPriceDTO itemPriceDTO = builderItemPriceDTO(itemId, priceDetailResp);
                resultMap.put(itemId, itemPriceDTO);
            }
        }
        return resultMap;
    }
}
