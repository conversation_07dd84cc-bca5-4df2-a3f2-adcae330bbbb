package com.cosfo.mall.openapi.service;

import cn.hutool.core.date.DateUtil;
import com.cosfo.mall.common.config.BusinessTimeConfig;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.SaleInventoryCommandFacade;
import com.cosfo.mall.openapi.convert.OrderSnapshotConverter;
import com.cosfo.mall.openapi.convert.StockConvert;
import com.cosfo.mall.openapi.model.bo.OrderBO;
import com.cosfo.mall.openapi.model.bo.OrderItemBO;
import com.cosfo.mall.order.converter.OrderConverter;
import com.cosfo.mall.order.converter.OrderItemConverter;
import com.cosfo.mall.order.model.dto.OrderItemAfterSaleRuleDTO;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.supplier.SupplierDeliveryInfoService;
import com.cosfo.mall.tenant.service.SpecialTenantService;
import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.OrderItemAfterSaleRuleReq;
import com.cosfo.ordercenter.client.req.OrderItemCreateReq;
import com.cosfo.ordercenter.client.req.event.LockStockSuccessReq;
import com.cosfo.ordercenter.client.req.event.OrderCancelReq;
import com.cosfo.ordercenter.client.req.event.OrderCreateReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * @author: xiaowk
 * @date: 2023/9/26 下午6:09
 */
@Service
@Slf4j
public class OrderBizService {

    @Autowired
    private MqProducer mqProducer;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Resource
    private SaleInventoryCommandFacade saleInventoryCommandFacade;
    @Resource
    private SupplierDeliveryInfoService supplierDeliveryInfoService;
    @Resource
    private OrderService orderService;
    @Resource
    private BusinessTimeConfig businessTimeConfig;
    @Resource
    private SpecialTenantService specialTenantService;

    @DubboReference
    private OrderCommandProvider orderCommandProvider;


    public void createOrder(OrderBO orderBO) {
        String orderNo = Global.createOrderNo(Global.NORMAL_ORDER_CODE);
        orderBO.setOrderNo(orderNo);

        // 保存订单相关记录
        initOrderAndSave(orderBO);

        orderService.sendOrderCancelMessage(orderBO.getOrderId(), OrderCancelTypeEnum.NORMAL_CANCEL.getType(), businessTimeConfig.getOrderCancelTime());
    }

    private void initOrderAndSave(OrderBO orderBO) {
        OrderCreateReq createReq = new OrderCreateReq();

        OrderDTO orderDTO = OrderConverter.INSTANCE.boToDTO(orderBO);
        if (!StringUtils.isEmpty(orderBO.getOrderPaymentTime())){
            LocalDateTime orderPayTime = DateUtil.parseLocalDateTime(orderBO.getOrderPaymentTime());
            orderDTO.setCreateTime(orderPayTime);
            orderDTO.setBeginDeliveryTime(orderPayTime);
        }
        orderDTO.setOrderSnapshot(OrderSnapshotConverter.convert(orderBO));
        createReq.setOrderDTO(orderDTO);

        createReq.setDeliveryFeeSnapshotDTO(null);

        List<OrderItemBO> orderItemBOS = orderBO.getOrderItemBOS();
        // 生成订单项
        List<OrderItemCreateReq> itemCreateList = orderItemBOS.stream().map(orderItemVO -> {
            OrderItemCreateReq orderItemCreateReq = OrderItemConverter.INSTANCE.boToReq(orderItemVO);
            OrderItemAfterSaleRuleDTO orderAfterSaleRule = orderItemVO.getOrderAfterSaleRule();
            if (orderAfterSaleRule != null) {
                OrderItemAfterSaleRuleReq orderItemAfterSaleRuleReq = new OrderItemAfterSaleRuleReq();
                orderItemAfterSaleRuleReq.setApplyEndTime(orderAfterSaleRule.getApplyEndTime());
                orderItemAfterSaleRuleReq.setAutoFinishedTime(orderAfterSaleRule.getAutoFinishedTime());
                orderItemCreateReq.setOrderAfterSaleRule(orderItemAfterSaleRuleReq);
            }

//            OrderCombineItemBO orderCombineItemVO = orderItemVO.getOrderCombineItemBO();
//            if (orderCombineItemVO != null) {
//                OrderCombineItemCreateReq combineItemCreateReq = new OrderCombineItemCreateReq();
//                combineItemCreateReq.setMarketItemId(orderCombineItemVO.getMarketItemId());
//                combineItemCreateReq.setQuantity(orderCombineItemVO.getQuantity());
//                combineItemCreateReq.setOriginalPrice(orderCombineItemVO.getOriginalPrice());
//                orderItemCreateReq.setOrderCombineItemCreateReq(combineItemCreateReq);
//            }
            return orderItemCreateReq;
        }).collect(Collectors.toList());
        createReq.setOrderItemList(itemCreateList);
        // 生成订单地址信息
        createReq.setOrderAddressDTO(orderBO.getOrderAddressDTO());
        Long orderId = RpcResultUtil.handle(orderCommandProvider.create(createReq));

        orderBO.setOrderId(orderId);
    }

    /**
     * 下单接口占用库存处理
     * @param orderBO
     * @param isOpenApi
     * @return
     */
    public boolean placeOrderLockStock(OrderBO orderBO, boolean isOpenApi, boolean needOccupyInventory) {
        // 不需要占用库存，直接更新订单状态为待支付
        if (!needOccupyInventory){
            return updateOrderAfterLockStockSuccess(orderBO);
        }

        //处理三方仓库存, 锁定库存
        boolean lockStockSuccess = false;
        try {
            OrderOccupyReqDTO orderOccupyReqDTO = StockConvert.convert2OrderOccupyReqDTO(orderBO, isOpenApi);
            saleInventoryCommandFacade.orderOccupy(orderOccupyReqDTO);


            lockStockSuccess = true;
        } catch (Exception e) {
            log.error("三方仓冻结库存失败：orderNo={}", orderBO.getOrderNo(), e);
        }

        if (lockStockSuccess) {
            lockStockSuccess = updateOrderAfterLockStockSuccess(orderBO);
        }

        // 冻结库存失败，或者更新订单状态失败，释放库存，取消订单
        if (!lockStockSuccess) {
            cancelOrderAfterLockStockFail(orderBO);
        }

        return lockStockSuccess;
    }


    /**
     * 三方仓冻结库存成功后，更新订单状态为待支付
     *
     * @param orderBO
     * @return
     */
    private boolean updateOrderAfterLockStockSuccess(OrderBO orderBO) {
        try {
            return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
                // 保存供应商运费, 柠季接口需要保留,默认运费0
                supplierDeliveryInfoService.saveSupplierDeliveryInfo(orderBO.getOrderNo(), BigDecimal.ZERO, null, TenantDeliveryEnum.TypeEnum.FOLLOW_SUPPLIER.getType());

//                // 更新订单状态为待支付
                LockStockSuccessReq lockStockSuccessReq = new LockStockSuccessReq();
                lockStockSuccessReq.setOrderId(orderBO.getOrderId());
                lockStockSuccessReq.setTenantId(orderBO.getTenantId());
                RpcResultUtil.handle(orderCommandProvider.lockStockSuccess(lockStockSuccessReq));
                return Boolean.TRUE;
            }));

        } catch (Exception e) {
            log.error("三方仓冻结库存成功后，更新订单状态失败，orderNo={}", orderBO.getOrderNo(), e);
            return false;
        }
    }


    /**
     * 冻结库存失败，或者更新订单状态失败，释放库存，取消订单
     *
     * @param orderBO
     * @return
     */
    public void cancelOrderAfterLockStockFail(OrderBO orderBO) {
        try {
            // 是否需要释放库存
            boolean needReleaseInventory = !specialTenantService.cancelOrderNotNeedReleaseInventory(orderBO.getTenantId());

            if (needReleaseInventory) {
                OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO = StockConvert.convert2ReqDTOWithoutWarehouseNo(orderBO);
                saleInventoryCommandFacade.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);
            }
        } catch (Exception e) {
            log.error("三方仓释放库存失败, orderNo={}", orderBO.getOrderNo(), e);
        }

        try {
            OrderCancelReq orderCancelReq = new OrderCancelReq();
            orderCancelReq.setOrderIds(Lists.newArrayList(orderBO.getOrderId()));
            orderCancelReq.setTenantId(orderBO.getTenantId());
            orderCancelReq.setTimeoutCancel(false);
            RpcResultUtil.handle(orderCommandProvider.cancel(orderCancelReq));
        } catch (Exception e) {
            log.error("三方仓取消订单失败, orderNo={}", orderBO.getOrderNo(), e);
        }
    }

}