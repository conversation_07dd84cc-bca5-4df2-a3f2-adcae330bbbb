package com.cosfo.mall.openapi.service;

import com.cosfo.mall.common.config.OpenApiConfig;
import com.cosfo.mall.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.robot.feishu.SignedFeishuBotUtil;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @author: xiaowk
 * @time: 2023/9/27 下午4:16
 */
@Service
@Slf4j
public class FeishuNotifyService {

    @Resource
    private OpenApiConfig openApiConfig;

    @Async
    public void placeOrderOutofStockNotify(Long tenantId, Long storeId, String address, String errmsg, String customerOrderId,
                                           String tenantName, String warehouseNames) {

        // 发送飞书群告警消息
        StringBuilder text = new StringBuilder();
        text.append("开放平台下单库存不足告警").append(Constants.LINE)
                .append("租户ID：").append(tenantId).append(Constants.LINE)
                .append("租户名称：").append(tenantName).append(Constants.LINE)
                .append("外部订单号：").append(customerOrderId).append(Constants.LINE)
                .append("鲜沐仓库：").append(warehouseNames).append(Constants.LINE)
                .append("门店ID：").append(storeId).append(Constants.LINE)
                .append("下单地址：").append(address).append(Constants.LINE)
                .append("错误信息：").append(errmsg).append(Constants.LINE)
                .append("请及时关注");

        log.info("发送飞书群告警消息,text:{}", text.toString());
        SignedFeishuBotUtil.sendTextMsgAndAtAll(openApiConfig.getOpenApiWarnUrl(), text.toString(), openApiConfig.getOpenApiWarnUrlSign());
    }

}
