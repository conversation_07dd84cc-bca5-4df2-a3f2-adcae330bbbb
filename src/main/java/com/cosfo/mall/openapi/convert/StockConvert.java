package com.cosfo.mall.openapi.convert;

import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.openapi.model.bo.OrderBO;
import com.cosfo.mall.openapi.model.bo.OrderItemBO;
import com.cosfo.mall.openapi.model.dto.PlaceOrderItemDetailDTO;
import com.cosfo.mall.openapi.model.dto.item.MarketItemDTO;
import com.cosfo.mall.stock.model.dto.PreDistributionOrderItemDTO;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * @author: xiaowk
 * @date: 2023/9/20 上午10:55
 */
public class StockConvert {

    public static List<PreDistributionOrderItemDTO> convertPreDisDTO(PlaceOrderItemDetailDTO placeOrderItemDetailDTO, Map<String, Integer> itemQuantityMap) {
        Map<String, MarketItemDTO> marketItemDTOMap = placeOrderItemDetailDTO.getMarketItemDTOMap();
        if (CollectionUtils.isEmpty(marketItemDTOMap) || CollectionUtils.isEmpty(itemQuantityMap)) {
            return null;
        }

        List<PreDistributionOrderItemDTO> resultList = new ArrayList<>(marketItemDTOMap.size());
        for (Entry<String, MarketItemDTO> entry : marketItemDTOMap.entrySet()) {
            String apiCode = entry.getKey();
            MarketItemDTO marketItemDTO = entry.getValue();

            PreDistributionOrderItemDTO dto = new PreDistributionOrderItemDTO();
            dto.setItemId(marketItemDTO.getItemId());
            dto.setGoodsType(marketItemDTO.getGoodsType());
            dto.setSkuId(marketItemDTO.getSkuId());
            dto.setAgentSkuId(marketItemDTO.getAgentSkuId());
            // 使用鲜沐sku
            dto.setAgentSku(marketItemDTO.getAgentSkuCode());
            dto.setQuantity(itemQuantityMap.get(apiCode));
            dto.setAgentTenantId(marketItemDTO.getAgentTenantId());
            resultList.add(dto);
        }

        return resultList;
    }

    /**
     * 组装自营仓冻结库存请求对象，指定库存仓号
     *
     * @param orderBO
     * @return
     */
    public static OrderOccupyBySpecifyWarehouseReqDTO convertToOrderOccupyBySpecifyWarehouseReqDTO(OrderBO orderBO) {
        if (orderBO == null) {
            return null;
        }

        OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = new OrderOccupyBySpecifyWarehouseReqDTO();
        orderOccupyBySpecifyWarehouseReqDTO.setTenantId(orderBO.getTenantId());
        orderOccupyBySpecifyWarehouseReqDTO.setOrderNo(orderBO.getOrderNo());
        orderOccupyBySpecifyWarehouseReqDTO.setOrderType(SaleStockChangeTypeEnum.PLACE_ORDER.getTypeName());
        // 操作单号
        orderOccupyBySpecifyWarehouseReqDTO.setOperatorNo(orderBO.getOrderNo());
        // 幂等单号
        orderOccupyBySpecifyWarehouseReqDTO.setIdempotentNo(orderBO.getOrderNo());
        orderOccupyBySpecifyWarehouseReqDTO.setWarehouseNo(orderBO.getWarehouseNo());
        // 订单明细
        List<OrderItemBO> orderItemDTOList = orderBO.getOrderItemBOS();
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = convertToOrderOccupySkuDetailReqDTOList(orderItemDTOList);
        orderOccupyBySpecifyWarehouseReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);
        return orderOccupyBySpecifyWarehouseReqDTO;
    }

    /**
     * 组装三方仓冻结库存请求对象，不指定库存仓号
     *
     * @param orderBO
     * @param isOpenApi 是否开放平台调用，true,需要传storeId
     * @return
     */
    public static OrderOccupyReqDTO convert2OrderOccupyReqDTO(OrderBO orderBO, boolean isOpenApi) {
        if (orderBO == null) {
            return null;
        }

        OrderAddressDTO orderAddress = orderBO.getOrderAddressDTO();

        OrderOccupyReqDTO orderOccupyReqDTO = new OrderOccupyReqDTO();
        orderOccupyReqDTO.setTenantId(orderBO.getTenantId());
        orderOccupyReqDTO.setWarehouseTenantId(XianmuSupplyTenant.TENANT_ID);
        orderOccupyReqDTO.setOrderNo(orderBO.getOrderNo());
        orderOccupyReqDTO.setOrderType(SaleStockChangeTypeEnum.PLACE_ORDER.getTypeName());
        orderOccupyReqDTO.setOperatorNo(orderBO.getOrderNo());
        orderOccupyReqDTO.setIdempotentNo(orderBO.getOrderNo());
        orderOccupyReqDTO.setProvince(orderAddress.getProvince());
        orderOccupyReqDTO.setCity(orderAddress.getCity());
        orderOccupyReqDTO.setArea(orderAddress.getArea());
        orderOccupyReqDTO.setAddress(orderAddress.getAddress());
        orderOccupyReqDTO.setPoi(orderAddress.getPoiNote());
        orderOccupyReqDTO.setOperatorName(orderBO.getStoreName());
        if (isOpenApi) {
            orderOccupyReqDTO.setContactId(orderBO.getStoreId());
        }

        // 订单明细
        List<OrderItemBO> orderItemDTOList = orderBO.getOrderItemBOS();
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = convertToOrderOccupySkuDetailReqDTOList(orderItemDTOList);
        orderOccupyReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);
        return orderOccupyReqDTO;
    }

    public static List<OrderOccupySkuDetailReqDTO> convertToOrderOccupySkuDetailReqDTOList(List<OrderItemBO> orderItemDTOList) {
        if (orderItemDTOList == null) {
            return Collections.emptyList();
        }
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOList = new ArrayList<>();
        for (OrderItemBO orderItemDTO : orderItemDTOList) {
            OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
            orderOccupySkuDetailReqDTO.setSkuCode(orderItemDTO.getSupplySku());
            orderOccupySkuDetailReqDTO.setOccupyQuantity(orderItemDTO.getAmount());
            orderOccupySkuDetailReqDTO.setSkuTenantId(orderItemDTO.getSkuTenantId());
            orderOccupySkuDetailReqDTOList.add(orderOccupySkuDetailReqDTO);
        }
        return orderOccupySkuDetailReqDTOList;
    }


    /**
     * 组装释放库存请求对象，不指定库存仓号
     *
     * @param orderBO
     * @return
     */
    public static OrderReleaseBySpecifySkuReqDTO convert2ReqDTOWithoutWarehouseNo(OrderBO orderBO) {
        OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO = new OrderReleaseBySpecifySkuReqDTO();
        orderReleaseBySpecifySkuReqDTO.setOrderNo(orderBO.getOrderNo());
        orderReleaseBySpecifySkuReqDTO.setOrderType(SaleStockChangeTypeEnum.CANCEL_ORDER.getTypeName());
        orderReleaseBySpecifySkuReqDTO.setTenantId(orderBO.getTenantId());
        orderReleaseBySpecifySkuReqDTO.setIdempotentNo(orderBO.getOrderNo());
        orderReleaseBySpecifySkuReqDTO.setOperatorNo(orderBO.getOrderNo());
        orderReleaseBySpecifySkuReqDTO.setOperatorName(orderBO.getStoreName());
        // 订单项
        orderReleaseBySpecifySkuReqDTO.setOrderReleaseSkuDetailReqDTOS(convertToOrderReleaseSkuDetailReqDTOList(orderBO.getOrderItemBOS(), null));
        return orderReleaseBySpecifySkuReqDTO;
    }

    public static List<OrderReleaseSkuDetailReqDTO> convertToOrderReleaseSkuDetailReqDTOList(List<OrderItemBO> orderItemDTOList, Long warehouseNo) {
        if (orderItemDTOList == null) {
            return Collections.emptyList();
        }

        List<OrderReleaseSkuDetailReqDTO> orderReleaseSkuDetailReqDTOList = new ArrayList<>();
        for (OrderItemBO orderItemDTO : orderItemDTOList) {
            OrderReleaseSkuDetailReqDTO orderReleaseSkuDetailReqDTO = new OrderReleaseSkuDetailReqDTO();
            orderReleaseSkuDetailReqDTO.setSkuCode(orderItemDTO.getSupplySku());
            orderReleaseSkuDetailReqDTO.setReleaseQuantity(orderItemDTO.getAmount());
            orderReleaseSkuDetailReqDTO.setWarehouseNo(warehouseNo);
            orderReleaseSkuDetailReqDTOList.add(orderReleaseSkuDetailReqDTO);
        }
        return orderReleaseSkuDetailReqDTOList;
    }

    /**
     * 自有商品库存路由判定入参校验
     * @param marketItemDTOList
     * @return
     */
    public static List<PreDistributionOrderItemDTO> convertPreDisDTOByItemCode(List<MarketItemDTO> marketItemDTOList) {
        if (CollectionUtils.isEmpty(marketItemDTOList)) {
            return null;
        }

        List<PreDistributionOrderItemDTO> resultList = new ArrayList<>(marketItemDTOList.size());
        for (MarketItemDTO marketItemDTO : marketItemDTOList) {
            PreDistributionOrderItemDTO dto = new PreDistributionOrderItemDTO();
            dto.setItemId(marketItemDTO.getItemId());
            dto.setGoodsType(marketItemDTO.getGoodsType());
            dto.setSkuId(marketItemDTO.getSkuId());
            dto.setAgentSkuId(marketItemDTO.getAgentSkuId());
            dto.setAgentSku(marketItemDTO.getAgentSkuCode());
            dto.setQuantity(NumberConstant.ONE);
            dto.setAgentTenantId(marketItemDTO.getAgentTenantId());
            resultList.add(dto);
        }
        return resultList;
    }
}
