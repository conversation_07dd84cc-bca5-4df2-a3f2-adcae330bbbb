package com.cosfo.mall.openapi.convert;

import com.cosfo.mall.common.constants.OvertimeOrderEnum;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.openapi.model.bo.OrderBO;
import com.cosfo.ordercenter.client.req.event.OrderSnapshotReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 订单快照转换器
 * <AUTHOR>
 * @Date 2025/4/15 20:55
 * @Version 1.0
 */
@Slf4j
public class OrderSnapshotConverter {

    public static OrderSnapshotReq convert(OrderBO orderBO) {
        OrderSnapshotReq orderSnapshotReq = new OrderSnapshotReq();
        if (!StringUtils.isEmpty(orderBO.getExpectedDeliveryDate())) {
            orderSnapshotReq.setExpectedDeliveryDate(TimeUtils.parseStringToLocalDate(orderBO.getExpectedDeliveryDate()).atStartOfDay());
        }
        if (orderBO.getOverTimeOrder() != null){
            orderSnapshotReq.setOverTimeOrder(orderBO.getOverTimeOrder() ? OvertimeOrderEnum.YES.getCode() : OvertimeOrderEnum.NO.getCode());
        }
        if (orderBO.getOrderPriority() != null){
            orderSnapshotReq.setOrderPriority(orderBO.getOrderPriority());
        }
        orderSnapshotReq.setCustomerOrderExtraNo(orderBO.getCustomerOrderExtraNo());
//        if (null == orderSnapshotReq.getExpectedDeliveryDate()
//                && null == orderSnapshotReq.getOverTimeOrder()
//                && null == orderSnapshotReq.getOrderPriority()){
//            log.info("All extra fields in OrderBO are null, return null OrderSnapshotReq.");
//            return null;
//        }
        return orderSnapshotReq;
    }
}
