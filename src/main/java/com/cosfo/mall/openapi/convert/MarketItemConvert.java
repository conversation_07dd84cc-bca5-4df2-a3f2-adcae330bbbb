package com.cosfo.mall.openapi.convert;

import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.mall.openapi.model.dto.item.MarketItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: xiaowk
 * @date: 2023/9/19 下午2:35
 */
@Mapper
public interface MarketItemConvert {

    MarketItemConvert INSTANCE = Mappers.getMapper(MarketItemConvert.class);

    @Mapping(target = "title", source = "itemTitle")
    MarketItemDTO respToDto(MarketItemInfoResp resp);
}
