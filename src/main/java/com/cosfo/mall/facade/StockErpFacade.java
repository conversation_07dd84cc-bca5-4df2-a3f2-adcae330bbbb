//package com.cosfo.mall.facade;
//
//import com.cosfo.erp.client.stock.StockCommandProvider;
//import com.cosfo.erp.client.stock.req.OrderAfterSaleLockStockReq;
//import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
//import lombok.extern.slf4j.Slf4j;
//import net.xianmu.common.exception.ProviderException;
//import net.xianmu.common.result.DubboResponse;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Component;
//
//
//@Component
//@Slf4j
//public class StockErpFacade {
//    @DubboReference
//    private StockCommandProvider stockCommandProvider;
//    public void unlockStockByAfterSale(OrderAfterSaleDTO orderAfterSaleDTO){
//        OrderAfterSaleLockStockReq req = new OrderAfterSaleLockStockReq ();
//        req.setTenantId(orderAfterSaleDTO.getTenantId ());
//        req.setStoreId(orderAfterSaleDTO.getStoreId ());
//        req.setAccountId(orderAfterSaleDTO.getAccountId ());
//        req.setOrderId(orderAfterSaleDTO.getOrderId ());
//        req.setOrderItemId(orderAfterSaleDTO.getOrderItemId ());
//        req.setAfterSaleOrderNo(orderAfterSaleDTO.getAfterSaleOrderNo ());
//        req.setAmount(orderAfterSaleDTO.getAmount ());
//        DubboResponse<Void> response = stockCommandProvider.unlockStockByAfterSale (req);
//        if (!response.isSuccess ()) {
//            throw new ProviderException ("释放库存失败：" + response.getMsg ());
//        }
//    }
//}
