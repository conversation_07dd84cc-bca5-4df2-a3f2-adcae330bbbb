package com.cosfo.mall.facade.sap.dto;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 伍尔特库存查询响应结果xml
 *
 * @author: xiaowk
 * @time: 2025/4/22 上午11:19
 */
@XmlRootElement(name = "cXML")
public class SapAtpCXML {

    private String version;
    private String material;
    private String timestamp;
    private Response response;

    // Getters and Setters

    @XmlElement(name = "Response")
    public Response getResponse() {
        return response;
    }

    public void setResponse(Response response) {
        this.response = response;
    }

    @XmlAttribute(name = "version")
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @XmlAttribute(name = "Material")
    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    @XmlAttribute(name = "timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }


    public static class Response {
        private Status status;
        private String currAtp;
        private String nextAtp;

        // Getters and Setters

        @XmlElement(name = "Status")
        public Status getStatus() {
            return status;
        }

        public void setStatus(Status status) {
            this.status = status;
        }

        @XmlElement(name = "curr_atp")
        public String getCurrAtp() {
            return currAtp;
        }

        public void setCurrAtp(String currAtp) {
            this.currAtp = currAtp;
        }

        @XmlElement(name = "next_atp")
        public String getNextAtp() {
            return nextAtp;
        }

        public void setNextAtp(String nextAtp) {
            this.nextAtp = nextAtp;
        }
    }

    public static class Status {
        private String code;

        // Getters and Setters

        @XmlAttribute(name = "code")
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }
}


