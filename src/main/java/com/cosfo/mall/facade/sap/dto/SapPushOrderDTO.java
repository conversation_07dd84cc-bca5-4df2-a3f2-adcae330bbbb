package com.cosfo.mall.facade.sap.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 伍尔特订单推送信息
 *
 * @author: xiaowk
 * @time: 2025/4/16 上午11:49
 */
@Data
public class SapPushOrderDTO {

    private List<OrderPO> orderList;

    @Data
    public static class OrderPO {

        /**
         * 鲜沐订单号
         */
        private String orderNo;

        /**
         * 售达方
         */
        private String soldTo;

        /**
         * 送达方
         */
        private String shipTo;

        /**
         * 订单日期 2025-04-16传20250416
         */
        private String orderDate;

        /**
         * 订单时间 09:59:59传095959
         */
        private String orderTime;

        /**
         * 税额 2位小数
         */
        private BigDecimal taxAmount;

        /**
         * 备注
         */
        private String remark;

        /**
         * 订单项商品信息
         * 包含运费项（指定物料号，如果免运费，则不需要传）
         */
        private List<OrderItemPO> itemList;

    }

    @Data
    public static class OrderItemPO {
        /**
         * 行项目号
         */
        private Integer itemNo;

        /**
         * 物料号（18位）
         */
        private String material;

        /**
         * 数量（运费项固定为1）
         */
        private Integer quantity;

        /**
         * 单价（未税）2位小数
         */
        private BigDecimal netPrice;

        /**
         * 价格单位（运费项固定为1）（本次对接固定为1）
         */
        private Integer unit;

        /**
         * 总价（未税）2位小数
         */
        private BigDecimal netValue;
    }

}
