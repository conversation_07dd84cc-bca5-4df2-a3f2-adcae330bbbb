package com.cosfo.mall.facade.sap;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.mall.common.utils.CXMLUtil;
import com.cosfo.mall.facade.sap.dto.SapAtpCXML;
import com.cosfo.mall.facade.sap.dto.SapPushOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBException;
import java.math.BigDecimal;

/**
 * @author: xiaowk
 * @time: 2025/4/21 下午4:17
 */
@Component
@Slf4j
public class SapApiFacade {


    /**
     * 库存查询url
     */
    @NacosValue(value = "${sap.item.atpUrl:http://lewis-test.edataservice.de/httpsync101/3129/atp/}", autoRefreshed = true)
    private String atpUrl;

    /**
     * 订单信息推送url
     */
    @NacosValue(value = "${sap.order.ImportUrl:http://lewis-test.edataservice.de/http101/3129/order_import/}", autoRefreshed = true)
    private String orderImportUrl;


    public BigDecimal queryAtp(String material) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("product_id", material);

        HttpResponse response = HttpRequest.post(atpUrl)
                .header("Content-Type", "application/json")
                .header("Accept", "*/*")
                .body(jsonObject.toString()).execute();

        log.info("请求url：{}, 请求参数：{}", atpUrl, jsonObject.toString());
        String resXml = response.body();
        log.info("响应结果：\n{}", resXml);

        SapAtpCXML sapAtpCXML = null;
        try {
            sapAtpCXML = CXMLUtil.fromXML(resXml, SapAtpCXML.class);
        } catch (JAXBException e) {
            log.warn("解析xml字符串报错，xml={}", resXml, e);
        }

        if (sapAtpCXML == null || sapAtpCXML.getResponse() == null || sapAtpCXML.getResponse().getCurrAtp() == null) {
            return BigDecimal.ZERO;
        }

        return NumberUtil.toBigDecimal(sapAtpCXML.getResponse().getCurrAtp().trim());
    }

    public void pushOrder(SapPushOrderDTO sapPushOrderDTO){

        HttpResponse response = HttpRequest.post(orderImportUrl)
                .header("Content-Type", "application/json")
                .header("Accept", "*/*")
                .body(JSONObject.toJSONString(sapPushOrderDTO)).execute();

        log.info("请求url：{}", orderImportUrl);
        log.info("请求参数：\n{}", JSONObject.toJSONString(sapPushOrderDTO));

        String res = response.body();
        log.info("响应结果：\n{}", res);

    }
}
