package com.cosfo.mall.facade.usercenter;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountQueryProvider;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterTenantAccountFacade {

    @DubboReference
    private TenantAccountQueryProvider tenantAccountQueryProvider;

    /**
     * 获取租户账号信息
     *
     * @param accountId
     * @return
     */
    public TenantAccountResultResp getTenantAccountInfoById(Long accountId) {
        DubboResponse<TenantAccountResultResp> response = tenantAccountQueryProvider.getTenantAccountById(accountId);
        if (!response.isSuccess()) {
            throw new BizException("獲取賬號信息失敗");
        }
        return response.getData();
    }
}

