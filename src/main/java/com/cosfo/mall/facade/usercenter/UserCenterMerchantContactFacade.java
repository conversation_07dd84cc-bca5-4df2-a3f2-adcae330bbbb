package com.cosfo.mall.facade.usercenter;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-29
 * @Description:
 */
@Service
@Slf4j
public class UserCenterMerchantContactFacade {

    @DubboReference
    private MerchantContactQueryProvider merchantContactQueryProvider;

    @DubboReference
    private MerchantContactCommandProvider merchantContactCommandProvider;

    /**
     * 根据指定参数查询门店联系人列表
     *
     * @param
     * @return
     */
    public List<MerchantContactResultResp> getMerchantContacts(MerchantContactQueryReq req) {
        DubboResponse<List<MerchantContactResultResp>> response = merchantContactQueryProvider.getMerchantContacts(req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取查询门店联系人列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }


    public MerchantContactResultResp getStoreContactByStoreId(Long tenantId, Long storeId){
        if (tenantId == null || storeId == null) {
            return null;
        }

        DubboResponse<List<MerchantContactResultResp>> response = merchantContactQueryProvider.getMerchantContactsByStoreId(tenantId, storeId);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        List<MerchantContactResultResp> merchantContactResultResps = response.getData();
        if (CollectionUtil.isEmpty(merchantContactResultResps)) {
            return null;
        }

        return merchantContactResultResps.get(0);
    }


    /**
     * 创建门店联系人
     */
    public Long create(MerchantContactCommandReq req) {
        DubboResponse<Long> response = merchantContactCommandProvider.create(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"门店联系人创建失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 修改门店联系人
     */
    public Boolean update(MerchantContactCommandReq req) {
        DubboResponse<Boolean> response = merchantContactCommandProvider.update(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"门店联系人修改失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 移除门店联系人
     */
    public Boolean remove(MerchantContactCommandReq req) {
        DubboResponse<Boolean> response = merchantContactCommandProvider.remove(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"门店联系人创建失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
}
