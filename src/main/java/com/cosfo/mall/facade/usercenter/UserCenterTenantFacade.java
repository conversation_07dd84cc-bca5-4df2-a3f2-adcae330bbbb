package com.cosfo.mall.facade.usercenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.tenant.TenantProvider;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.provider.TenantCommandProvider;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterTenantFacade {

    @DubboReference
    private TenantQueryProvider tenantQueryProvider;
    @DubboReference
    private TenantProvider tenantProvider;
    @DubboReference
    private TenantCommandProvider tenantCommandProvider;

    /**
     * 获取租户信息
     *
     * @param tenantIds
     * @return
     */
    public List<TenantResultResp> getTenantsByIds(List<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Collections.emptyList();
        }
        DubboResponse<List<TenantResultResp>> response = tenantQueryProvider.getTenantsByIds(tenantIds);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }


    /**
     * 获取租户信息
     *
     * @param tenantId
     * @return
     */
    public TenantResultResp getTenantById(Long tenantId) {
        DubboResponse<TenantResultResp> response = tenantQueryProvider.getTenantById(tenantId);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }


    /**
     * 根据查询条件获取租户信息列表
     *
     * @param req
     * @return
     */
    public List<TenantResultResp> getTenantsByQuery(TenantQueryReq req) {
        DubboResponse<List<TenantResultResp>> response = tenantQueryProvider.getTenants(req);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }

    /**
     * 查詢组合和公司信息
     *
     * @param tenantIds
     * @return
     */
    public List<TenantAndBusinessInfoResultResp> getTenantAndCompanyByIds(List<Long> tenantIds){
        DubboResponse<List<TenantAndBusinessInfoResultResp>> response = tenantQueryProvider.getTenantAndCompanyByIds(tenantIds);
        if(!response.isSuccess()){
            throw new BizException(response.getMsg());
        }

        return response.getData();
    }

    public Boolean getStoreInventorySwitch(Long tenantId) {
        DubboResponse<Boolean> response = tenantProvider.getStoreInventorySwitch(tenantId);
        if(!response.isSuccess()){
            throw new BizException(response.getMsg());
        }

        return response.getData();
    }
}
