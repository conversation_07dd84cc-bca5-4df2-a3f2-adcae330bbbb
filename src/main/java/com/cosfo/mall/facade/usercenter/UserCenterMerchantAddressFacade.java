package com.cosfo.mall.facade.usercenter;

import com.cosfo.mall.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-29
 * @Description:
 */
@Service
@Slf4j
public class UserCenterMerchantAddressFacade {

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;
    @DubboReference
    private MerchantAddressCommandProvider merchantAddressCommandProvider;


    /**
     * 根据指定参数查询门店地址列表
     * @param
     * @return
     */
    public List<MerchantAddressResultResp> getMerchantAddressList(MerchantAddressQueryReq req){
        DubboResponse<List<MerchantAddressResultResp>> response = merchantAddressQueryProvider.getMerchantAddressList(req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取门店地址列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

//    /**
//     * 查询门店默认地址
//     * defaultFlag：是
//     * status：正常
//     * @param
//     * @return
//     */
//    public MerchantAddressResultResp getDefaultMerchantAddress(Long storeId, Long tenantId){
//        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
//        merchantAddressQueryReq.setStoreId(storeId);
//        merchantAddressQueryReq.setTenantId(tenantId);
//        DubboResponse<MerchantAddressResultResp> response = merchantAddressQueryProvider.getDefaultMerchantAddress(merchantAddressQueryReq);
//        if (!response.isSuccess()) {
//            String errorMsg = StringUtils.builderErrorMsg(response,"获取租户信息失败");
//            throw new BizException(errorMsg);
//        }
//        return response.getData();
//    }

    /**
     * 修改地址
     */
    public Boolean update(MerchantAddressCommandReq req) {
        DubboResponse<Boolean> response = merchantAddressCommandProvider.update(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取租户信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
}
