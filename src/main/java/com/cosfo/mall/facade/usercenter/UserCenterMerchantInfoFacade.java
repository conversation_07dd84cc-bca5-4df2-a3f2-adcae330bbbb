package com.cosfo.mall.facade.usercenter;

import com.cosfo.mall.facade.converter.MerchantConverter;
import com.cosfo.mall.merchant.model.po.Merchant;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.provider.MerchantQueryProvider;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Service
@Slf4j
public class UserCenterMerchantInfoFacade {

    @DubboReference
    private MerchantQueryProvider merchantQueryProvider;

    /**
     * 根据租户查找品牌商户信息
     * @param tenantId
     * @return
     */
    public Merchant getMerchantByTenantId(Long tenantId) {
        DubboResponse<MerchantResultResp> response = merchantQueryProvider.getMerchantByTenantId(tenantId);
        if (!response.isSuccess()) {
            throw new BizException("获取门店账号信息失败");
        }
        return MerchantConverter.INSTANCE.respToMerchant(response.getData());
    }
}
