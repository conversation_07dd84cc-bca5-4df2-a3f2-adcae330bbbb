package com.cosfo.mall.facade.usercenter;

import com.cosfo.mall.common.constants.BusinessInformationTypeEnum;
import com.cosfo.mall.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationCommandProvider;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationQueryProvider;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterBusinessInfoFacade {

    @DubboReference
    private BusinessInformationQueryProvider queryProvider;
    @DubboReference
    private BusinessInformationCommandProvider commandProvider;

    /**
     * 获取租户工商信息
     *
     * @param tenantId
     * @return
     */
    public BusinessInformationResultResp getBusinessInfo(Long tenantId) {
        BusinessInformationQueryReq queryReq = new BusinessInformationQueryReq();
        queryReq.setBizId(tenantId);
        queryReq.setType(BusinessInformationTypeEnum.BRAND_USER_TYPE.getType());
        DubboResponse<BusinessInformationResultResp> response = queryProvider.getBusinessInfoByBizIdAndType(queryReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"获取租户信息失败");
            throw new BizException(errorMsg);
        }

        return response.getData();
    }

}
