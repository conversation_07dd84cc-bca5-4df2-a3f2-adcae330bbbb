package com.cosfo.mall.facade.usercenter;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.common.utils.StringUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreAccountFacade {

    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;

    @DubboReference
    private MerchantStoreAccountCommandProvider merchantStoreAccountCommandProvider;

    /**
     * 批量获取门店账号信息
     * @param accountIds
     * @return
     */
    public List<MerchantStoreAccountResultResp> getMerchantStoreAccountInfo(List<Long> accountIds) {
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountByIds(accountIds);
        if (!response.isSuccess()) {
            throw new BizException("获取门店账号信息失败");
        }
        return response.getData();
    }

    /**
     * 获取门店账号信息
     * @param accountId
     * @return
     */
    public MerchantStoreAccountResultResp getMerchantStoreAccountInfo(Long accountId) {
        DubboResponse<MerchantStoreAccountResultResp> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountById(accountId);
        if (!response.isSuccess()) {
            throw new BizException("获取门店账号信息失败");
        }
        return response.getData();
    }


    public MerchantStoreAccountResultResp getMerchantStoreAccountByStoreId(Long tenantId, Long storeId){
        if (tenantId == null || storeId == null) {
            return null;
        }

        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        merchantStoreAccountQueryReq.setStoreId(storeId);
        merchantStoreAccountQueryReq.setDeleteFlag(1);
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccounts(merchantStoreAccountQueryReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        List<MerchantStoreAccountResultResp> merchantStoreAccounts = response.getData();
        if (CollectionUtil.isEmpty(merchantStoreAccounts)) {
            return null;
        }

        return merchantStoreAccounts.get(0);
    }

    /**
     * 获取门店账号信息
     * @param merchantStoreAccountQueryReq
     * @return
     */
    public List<MerchantStoreAccountResultResp> getMerchantStoreList(MerchantStoreAccountQueryReq merchantStoreAccountQueryReq) {
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(merchantStoreAccountQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("获取门店账号信息失败");
        }
        return response.getData();
    }


    /**
     * 分页获取门店账号信息
     * 当前接口会过滤关联 store 状态是关店的数据
     *
     * @param merchantStoreAccountPageReq
     * @param pageQueryReq
     * @return
     */
    public PageInfo<MerchantStoreAccountPageResp> getMerchantStorePage(MerchantStoreAccountPageReq merchantStoreAccountPageReq, PageQueryReq pageQueryReq) {
        DubboResponse<PageInfo<MerchantStoreAccountPageResp>> response = merchantStoreAccountQueryProvider.getAccountPageByPhone(merchantStoreAccountPageReq, pageQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("获取门店账号信息失败");
        }
        return response.getData();
    }


    /**
     * 创建账户
     */
    public Long create(MerchantStoreAccountCommandReq req) {
        DubboResponse<Long> response = merchantStoreAccountCommandProvider.create(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response, "创建账户信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 修改账户
     */
    public Boolean update(MerchantStoreAccountCommandReq req) {
        DubboResponse<Boolean> response = merchantStoreAccountCommandProvider.update(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"修改账户信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 移除账户
     */
    public Boolean remove(MerchantStoreAccountCommandReq req) {
        DubboResponse<Boolean> response = merchantStoreAccountCommandProvider.remove(SystemOriginEnum.COSFO_MALL, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"移除账户信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
}
