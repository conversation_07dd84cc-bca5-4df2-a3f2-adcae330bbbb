package com.cosfo.mall.facade.usercenter;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreGroupQueryProvider;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreGroupFacade {

    @DubboReference
    private MerchantStoreGroupQueryProvider merchantStoreGroupQueryProvider;

    /**
     * 获取门店分组信息
     * @param tenantId
     * @param storeIds
     * @return
     */
    public List<MerchantStoreGroupResultResp> getGroupByStoreIds(Long tenantId, List<Long> storeIds) {
        DubboResponse<List<MerchantStoreGroupResultResp>> response = merchantStoreGroupQueryProvider.getGroupByStoreIds(tenantId, storeIds);
        if (!response.isSuccess()) {
            throw new BizException("获取门店分组失败");
        }
        return response.getData();
    }
}
