package com.cosfo.mall.facade;

import com.cosfo.manage.client.tenant.SpecialTenantQueryProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 特殊定制化租户 查询Facade
 * <AUTHOR>
 * @Date 2025/4/15 15:00
 * @Version 1.0
 */
@Slf4j
@Component
public class SpecialTenantQueryFacade {

    @DubboReference
    private SpecialTenantQueryProvider specialTenantQueryProvider;

    /**
     * 判断租户是否为霸王茶姬租户
     * @param tenantId 租户id
     * @return 霸王茶姬
     */
    public boolean isChageeTenant(Long tenantId){
        try {
            return specialTenantQueryProvider.judgeIsTenantOfChagee(tenantId).getData();
        } catch (Exception e) {
            log.error("判断租户是否为霸王茶姬租户失败 tenantId >>> {}", tenantId, e);
            return false;
        }
    }
}
