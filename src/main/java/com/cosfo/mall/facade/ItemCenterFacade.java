package com.cosfo.mall.facade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ItemCenterFacade {
//    @DubboReference
//    private CostPriceProvider costPriceProvider;
//
//    public List<SummerfarmSkuMallPriceDTO> queryCostPriceByAddressAndSkuIds(List<Long> skuIds, Long tenantId, String province, String city, String area){
//        CostPriceQueryReq req = new CostPriceQueryReq ();
//        req.setSkuIds(skuIds);
//        req.setTenantId(tenantId);
//        req.setProvince(province);
//        req.setCity(city);
//        req.setArea(area);
//        DubboResponse<List<CostPriceResultResp>> resp = costPriceProvider.queryCostPriceByAddressAndSkuIds (req);
//        if (!resp.isSuccess()) {
//            log.error("编辑message.supportNotic：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
//            throw new DefaultServiceException (resp.getMsg());
//        }
//        List<CostPriceResultResp> data = resp.getData ();
//        if(CollectionUtil.isEmpty (data)){
//            return Collections.emptyList ();
//        }
//       return data.stream().map (e->{
//            SummerfarmSkuMallPriceDTO summerfarmSkuMallPriceDto = new SummerfarmSkuMallPriceDTO();
//            BeanUtils.copyProperties(e, summerfarmSkuMallPriceDto);
//            return summerfarmSkuMallPriceDto;
//        }).collect(Collectors.toList());
//    }
}
