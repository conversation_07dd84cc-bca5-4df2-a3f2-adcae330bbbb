package com.cosfo.mall.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.warehouse.model.vo.FastMallVO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.fms.FastMallServiceQueryProvider;
import net.summerfarm.wnc.client.resp.FastMallResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WmsServiceFacade {
    @DubboReference
    private FastMallServiceQueryProvider fastMallServiceQueryProvider;
    public List<FastMallVO> queryFastMallList(){
        DubboResponse<List<FastMallResp>> response = fastMallServiceQueryProvider.queryFastMallList ();
        if (!response.isSuccess ()) {
            throw new ProviderException ("获取物流公司失败：" + response.getMsg ());
        }
        List<FastMallResp> data = response.getData ();
        if(CollectionUtil.isEmpty (data)){
            return Collections.emptyList ();
        }
        return data.stream ().map (e->{
            FastMallVO fastMallVO = new FastMallVO ();
            BeanUtils.copyProperties (e,fastMallVO);
            return fastMallVO;
        }).collect(Collectors.toList());
    }
}
