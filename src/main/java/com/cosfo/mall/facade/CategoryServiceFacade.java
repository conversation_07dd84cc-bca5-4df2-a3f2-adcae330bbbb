//package com.cosfo.mall.facade;
//
//import com.cosfo.erp.client.category.provider.CategoryCommandProvider;
//import com.cosfo.erp.client.category.provider.CategoryQueryProvider;
//import com.cosfo.erp.client.category.req.CategoryQueryReq;
//import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
//import com.cosfo.erp.client.category.resp.CategoryLevelResultResp;
//import com.cosfo.mall.facade.dto.ProductCategoryDTO;
//import com.cosfo.mall.product.convert.CategoryConverter;
//import com.cosfo.mall.product.model.po.Category;
//import net.xianmu.common.result.DubboResponse;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @date : 2023/2/2 9:55
// */
//@Component
//public class CategoryServiceFacade {
//    @DubboReference
//    private CategoryQueryProvider cateGoryQueryProvider;
//    @DubboReference
//    private CategoryCommandProvider cateGoryCommandProvider;
//
//    /**
//     * 通过类目id查询
//     * @param id
//     * @return
//     */
//    public Category selectByPrimaryKey(Long id){
//        DubboResponse<CategoryDetailResultResp> response = cateGoryQueryProvider.selectByPrimaryKey(id);
//        CategoryDetailResultResp resultResp = response.getData();
//        Category category = CategoryConverter.toCategory(resultResp);
//        return category;
//    }
//
//    /**
//     * 新增类目
//     * @param category
//     */
//    public void add(Category category){
//        CategoryQueryReq cateGoryQueryReq = CategoryConverter.toCateGoryQueryReq(category);
//        cateGoryCommandProvider.add(cateGoryQueryReq);
//    }
//
//    /**
//     * 编辑类目
//     * @param category
//     */
//    public void edit(Category category){
//        CategoryQueryReq cateGoryQueryReq = CategoryConverter.toCateGoryQueryReq(category);
//        cateGoryCommandProvider.edit(cateGoryQueryReq);
//    }
//
//    /**
//     * 查询完整类目树
//     *
//     * @param id
//     */
//    public ProductCategoryDTO selectWholeCategory(Long id){
//        DubboResponse<CategoryLevelResultResp> response = cateGoryQueryProvider.selectWholeCategory(id);
//        CategoryLevelResultResp data = response.getData();
//        return CategoryConverter.convertToProductCategoryDTO(data);
//    }
//}
