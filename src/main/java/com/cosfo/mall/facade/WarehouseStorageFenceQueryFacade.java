package com.cosfo.mall.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageFenceRuleResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.exception.I18nProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: fss
 */
@Slf4j
@Component
public class WarehouseStorageFenceQueryFacade {

    @DubboReference
    private WarehouseStorageFenceQueryProvider warehouseStorageFenceQueryProvider;

    /**
     * 根据查询条件获取配送规则
     *
     * @param warehouseStorageListQueryReq
     * @return
     */
    public List<WarehouseStorageFenceRuleResp> queryWarehouseStorageFence(WarehouseStorageFenceQueryReq warehouseStorageListQueryReq) {
        DubboResponse<List<WarehouseStorageFenceRuleResp>> response = warehouseStorageFenceQueryProvider.queryWarehouseStorageFence(warehouseStorageListQueryReq);
        if (!response.isSuccess()) {
            throw new I18nProviderException("根据查询条件获取配送规则失败, 失败原因：{0}", response.getMsg());
        }

        return response.getData();
    }
}
