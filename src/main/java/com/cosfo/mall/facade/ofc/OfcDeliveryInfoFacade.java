package com.cosfo.mall.facade.ofc;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.facade.converter.OfcConvert;
import com.cosfo.mall.facade.dto.DeliveryDate4SkuDTO;
import com.cosfo.mall.facade.dto.DeliveryDateSkuQueryDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.provider.DeliveryInfoQueryProvider;
import net.summerfarm.ofc.client.req.DeliveryDateQueryReq;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2023/7/19 下午7:07
 */
@Component
@Slf4j
public class OfcDeliveryInfoFacade {

    @DubboReference
    private DeliveryInfoQueryProvider deliveryInfoQueryProvider;


    public DeliveryDateQueryResp queryDeliveryDate(MerchantAddressDTO orderAddress, LocalDateTime orderTime, Boolean overTimeOrder) {
        DeliveryDateQueryReq req = new DeliveryDateQueryReq();
        try {
//            LocalDate assignDeliveryDate = getDeliveryTimeByTenantId(orderAddress.getTenantId(), orderAddress.getCity(), orderAddress.getArea());
            req.setCity(orderAddress.getCity());
            req.setArea(orderAddress.getArea());
            if (orderTime == null) {
                orderTime = LocalDateTime.now();
            }
            req.setPayTime(orderTime);
//            req.setAssignDeliveryDate(assignDeliveryDate);
            req.setSource(OfcOrderSourceEnum.SAAS_MALL);
            req.setTenantId(orderAddress.getTenantId());
            req.setStoreId(orderAddress.getStoreId());
            req.setAddOrderFlag(overTimeOrder);

            DubboResponse<DeliveryDateQueryResp> dubboResponse = deliveryInfoQueryProvider.queryDeliveryDate(req);
            if (!dubboResponse.isSuccess()) {
                throw new ProviderException(dubboResponse.getMsg());
            }

            if (dubboResponse.getData() == null || dubboResponse.getData().getDeliveryDate() == null) {
                throw new ProviderException("配送时间为空异常");
            }

            return dubboResponse.getData();
        } catch (ProviderException e) {
            log.error("三方仓订单配送时间查询业务异常，参数：{}，异常信息：", req, e);
            throw new BizException("三方配送时间查询异常");
        } catch (Throwable e) {
            log.error("三方仓订单配送时间查询异常，参数：{}，异常信息：", req, e);
            throw e;
        }
    }


    /**
     * 商品配送规则-仅对苏阁生效
     * <p>
     * 湖北 武汉（除青山区） 每周二配送
     * 湖北 武汉 仅青山区 每周三配送
     * 湖北 荆州、黄冈浠水 每周四配送
     *
     * @return
     */
//    @Deprecated
//    private LocalDate getDeliveryTimeByTenantId(Long tenantId, String city, String area) {
//        LocalDate now = LocalDate.now().plusDays(1);
//        if (sugeTenantId.equals(tenantId)) {
//            if (city.equals("武汉市")) {
//                if (area.equals("青山区")) {
//                    return getLocalDateByWeek(now, DayOfWeek.WEDNESDAY);
//                } else {
//                    return getLocalDateByWeek(now, DayOfWeek.TUESDAY);
//                }
//            } else if (city.equals("荆州市") || (city.equals("黄冈市") && area.equals("浠水县"))) {
//                return getLocalDateByWeek(now, DayOfWeek.THURSDAY);
//            }
//        }
//        return null;
//    }

//    private LocalDate getLocalDateByWeek(LocalDate now, DayOfWeek aimDayOfWeek) {
//        DayOfWeek dayOfWeek = now.getDayOfWeek();
//        if (aimDayOfWeek.compareTo(dayOfWeek) == 0) {
//            LocalDateTime thisTime = LocalDateTime.now();
//            if (thisTime.getHour() < 20) {
//                // 如果是8点前
//                return now.with(aimDayOfWeek);
//            } else {
//                // 如果是8点后
//                return now.with(TemporalAdjusters.next(aimDayOfWeek));
//            }
//        } else if (aimDayOfWeek.compareTo(dayOfWeek) > 0) {
//            // 如果今天已经是周aimDayOfWeek或周aimDayOfWeek之前，返回本周aimDayOfWeek
//            return now.with(aimDayOfWeek);
//        } else {
//            // 否则返回下周aimDayOfWeek
//            return now.with(TemporalAdjusters.next(aimDayOfWeek));
//        }
//    }

    /**
     * 根据sku查询预计配送日期，sku可能含有代销不入仓品
     * @param queryDTO
     * @return
     */
    public List<DeliveryDate4SkuDTO> queryDeliveryDateBySku(DeliveryDateSkuQueryDTO queryDTO) {
        if (queryDTO == null || CollectionUtils.isEmpty(queryDTO.getSkuCodeList())) {
            return Collections.emptyList();
        }
        DeliveryDateQueryReq req = new DeliveryDateQueryReq();
        try {
            if (queryDTO.getPayTime() == null) {
                queryDTO.setPayTime(LocalDateTime.now());
            }
            req.setSource(OfcOrderSourceEnum.SAAS_MALL);
            req.setTenantId(queryDTO.getTenantId());
            req.setStoreId(queryDTO.getStoreId());
            req.setCity(queryDTO.getCity());
            req.setArea(queryDTO.getArea());
            req.setPayTime(Optional.ofNullable(queryDTO.getPayTime()).orElse(LocalDateTime.now()));
            req.setSkuCodeList(queryDTO.getSkuCodeList());
            DubboResponse<DeliveryDateQueryResp> dubboResponse = deliveryInfoQueryProvider.queryDeliveryDate(req);
            if (!dubboResponse.isSuccess()) {
                log.error("查询配送时间错误, req={}, error={}", JSON.toJSONString(req), dubboResponse.getMsg());
                return Collections.emptyList();
            }

            DeliveryDateQueryResp deliveryDateQueryResp = dubboResponse.getData();
            if (deliveryDateQueryResp == null || CollectionUtils.isEmpty(deliveryDateQueryResp.getSkuDeliveryDateList())) {
                log.error("查询配送时间为空异常, req={}", JSON.toJSONString(req));
                return Collections.emptyList();
            }

            return deliveryDateQueryResp.getSkuDeliveryDateList().stream().map(e -> OfcConvert.toDeliveryDate4SkuDTO(e, deliveryDateQueryResp.getFulfillmentType())).collect(Collectors.toList());
        } catch (Throwable e) {
            log.error("三方仓订单配送时间查询异常，参数：{}，异常信息：", JSON.toJSONString(req), e);
        }
        return Collections.emptyList();
    }
}
