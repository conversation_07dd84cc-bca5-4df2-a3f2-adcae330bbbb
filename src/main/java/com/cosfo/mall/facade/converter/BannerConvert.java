package com.cosfo.mall.facade.converter;

import com.cosfo.mall.facade.dto.BannerDTO;
import net.xianmu.marketing.center.client.banner.resp.BannerInfoResp;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class BannerConvert {
    public static List<BannerDTO> toBannerDTOList(List<BannerInfoResp> bannerInfoRespList) {
        if (CollectionUtils.isEmpty(bannerInfoRespList)) {
            return Collections.emptyList();
        }
        List<BannerDTO> bannerDTOS = new ArrayList<>(bannerInfoRespList.size());
        bannerInfoRespList.forEach(bannerInfoResp -> {
            BannerDTO bannerDTO = new BannerDTO();
            bannerDTO.setId(bannerInfoResp.getId());
            bannerDTO.setName(bannerInfoResp.getName());
            bannerDTO.setUrl(bannerInfoResp.getUrl());
            bannerDTO.setLink(bannerInfoResp.getLink());
            bannerDTO.setShowRule(bannerInfoResp.getShowRule());
            bannerDTO.setStatus(bannerInfoResp.getStatus());
            bannerDTO.setStartTime(bannerInfoResp.getStartTime());
            bannerDTO.setEndTime(bannerInfoResp.getEndTime());
            bannerDTO.setType(bannerInfoResp.getType());
            bannerDTO.setDisplayFormat(bannerInfoResp.getDisplayFormat());
            bannerDTO.setTenantId(bannerInfoResp.getTenantId());
            bannerDTO.setWeight(bannerInfoResp.getWeight());
            bannerDTOS.add(bannerDTO);
        });
        return bannerDTOS;
    }
}
