package com.cosfo.mall.facade.converter;

import com.cosfo.mall.facade.dto.SummerFarmDeliveryDTO;
import com.cosfo.mall.facade.input.OrderItemCalcInput;
import com.cosfo.mall.facade.input.SummerFarmDeliveryInput;
import net.summerfarm.mall.client.saas.req.OrderItemCalcReq;
import net.summerfarm.mall.client.saas.req.SummerFarmDeliveryReq;
import net.summerfarm.mall.client.saas.resp.SummerFarmDeliveryResp;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
public class DeliveryFeeConverter {

    /**
     * 转化为 SummerFarmDeliveryDTO
     *
     * @param summerFarmDeliveryResp
     * @return
     */
    public static SummerFarmDeliveryDTO convertToSummerFarmDeliveryDTO(SummerFarmDeliveryResp summerFarmDeliveryResp){
        if (summerFarmDeliveryResp == null) {
            return null;
        }

        SummerFarmDeliveryDTO summerFarmDeliveryDTO = new SummerFarmDeliveryDTO();
        summerFarmDeliveryDTO.setDeliveryFee(summerFarmDeliveryResp.getDeliveryFee());
        summerFarmDeliveryDTO.setDeliveryFeeRule(summerFarmDeliveryResp.getDeliveryFeeRule());
        summerFarmDeliveryDTO.setHavingRule(summerFarmDeliveryResp.getHavingRule());
        return summerFarmDeliveryDTO;
    }

    /**
     * 转化为SummerFarmDeliveryReq
     *
     * @param summerFarmDeliveryInput
     * @return
     */
    public static SummerFarmDeliveryReq convertToSummerFarmDeliveryReq(SummerFarmDeliveryInput summerFarmDeliveryInput){
        if (summerFarmDeliveryInput == null) {
            return null;
        }

        SummerFarmDeliveryReq summerFarmDeliveryReq = new SummerFarmDeliveryReq();
        summerFarmDeliveryReq.setCity(summerFarmDeliveryInput.getCity());
        summerFarmDeliveryReq.setArea(summerFarmDeliveryInput.getArea());
        List<OrderItemCalcInput> orderItemDTOList = summerFarmDeliveryInput.getOrderItemDTOList();
        List<OrderItemCalcReq> orderItemCalcReqs = orderItemDTOList.stream().map(item -> {
            OrderItemCalcReq orderItemCalcDTO = new OrderItemCalcReq();
            orderItemCalcDTO.setCalcPartDeliveryFee(item.getCalcPartDeliveryFee());
            orderItemCalcDTO.setSupplySkuId(item.getSupplySkuId());
            return orderItemCalcDTO;
        }).collect(Collectors.toList());
        summerFarmDeliveryReq.setOrderItemDTOList(orderItemCalcReqs);
        return summerFarmDeliveryReq;
    }
}
