package com.cosfo.mall.facade.converter;

import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.model.dto.ProductSkuDTO;
import com.cosfo.mall.product.model.po.ProductAgentSkuMapping;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/9/27 17:26
 * @Description:
 */
@Mapper
public interface ProductFacadeConvert {
    ProductFacadeConvert INSTANCE = Mappers.getMapper(ProductFacadeConvert.class);


    List<ProductAgentSkuDTO> convert2Dtos(List<ProductsMappingResp> resps);

    @Mapping(source = "sku", target = "agentSkuCode")
    @Mapping(source = "id",target = "agentId")
    ProductAgentSkuDTO convert2Dto(ProductsMappingResp resp);

    List<ProductSkuDTO> convert2Skus(List<ProductSkuDetailResp> resp);

    @Mapping(source = "firstCategory", target = "firstCategoryName")
    @Mapping(source = "secondCategory", target = "secondCategoryName")
    @Mapping(source = "categoryName", target = "thirdCategoryName")
    ProductSkuDTO convert2Sku(ProductSkuDetailResp resp);


    List<ProductAgentSkuDTO> convertEntity2Dtos(List<ProductAgentSkuMapping> resps);

    @Mapping(source = "id",target = "agentId")
    ProductAgentSkuDTO convertEntity2Dto(ProductAgentSkuMapping resp);
}
