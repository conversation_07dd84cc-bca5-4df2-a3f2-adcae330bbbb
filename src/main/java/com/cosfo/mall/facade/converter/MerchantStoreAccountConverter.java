package com.cosfo.mall.facade.converter;

import com.cosfo.mall.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreAccount;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Mapper
public interface MerchantStoreAccountConverter {

    MerchantStoreAccountConverter INSTANCE = Mappers.getMapper(MerchantStoreAccountConverter.class);

    /**
     * rpc返回数据列表转换为dto列表
     * @param respList
     * @return
     */
    List<MerchantStoreAccountDTO> respListToDtoList(List<MerchantStoreAccountResultResp> respList);

    /**
     * merchantStoreAccount转换为req
     * @param merchantStoreAccount
     * @return
     */
    MerchantStoreAccountCommandReq accountToCommangReq(MerchantStoreAccount merchantStoreAccount);

    MerchantStoreAccount respToAccount(MerchantStoreAccountResultResp resp);

    MerchantStoreAccountCommandReq respToCommandReq(MerchantStoreAccountResultResp merchantStoreAccount);
}
