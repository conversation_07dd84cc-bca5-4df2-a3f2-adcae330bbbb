package com.cosfo.mall.facade.converter;

import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import com.cosfo.mall.marketing.model.vo.ActivityItemConfigVO;
import com.cosfo.mall.marketing.model.vo.ActivitySkuDetailVO;
import net.xianmu.marketing.center.client.activity.resp.ActivityBasicInfoResp;
import net.xianmu.marketing.center.client.activity.resp.ActivitySkuDetailResp;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: lzh
 * @Date: 2025/10/16 15:25
 * @Param:
 * @Return:
 * @Description:
 **/
public class ActivityConvert {

    public static ActivityInfoVO toActivityInfoVO(ActivityBasicInfoResp resp) {
        if (resp == null) {
            return null;
        }

        ActivityInfoVO activityInfoVO = new ActivityInfoVO();
        activityInfoVO.setId(resp.getBasicInfoId());
        activityInfoVO.setName(resp.getName());
        activityInfoVO.setStartTime(resp.getStartTime());
        activityInfoVO.setEndTime(resp.getEndTime());
        activityInfoVO.setIsPermanent(resp.getIsPermanent());
        activityInfoVO.setTag(resp.getTag());
        activityInfoVO.setRemark(resp.getRemark());
        activityInfoVO.setType(resp.getType());

        ActivityItemConfigVO activityItemConfigVO = ActivityConvert.toActivityItemConfigVO(resp);
        activityInfoVO.setActivityItemConfigVO(activityItemConfigVO);
        return activityInfoVO;
    }

    private static ActivityItemConfigVO toActivityItemConfigVO(ActivityBasicInfoResp resp) {
        ActivityItemConfigVO activityItemConfigVO = new ActivityItemConfigVO();
        if (resp.getActivityItemConfigResp() == null) {
            return activityItemConfigVO;
        }

        activityItemConfigVO.setPricingType(resp.getActivityItemConfigResp().getPricingType());
        activityItemConfigVO.setPricingTypeExt(resp.getActivityItemConfigResp().getPricingTypeExt());
        activityItemConfigVO.setGoodSelectWay(resp.getActivityItemConfigResp().getGoodSelectWay());
        activityItemConfigVO.setDiscount(resp.getActivityItemConfigResp().getDiscount());
        activityItemConfigVO.setDiscountPercentage(resp.getActivityItemConfigResp().getDiscountPercentage());
        activityItemConfigVO.setSkuDetailList(ActivityConvert.toActivitySkuDetailVOList(resp.getActivityItemConfigResp().getActivitySkuDetailRespList()));
        return activityItemConfigVO;
    }

    private static List<ActivitySkuDetailVO> toActivitySkuDetailVOList(List<ActivitySkuDetailResp> activitySkuDetailRespList) {
        List<ActivitySkuDetailVO> activitySkuDetailVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(activitySkuDetailRespList)) {
            return activitySkuDetailVOList;
        }

        for (ActivitySkuDetailResp activitySkuDetailResp : activitySkuDetailRespList) {
            ActivitySkuDetailVO activitySkuDetailVO = new ActivitySkuDetailVO();
            activitySkuDetailVO.setItemId(activitySkuDetailResp.getItemId());
            activitySkuDetailVO.setBlackWhiteType(activitySkuDetailResp.getBlackWhiteType());
            activitySkuDetailVO.setTitle(activitySkuDetailResp.getTitle());
            activitySkuDetailVO.setSapSkuCode(activitySkuDetailResp.getSapSkuCode());
            activitySkuDetailVO.setSapMaterialCode(activitySkuDetailResp.getSapMaterialCode());
            activitySkuDetailVO.setFirstClassificationName(activitySkuDetailResp.getFirstClassificationName());
            activitySkuDetailVO.setSecondClassificationName(activitySkuDetailResp.getSecondClassificationName());
            activitySkuDetailVO.setBrandName(activitySkuDetailResp.getBrandName());
            activitySkuDetailVOList.add(activitySkuDetailVO);
        }
        return activitySkuDetailVOList;
    }
}
