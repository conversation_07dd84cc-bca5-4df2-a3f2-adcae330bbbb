package com.cosfo.mall.facade.converter;

import com.cofso.preferential.client.req.OptAvailableQuantityReq;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProductSkuPreferentialCostPriceFacadeConvert {
    ProductSkuPreferentialCostPriceFacadeConvert INSTANCE = Mappers.getMapper(ProductSkuPreferentialCostPriceFacadeConvert.class);

    List<OptAvailableQuantityReq> convert2OptAvailableQuantityReqList(List<OrderItemDTO> orderItemDTOList);

    @Mapping(source = "skuId",target = "skuId")
    @Mapping(source = "amount",target = "optQuantity")
    OptAvailableQuantityReq convert2OptAvailableQuantityReq(OrderItemDTO orderItemDTO);
}
