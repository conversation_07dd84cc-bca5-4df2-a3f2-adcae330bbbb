package com.cosfo.mall.facade.converter;

import com.cofso.item.client.req.CombineMarketQueryInputReq;
import com.cofso.item.client.resp.CombineItemResp;
import com.cofso.item.client.resp.CombineMarketDetailResp;
import com.cosfo.mall.facade.dto.CombineMarketDetailDTO;
import com.cosfo.mall.facade.dto.CombineMarketQueryInputDTO;
import com.cosfo.mall.market.model.dto.CombineMarketItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 10:31
 * @Description:
 */
@Mapper
public interface CombineMarketConvert {
    CombineMarketConvert INSTANCE = Mappers.getMapper(CombineMarketConvert.class);

    List<CombineMarketItemDTO> convert2Combines(List<CombineItemResp> resps);

    /**
     * 转化为 CombineMarketQueryInputReq
     *
     * @param combineMarketQueryInputDTO
     * @return
     */
    CombineMarketQueryInputReq convertToCombineMarketQueryInputReq(CombineMarketQueryInputDTO combineMarketQueryInputDTO);

    /**
     * 转化为CombineMarketDetailDTO
     *
     * @param combineMarketDetailResp
     * @return
     */
    CombineMarketDetailDTO convertToCombineMarketDetailDTO(CombineMarketDetailResp combineMarketDetailResp);
}
