package com.cosfo.mall.facade.converter;

import com.cosfo.mall.delivery.model.vo.FulfillmentDeliveryVO;
import com.cosfo.mall.facade.dto.DeliveryDate4SkuDTO;
import net.summerfarm.ofc.client.resp.FulfillmentWaitDeliveryResp;
import net.summerfarm.ofc.client.resp.SkuDeliveryDateResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/13
 */
public class OfcConvert {

    /**
     * 转化为FulfillmentDeliveryVOList
     *
     * @param list
     * @return
     */
    public static List<FulfillmentDeliveryVO> convertToList(List<FulfillmentWaitDeliveryResp> list){
        if (list == null) {
            return Collections.emptyList();
        }

        List<FulfillmentDeliveryVO> fulfillmentDeliveryVOList = new ArrayList<>();
        for (FulfillmentWaitDeliveryResp fulfillmentWaitDeliveryResp : list) {
            fulfillmentDeliveryVOList.add(toFulfillmentDeliveryVO(fulfillmentWaitDeliveryResp));
        }
        return fulfillmentDeliveryVOList;
    }

    public static FulfillmentDeliveryVO toFulfillmentDeliveryVO(FulfillmentWaitDeliveryResp fulfillmentWaitDeliveryResp) {
        if (fulfillmentWaitDeliveryResp == null) {
            return null;
        }
        FulfillmentDeliveryVO fulfillmentDeliveryVO = new FulfillmentDeliveryVO();
        fulfillmentDeliveryVO.setOrderNo(fulfillmentWaitDeliveryResp.getOrderNo());
        fulfillmentDeliveryVO.setItemId(fulfillmentWaitDeliveryResp.getItemId());
        fulfillmentDeliveryVO.setSkuCode(fulfillmentWaitDeliveryResp.getSkuCode());
        fulfillmentDeliveryVO.setQuantity(fulfillmentWaitDeliveryResp.getQuantity());
        fulfillmentDeliveryVO.setPics(fulfillmentWaitDeliveryResp.getPics());
        return fulfillmentDeliveryVO;
    }

    public static DeliveryDate4SkuDTO toDeliveryDate4SkuDTO(SkuDeliveryDateResp skuDeliveryDateResp, Integer fulfillmentType) {
        if(skuDeliveryDateResp == null){
            return null;
        }
        DeliveryDate4SkuDTO deliveryDate4SkuDTO = new DeliveryDate4SkuDTO();
        deliveryDate4SkuDTO.setSku(skuDeliveryDateResp.getSku());
        deliveryDate4SkuDTO.setDeliveryDate(skuDeliveryDateResp.getDeliveryDate());
        deliveryDate4SkuDTO.setDeliveryCloseTime(skuDeliveryDateResp.getDeliveryCloseTime());
        deliveryDate4SkuDTO.setFulfillmentType(fulfillmentType);
        return deliveryDate4SkuDTO;
    }

}
