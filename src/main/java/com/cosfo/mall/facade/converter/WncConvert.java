package com.cosfo.mall.facade.converter;

import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * 描述:
 * @author: <EMAIL>
 * @创建时间: 2023/4/13
 */
public class WncConvert {

    /**
     * 转化为 List<WarehouseStorageVO>
     *
     * @return
     */
    public static List<WarehouseStorageVO> convertToList(List<WarehouseStorageResp> warehouseStorageResps){

        if (warehouseStorageResps == null) {
            return Collections.emptyList();
        }
        List<WarehouseStorageVO> warehouseStorageVOList = new ArrayList<>();
        for (WarehouseStorageResp warehouseStorageResp : warehouseStorageResps) {
            warehouseStorageVOList.add(toWarehouseStorageVO(warehouseStorageResp));
        }
        return warehouseStorageVOList;
    }

    public static WarehouseStorageVO toWarehouseStorageVO(WarehouseStorageResp warehouseStorageResp) {
        if (warehouseStorageResp == null) {
            return null;
        }
        WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
        warehouseStorageVO.setId(warehouseStorageResp.getId());
        warehouseStorageVO.setWarehouseNo(warehouseStorageResp.getWarehouseNo());
        warehouseStorageVO.setWarehouseName(warehouseStorageResp.getWarehouseName());
        warehouseStorageVO.setAddress(warehouseStorageResp.getAddress());
        warehouseStorageVO.setPersonContact(warehouseStorageResp.getPersonContact());
        warehouseStorageVO.setPhone(warehouseStorageResp.getPhone());
        warehouseStorageVO.setTenantId(warehouseStorageResp.getTenantId());
        warehouseStorageVO.setWarehouseSourceEnum(warehouseStorageResp.getWarehouseSourceEnum());
        return warehouseStorageVO;
    }
}
