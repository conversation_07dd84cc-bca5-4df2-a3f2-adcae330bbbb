package com.cosfo.mall.facade.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.enums.MarketItemUnitTypeEnum;
import com.cofso.item.client.req.MarketItemSimpleInfoResp;
import com.cofso.item.client.resp.*;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.vo.CombineItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 17:33
 * @Description:
 */
@Mapper
public interface MarketFacadeConvert {
    MarketFacadeConvert INSTANCE = Mappers.getMapper(MarketFacadeConvert.class);

    @Mapping(source = "itemId", target = "id")
    @Mapping(source = "itemTitle", target = "title")
    MarketItemDTO convert2dto(MarketItemInfoResp resp);

    @Mapping(source = "marketItemId", target = "id")
    @Mapping(source = "price", target = "basePrice")
    @Mapping(source = "combineItemRespList", target = "subItemList")
    MarketItemDTO convert2dto(MarketItem4StoreResp resp);

    @Mapping(source = "marketItemId", target = "id")
    MarketItemDTO convert2dto(MarketItemSimpleInfoResp resp);

    @Mapping(source = "itemId", target = "id")
    @Mapping(source = "price", target = "basePrice")
    @Mapping(source = "combineItemRespList", target = "subItemList")
    @Mapping(source = "packingQuantity", target = "packageQuantity")
    MarketItemDTO convert2dto(MarketItemDetail4StoreResp resp);

    @Mapping(source = "marketItemId", target = "itemId")
    CombineItemVO convert2Vo(CombineItemResp resp);

    @Named("getStoreInventoryUnit")
    static String getStoreInventoryUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }
    @Named("getUnitByEnum")
    static MarketItemUnitResp getUnitByEnum(List<MarketItemUnitResp> marketItemUnitList,MarketItemUnitTypeEnum storeOrderingUnit) {
        if(CollectionUtil.isEmpty (marketItemUnitList)){
            return null;
        }
        Optional<MarketItemUnitResp> first = marketItemUnitList.stream ().filter (e -> storeOrderingUnit.getCode ().equals (e.getUnitType ())).findFirst ();
        if(first.isPresent ()){
            return first.get ();
        }
        return null;
    }
    @Named("getStoreOrderingInventoryUnitMultiple")
    static BigDecimal getStoreOrderingInventoryUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp orderingUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_ORDERING_UNIT);
        if(inventoryUnit!=null && orderingUnit!=null){
            return inventoryUnit.getStoreOrderingUnitMultiple ().divide (orderingUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }
}
