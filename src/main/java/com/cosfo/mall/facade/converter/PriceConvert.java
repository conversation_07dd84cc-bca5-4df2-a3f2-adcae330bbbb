package com.cosfo.mall.facade.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.req.LadderPrice;
import com.cofso.item.client.resp.ItemPriceDetailResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cosfo.mall.facade.dto.ItemPriceDetailDTO;
import com.cosfo.mall.facade.dto.PriceDetailDTO;
import com.cosfo.mall.market.model.vo.LadderPriceVO;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/11
 */
public class PriceConvert {

    /**
     * 转化为PriceDetailDTOMap
     *
     * @param priceDetailRespMap
     * @return
     */
    public static Map<Long, PriceDetailDTO> convertToPriceDetailDTOMap(Map<Long, PriceDetailResp> priceDetailRespMap){
        Map<Long, PriceDetailDTO> map = new HashMap<>();
        Set<Long> itemIds = priceDetailRespMap.keySet();
        itemIds.forEach(itemId -> {
            PriceDetailResp priceDetailResp = priceDetailRespMap.get(itemId);
            PriceDetailDTO priceDetailDTO = convertToPriceDetailDTO(priceDetailResp);
            map.put(itemId, priceDetailDTO);
        });

        return map;
    }

    /**
     * 转化为PriceDetailDTO
     *
     * @param priceDetailResp
     * @return
     */
    public static PriceDetailDTO convertToPriceDetailDTO(PriceDetailResp priceDetailResp){
        if (priceDetailResp == null) {
            return null;
        }

        PriceDetailDTO priceDetailDTO = new PriceDetailDTO();
        priceDetailDTO.setPrice(priceDetailResp.getPrice());
        priceDetailDTO.setCostPrice(priceDetailResp.getCostPrice());
        priceDetailDTO.setMarketItemPrice(priceDetailResp.getMarketItemPrice());
        if(CollectionUtil.isNotEmpty (priceDetailResp.getLadderPrices())) {
            priceDetailDTO.setLadderPrices (priceDetailResp.getLadderPrices().stream().map (e->{
                LadderPriceVO ladderPrice = new LadderPriceVO ();
                ladderPrice.setPrice(e.getPrice ());
                ladderPrice.setUnit(e.getUnit ());
                return ladderPrice;
            }).collect(Collectors.toList()));
        }
        return priceDetailDTO;
    }

    public static List<ItemPriceDetailDTO> convertToItemPriceDetailDTO(List<ItemPriceDetailResp> responseData){
        if (responseData == null) {
            return Collections.emptyList();
        }
        List<ItemPriceDetailDTO> itemPriceDetailDTOList = new ArrayList<>();
        for (ItemPriceDetailResp itemPriceDetailResp : responseData) {
            itemPriceDetailDTOList.add(toItemPriceDetailDTO(itemPriceDetailResp));
        }
        return itemPriceDetailDTOList;
    }

    public static ItemPriceDetailDTO toItemPriceDetailDTO(ItemPriceDetailResp itemPriceDetailResp) {
        if (itemPriceDetailResp == null) {
            return null;
        }
        ItemPriceDetailDTO itemPriceDetailDTO = new ItemPriceDetailDTO();
        itemPriceDetailDTO.setItemId(itemPriceDetailResp.getItemId());
        itemPriceDetailDTO.setPrice(itemPriceDetailResp.getPrice());
        itemPriceDetailDTO.setCostPrice(itemPriceDetailResp.getCostPrice());
        itemPriceDetailDTO.setMarketItemPrice(itemPriceDetailResp.getMarketItemPrice());
        List<ItemPriceDetailResp> subItemPriceDetails = itemPriceDetailResp.getSubItemPriceDetails();
        if(!CollectionUtils.isEmpty(subItemPriceDetails)){
            List<ItemPriceDetailDTO> itemPriceDetailDTOS = convertToItemPriceDetailDTO(subItemPriceDetails);
            itemPriceDetailDTO.setSubItemPriceDetails(itemPriceDetailDTOS);
        }
        return itemPriceDetailDTO;
    }
}
