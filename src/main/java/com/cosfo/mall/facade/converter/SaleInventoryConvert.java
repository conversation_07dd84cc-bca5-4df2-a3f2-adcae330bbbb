package com.cosfo.mall.facade.converter;

import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.model.po.OrderAddress;
import net.xianmu.inventory.client.saleinventory.dto.req.*;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/10
 */
public class SaleInventoryConvert {

    /**
     * 组装自营仓冻结库存请求对象，指定库存仓号
     * @param orderDto
     * @return
     */
    public static OrderOccupyBySpecifyWarehouseReqDTO convertToOrderOccupyBySpecifyWarehouseReqDTO(OrderDTO orderDto){
        if(orderDto == null){
            return null;
        }

        OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = new OrderOccupyBySpecifyWarehouseReqDTO();
        orderOccupyBySpecifyWarehouseReqDTO.setTenantId(orderDto.getTenantId());
        orderOccupyBySpecifyWarehouseReqDTO.setOrderNo(orderDto.getOrderNo());
        orderOccupyBySpecifyWarehouseReqDTO.setOrderType(SaleStockChangeTypeEnum.PLACE_ORDER.getTypeName());
        // 操作单号
        orderOccupyBySpecifyWarehouseReqDTO.setOperatorNo(orderDto.getOrderNo());
        // 幂等单号
        orderOccupyBySpecifyWarehouseReqDTO.setIdempotentNo(orderDto.getOrderNo());
        orderOccupyBySpecifyWarehouseReqDTO.setWarehouseNo(orderDto.getWarehouseNo());
        // 订单明细
        List<OrderItemDTO> orderItemDTOList = orderDto.getOrderItemDTOList();
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = convertToOrderOccupySkuDetailReqDTOList(orderItemDTOList);
        orderOccupyBySpecifyWarehouseReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);
        return orderOccupyBySpecifyWarehouseReqDTO;
    }

    public static  List<OrderOccupySkuDetailReqDTO> convertToOrderOccupySkuDetailReqDTOList(List<OrderItemDTO> orderItemDTOList){
        if (orderItemDTOList == null) {
            return Collections.emptyList();
        }
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOList = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            orderOccupySkuDetailReqDTOList.add(toOrderOccupySkuDetailReqDTO(orderItemDTO));
        }
        return orderOccupySkuDetailReqDTOList;
    }

    public static OrderOccupySkuDetailReqDTO toOrderOccupySkuDetailReqDTO(OrderItemDTO orderItemDTO) {
        if (orderItemDTO == null) {
            return null;
        }
        OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
        orderOccupySkuDetailReqDTO.setSkuCode(orderItemDTO.getSupplySku());
        orderOccupySkuDetailReqDTO.setOccupyQuantity(orderItemDTO.getAmount());
        orderOccupySkuDetailReqDTO.setSkuTenantId(orderItemDTO.getSkuTenantId());
        // TODO 预售presaleSwitch
        orderOccupySkuDetailReqDTO.setMarketItemPresaleSwitch(orderItemDTO.getPresaleSwitch());
        return orderOccupySkuDetailReqDTO;
    }

    public static List<OrderReleaseSkuDetailReqDTO> convertToOrderReleaseSkuDetailReqDTOList(List<OrderItemDTO> orderItemDTOList, Long warehouseNo){
        if (orderItemDTOList == null) {
            return Collections.emptyList();
        }

        List<OrderReleaseSkuDetailReqDTO> orderReleaseSkuDetailReqDTOList = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            orderReleaseSkuDetailReqDTOList.add(toOrderReleaseSkuDetailReqDTO(orderItemDTO, warehouseNo));
        }
        return orderReleaseSkuDetailReqDTOList;
    }

    public static OrderReleaseSkuDetailReqDTO toOrderReleaseSkuDetailReqDTO(OrderItemDTO orderItemDTO,Long warehouseNo) {
        if (orderItemDTO == null) {
            return null;
        }
        OrderReleaseSkuDetailReqDTO orderReleaseSkuDetailReqDTO = new OrderReleaseSkuDetailReqDTO();
        orderReleaseSkuDetailReqDTO.setSkuCode(orderItemDTO.getSupplySku());
        orderReleaseSkuDetailReqDTO.setReleaseQuantity(orderItemDTO.getAmount());
        orderReleaseSkuDetailReqDTO.setWarehouseNo(warehouseNo);
        return orderReleaseSkuDetailReqDTO;
    }
}
