package com.cosfo.mall.facade.converter;

import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.model.po.Tenant;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface TenantInfoMapper {

    TenantInfoMapper INSTANCE = Mappers.getMapper(TenantInfoMapper.class);

    /**
     * 转换为Tenant模型
     * @param tenantResultResp
     * @return
     */
    @Mapping(target = "createTime", expression = "java(com.cosfo.mall.common.utils.TimeUtils.localDateTimeConvertDate(tenantResultResp.getCreateTime()))")
    @Mapping(target = "updateTime", expression = "java(com.cosfo.mall.common.utils.TimeUtils.localDateTimeConvertDate(tenantResultResp.getUpdateTime()))")
    Tenant respToTenant(TenantResultResp tenantResultResp);

    /**
     * 转换为TenantDTO模型
     * @param tenantResultResp
     * @return
     */
    TenantDTO respToDto(TenantResultResp tenantResultResp);

}
