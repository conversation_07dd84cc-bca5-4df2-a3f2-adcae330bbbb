package com.cosfo.mall.facade.converter;

import com.cosfo.mall.facade.dto.WarehouseInventoryDTO;
import com.cosfo.mall.facade.dto.WarehouseInventoryQueryDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.QueryWarehouseAddressSkuInventoryReq;
import net.xianmu.inventory.client.saleinventory.dto.req.WarehouseInventoryAggregatedByAddressReq;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseInventoryAggregatedByAddressDetailResp;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseInventoryByAreaDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/16
 */
public class WarehouseInventoryConverter {


    public static WarehouseInventoryAggregatedByAddressReq convertWarehouseInventoryAggregatedByAddressReq(WarehouseInventoryQueryDTO warehouseInventoryQueryDTO) {
        if (warehouseInventoryQueryDTO == null) {
            return null;
        }
        WarehouseInventoryAggregatedByAddressReq warehouseInventoryByAreaQueryReq = new WarehouseInventoryAggregatedByAddressReq();
        warehouseInventoryByAreaQueryReq.setProvince(warehouseInventoryQueryDTO.getProvince());
        warehouseInventoryByAreaQueryReq.setCity(warehouseInventoryQueryDTO.getCity());
        warehouseInventoryByAreaQueryReq.setArea(warehouseInventoryQueryDTO.getArea());
        warehouseInventoryByAreaQueryReq.setPoi(warehouseInventoryQueryDTO.getPoi());
        warehouseInventoryByAreaQueryReq.setAddress(warehouseInventoryQueryDTO.getAddress());
        warehouseInventoryByAreaQueryReq.setTenantId(warehouseInventoryQueryDTO.getTenantId());
        warehouseInventoryByAreaQueryReq.setSkuDetailList(warehouseInventoryQueryDTO.getSkuDetailList().stream().map(e -> e.buildReq()).collect(Collectors.toList()));
        warehouseInventoryByAreaQueryReq.setContactId(warehouseInventoryQueryDTO.getStoreId());
        return warehouseInventoryByAreaQueryReq;
    }

    public static List<WarehouseInventoryDTO> convertToListByResp(List<WarehouseInventoryAggregatedByAddressDetailResp> warehouseInventoryList) {
        if (warehouseInventoryList == null) {
            return Collections.emptyList();
        }
        List<WarehouseInventoryDTO> warehouseInventoryDTOList = new ArrayList<>();
        for (WarehouseInventoryAggregatedByAddressDetailResp warehouseInventoryByAreaDTO : warehouseInventoryList) {
            warehouseInventoryDTOList.add(toWarehouseInventoryDTO(warehouseInventoryByAreaDTO));
        }
        return warehouseInventoryDTOList;
    }

    public static WarehouseInventoryDTO toWarehouseInventoryDTO(WarehouseInventoryAggregatedByAddressDetailResp warehouseInventoryByAreaDTO) {
        if (warehouseInventoryByAreaDTO == null) {
            return null;
        }
        WarehouseInventoryDTO warehouseInventoryDTO = new WarehouseInventoryDTO();
        warehouseInventoryDTO.setSkuCode(warehouseInventoryByAreaDTO.getSkuCode());
        warehouseInventoryDTO.setInventory(warehouseInventoryByAreaDTO.getInventory());
        warehouseInventoryDTO.setQualityDate(warehouseInventoryByAreaDTO.getQualityDate());
        return warehouseInventoryDTO;
    }



    @Deprecated
    public static QueryWarehouseAddressSkuInventoryReq convertQueryWarehouseAddressSkuInventoryReq(WarehouseInventoryQueryDTO warehouseInventoryQueryDTO) {
        if (warehouseInventoryQueryDTO == null) {
            return null;
        }
        QueryWarehouseAddressSkuInventoryReq warehouseInventoryByAreaQueryReq = new QueryWarehouseAddressSkuInventoryReq();
        warehouseInventoryByAreaQueryReq.setProvince(warehouseInventoryQueryDTO.getProvince());
        warehouseInventoryByAreaQueryReq.setCity(warehouseInventoryQueryDTO.getCity());
        warehouseInventoryByAreaQueryReq.setArea(warehouseInventoryQueryDTO.getArea());
        warehouseInventoryByAreaQueryReq.setPoi(warehouseInventoryQueryDTO.getPoi());
        warehouseInventoryByAreaQueryReq.setAddress(warehouseInventoryQueryDTO.getAddress());
        warehouseInventoryByAreaQueryReq.setTenantId(warehouseInventoryQueryDTO.getTenantId());
        warehouseInventoryByAreaQueryReq.setSkuIdList(warehouseInventoryQueryDTO.getSkuIdList());
        warehouseInventoryByAreaQueryReq.setContactId(warehouseInventoryQueryDTO.getStoreId());
        return warehouseInventoryByAreaQueryReq;
    }

    @Deprecated
    public static List<WarehouseInventoryDTO> convertToList(List<WarehouseInventoryByAreaDTO> warehouseInventoryList) {
        if (warehouseInventoryList == null) {
            return Collections.emptyList();
        }
        List<WarehouseInventoryDTO> warehouseInventoryDTOList = new ArrayList<>();
        for (WarehouseInventoryByAreaDTO warehouseInventoryByAreaDTO : warehouseInventoryList) {
            warehouseInventoryDTOList.add(toWarehouseInventoryDTO(warehouseInventoryByAreaDTO));
        }
        return warehouseInventoryDTOList;
    }

    @Deprecated
    public static WarehouseInventoryDTO toWarehouseInventoryDTO(WarehouseInventoryByAreaDTO warehouseInventoryByAreaDTO) {
        if (warehouseInventoryByAreaDTO == null) {
            return null;
        }
        WarehouseInventoryDTO warehouseInventoryDTO = new WarehouseInventoryDTO();
        warehouseInventoryDTO.setSkuId(warehouseInventoryByAreaDTO.getSkuId());
        warehouseInventoryDTO.setInventory(warehouseInventoryByAreaDTO.getInventory());
        warehouseInventoryDTO.setQualityDate(warehouseInventoryByAreaDTO.getQualityDate());
        warehouseInventoryDTO.setFruitFlag(warehouseInventoryByAreaDTO.getFruitFlag());
        return warehouseInventoryDTO;
    }
}
