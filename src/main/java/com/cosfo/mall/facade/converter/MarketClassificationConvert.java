package com.cosfo.mall.facade.converter;

import com.cofso.item.client.resp.MarketClassificationTreeResp;
import com.cosfo.mall.market.model.dto.MarketClassificationTreeDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/10
 */
public class MarketClassificationConvert {

    /**
     * 转化为List<MarketClassificationTreeDTO>
     *
     * @param marketClassificationTreeResps
     * @return
     */
    public static List<MarketClassificationTreeDTO> convertToMarketClassificationTreeDTOList(List<MarketClassificationTreeResp> marketClassificationTreeResps) {

        if (marketClassificationTreeResps == null) {
            return Collections.emptyList();
        }
        List<MarketClassificationTreeDTO> marketClassificationTreeDTOList = new ArrayList<>();
        for (MarketClassificationTreeResp marketClassificationTreeResp : marketClassificationTreeResps) {
            marketClassificationTreeDTOList.add(toMarketClassificationTreeDTO(marketClassificationTreeResp));
        }
        return marketClassificationTreeDTOList;
    }

    public static MarketClassificationTreeDTO toMarketClassificationTreeDTO(MarketClassificationTreeResp marketClassificationTreeResp) {
        if (marketClassificationTreeResp == null) {
            return null;
        }
        MarketClassificationTreeDTO marketClassificationTreeDTO = new MarketClassificationTreeDTO();
        marketClassificationTreeDTO.setId(marketClassificationTreeResp.getId());
        marketClassificationTreeDTO.setName(marketClassificationTreeResp.getName());
        marketClassificationTreeDTO.setParentId(marketClassificationTreeResp.getParentId());
        marketClassificationTreeDTO.setIcon(marketClassificationTreeResp.getIcon());
        marketClassificationTreeDTO.setSort(marketClassificationTreeResp.getSort());
        List<MarketClassificationTreeResp> childList = marketClassificationTreeResp.getChildList();
        if (!CollectionUtils.isEmpty(childList)) {
            List<MarketClassificationTreeDTO> marketClassificationTreeVOS = convertToMarketClassificationTreeDTOList(childList);
            marketClassificationTreeDTO.setChildList(marketClassificationTreeVOS);
        }

        return marketClassificationTreeDTO;
    }
}
