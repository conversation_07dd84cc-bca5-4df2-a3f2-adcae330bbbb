package com.cosfo.mall.facade.converter;

import com.cosfo.mall.merchant.model.po.Merchant;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Mapper
public interface MerchantConverter {

    MerchantConverter INSTANCE = Mappers.getMapper(MerchantConverter.class);

    /**
     * rpc返回数据转换为实体类
     * @param merchantResultResp
     * @return
     */
    Merchant respToMerchant(MerchantResultResp merchantResultResp);
}
