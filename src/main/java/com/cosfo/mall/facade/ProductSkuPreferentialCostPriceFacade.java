package com.cosfo.mall.facade;

import cn.hutool.core.util.ObjectUtil;
import com.cofso.preferential.client.provider.ProductSkuPreferentialCostPriceProvider;
import com.cofso.preferential.client.req.OptAvailableQuantityReq;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cosfo.mall.common.model.dto.CommonLocationCityDTO;
import com.cosfo.mall.common.service.AreaService;
import com.cosfo.mall.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductSkuPreferentialCostPriceFacade {

    @DubboReference
    private ProductSkuPreferentialCostPriceProvider priceProvider;

    @Resource
    private AreaService areaService;

    @Resource
    private TenantService tenantService;

    /**
     * 查询sku+城市 生效中的省心定
     *
     * @returnF
     */
    public Map<Long, ProductSkuCityPreferentialCostPriceResp> queryPreferentialCostPriceBySkuIds4City(Long tenantId, Long cityId, Set<Long> skuIds) {
        if(CollectionUtils.isEmpty (skuIds)){
         return Collections.emptyMap ();
        }
        if(tenantService.getSaveWorrySwitchByTenantId (tenantId)) {
            DubboResponse<List<ProductSkuCityPreferentialCostPriceResp>> dubboResponse = priceProvider.queryPreferentialCostPriceBySkuIds4City (tenantId, cityId, skuIds.stream().filter (ObjectUtil::isNotNull).collect(Collectors.toSet ()));
            if (!dubboResponse.isSuccess()) {
                throw new ProviderException(dubboResponse.getMsg());
            }
            return dubboResponse.getData ().stream().collect (Collectors.toMap (ProductSkuCityPreferentialCostPriceResp::getSkuId, Function.identity()));
        }else{
            return Collections.emptyMap ();
        }
    }

    /**
     * 扣省心定库存
     *
     * @returnF
     */
    public void occupyAvailableQuantity(Long tenantId,String city, Long orderId, List<OptAvailableQuantityReq> req) {
        if (CollectionUtils.isEmpty (req)) {
            throw new BizException ("skuIds 空");
        }
        CommonLocationCityDTO commonLocationCityDTO = areaService.selectByCityName (city);
        if (ObjectUtil.isEmpty (commonLocationCityDTO)) {
            throw new BizException ("城市为空");
        }
        if (tenantService.getSaveWorrySwitchByTenantId (tenantId)) {
            DubboResponse dubboResponse = priceProvider.occupyAvailableQuantity (tenantId, commonLocationCityDTO.getId (), orderId, req);
            if (!dubboResponse.isSuccess ()) {
                throw new ProviderException (dubboResponse.getMsg ());
            }
        }
    }
    /**
     * 释放省心定库存
     *
     * @returnF
     */
    public void releaseAvailableQuantity(Long tenantId,Long orderId,List<OptAvailableQuantityReq> req) {
        if (CollectionUtils.isEmpty (req)) {
            return;
        }
        if (tenantService.getSaveWorrySwitchByTenantId (tenantId)) {
            DubboResponse dubboResponse = priceProvider.releaseAvailableQuantity (tenantId, orderId, req);
            if (!dubboResponse.isSuccess ()) {
                throw new ProviderException (dubboResponse.getMsg ());
            }
        }
    }
}
