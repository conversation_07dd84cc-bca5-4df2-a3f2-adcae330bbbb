package com.cosfo.mall.facade;

import com.cofso.item.client.provider.MarketClassificationProvider;
import com.cofso.item.client.resp.MarketClassificationTreeResp;
import com.cosfo.mall.facade.converter.MarketClassificationConvert;
import com.cosfo.mall.market.model.dto.MarketClassificationTreeDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/10
 */
@Slf4j
@Component
public class MarketClassificationFacade {

    @DubboReference
    private MarketClassificationProvider marketClassificationProvider;
    /**
     * 前台分组树
     *
     * @param tenantId
     * @return
     */
    public List<MarketClassificationTreeDTO> selectClassificationTree(Long tenantId){
        DubboResponse<List<MarketClassificationTreeResp>> dubboResponse = marketClassificationProvider.selectClassificationTree(tenantId);
        if(!dubboResponse.isSuccess()){
            throw new BizException(dubboResponse.getMsg());
        }

        List<MarketClassificationTreeResp> marketClassificationTreeResps = dubboResponse.getData();
        List<MarketClassificationTreeDTO> marketClassificationTreeVOS = MarketClassificationConvert.convertToMarketClassificationTreeDTOList(marketClassificationTreeResps);
        return marketClassificationTreeVOS;
    }
}
