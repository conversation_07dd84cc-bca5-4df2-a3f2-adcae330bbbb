package com.cosfo.mall.facade;

import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.openapi.model.dto.item.MarketItemDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterQueryProvider;
import net.xianmu.inventory.client.saleinventory.dto.req.QueryAddressSkuInventoryReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryResDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-12-25
 * @Description:
 */
@Slf4j
@Component
public class SaleInventoryCenterQueryFacade {

    @DubboReference
    private SaleInventoryCenterQueryProvider saleInventoryCenterQueryProvider;

    /**
     * 查询地址SKU的库存可售信息
     * @param queryAddressSkuInventoryReqDTO
     * @return
     */
    public WarehouseSkuInventoryResDTO queryAddressSkuInventoryInfo(QueryAddressSkuInventoryReqDTO queryAddressSkuInventoryReqDTO){
        DubboResponse<WarehouseSkuInventoryResDTO> response = saleInventoryCenterQueryProvider.queryAddressSkuInventoryInfo(queryAddressSkuInventoryReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("查询地址SKU的库存可售信息失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 查询地址sku库存可售信息
     * @param tenantId
     * @param merchantAddressDTO
     * @param marketItemDTOList
     * @return
     */
    public List<StockDTO> queryAddressSkuInventoryInfo(Long tenantId, MerchantAddressDTO merchantAddressDTO, List<MarketItemDTO> marketItemDTOList) {
        if (CollectionUtils.isEmpty(marketItemDTOList)) {
            return Collections.EMPTY_LIST;
        }
        List<String> agentSkuCodeList = marketItemDTOList.stream().map(MarketItemDTO::getAgentSkuCode).distinct().collect(Collectors.toList());
        QueryAddressSkuInventoryReqDTO queryAddressSkuInventoryReqDTO = new QueryAddressSkuInventoryReqDTO();
        queryAddressSkuInventoryReqDTO.setTenantId(tenantId);
        queryAddressSkuInventoryReqDTO.setProvince(merchantAddressDTO.getProvince());
        queryAddressSkuInventoryReqDTO.setCity(merchantAddressDTO.getCity());
        queryAddressSkuInventoryReqDTO.setArea(merchantAddressDTO.getArea());
        queryAddressSkuInventoryReqDTO.setAddress(merchantAddressDTO.getAddress());
        queryAddressSkuInventoryReqDTO.setPoi(merchantAddressDTO.getPoiNote());
        queryAddressSkuInventoryReqDTO.setContactId(merchantAddressDTO.getStoreId());
        queryAddressSkuInventoryReqDTO.setSkuCodeList(agentSkuCodeList);
        List<WarehouseSkuInventoryDetailResDTO> warehouseSkuInventoryDetailResDTOS = queryAddressSkuInventoryInfo(queryAddressSkuInventoryReqDTO)
                .getWarehouseSkuInventoryDetailResDTOS();
        if (CollectionUtils.isEmpty(warehouseSkuInventoryDetailResDTOS)) {
            return Collections.EMPTY_LIST;
        }
        // 过滤掉自营仓
        warehouseSkuInventoryDetailResDTOS = warehouseSkuInventoryDetailResDTOS.stream()
                .filter(dto -> XianmuSupplyTenant.TENANT_ID.equals(dto.getWarehouseTenantId())).collect(Collectors.toList());

        List<StockDTO> result = Lists.newArrayList();
        for (WarehouseSkuInventoryDetailResDTO warehouseSkuInventoryDetailResDTO : warehouseSkuInventoryDetailResDTOS) {
            StockDTO stockDTO = new StockDTO();
            // 最大可占用数量
            stockDTO.setMaxAmount(warehouseSkuInventoryDetailResDTO.getAvailableQuantity());
            // 仓库编号
            stockDTO.setWarehouseNo(warehouseSkuInventoryDetailResDTO.getWarehouseNo());
            // 租户编号 鲜沐为0
            stockDTO.setWarehouseTenantId(warehouseSkuInventoryDetailResDTO.getWarehouseTenantId());
            stockDTO.setSupplySku(warehouseSkuInventoryDetailResDTO.getSkuCode());

            result.add(stockDTO);
        }
        return result;
    }
}
