package com.cosfo.mall.facade;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterQueryProvider;
import net.xianmu.inventory.client.saleinventory.dto.req.QueryAddressSkuInventoryReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryResDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: fansongsong
 * @Date: 2023-12-25
 * @Description:
 */
@Slf4j
@Component
public class SaleInventoryCenterQueryFacade {

    @DubboReference
    private SaleInventoryCenterQueryProvider saleInventoryCenterQueryProvider;

    /**
     * 查询地址SKU的库存可售信息
     * @param queryAddressSkuInventoryReqDTO
     * @return
     */
    public WarehouseSkuInventoryResDTO queryAddressSkuInventoryInfo(QueryAddressSkuInventoryReqDTO queryAddressSkuInventoryReqDTO){
        DubboResponse<WarehouseSkuInventoryResDTO> response = saleInventoryCenterQueryProvider.queryAddressSkuInventoryInfo(queryAddressSkuInventoryReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("查询地址SKU的库存可售信息失败：" + response.getMsg());
        }
        return response.getData();
    }
}
