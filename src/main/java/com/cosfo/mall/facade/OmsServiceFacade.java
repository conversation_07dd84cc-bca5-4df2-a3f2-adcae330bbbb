package com.cosfo.mall.facade;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.msg.model.req.MessageTemplateQueryDTO;
import com.cosfo.mall.msg.model.resp.MsgSceneTenantVO;
import com.cosfo.oms.client.common.MsgSceneTenantMappingEnum;
import com.cosfo.oms.client.provider.msgscene.MsgSceneQueryProvider;
import com.cosfo.oms.client.req.MsgSceneQueryReq;
import com.cosfo.oms.client.resp.MsgSceneTenantResultResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OmsServiceFacade {
    @DubboReference
    private MsgSceneQueryProvider msgSceneQueryProvider;

    public List<MsgSceneTenantVO> listSceneTenantByTenantId(MessageTemplateQueryDTO dto, Long tenantId) {
        MsgSceneQueryReq req = new MsgSceneQueryReq();
        req.setTemplateType(dto.getTemplateType());
        req.setSceneId(dto.getSceneId());
        req.setAvailableStatus(MsgSceneTenantMappingEnum.AvailableStatus.ABLED.getValue());
//        log.info("查询message.getThirdTemplate：req={}" , JSONObject.toJSON(req));
        DubboResponse<List<MsgSceneTenantResultResp>> resp = msgSceneQueryProvider.listSceneTenantByTenantId(req, tenantId);
        if (!resp.isSuccess()) {
            log.error("查询message.getThirdTemplate：req={},resp={}" ,JSONObject.toJSON(req),JSONObject.toJSON(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
//        log.info("查询message.getThirdTemplate：req={},resp={}" ,JSONObject.toJSON(req),JSONObject.toJSON(resp));
        List<MsgSceneTenantResultResp> data = resp.getData();
        return data.stream().map(e-> {
            MsgSceneTenantVO vo = new MsgSceneTenantVO();
            BeanUtils.copyProperties(e,vo);
            return vo;
        }).collect(Collectors.toList());
    }
}
