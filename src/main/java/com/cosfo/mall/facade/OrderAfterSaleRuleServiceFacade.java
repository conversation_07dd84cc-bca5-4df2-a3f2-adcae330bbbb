//package com.cosfo.mall.facade;
//
//import com.cosfo.erp.client.aftersale.provider.OrderAfterSaleRuleQueryProvider;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Component;
//
///**
// * @desc：售后规则 facade
// * <AUTHOR>
// * @date 2022/12/28 10:17
// */
//@Component
//public class OrderAfterSaleRuleServiceFacade {
//
//    @DubboReference
//    private OrderAfterSaleRuleQueryProvider orderAfterSaleRuleQueryProvider;
//
////    /**
////     * 查询所有
////     * @return
////     */
////    public List<OrderAfterSaleRuleDTO> listAll() {
////        OrderAfterSaleRuleQueryReq orderAfterSaleRuleQueryReq = new OrderAfterSaleRuleQueryReq();
////        List<OrderAfterSaleRuleResp> orderAfterSaleRuleRespList = orderAfterSaleRuleQueryProvider.listAll(orderAfterSaleRuleQueryReq);
////        return orderAfterSaleRuleRespList.stream().map(OrderAfterSaleConverter::orderAfterSaleRuleResp2DTO).collect(Collectors.toList());
////    }
//}
