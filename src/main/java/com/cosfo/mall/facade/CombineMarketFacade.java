package com.cosfo.mall.facade;

import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.provider.CombineMarketProvider;
import com.cofso.item.client.req.CombineMarketQueryInputReq;
import com.cofso.item.client.resp.CombineMarketDetailResp;
import com.cosfo.mall.facade.converter.CombineMarketConvert;
import com.cosfo.mall.facade.dto.CombineMarketDetailDTO;
import com.cosfo.mall.facade.dto.CombineMarketQueryInputDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 10:29
 * @Description:
 */
@Service
@Slf4j
public class CombineMarketFacade {
    @DubboReference
    private CombineMarketProvider combineMarketProvider;

//    public List<CombineMarketItemDTO> queryMarketItemById(Long marketItemId) {
//        DubboResponse<List<CombineItemResp>> dubboResponse = combineMarketProvider.getMarketItemDetail(marketItemId);
//        if (!dubboResponse.isSuccess()) {
//            throw new ProviderException(dubboResponse.getMsg());
//        }
//        return CombineMarketConvert.INSTANCE.convert2Combines(dubboResponse.getData());
//    }

    /**
     * 组合商品详情
     *
     * @param combineMarketQueryInputDTO
     */
    public CombineMarketDetailDTO combineDetail(CombineMarketQueryInputDTO combineMarketQueryInputDTO){
        CombineMarketQueryInputReq combineMarketQueryInputReq = CombineMarketConvert.INSTANCE.convertToCombineMarketQueryInputReq(combineMarketQueryInputDTO);
        log.info("combineMarketProvider.combineDetail request:{}", JSONObject.toJSONString(combineMarketQueryInputDTO));
        DubboResponse<CombineMarketDetailResp> dubboResponse = combineMarketProvider.combineDetail(combineMarketQueryInputReq);
        log.info("combineMarketProvider.combineDetail response:{}", JSONObject.toJSONString(dubboResponse));
        if (!dubboResponse.isSuccess()) {
            throw new BizException("该组合包已下架，请查看别的商品");
        }

        CombineMarketDetailResp combineMarketDetailResp = dubboResponse.getData();
        return CombineMarketConvert.INSTANCE.convertToCombineMarketDetailDTO(combineMarketDetailResp);
    }
}
