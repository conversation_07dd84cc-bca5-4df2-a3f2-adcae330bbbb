package com.cosfo.mall.facade;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterCommandProvider;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.PreDistributionOrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderOccupyResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderReleaseResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.PreDistributionOrderOccupyResDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * WMS 库存
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/10
 */
@Slf4j
@Component
public class SaleInventoryCommandFacade {

    @DubboReference
    private SaleInventoryCenterCommandProvider saleInventoryCenterCommandProvider;

    /**
     * 购物车、预下单库存查询接口
     * @param preDistributionOrderOccupyReqDTO
     * @return
     */
    public PreDistributionOrderOccupyResDTO preDistributionOrderOccupy(PreDistributionOrderOccupyReqDTO preDistributionOrderOccupyReqDTO){
        DubboResponse<PreDistributionOrderOccupyResDTO> response = saleInventoryCenterCommandProvider.preDistributionOrderOccupy(preDistributionOrderOccupyReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("查询可占用库存接口失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 下单冻结自营仓库存接口
     *
     * @param orderOccupyBySpecifyWarehouseReqDTO
     * @return
     */
    public OrderOccupyResDTO orderOccupyBySpecifyWarehouseAndSku(OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO){
        DubboResponse<OrderOccupyResDTO> response = saleInventoryCenterCommandProvider.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("调用自营仓库存冻结接口失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 下单冻结三方仓库存接口
     *
     * @param orderOccupyReqDTO
     * @return
     */
    public OrderOccupyResDTO orderOccupy(OrderOccupyReqDTO orderOccupyReqDTO){
        DubboResponse<OrderOccupyResDTO> response = saleInventoryCenterCommandProvider.orderOccupy(orderOccupyReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("下单冻结三方仓库存接口失败：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 释放自营仓库存接口
     *
     * @param orderReleaseBySpecifySkuReqDTO
     * @return
     */
    public OrderReleaseResDTO orderReleaseBySpecifySku(OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO){
        DubboResponse<OrderReleaseResDTO> response = saleInventoryCenterCommandProvider.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);
        if (!response.isSuccess()) {
            throw new ProviderException("订单" + orderReleaseBySpecifySkuReqDTO.getOrderNo() + "调用库存释放接口失败：" + response.getMsg());
        }
        return response.getData();
    }
}
