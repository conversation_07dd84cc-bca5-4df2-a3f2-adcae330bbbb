package com.cosfo.mall.facade.input;

import lombok.Data;

/**
 * @Author: lzh
 * @Date: 2025/10/16 15:24
 * @Param:
 * @Return:
 * @Description:
 **/
@Data
public class ActivityDetailQueryInput {

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 店铺Id
     */
    private Long storeId;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * sku信息 saas传itemId
     */
    private String sku;

    /**
     * 活动Id
     */
    private Long activityId;
}
