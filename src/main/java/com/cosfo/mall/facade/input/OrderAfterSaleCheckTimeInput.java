package com.cosfo.mall.facade.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/1/11 10:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderAfterSaleCheckTimeInput {

    /**
     * @see com.cosfo.mall.common.constants.WarehouseTypeEnum
     */
    private Integer warehouseType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

}
