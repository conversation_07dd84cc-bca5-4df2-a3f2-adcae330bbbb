package com.cosfo.mall.facade.input;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/10/14 14:23
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class OrderPreferentialQueryInput {

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 购买数量
     */
    private Integer quantity;
}
