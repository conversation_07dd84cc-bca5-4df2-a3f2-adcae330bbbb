package com.cosfo.mall.facade.input;

import lombok.Data;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/5 13:54
 */
@Data
public class OrderAfterSaleProcessFinishInput {

    /**
     * 售后订单号
     */
    private String orderNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 应回收/配送数量
     */
    private Integer shouldCount;

    /**
     * 0配送 1回收
     */
    private Integer deliveryType;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 0正常 1异常
     */
    private Integer state;

    /**
     * 缺货原因
     */
    private String remark;
}
