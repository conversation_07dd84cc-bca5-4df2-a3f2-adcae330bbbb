package com.cosfo.mall.facade;

import com.cofso.item.client.provider.PriceProvider;
import com.cofso.item.client.resp.ItemPriceDetailResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cosfo.mall.facade.converter.PriceConvert;
import com.cosfo.mall.facade.dto.ItemPriceDetailDTO;
import com.cosfo.mall.facade.dto.PriceDetailDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/11
 */
@Slf4j
@Component
public class PriceFacade {

    @DubboReference
    private PriceProvider priceProvider;

    /**
     * 查询商品价格详情
     *
     * @param tenantId
     * @param targetId
     * @param targetType
     * @param itemBuyAmount
     * @return
     */
    public Map<Long, PriceDetailDTO> listItemPriceDetailByItemIds(Long tenantId, Long targetId, Integer targetType,  Map<Long, Integer> itemBuyAmount,boolean throwExceptionFlag){
        log.info("priceProvider.listItemPriceDetailByItemIds request: tenantId: {}, targetId: {}, targetType: {}, itemBuyAmount: {}", tenantId, targetId, targetType, itemBuyAmount);
        DubboResponse<Map<Long, PriceDetailResp>> dubboResponse = priceProvider.listItemPriceDetailByItemIds(tenantId, targetId, targetType, itemBuyAmount, throwExceptionFlag);
        log.info("priceProvider.listItemPriceDetailByItemIds response: {}", dubboResponse.toString());
        if(!dubboResponse.isSuccess()){
            log.error("查询商品价格详情失败，失败原因{}", dubboResponse.getMsg());
            throw new ProviderException(dubboResponse.getMsg());
        }

        Map<Long, PriceDetailResp> priceDetailRespMap = dubboResponse.getData();
        Map<Long, PriceDetailDTO> priceDetailDTOMap = PriceConvert.convertToPriceDetailDTOMap(priceDetailRespMap);
        return priceDetailDTOMap;
    }

    /**
     * 组合包商品价格
     *
     * @param tenantId
     * @param targetId
     * @param targetType
     * @param itemBuyAmount
     * @return
     */
    public List<ItemPriceDetailDTO>  listItemPriceDetailByItemIds4CombineItem(Long tenantId, Long targetId, Integer targetType, Map<Long, Integer> itemBuyAmount,boolean throwExceptionFlag){
        // TODO 组合包价格
        log.info("priceProvider.listItemPriceDetailByItemIds4CombineItem request: tenantId: {}, targetId: {}, targetType: {}, itemBuyAmount: {}", tenantId, targetId, targetType, itemBuyAmount);
        DubboResponse<List<ItemPriceDetailResp>> dubboResponse = priceProvider.listItemPriceDetailByItemIds4CombineItem(tenantId, targetId,  itemBuyAmount,throwExceptionFlag);
        log.info("priceProvider.listItemPriceDetailByItemIds response: {}", dubboResponse.toString());
        if(!dubboResponse.isSuccess()){
            log.error("查询组合包商品价格详情失败，失败原因{}", dubboResponse.getMsg());
            throw new ProviderException(dubboResponse.getMsg());
        }

        List<ItemPriceDetailResp> responseData = dubboResponse.getData();
        List<ItemPriceDetailDTO> itemPriceDetailDTOS = PriceConvert.convertToItemPriceDetailDTO(responseData);
        return itemPriceDetailDTOS;
    }

}
