package com.cosfo.mall.facade.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 18:02
 * @Description:
 */
@Data
public class CombineItemDTO implements Serializable {
    private static final long serialVersionUID = -8715412174943473644L;

    /**
     * 组合品映射表的主键
     */
    private Long mappingId;

    /**
     * market_item_id
     */
    private Long marketItemId;

    /**
     * 商品名
     */
    private String itemTitle;
    /**
     * 头图
     */
    private String mainPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private String priceStr;


}
