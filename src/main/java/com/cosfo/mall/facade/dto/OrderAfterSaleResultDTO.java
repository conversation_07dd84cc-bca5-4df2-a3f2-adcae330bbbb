package com.cosfo.mall.facade.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.mall.delivery.model.vo.FulfillmentDeliveryVO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/6 14:47
 */
@Data
public class OrderAfterSaleResultDTO {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 订单项Id
     */
    private Long orderItemId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 售后类型
     */
    private Integer afterSaleType;

    /**
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发
     */
    private Integer serviceType;

    /**
     * 退款方式（取支付方式） 1、微信支付 2、账期 3、余额支付 4、支付宝支付 5、无需支付 6、线下支付 7、非现金支付 8、组合支付
     */
    private Integer payType;

    /**
     * 申请金额
     */
    private BigDecimal applyPrice;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 售后原因
     */
    private String reason;

    /**
     * 客户留言
     */
    private String userRemark;

    /**
     * 售后凭证照片
     */
    private String proofPicture;

    /**
     * 状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款 9 三方处理中
     */
    private Integer status;

    /**
     * 处理结果
     */
    private String handleRemark;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * sku编码
     */
    private Long skuId;

    /**
     * 下单账号
     */
    private String accountName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 售后类型描述
     */
    private String afterSaleTypeDesc;
    /**
     * 商品名称
     */
    private String title;

    /**
     * 售后服务类型描述
     */
    private String serviceTypeDesc;

    /**
     * 实际退款金额
     */
    private BigDecimal returnPrice;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消描述
     */
    private String statusDesc;
    /**
     * 下单日期
     */
    private LocalDateTime orderTime;

    /**
     * 回收时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDateTime recycleTime;

    /**
     * 回收详情
     */
    private String recycleDetails;

    /**
     * 责任类型0供应商1品牌方2门店
     */
    private Integer responsibilityType;

    /**
     * 审核时间
     */
    private LocalDateTime handleTime;

    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 规格单位
     */
    private Integer orderAmount;
    /**
     * 下单金额
     */
    private BigDecimal orderPrice;
    /**
     *单价
     */
    private BigDecimal price;

    /**
     * @see com.cosfo.mall.common.constants.WarehouseTypeEnum
     */
    private Integer warehouseType;

    /**
     * 申请账号
     */
    private String applyAccount;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数
     */
    private Integer maxAfterSaleAmount;

    /**
     * 补发 物流信息
     */
    private List<FulfillmentDeliveryVO> fulfillmentDeliveryVOList;
    /**
     * 退货 物流信息
     */
    private FulfillmentDeliveryVO fulfillmentDeliveryVO;

    /**
     * 订单类型 0普通订单 1组合订单
     */
    private Integer orderType;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 退款服务费
     */
    private BigDecimal refundSalesServiceFee;
}
