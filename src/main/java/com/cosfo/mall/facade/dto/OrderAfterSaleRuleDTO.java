package com.cosfo.mall.facade.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/5 19:26
 */
@Data
public class OrderAfterSaleRuleDTO implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 配送方式
     */
    private Integer deliveryType;

    /**
     * 申请结束天数
     */
    private Integer applyEndTime;

    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;

    /**
     * 处理方式：0同意1拒绝
     */
    private Integer dealType;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
