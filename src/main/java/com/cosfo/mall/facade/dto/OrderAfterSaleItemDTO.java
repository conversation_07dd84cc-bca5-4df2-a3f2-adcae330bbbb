package com.cosfo.mall.facade.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/1/5 11:23
 */
@Data
public class OrderAfterSaleItemDTO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * sku编码
     */
    private Long itemId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 供应商商品skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 供应商Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
}
