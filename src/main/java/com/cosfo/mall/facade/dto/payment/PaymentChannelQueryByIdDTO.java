package com.cosfo.mall.facade.dto.payment;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-08-21
 **/
@Data
public class PaymentChannelQueryByIdDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private List<String> subMerchantNos;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 收银员Id
     */
    private String userId;

    /**
     * appId（招行）
     */
    private String appId;

    /**
     * app密钥（招行）
     */
    private String appSecret;

    /**
     * 证书路径（用于各渠道退款 微信原生v2版本需要该参数）
     */
    private String certPath;

    /**
     * 操作人ID
     */
    private Long operatorAdminId;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 退款密码
     */
    private String refundPassword;
}
