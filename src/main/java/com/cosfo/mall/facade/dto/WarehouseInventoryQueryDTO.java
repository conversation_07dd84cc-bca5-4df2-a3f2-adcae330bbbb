package com.cosfo.mall.facade.dto;

import lombok.Data;
import net.xianmu.inventory.client.saleinventory.dto.req.WarehouseInventoryAggregatedByAddressDetailReq;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/16
 */
@Data
public class WarehouseInventoryQueryDTO {

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 租户Id
     */
    private Long tenantId;
    
    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店坐标
     */
    private String poi;
    /**
     * 地址
     */
    private String address;

    /**
     * sku主键id
     */
    @Deprecated
    private List<Long> skuIdList;

    /**
     * skucode信息
     */
    private List<SkuDetail> skuDetailList;

    @Data
    public static class SkuDetail{

        /**
         * sku编码
         */
        private String skuCode;

        /**
         * 帆台skuId
         */
        private Long skuId;

        /**
         * sku租户id
         */
        private Long skuTenantId;

        /** 所属类目类型 **/
        private Integer categoryType;

        /**
         * 商品预售开关 0-不可预售 1-可预售 默认值0
         */
        private Integer presaleSwitch;

        public WarehouseInventoryAggregatedByAddressDetailReq buildReq(){
            WarehouseInventoryAggregatedByAddressDetailReq req = new WarehouseInventoryAggregatedByAddressDetailReq();
            req.setSkuCode(skuCode);
            req.setSkuTenantId(skuTenantId);
            // TODO 预售presaleSwitch
            req.setMarketItemPresaleSwitch(presaleSwitch);
            return req;
        }
    }
}
