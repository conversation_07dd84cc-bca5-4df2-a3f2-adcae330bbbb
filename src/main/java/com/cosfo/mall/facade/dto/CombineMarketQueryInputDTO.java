package com.cosfo.mall.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2023/5/8 10:19
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CombineMarketQueryInputDTO implements Serializable {
    private static final long serialVersionUID = 2863341666292040044L;

    /**
     * 组合包标题
     */
    private String combineMarketTitle;

    /**
     * 组合包编码
     */
    private Long combineMarketId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 分页
     */
    @Builder.Default
    private Integer pageNum = 1;
    @Builder.Default
    private Integer pageSize = 1000;
}
