package com.cosfo.mall.facade.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/10/14 16:28
 * @Version 1.0
 */
@Data
public class OrderPreferentialDTO {

    /**
     * 优惠类型
     */
    private Integer preferentialType;

    /**
     * 原价
     */
    private BigDecimal originalAmount;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 优惠金额
     */
    private BigDecimal preferentialAmount;
    public BigDecimal getPreferentialAmount() {
        return preferentialAmount == null ? BigDecimal.ZERO : preferentialAmount;
    }

    /**
     * 关联id
     */
    private Long relatedId;

    /**
     * 优惠详情快照
     */
    private String discountsDetailSnapshot;
}
