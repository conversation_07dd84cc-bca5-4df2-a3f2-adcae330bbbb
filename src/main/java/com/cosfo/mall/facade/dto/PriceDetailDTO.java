package com.cosfo.mall.facade.dto;

import com.cofso.item.client.req.LadderPrice;
import com.cosfo.mall.market.model.vo.LadderPriceVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/11
 */
@Data
public class PriceDetailDTO {
    /**
     * 最终销售单价
     */
    private BigDecimal price;
    /**
     * 成本单价 - 供应价
     */
    private BigDecimal costPrice;
    /**
     * 原销售单价
     */
    private BigDecimal marketItemPrice;

    private List<LadderPriceVO> ladderPrices;
}
