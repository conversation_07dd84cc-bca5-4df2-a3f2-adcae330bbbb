package com.cosfo.mall.facade.dto.payment;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 支付路由查询响应
 * @author: Gemini
 * @date: 2025-08-15
 */
@Data
public class PaymentRoutingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 证书路径(微信V3需要)
     */
    private String certPath;

}
