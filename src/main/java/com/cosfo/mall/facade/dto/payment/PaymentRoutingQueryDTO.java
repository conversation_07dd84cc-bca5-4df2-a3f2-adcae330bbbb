
package com.cosfo.mall.facade.dto.payment;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 支付路由查询请求
 * @author: Gemini
 * @date: 2025-08-15
 */
@Data
public class PaymentRoutingQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 平台
     */
    private String platform;

    /**
     * 路由键
     */
    private String routeKey;

    /**
     * 路由值
     */
    private String routeValue;

    /**
     * 支付方式
     */
    private String paymentMethod;

}
