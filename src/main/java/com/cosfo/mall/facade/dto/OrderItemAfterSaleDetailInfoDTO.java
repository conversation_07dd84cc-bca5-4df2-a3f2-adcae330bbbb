package com.cosfo.mall.facade.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述: 售后订单项详情
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/21
 */
@Data
public class OrderItemAfterSaleDetailInfoDTO {
    /**
     * 订单项Id
     */
    private Long orderItemId;
    /**
     * 购买数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 可申请售后数量
     */
    private Integer enableApplyAmount;
    /**
     * 可申请售后数量
     */
    private Integer enableApplyQuantity;
    /**
     * 售后类型
     */
    private Integer afterSaleType;

    /**
     * 订单支付类型
     */
    private Integer payType;

    /**
     * 售后到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数
     */
    private Integer maxAfterSaleAmount;
    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 配送方式，0-无需物流，1-商家物流配送，2-仓库物流配送
     */
    private Integer deliveryType;

    /**
     * 图片说明
     */
    private String pics;
}
