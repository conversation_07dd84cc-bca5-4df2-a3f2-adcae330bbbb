package com.cosfo.mall.facade.auth;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.enums.LoginLockEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserBaseQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthUserQueryFacade {

    @DubboReference
    private AuthUserQueryProvider authUserQueryProvider;

    @DubboReference
    private AuthUserProvider authUserProvider;

    @DubboReference
    private AuthBaseUserProvider authBaseUserProvider;

    public List<AuthUserResp> queryAuthUserList(Long tenantId, String phone) {
        AuthUserQueryInput queryInput = new AuthUserQueryInput();
        queryInput.setPhone(phone);
        queryInput.setTenantId(tenantId);
        DubboResponse<List<AuthUserResp>> response = authUserQueryProvider.queryAuthUserList(SystemOriginEnum.COSFO_MALL, queryInput);
        if (!response.isSuccess()) {
            throw new BizException("获取最新登录信息失败");
        }
        return response.getData();
    }



    /**
     * 校验用户名和密码
     *
     * @param phone
     * @param password
     * @return
     */
    public boolean checkUsernamePassword(String username, String password) {
        DubboResponse<Boolean> response = authUserProvider.checkUserNamePassword(SystemOriginEnum.COSFO_MALL, username, password);
        if (!response.isSuccess()) {
            log.info("校验用户信息失败，用户或密码错误.msg:{}", JSON.toJSONString(response));
            return false;
        }

        return response.getData();
    }

    public AuthUserBase selectByUsername(String username) {
        AuthUserBaseQueryInput input = new AuthUserBaseQueryInput();
        input.setUsername(username);
        List<AuthUserBase> list = queryAuthUserBase(input);
        return CollUtil.isEmpty(list) ? null : list.get(0);
    }

    public AuthUserBase selectByPhone(String phone) {
        AuthUserBaseQueryInput input = new AuthUserBaseQueryInput();
        input.setPhone(phone);
        List<AuthUserBase> list = queryAuthUserBase(input);
        return CollUtil.isEmpty(list) ? null : list.get(0);
    }


    public List<AuthUserBase> queryAuthUserBase(AuthUserBaseQueryInput input) {
        DubboResponse<List<AuthUserBase>> dubboResponse = authBaseUserProvider.queryAuthUserBase(input);
        if (!dubboResponse.isSuccess()) {
            log.warn("获取用户信息失败!dubboResponse:{}", JSON.toJSONString(dubboResponse));
            throw new BizException("获取用户信息失败!");
        }
        return dubboResponse.getData();
    }
}
