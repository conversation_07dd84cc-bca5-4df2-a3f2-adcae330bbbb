package com.cosfo.mall.facade.auth;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserBaseQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserPasswordUpdateInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.authentication.client.provider.AuthUserUpdateProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthUserUpdateFacade {

    @DubboReference
    private AuthUserUpdateProvider authUserUpdateProvider;

    /**
     * 修改用户密码
     * @param authUserPasswordUpdateInput
     * @return
     */
    public Boolean updateAuthUserPassword(AuthUserPasswordUpdateInput authUserPasswordUpdateInput) {
        DubboResponse<Boolean> response = authUserUpdateProvider.updateAuthUserPassword(authUserPasswordUpdateInput);
        if (!response.isSuccess()) {
            throw new BizException("修改密码失败!");
        }
        return response.getData();
    }




}
