package com.cosfo.mall.facade.auth;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.login.AuthClientLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.provider.AuthClientLoginProvider;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthClientLoginFacade {

    @DubboReference
    private AuthClientLoginProvider authClientLoginProvider;

    public AuthQueryWechatInfoDTO queryWeChatInfo(String code, String appId, String myAppID, String accessToken) {
        AuthQueryWechatInfoInput queryWechatInfoInput = new AuthQueryWechatInfoInput();
        queryWechatInfoInput.setCode(code);
        queryWechatInfoInput.setAppId(appId);
        queryWechatInfoInput.setMyAppId(myAppID);
        queryWechatInfoInput.setAccessToken(accessToken);
        queryWechatInfoInput.setType(AuthTypeEnum.WEI_CHAT);

        DubboResponse<AuthQueryWechatInfoDTO> response = authClientLoginProvider.authQueryWechatInfo(queryWechatInfoInput);
        if (!response.isSuccess()) {
            log.error("获取微信小程序 session 失败, msg={}", response);
            throw new BizException("获取小程序session失败");
        }
        return response.getData();
    }

    public AuthLoginDto authClientLogin(Long tenantId,String openId, String phone, Long accountId, Long expireTime) {
        AuthClientLoginProviderInput input = new AuthClientLoginProviderInput();
        input.setLoginType(AuthTypeEnum.WEI_CHAT);
        input.setOpenid(openId);
        input.setPhone(phone);
        input.setBizId(accountId);
        input.setTenantId(tenantId);
        input.setSystemOriginEnum(SystemOriginEnum.COSFO_MALL);
        input.setExpireTime(expireTime);
        DubboResponse<AuthLoginDto> response = authClientLoginProvider.authClientLogin(input);
        if (!response.isSuccess()) {
            throw new BizException("登录失败");
        }
        return response.getData();
    }

    public Boolean logout(Long accountId, String token) {
        AuthClientLoginProviderInput input = new AuthClientLoginProviderInput();
        input.setBizId(accountId);
        input.setToken(token);
        DubboResponse<Boolean> response = authClientLoginProvider.loginOut(input);
        if (!response.isSuccess()) {
            throw new BizException("退出失败");
        }
        return response.getData();
    }
}
