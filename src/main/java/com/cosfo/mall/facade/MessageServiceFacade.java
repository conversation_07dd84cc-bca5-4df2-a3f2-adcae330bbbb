package com.cosfo.mall.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.utils.PageInfoHelper;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.cosfo.mall.msg.model.req.MsgQueryDTO;
import com.cosfo.mall.msg.model.req.MsgReadLogDTO;
import com.cosfo.mall.msg.model.resp.MsgDetailVO;
import com.cosfo.mall.msg.model.resp.MsgTipListVO;
import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.msgtemplate.provider.MsgTemplateQueryProvider;
import com.cosfo.message.client.provider.MessageSendLogProvider;
import com.cosfo.message.client.provider.NoticeProvider;
import com.cosfo.message.client.req.MsgReadLogReq;
import com.cosfo.message.client.req.NoticeQuery4ReceiverReq;
import com.cosfo.message.client.resp.NoticeDetailResultResp;
import com.cosfo.message.client.resp.NoticeList4ReceiverResultResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MessageServiceFacade {
    @DubboReference
    private NoticeProvider noticeProvider;
    @DubboReference
    private MsgTemplateQueryProvider msgTemplateQueryProvider;
    @Lazy
    @Autowired
    private MerchantStoreAccountService accountService;
    @DubboReference
    private MessageSendLogProvider messageSendLogProvider;


    public void supportNotic(MsgReadLogDTO dto, LoginContextInfoDTO contextInfoDTO) {
        MsgReadLogReq req = new MsgReadLogReq();
        req.setNoticeId(dto.getId());
        BeanUtils.copyProperties(dto,req);
        req.setTenantId(contextInfoDTO.getTenantId());
        req.setUId(contextInfoDTO.getAccountId());
        MerchantStoreAccountVO merchantStoreAccountVO = accountService.queryAccountInfo(contextInfoDTO.getAccountId(), contextInfoDTO.getTenantId());
        if(ObjectUtil.isNotNull(merchantStoreAccountVO)) {
            req.setRoleType(merchantStoreAccountVO.getType());
        }
        req.setUName(contextInfoDTO.getAccountName());
        req.setStoreId(contextInfoDTO.getStoreId());
        req.setPhone(contextInfoDTO.getPhone());
        req.setNoticeId(dto.getId());
//        log.error("编辑message.supportNotic：req={}," , JSONObject.toJSONString(req));
        DubboResponse<Void> resp = noticeProvider.supportMsgReadLog(req);
        if (!resp.isSuccess()) {
            log.error("编辑message.supportNotic：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
//        log.error("编辑message.supportNotic：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
    }

    public MsgDetailVO readNotic(MsgReadLogDTO dto, LoginContextInfoDTO contextInfoDTO) {
        MsgReadLogReq req = new MsgReadLogReq();
        req.setNoticeId(dto.getId());
        BeanUtils.copyProperties(dto,req);
        req.setTenantId(contextInfoDTO.getTenantId());
        req.setUId(contextInfoDTO.getAccountId());
        MerchantStoreAccountVO merchantStoreAccountVO = accountService.queryAccountInfo(contextInfoDTO.getAccountId(), contextInfoDTO.getTenantId());
        if(ObjectUtil.isNotNull(merchantStoreAccountVO)) {
            req.setRoleType(merchantStoreAccountVO.getType());
        }
        req.setUName(contextInfoDTO.getAccountName());
        req.setStoreId(contextInfoDTO.getStoreId());
        req.setPhone(contextInfoDTO.getPhone());
//        log.error("查询message.getNoticeById：req={}," , JSONObject.toJSONString(req));
        DubboResponse<NoticeDetailResultResp> resp = noticeProvider.getNoticeById(req, Boolean.TRUE);
        if (!resp.isSuccess()) {
            log.error("查询message.getNoticeById：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
//        log.error("查询message.getNoticeById：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
        MsgDetailVO msgDetailVO = new MsgDetailVO();
        NoticeDetailResultResp data = resp.getData();
        if(ObjectUtil.isNotNull(data)) {
            BeanUtils.copyProperties(data, msgDetailVO);
        }
        return msgDetailVO;
    }

    public PageInfo<MsgTipListVO> pageNotice(MsgQueryDTO dto, LoginContextInfoDTO contextInfoDTO) {
        NoticeQuery4ReceiverReq req = new NoticeQuery4ReceiverReq();
        BeanUtils.copyProperties(dto,req);
        req.setRecevieUid(contextInfoDTO.getAccountId());
        req.setTenantId(contextInfoDTO.getTenantId());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(dto.getPageNum());
        pageQueryReq.setPageSize(dto.getPageSize());
//        log.error("查询message.pageNotice：req={}," , JSONObject.toJSONString(req));
        DubboResponse<PageResp<NoticeList4ReceiverResultResp>> resp = noticeProvider.page4Receiver(req, pageQueryReq);
        if (!resp.isSuccess()) {
            log.error("查询message.pageNotice：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
//        log.error("查询message.pageNotice：req={},resp={}" , JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
        PageResp<NoticeList4ReceiverResultResp> data = resp.getData();
        List<NoticeList4ReceiverResultResp> list = data.getData();
        if(CollectionUtil.isEmpty(list)){
            return PageInfoHelper.createPageInfo(Collections.emptyList(), dto.getPageSize());
        }
        PageInfo pageInfo = PageInfoHelper.createPageInfo(list, dto.getPageSize());
        List<MsgTipListVO> result = list.stream().map(e -> {
            MsgTipListVO msgTipListVO = new MsgTipListVO();
            BeanUtils.copyProperties(e, msgTipListVO);
            msgTipListVO.setContentId(e.getId());
            return msgTipListVO;
        }).collect(Collectors.toList());
        pageInfo.setList(result);
        pageInfo.setIsLastPage(data.getIsLastPage());
        pageInfo.setTotal(data.getTotal());
        return pageInfo;
    }

    public Integer noticUnReadAmount(LoginContextInfoDTO contextInfoDTO) {
//        log.info("查询message.pageNotice：userid={},tenantId={}" ,contextInfoDTO.getAccountId(), contextInfoDTO.getTenantId());
        DubboResponse<Integer> resp = noticeProvider.getUnReadNoticeAmountByUserId(contextInfoDTO.getAccountId(), contextInfoDTO.getTenantId());
        if (!resp.isSuccess()) {
            log.error("查询message.pageNotice：userid={},tenantId={},resp={}" ,contextInfoDTO.getAccountId(), contextInfoDTO.getTenantId(),JSONObject.toJSON(resp));
            throw new DefaultServiceException(resp.getMsg());
        }
//        log.error("查询message.pageNotice：userid={},tenantId={},resp={}" ,contextInfoDTO.getAccountId(), contextInfoDTO.getTenantId(),JSONObject.toJSON(resp));
        return resp.getData();
    }

//    public Boolean createNotifyMessage(NotifyMessageReq notifyMessageReq){
//        DubboResponse<Boolean> response = messageSendLogProvider.createNotifyMessage(notifyMessageReq);
//        if(!response.isSuccess()){
//            throw new ProviderException(response.getMsg());
//
//        }
//
//        return response.getData();
//    }
}
