package com.cosfo.mall.facade;

import com.cosfo.message.client.provider.MessageSendLogProvider;
import com.cosfo.message.client.req.NotifyMessageReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: fansongsong
 * @Date: 2023-07-21
 * @Description:
 */
@Slf4j
@Component
public class SendLogFacade {

    @DubboReference
    private MessageSendLogProvider sendLogProvider;

    public Boolean createNotifyMessage(NotifyMessageReq notifyMessageReq) {
        DubboResponse<Boolean> response = sendLogProvider.createNotifyMessage(notifyMessageReq);
        if (!response.isSuccess()) {
            throw new ProviderException("添加帆台消息失败");
        }
        return response.getData();
    }


}
