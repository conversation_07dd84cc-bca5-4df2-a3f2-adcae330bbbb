package com.cosfo.mall.facade;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseStorageQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WncServiceFacade {
    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;

    /**
     * 为售后单添加退货物流
     */
    public WarehouseStorageVO queryOneWarehouseStorage(Integer warehouseNo) {
        WarehouseStorageQueryReq req = new WarehouseStorageQueryReq ();
        req.setWarehouseNo(warehouseNo);
        DubboResponse<WarehouseStorageResp> resp = warehouseStorageQueryProvider.queryOneWarehouseStorage (req);
        if (!resp.isSuccess ()) {
            throw new ProviderException(resp.getMsg ());
        }
        WarehouseStorageResp data = resp.getData ();
        if (ObjectUtil.isNotNull (data)) {
            WarehouseStorageVO vo = new WarehouseStorageVO ();
            BeanUtils.copyProperties (data,vo);
            return vo;
        }
        return null;
    }

    /**
     * 批量查询自营仓库信息
     *
     * @param warehouseNos
     * @param tenantId
     * @return
     */
//    public List<WarehouseStorageVO> queryWarehouseStorageList(List<Long> warehouseNos, Long tenantId){
//        WarehouseStorageListQueryReq warehouseStorageListQueryReq = new WarehouseStorageListQueryReq();
//        warehouseStorageListQueryReq.setWarehouseNos(warehouseNos.stream().map(item -> item.intValue()).collect(Collectors.toList()));
//        warehouseStorageListQueryReq.setTenantId(tenantId);
//        warehouseStorageListQueryReq.setWarehouseSource(WarehouseSourceEnum.SAAS_WAREHOUSE.getCode());
//        DubboResponse<List<WarehouseStorageResp>> response = warehouseStorageQueryProvider.queryWarehouseStorageList(warehouseStorageListQueryReq);
//        if (!response.isSuccess ()) {
//            throw new ProviderException("批量查询仓库信息失败：" + response.getMsg ());
//        }
//
//        List<WarehouseStorageResp> responseData = response.getData();
//        List<WarehouseStorageVO> warehouseStorageVOS = WncConvert.convertToList(responseData);
//        return warehouseStorageVOS;
//    }
}
