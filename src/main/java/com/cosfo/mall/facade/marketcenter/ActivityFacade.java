package com.cosfo.mall.facade.marketcenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.facade.converter.ActivityConvert;
import com.cosfo.mall.facade.input.ActivityDetailQueryInput;
import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.activity.provide.ActivityProvide;
import net.xianmu.marketing.center.client.activity.req.ActivityDetailReq;
import net.xianmu.marketing.center.client.activity.resp.ActivityBasicInfoResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: lzh
 * @Date: 2025/10/16 15:19
 * @Param:
 * @Return:
 * @Description:
 **/
@Slf4j
@Component
public class ActivityFacade {

    @DubboReference
    private ActivityProvide activityProvide;

    public ActivityInfoVO getActivityDetail(ActivityDetailQueryInput queryInput) {
        ActivityDetailReq activityDetailReq = new ActivityDetailReq();
        activityDetailReq.setTenantId(queryInput.getTenantId());
        activityDetailReq.setStoreId(queryInput.getStoreId());
        activityDetailReq.setType(queryInput.getType());
        activityDetailReq.setSku(queryInput.getSku());
        DubboResponse<ActivityBasicInfoResp> dubboResponse = null;
        try {
            dubboResponse = activityProvide.getActivityDetail(activityDetailReq);
        } catch (Exception e) {
            log.error("调用营销中心接口失败：{}", JSON.toJSONString(dubboResponse));
            return null;
        }
        if (!DubboResponse.isSuccessResponse(dubboResponse)) {
            log.error("调用营销中心接口失败：{}", JSON.toJSONString(dubboResponse));
            return null;
        }
        return ActivityConvert.toActivityInfoVO(dubboResponse.getData());
    }
}
