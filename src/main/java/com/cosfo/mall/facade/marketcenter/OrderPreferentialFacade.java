package com.cosfo.mall.facade.marketcenter;

import com.cosfo.mall.common.constants.OrderPreferentialTypeEnum;
import com.cosfo.mall.facade.dto.OrderPreferentialDTO;
import com.cosfo.mall.facade.input.OrderPreferentialQueryInput;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.activity.enums.ActivityTypeEnum;
import net.xianmu.marketing.center.client.activity.provide.ActivityProvide;
import net.xianmu.marketing.center.client.activity.req.ActivityDiscountInfoReq;
import net.xianmu.marketing.center.client.activity.resp.ActivityDiscountInfoResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/10/14 14:09
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderPreferentialFacade {

    @DubboReference
    private ActivityProvide activityProvide;

    /**
     * 查询订单优惠计算结果
     *
     * @param orderPreferentialInfoList 订单优惠信息列表
     * @return 订单优惠信息DTO对象
     */
    public Map<Long, OrderPreferentialDTO> queryOrderDiscountCalculateResult(List<OrderPreferentialQueryInput> orderPreferentialInfoList,
                                                                             Long tenantId, Long storeId) {
        if (CollectionUtils.isEmpty(orderPreferentialInfoList)) {
            return Collections.emptyMap();
        }

        ActivityDiscountInfoReq activityDiscountInfoReq = new ActivityDiscountInfoReq();
        activityDiscountInfoReq.setTenantId(tenantId);
        activityDiscountInfoReq.setStoreId(storeId);
        activityDiscountInfoReq.setType(ActivityTypeEnum.FULL_REDUCE.getCode());
        List<ActivityDiscountInfoReq.OrderItemInfo> orderItemInfoList = new ArrayList<>();
        for (OrderPreferentialQueryInput orderPreferentialQueryInput : orderPreferentialInfoList) {
            ActivityDiscountInfoReq.OrderItemInfo orderItemInfo = new ActivityDiscountInfoReq.OrderItemInfo();
            orderItemInfo.setSku(String.valueOf(orderPreferentialQueryInput.getItemId()));
            orderItemInfo.setPrice(orderPreferentialQueryInput.getPrice());
            orderItemInfo.setQuantity(orderPreferentialQueryInput.getQuantity());
            orderItemInfoList.add(orderItemInfo);
        }
        activityDiscountInfoReq.setOrderItemInfoList(orderItemInfoList);

        final DubboResponse<ActivityDiscountInfoResp> dubboResponse = activityProvide.orderItemActivityDiscountInfo(activityDiscountInfoReq);
        if (!DubboResponse.isSuccessResponse(dubboResponse)) {
            throw new ProviderException(DubboResponse.getErrorMsg(dubboResponse));
        }
        final ActivityDiscountInfoResp activityDiscountInfoResp = dubboResponse.getData();
        Map<Long, OrderPreferentialDTO> resultMap = new HashMap<>();
        if (activityDiscountInfoResp == null || CollectionUtils.isEmpty(activityDiscountInfoResp.getOrderItemDiscountInfoList())){
            return resultMap;
        }
        for (ActivityDiscountInfoResp.OrderItemDiscountInfoResp orderItemDiscountInfo : activityDiscountInfoResp.getOrderItemDiscountInfoList()) {
            OrderPreferentialDTO orderPreferentialDTO = new OrderPreferentialDTO();
            orderPreferentialDTO.setPreferentialType(OrderPreferentialTypeEnum.MINUS.getCode());
            orderPreferentialDTO.setOriginalAmount(orderItemDiscountInfo.getPrice());
            orderPreferentialDTO.setQuantity(orderItemDiscountInfo.getQuantity());
            orderPreferentialDTO.setPreferentialAmount(orderItemDiscountInfo.getDiscountAmount());
            orderPreferentialDTO.setRelatedId(activityDiscountInfoResp.getBasicInfoId());
            orderPreferentialDTO.setDiscountsDetailSnapshot(activityDiscountInfoResp.getPricingTypeExt());
            resultMap.put(Long.valueOf(orderItemDiscountInfo.getSku()), orderPreferentialDTO);
        }
        return resultMap;
    }

}
