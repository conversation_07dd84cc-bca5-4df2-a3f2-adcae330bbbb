package com.cosfo.mall.facade;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.facade.converter.ProductFacadeConvert;
import com.cosfo.mall.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.service.ProductAgentSkuService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.constant.GoodsConstant;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.provider.ProductsSpuQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.req.ProductSkuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/9/27 16:02
 * @Description:
 */
@Slf4j
@Component
public class ProductQueryFacade {
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private ProductAgentSkuService productAgentSkuService;

    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;
    @DubboReference
    private ProductsSpuQueryProvider productsSpuQueryProvider;
    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;


    /**
     * 根据条件查询sku_maping
     * @param skuId
     * @param tenantId
     * @return
     */
    public ProductAgentSkuDTO selectBySkuIdAndTenantId(Long skuId, Long tenantId) {
        if (Objects.isNull(skuId) || Objects.isNull(tenantId)) {
            return null;
        }
        if (grayReleaseConfig.isGoodsCenterGrayRelease()) {
            ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setSkuIds(Collections.singletonList(skuId));
            List<ProductsMappingResp> handler = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
            return ProductFacadeConvert.INSTANCE.convert2Dto(handler.get(0));
        } else {
            return productAgentSkuMappingMapper.selectBySkuIdAndTenantId(skuId, tenantId);
        }
    }

    /**
     * 根据条件查询sku_maping
     * @param skuIds
     * @param tenantId
     * @return
     */
    public List<ProductAgentSkuDTO> queryAgentSkuInfo(List<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds) || Objects.isNull(tenantId)) {
            return Collections.emptyList();
        }
        if (grayReleaseConfig.isGoodsCenterGrayRelease()) {
            ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setSkuIds(skuIds);
            List<ProductsMappingResp> handler = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
            return ProductFacadeConvert.INSTANCE.convert2Dtos(handler);
        } else {
            return productAgentSkuService.queryAgentSkuInfo(skuIds, tenantId);
        }
    }

    /**
     * 根据条件查询sku_maping
     * @param agentSkuCodes
     * @param tenantId
     * @return
     */
    public List<ProductAgentSkuDTO> selectBySkuIdAndTenantId(Long tenantId,List<String> agentSkuCodes) {
        if (Objects.isNull(tenantId) || CollectionUtils.isEmpty(agentSkuCodes)) {
            return null;
        }
        if (grayReleaseConfig.isGoodsCenterGrayRelease()) {
            ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setSkuList(agentSkuCodes);
            List<ProductsMappingResp> handler = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
            return ProductFacadeConvert.INSTANCE.convert2Dtos(handler);
        } else {
            return ProductFacadeConvert.INSTANCE.convertEntity2Dtos(productAgentSkuMappingMapper.listBySkuCodes(tenantId,agentSkuCodes));
        }
    }


    /**
     * 分页查询SKU列表
     *(目前未使用，但是别删。这个属于通用写法，可能以后会用到)
     * @param queryReq
     * @return
     */
    public List<ProductSkuDetailResp> selectSkuPage(ProductSkuPageQueryReq queryReq) {
        if (Objects.isNull(queryReq.getPageIndex())) {
            queryReq.setPageIndex(1);
        }
        if (Objects.isNull(queryReq.getPageSize())) {
            queryReq.setPageSize(GoodsConstant.MAX_SIZE_FOR_PAGE_SEARCH);
        }
        PageInfo<ProductSkuDetailResp> handler = RpcResponseUtil.handler(productsSkuQueryProvider.selectSkuPage(queryReq));
        return handler.getList();
    }

    /**
     * 不分页，查询所有SKu列表
     *
     * @param queryReq
     * @return
     */
//    private List<ProductSkuDetailResp> selectSkuList(ProductSkuPageQueryReq queryReq) {
//        int pageIndex = 1;
//        List<ProductSkuDetailResp> skuDetailRespList = new ArrayList<>();
//        // 没有分页参数查询所有
//        while (true) {
//            queryReq.setPageIndex(pageIndex++);
//            queryReq.setPageSize(GoodsConstant.MAX_SIZE_FOR_PAGE_SEARCH);
//            PageInfo<ProductSkuDetailResp> handler = RpcResponseUtil.handler(productsSkuQueryProvider.selectSkuPage(queryReq));
//            if (CollectionUtils.isNotEmpty(handler.getList())) {
//                skuDetailRespList.addAll(handler.getList());
//            }
//            if (!handler.isHasNextPage()) {
//                break;
//            }
//        }
//        return skuDetailRespList;
//    }



    /**
     * 查询sku信息
     *
     * @param skuIds
     * @return
     */
    public List<ProductSkuDetailResp> querySkuInfo(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        return RpcResponseUtil.handler(productsSkuQueryProvider.selectProductSkuDetailById(skuIds));
    }
}
