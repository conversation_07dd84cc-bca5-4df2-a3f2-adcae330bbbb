package com.cosfo.mall.facade;

import com.cosfo.mall.common.constant.XianmuSupplyTenant;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseListQuery;
import net.summerfarm.wnc.client.resp.WarehouseStorageDetailResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-09-08
 * @Description:
 */
@Slf4j
@Service
public class WarehouseStorageQueryFacade {

    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;

    /**
     * @param warehouseListQuery
     * @return
     */
    public List<WarehouseStorageDetailResp> queryWarehouseList(WarehouseListQuery warehouseListQuery) {
        DubboResponse<List<WarehouseStorageDetailResp>> response = warehouseStorageQueryProvider.queryWarehouseList(warehouseListQuery);
        if (!response.isSuccess()) {
            throw new BizException("根据条件获取批量仓库信息失败");
        }
        return response.getData();
    }

    public List<WarehouseStorageDetailResp> warehouseList(List<Integer> warehouseNos) {
        try {
            WarehouseListQuery warehouseListQuery = new WarehouseListQuery();
            warehouseListQuery.setTenantId(XianmuSupplyTenant.TENANT_ID);
            // 开放状态：1、开放
            warehouseListQuery.setStatus(1);
            warehouseListQuery.setWarehouseNos(warehouseNos);
            List<WarehouseStorageDetailResp> respList = queryWarehouseList(warehouseListQuery);
            return respList;
        } catch (Exception e) {
            log.error("获取鲜沐仓库列表信息失败", e);
        }

        return Collections.emptyList();
    }

}
