package com.cosfo.mall.facade;

import com.cosfo.mall.facade.converter.WarehouseInventoryConverter;
import com.cosfo.mall.facade.dto.WarehouseInventoryDTO;
import com.cosfo.mall.facade.dto.WarehouseInventoryQueryDTO;
import net.summerfarm.goods.client.enums.CategoryTypeEnum;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterQueryProvider;
import net.xianmu.inventory.client.saleinventory.dto.req.WarehouseInventoryAggregatedByAddressReq;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseInventoryAggregatedByAddressResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/16
 */
@Component
public class WarehouseInventoryFacade {

    @DubboReference
    private SaleInventoryCenterQueryProvider saleInventoryCenterQueryProvider;

    public List<WarehouseInventoryDTO> queryAreaStoreQuantityList(WarehouseInventoryQueryDTO warehouseInventoryQueryDTO) {
        WarehouseInventoryAggregatedByAddressReq req = WarehouseInventoryConverter.convertWarehouseInventoryAggregatedByAddressReq(warehouseInventoryQueryDTO);
        DubboResponse<WarehouseInventoryAggregatedByAddressResp> response = saleInventoryCenterQueryProvider.queryWarehouseInventoryAggregatedByAddress(req);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        // skucode对应类目类型
        Map<String, WarehouseInventoryQueryDTO.SkuDetail> skucode2DetailMap = warehouseInventoryQueryDTO.getSkuDetailList().stream().collect(Collectors.toMap(WarehouseInventoryQueryDTO.SkuDetail::getSkuCode, Function.identity(), (v1, v2) -> v1));

        WarehouseInventoryAggregatedByAddressResp data = response.getData();
        List<WarehouseInventoryDTO> warehouseInventoryDTOS = WarehouseInventoryConverter.convertToListByResp(data.getWarehouseInventoryList());

        warehouseInventoryDTOS.forEach(e -> {
            e.setFruitFlag(CategoryTypeEnum.FRUIT.getValue().equals(skucode2DetailMap.get(e.getSkuCode()).getCategoryType()));
            e.setSkuId(skucode2DetailMap.get(e.getSkuCode()).getSkuId());
        });

        return warehouseInventoryDTOS;
    }

}
