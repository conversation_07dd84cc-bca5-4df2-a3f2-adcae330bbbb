package com.cosfo.mall.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.provider.Market4StoreProvider;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.page.PageResp;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.utils.PageInfoHelper;
import com.cosfo.mall.facade.converter.MarketFacadeConvert;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.query.MarketItemQuery;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 17:20
 * @Description:
 */
@Service
@Slf4j
public class MarketFacade {

    @DubboReference
    private MarketProvider marketProvider;

    @DubboReference
    private MarketItemProvider marketItemProvider;

    @DubboReference
    private Market4StoreProvider market4StoreProvider;

    /**
     * 用于首页查询商品信息
     * @param marketItemQuery
     * @return
     */
    public PageInfo<MarketItemDTO> listAllMarketItemForHome(MarketItemQuery marketItemQuery) {
        MarketItemPageQuery4StoreReq req = MarketItemPageQuery4StoreReq.builder()
                .tenantId(marketItemQuery.getTenantId())
                .storeId(marketItemQuery.getStoreId())
                .title(marketItemQuery.getTitle())
                .classificationId(marketItemQuery.getClassificationId())
                .brandNames(marketItemQuery.getBrandNames ())
                .notInItemIds(marketItemQuery.getNotInItemIds())
                .pageNum(marketItemQuery.getPageNum())
                .pageSize(marketItemQuery.getPageSize())
                .build();
        DubboResponse<PageResp<MarketItem4StoreResp>> dubboResponse = market4StoreProvider.listAllMarketItem(req);
        if (!dubboResponse.isSuccess()) {
            log.error("查询首页商品信息错误。marketItemQuery={}, dubboResponse={}", marketItemQuery, JSONObject.toJSONString(dubboResponse));
            throw new ProviderException(dubboResponse.getMsg());
        }

        PageResp<MarketItem4StoreResp> pageData = dubboResponse.getData();
        List<MarketItem4StoreResp> list = pageData.getList();
        if (CollectionUtil.isEmpty(list)) {
            return PageInfoHelper.createPageInfo(Collections.emptyList(), marketItemQuery.getPageSize());
        }
        PageInfo pageInfo = PageInfoHelper.createPageInfo(list, marketItemQuery.getPageSize());

        List<MarketItemDTO> resultList = list.stream().map(e -> MarketFacadeConvert.INSTANCE.convert2dto(e)).collect(Collectors.toList());

        pageInfo.setList(resultList);
        pageInfo.setIsLastPage(pageData.getIsLastPage());
        pageInfo.setHasNextPage(!pageData.getIsLastPage());
        pageInfo.setTotal(pageData.getTotal());
        long pages = pageInfo.getTotal() != 0 ? ((pageInfo.getTotal() - 1) / pageInfo.getPageSize()) + 1 : 0;
        pageInfo.setSize((int) pages);
        pageInfo.setPages((int) pages);

        return pageInfo;
    }

    /**
     * 查询简单的商品信息，用于根据title获取商品列表
     * @param tenantId
     * @param storeId
     * @param title
     * @return
     */
    public List<MarketItemDTO> querySimpleMarketItemInfo(Long tenantId, Long storeId, String title) {
        MarketItemQuery4StoreReq req = MarketItemQuery4StoreReq.builder()
                .tenantId(tenantId)
                .storeId(storeId)
                .title(title)
                .pageNum(1)
                .pageSize(200)
                .build();
        DubboResponse<PageResp<MarketItemSimpleInfoResp>> dubboResponse = market4StoreProvider.queryMarketItemInfo(req);
        if (!dubboResponse.isSuccess()) {
            log.error("查询简单商品信息错误。tenantId={}, storeId={}, title={}, dubboResponse={}", tenantId, storeId, title, JSONObject.toJSONString(dubboResponse));
            throw new ProviderException(dubboResponse.getMsg());
        }

        List<MarketItemSimpleInfoResp> respList = dubboResponse.getData().getList();
        if (CollectionUtils.isEmpty(respList)) {
            return Lists.newArrayList();
        }

        return respList.stream().map(MarketFacadeConvert.INSTANCE::convert2dto).collect(Collectors.toList());
    }

    public List<MarketItemDTO> querySimpleMarketItemInfo(Long tenantId,Long storeId,List<Long> itemIds,Integer onSale,Integer pageNum,Integer pageSize) {
        MarketItemQuery4StoreReq req = MarketItemQuery4StoreReq.builder()
                .tenantId(tenantId)
                .storeId(storeId)
                .itemIds(itemIds)
                .onSale(onSale)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
        DubboResponse<PageResp<MarketItemSimpleInfoResp>> dubboResponse = market4StoreProvider.queryMarketItemInfo(req);
        if (!dubboResponse.isSuccess()) {
            log.error("查询简单商品信息错误。tenantId={}, itemIds={}, dubboResponse={}", tenantId, itemIds, JSONObject.toJSONString(dubboResponse));
            throw new ProviderException(dubboResponse.getMsg());
        }

        List<MarketItemSimpleInfoResp> respList = dubboResponse.getData().getList();
        if (CollectionUtils.isEmpty(respList)) {
            return Lists.newArrayList();
        }

        List<MarketItemDTO> resultList = respList.stream().map(e -> MarketFacadeConvert.INSTANCE.convert2dto(e)).collect(Collectors.toList());
        return resultList;
    }

    /**
     * 查询商品详情，包含组合包信息、价格信息
     * @param tenantId
     * @param storeId
     * @param itemId
     * @return
     */
    public MarketItemDTO getMarketItemDetail(Long tenantId, Long storeId, Long itemId) {
        MarketItemDetailQueryReq req = MarketItemDetailQueryReq.builder()
                .tenantId(tenantId)
                .storeId(storeId)
                .itemId(itemId)
                .build();
        DubboResponse<MarketItemDetail4StoreResp> dubboResponse = market4StoreProvider.getMarketItemDetail(req);
        if (!dubboResponse.isSuccess()) {
            log.error("查询商品详情信息错误。tenantId={}, storeId={}, itemId={}, dubboResponse={}", tenantId, storeId, itemId, JSONObject.toJSONString(dubboResponse));
            throw new ProviderException(dubboResponse.getMsg());
        }

        MarketItemDetail4StoreResp resp = dubboResponse.getData();
        if (resp == null) {
            log.error("查询商品详情信息为空。tenantId={}, storeId={}, itemId={}, dubboResponse={}", tenantId, storeId, itemId, JSONObject.toJSONString(dubboResponse));
            return null;
        }

        return MarketFacadeConvert.INSTANCE.convert2dto(resp);
    }

    /**
     * 查询商品信息
     *
     * @param marketItemCommonQueryReq
     */
    public List<MarketItemInfoResp> queryMarketItemList(MarketItemCommonQueryReq marketItemCommonQueryReq){
        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = new MarketItemInfoQueryFlagReq();
        marketItemInfoQueryFlagReq.setClassificationIdFlag (true);
        marketItemCommonQueryReq.setMarketItemInfoQueryFlagReq (marketItemInfoQueryFlagReq);
        DubboResponse<PageResp<MarketItemInfoResp>> dubboResponse = marketItemProvider.queryMarketItemList(marketItemCommonQueryReq);
        if (!dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse.getMsg());
        }

        PageResp<MarketItemInfoResp> pageResp = dubboResponse.getData();
        return pageResp.getList();
    }

    public PageResp<MarketResp> queryMarketListPage(MarketQueryReq req) {
        DubboResponse<PageResp<MarketResp>> response = marketProvider.queryMarketListPage(req);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }

        return response.getData();
    }

    public List<MarketResp> queryMarketListByMarketIds(Long tenantId, List<Long> marketIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(marketIds)) {
            return Collections.emptyList();
        }

        MarketQueryReq marketQueryReq = new MarketQueryReq();
        marketQueryReq.setTenantId(tenantId);
        marketQueryReq.setMarketIds(marketIds);
        marketQueryReq.setPageNum(NumberConstant.ONE);
        marketQueryReq.setPageSize(marketIds.size());
        PageResp<MarketResp> marketRespPageResp = queryMarketListPage(marketQueryReq);
        return marketRespPageResp.getList();
    }

    /**
     * 返回商品信息 带商品单位
     * @param itemIds
     * @param tenantId
     * @return
     */
    public Map<Long, MarketItemInfoResp> queryItemWithUnit(List<Long> itemIds, Long tenantId) {
        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = new MarketItemInfoQueryFlagReq();
        marketItemInfoQueryFlagReq.setClassificationIdFlag(false);
        marketItemInfoQueryFlagReq.setPriceRangeFlag(false);
        marketItemInfoQueryFlagReq.setStockFlag(false);
        marketItemInfoQueryFlagReq.setCategoryIdFlag(false);
        marketItemInfoQueryFlagReq.setUnitFlag (true);

        MarketItemCommonQueryReq marketItemCommonQueryReq = new MarketItemCommonQueryReq();
        marketItemCommonQueryReq.setTenantId(tenantId);
        marketItemCommonQueryReq.setItemIds(itemIds);
        marketItemCommonQueryReq.setMarketItemInfoQueryFlagReq (marketItemInfoQueryFlagReq);

        PageResp<MarketItemInfoResp> itemInfoRespPageResp = RpcResponseUtil.handler(marketItemProvider.queryMarketItemList (marketItemCommonQueryReq));
        Map<Long, MarketItemInfoResp> marketItemInfoRespMap = itemInfoRespPageResp.getList().stream()
                .collect(Collectors.toMap(MarketItemInfoResp::getItemId, Function.identity(), (k1, k2) -> k1));

        return marketItemInfoRespMap;
    }

    /**
     * 查询set（marekt_item.brandname）
     *
     * @return
     */
    @InMemoryCache
    public Set<String> queryBrandNameByTenantId(Long tenantId) {
        DubboResponse<Set<String>> dubboResponse = marketProvider.queryBrandNameByTenantId(tenantId);
        return RpcResponseUtil.handler (dubboResponse);
    }
}
