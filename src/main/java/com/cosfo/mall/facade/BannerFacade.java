package com.cosfo.mall.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.facade.converter.BannerConvert;
import com.cosfo.mall.facade.dto.BannerDTO;
import com.cosfo.mall.facade.input.BannerInput;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.banner.provide.BannerProvide;
import net.xianmu.marketing.center.client.banner.req.BannerInfoReq;
import net.xianmu.marketing.center.client.banner.resp.BannerInfoResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class BannerFacade {

    @DubboReference
    private BannerProvide bannerProvide;


    public List<BannerDTO> getAllBanner(BannerInput input) {
        BannerInfoReq infoReq = new BannerInfoReq();
        infoReq.setTenantId(input.getTenantId());
        DubboResponse<List<BannerInfoResp>> response;
        try {
            response = bannerProvide.getAllBanner(infoReq);
            if (response == null || !response.isSuccess()) {
                log.warn("BannerFacade[]getAllBanner[]error[]input:{}, response:{}", JSON.toJSONString(input), JSON.toJSONString(response));
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.warn("BannerFacade[]getAllBanner[]error[]input:{}, cause:{}", JSON.toJSONString(input), JSON.toJSONString(e));
            return Collections.emptyList();
        }
        List<BannerInfoResp> bannerInfoRespList = response.getData();
        return BannerConvert.toBannerDTOList(bannerInfoRespList);
    }
}
