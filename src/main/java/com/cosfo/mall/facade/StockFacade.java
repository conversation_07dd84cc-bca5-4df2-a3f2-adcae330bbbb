package com.cosfo.mall.facade;

import com.cofso.item.client.enums.StockRecordType;
import com.cofso.item.client.provider.StockProvider;
import com.cofso.item.client.resp.StockResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/16
 */
@Slf4j
@Component
public class StockFacade {

    @DubboReference
    private StockProvider stockProvider;

    public Boolean  increaseSelfStock(Long tenantId, StockRecordType recordType, Long itemId, Integer addAmount, String recordNo){
        DubboResponse<Boolean> dubboResponse = stockProvider.increaseSelfStock(tenantId, recordType, itemId, addAmount, recordNo);
        if(!dubboResponse.isSuccess()){
            throw new BizException(dubboResponse.getMsg());
        }

        return dubboResponse.getData();
    }

    public List<StockResp> batchQuery(Long tenantId, List<Long> itemIds){
        DubboResponse<List<StockResp>> dubboResponse = stockProvider.batchQuery(tenantId, itemIds);
        if(!dubboResponse.isSuccess()){
            throw new BizException(dubboResponse.getMsg());
        }

        return dubboResponse.getData();
    }
}
