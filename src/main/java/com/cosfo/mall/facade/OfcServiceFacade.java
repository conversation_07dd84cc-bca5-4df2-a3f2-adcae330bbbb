package com.cosfo.mall.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.delivery.model.vo.FulfillmentDeliveryVO;
import com.cosfo.mall.facade.converter.OfcConvert;
import com.cosfo.mall.order.model.dto.LogisticsDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderOperateProvider;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.*;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.summerfarm.ofc.client.resp.FulfillmentWaitDeliveryResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.exception.I18nProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OfcServiceFacade {
    @DubboReference
    private FulfillmentOrderOperateProvider fulfillmentOrderOperateProvider;
    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    /**
     * 为售后单添加退货物流
     */
    public void insertAfterSaleLogistics(LogisticsDTO dto,String userName,Long warehouseNo) {
        InsertAfterSaleLogisticsReq req = new InsertAfterSaleLogisticsReq ();
        req.setAfterSaleOrderNo(dto.getAfterSaleOrderNo ());
        req.setOperator(userName);
        FulfillmentDeliveryInfoReq fulfillmentDeliveryInfoReq = new FulfillmentDeliveryInfoReq ();
        fulfillmentDeliveryInfoReq.setType(dto.getType ());
        fulfillmentDeliveryInfoReq.setLogisticsCompany(dto.getLogisticsCompany ());
        fulfillmentDeliveryInfoReq.setLogisticsNo(dto.getLogisticsNo ());
        fulfillmentDeliveryInfoReq.setRemark(dto.getRemark ());
        fulfillmentDeliveryInfoReq.setPics(dto.getPics ());
        req.setDeliveryInfo(fulfillmentDeliveryInfoReq);
        req.setWarehouseNo (ObjectUtil.isNotNull (warehouseNo)?warehouseNo.intValue ():null);
        DubboResponse<Void> resp = fulfillmentOrderOperateProvider.insertAfterSaleLogistics (req);
        if (!resp.isSuccess ()) {
            throw new DefaultServiceException (resp.getMsg ());
        }
    }

    public void upadteSaleLogistics(LogisticsDTO dto,String userName) {
        UpdateFulfillmentDeliveryInfoReq req = new UpdateFulfillmentDeliveryInfoReq ();
        req.setSourceOrderNo(dto.getAfterSaleOrderNo ());
        req.setOperator(userName);
        req.setBatchNo (dto.getBatchNo ());
        FulfillmentDeliveryInfoReq fulfillmentDeliveryInfoReq = new FulfillmentDeliveryInfoReq ();
        fulfillmentDeliveryInfoReq.setType(dto.getType ());
        fulfillmentDeliveryInfoReq.setLogisticsCompany(dto.getLogisticsCompany ());
        fulfillmentDeliveryInfoReq.setLogisticsNo(dto.getLogisticsNo ());
        fulfillmentDeliveryInfoReq.setRemark(dto.getRemark ());
        fulfillmentDeliveryInfoReq.setPics(dto.getPics ());
        req.setNewDeliveryInfo(fulfillmentDeliveryInfoReq);
        DubboResponse<Void> resp = fulfillmentOrderOperateProvider.updateDeliveryInfo (req);
        if (!resp.isSuccess ()) {
            throw new DefaultServiceException (resp.getMsg ());
        }
    }

    /**
     * 查询物流
     * @param orderNos
     * @return
     */
    public List<FulfillmentDeliveryVO> queryDelivery(List<String> orderNos) {
        QueryFulfillmentDeliveryReq req = new QueryFulfillmentDeliveryReq();
        req.setOrderNoList(orderNos);
        DubboResponse<List<FulfillmentDeliveryResp>> resp = fulfillmentOrderQueryProvider.queryOrderDelivery(req);
        if (!resp.isSuccess()) {
            throw new DefaultServiceException(resp.getMsg());
        }
        List<FulfillmentDeliveryResp> data = resp.getData();
        if (CollectionUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data.stream().map(e -> {
            FulfillmentDeliveryVO fulfillmentDeliveryVO = new FulfillmentDeliveryVO();
            BeanUtils.copyProperties(e, fulfillmentDeliveryVO);
            return fulfillmentDeliveryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询等待配送清单
     *
     * @param orderNos
     * @return
     */
    public List<FulfillmentDeliveryVO> queryWaitDelivery(List<String> orderNos){
        QueryFulfillmentWaitDeliveryReq req = new QueryFulfillmentWaitDeliveryReq();
        req.setOrderNoList(orderNos);
        DubboResponse<List<FulfillmentWaitDeliveryResp>> response = fulfillmentOrderQueryProvider.queryWaitDelivery(req);
        if (!response.isSuccess ()) {
            throw new I18nProviderException("查询等待配送清单失败：{0}", response.getMsg ());
        }

        List<FulfillmentWaitDeliveryResp> list = response.getData();
        List<FulfillmentDeliveryVO> fulfillmentDeliveryVOS = OfcConvert.convertToList(list);
        return fulfillmentDeliveryVOS;
    }
}
