package com.cosfo.mall.storeinventory.convert;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckInput;
import com.cosfo.storeinventory.model.dto.req.CheckOrderItemDTO;
import com.cosfo.storeinventory.model.dto.req.CheckOrderOptReqDTO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2024/8/2 下午5:13
 */
public class CheckOrderConvert {

    public static CheckOrderOptReqDTO convert(StoreInventoryCheckInput input, LoginContextInfoDTO loginContextInfoDTO){
        if (input == null || loginContextInfoDTO == null) {
            return null;
        }
        CheckOrderOptReqDTO checkOrderOptReqDTO = new CheckOrderOptReqDTO();
        checkOrderOptReqDTO.setTenantId(loginContextInfoDTO.getTenantId());
        checkOrderOptReqDTO.setStoreId(loginContextInfoDTO.getStoreId());
        checkOrderOptReqDTO.setUpdatorId(loginContextInfoDTO.getAccountId());
        checkOrderOptReqDTO.setUpdatorName(loginContextInfoDTO.getAccountName());
        checkOrderOptReqDTO.setInventoryCheckNo(input.getInventoryCheckNo());
        checkOrderOptReqDTO.setCheckName(input.getCheckName());
        checkOrderOptReqDTO.setRemark (input.getRemark ());
        if(CollectionUtils.isEmpty(input.getItemList())){
            return checkOrderOptReqDTO;
        }

        List<CheckOrderItemDTO> itemDTOList = input.getItemList().stream().map(e -> {
            CheckOrderItemDTO checkOrderItemDTO = new CheckOrderItemDTO();
            checkOrderItemDTO.setItemId(e.getItemId());
            checkOrderItemDTO.setExpectedQuantity(e.getExpectedQuantity());
            checkOrderItemDTO.setActualQuantity(new BigDecimal (e.getActualQuantity()));
            checkOrderItemDTO.setTitle(e.getTitle());
            checkOrderItemDTO.setSpecification(e.getSpecification());
            checkOrderItemDTO.setStoreInventoryUnit(e.getStoreInventoryUnit());
            return checkOrderItemDTO;
        }).collect(Collectors.toList());

        checkOrderOptReqDTO.setItemDTOList(itemDTOList);
        return checkOrderOptReqDTO;
    }

}