package com.cosfo.mall.storeinventory.convert;

import com.cosfo.mall.storeinventory.model.vo.StoreItemInventoryLogVO;
import com.cosfo.storeinventory.model.po.StoreItemInventoryLog;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StoreItemInventoryLogConvert {

    StoreItemInventoryLogConvert INSTANCE = Mappers.getMapper (StoreItemInventoryLogConvert.class);

    StoreItemInventoryLogVO e2StoreItemInventoryLogVO(StoreItemInventoryLog e);
}