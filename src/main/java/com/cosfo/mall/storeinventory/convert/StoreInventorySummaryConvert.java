package com.cosfo.mall.storeinventory.convert;

import com.cosfo.storeinventory.model.vo.StoreInventorySummaryVO;
import com.cosfo.storeinventory.offline.po.StoreInventorySummaryDay;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StoreInventorySummaryConvert {

    StoreInventorySummaryConvert INSTANCE = Mappers.getMapper (StoreInventorySummaryConvert.class);

    StoreInventorySummaryVO e2StoreInventorySummaryVO(StoreInventorySummaryDay e);
}