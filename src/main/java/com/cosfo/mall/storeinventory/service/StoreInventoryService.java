package com.cosfo.mall.storeinventory.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.storeinventory.model.dto.StoreInventoryItemPageQueryDTO;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckDeleteInput;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckInput;
import com.cosfo.mall.storeinventory.model.vo.MarketItemStoreInventoryVO;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryCheckOrderVO;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryWarningVO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.storeinventory.model.dto.*;
import com.cosfo.storeinventory.model.vo.StoreInventoryBillItemVO;
import com.cosfo.storeinventory.model.vo.StoreInventoryBillVO;
import com.cosfo.storeinventory.model.vo.StoreInventoryCheckRecordItemVO;
import com.cosfo.storeinventory.model.vo.StoreInventoryCheckRecordVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 *
 */
public interface StoreInventoryService {

    /**
     * 盘点单提交
     * @param input
     * @param loginContextInfoDTO
     * @return
     */
    StoreInventoryCheckOrderVO checkSubmit(StoreInventoryCheckInput input, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 盘点单删除
     * @param input
     * @param loginContextInfoDTO
     * @return
     */
    StoreInventoryCheckOrderVO checkDelete(StoreInventoryCheckDeleteInput input, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 盘点单暂存
     * @param input
     * @param loginContextInfoDTO
     * @return
     */
    StoreInventoryCheckOrderVO checkStaging(StoreInventoryCheckInput input, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询门店商品库存列表
     * @param storeInventoryItemPageQueryDTO
     * @param storeId
     * @param tenantId
     * @return
     */
    PageInfo<MarketItemStoreInventoryVO> pageItem(StoreInventoryItemPageQueryDTO storeInventoryItemPageQueryDTO, Long storeId, Long tenantId);

    /**
     * 查询门店盘点单列表
     * @param dto
     * @param storeId
     * @param tenantId
     * @return
     */
    PageInfo<StoreInventoryCheckRecordVO> pageCheckRecord(StoreInventoryCheckRecordPageQueryDTO dto, Long storeId, Long tenantId);
    /**
     * 订单完成，门店库存入库
     * @param orderId
     */
    void orderFinishStoreInventoryInboundWithBill(Long orderId);

    /**
     * 售后单完成，门店库存出库
     */
    void afterOrderFinishStoreInventoryOutboundWithBill(OrderAfterSaleResp afterSaleDTO);

    /**
     * 查询门店盘点单详情列表
     * @param dto
     * @param storeId
     * @param tenantId
     * @return
     */
    List<StoreInventoryCheckRecordItemVO> listCheckRecordItem(StoreInventoryCheckRecordDetailQueryDTO dto, Long storeId, Long tenantId);

    /**
     * 提交出/入库单
     * @param input
     * @param requestContextInfoDTO
     * @return
     */
    String billSubmit(StoreInventoryBillDTO input, LoginContextInfoDTO requestContextInfoDTO);

    /**
     * 入库单/出库单删除
     * @param input
     * @param requestContextInfoDTO
     * @return
     */
    String billDelete(StoreInventoryBillDeleteDTO input, LoginContextInfoDTO requestContextInfoDTO);
    /**
     * 暂存出/入库单
     * @param input
     * @param requestContextInfoDTO
     * @return
     */
    String billStaging(StoreInventoryBillDTO input, LoginContextInfoDTO requestContextInfoDTO);

    /**
     * 查询门店出入库列表
     */
    PageInfo<StoreInventoryBillVO> pageBill(StoreInventoryBillPageQueryDTO dto, Long storeId, Long tenantId);


    /**
     * 查询门店出入库详情列表
     */
    List<StoreInventoryBillItemVO> listBillItem(StoreInventoryBillDetailQueryDTO dto, Long storeId, Long tenantId);

    /**
     * 保存库存预警设置
     * @param dto
     * @param storeId
     * @param tenantId
     */
    void stockConfigSave(StoreItemInventoryConfigDTO dto, Long storeId, Long tenantId);

    /**
     * 库存预警数量查询
     * @return
     */
    StoreInventoryWarningVO warningCount(Long storeId, Long tenantId);

}
