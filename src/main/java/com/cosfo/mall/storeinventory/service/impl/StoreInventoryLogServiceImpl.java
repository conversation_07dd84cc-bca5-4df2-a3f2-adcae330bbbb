package com.cosfo.mall.storeinventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.mall.common.utils.PageInfoConverter;
import com.cosfo.mall.facade.MarketFacade;
import com.cosfo.mall.storeinventory.convert.StoreItemInventoryLogConvert;
import com.cosfo.mall.storeinventory.model.dto.StoreInventoryItemPageQueryDTO;
import com.cosfo.mall.storeinventory.model.vo.MarketItemStoreInventoryVO;
import com.cosfo.mall.storeinventory.model.vo.StoreItemInventoryLogVO;
import com.cosfo.mall.storeinventory.service.StoreInventoryLogService;
import com.cosfo.storeinventory.model.po.StoreItemInventoryLog;
import com.cosfo.storeinventory.service.StoreInventoryLogQueryService;
import com.cosfo.storeinventory.model.dto.StoreInventoryItemLogPageQueryDTO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StoreInventoryLogServiceImpl implements StoreInventoryLogService {

    @Resource
    private StoreInventoryLogQueryService logQueryService;

    @Resource
    private MarketFacade marketFacade;

    @Override
    public PageInfo<StoreItemInventoryLogVO> pageLog(StoreInventoryItemLogPageQueryDTO dto, Long storeId, Long tenantId) {
        Page<StoreItemInventoryLog> page = logQueryService.pageLog (dto, storeId, tenantId);
        PageInfo<StoreItemInventoryLogVO> result = PageInfoConverter.toPageInfo (page, StoreItemInventoryLogConvert.INSTANCE::e2StoreItemInventoryLogVO);
        List<StoreItemInventoryLogVO> resultList = result.getList ();
        List<Long> itemIds = resultList.stream ().map (StoreItemInventoryLogVO::getItemId).collect (Collectors.toList ());

        StoreInventoryItemPageQueryDTO queryDTO =  new StoreInventoryItemPageQueryDTO ();
        queryDTO.setItemIds (itemIds);
        PageInfo<MarketItemStoreInventoryVO> pageResp = marketFacade.pageItemWithoutStore (queryDTO, tenantId);
        List<MarketItemStoreInventoryVO> list = pageResp.getList ();
        Map<Long, MarketItemStoreInventoryVO> map = list.stream().collect(Collectors.toMap(MarketItemStoreInventoryVO::getId, x -> x));

        resultList.forEach (e->{
            Long itemId = e.getItemId ();
            MarketItemStoreInventoryVO marketItemStoreInventoryVO = map.get (itemId);
            if(marketItemStoreInventoryVO!=null) {
                e.setSpecification (marketItemStoreInventoryVO.getSpecification ());
                e.setMainPicture (marketItemStoreInventoryVO.getMainPicture ());
            }
        });

        result.setList (resultList);
        return result;
    }

}
