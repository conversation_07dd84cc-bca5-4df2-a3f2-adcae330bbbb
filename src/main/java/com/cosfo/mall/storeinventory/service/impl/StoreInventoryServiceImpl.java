package com.cosfo.mall.storeinventory.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.mall.common.utils.PageInfoConverter;
import com.cosfo.mall.facade.MarketFacade;
import com.cosfo.mall.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.mall.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.openapi.service.MarketItemBizService;
import com.cosfo.mall.storeinventory.convert.CheckOrderConvert;
import com.cosfo.mall.storeinventory.model.dto.StoreInventoryItemPageQueryDTO;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckDeleteInput;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckInput;
import com.cosfo.mall.storeinventory.model.vo.MarketItemStoreInventoryVO;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryCheckOrderVO;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryWarningVO;
import com.cosfo.mall.storeinventory.service.StoreInventoryService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.storeinventory.application.checkorder.CheckOrderCommandService;
import com.cosfo.storeinventory.application.checkorder.CheckOrderQueryService;
import com.cosfo.storeinventory.application.inventory.StoreInventoryCommandService;
import com.cosfo.storeinventory.application.inventory.StoreInventoryQueryService;
import com.cosfo.storeinventory.enums.*;
import com.cosfo.storeinventory.model.dto.*;
import com.cosfo.storeinventory.model.po.StoreItemInventory;
import com.cosfo.storeinventory.model.vo.*;
import com.cosfo.storeinventory.service.StoreInventoryBillCommandService;
import com.cosfo.storeinventory.service.StoreInventoryBillQueryService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *  门店库存服务类
 *
 * @author: xiaowk
 * @date: 2024/8/2 下午2:12
 */
@Slf4j
@Service
public class StoreInventoryServiceImpl implements StoreInventoryService {

    @Resource
    private CheckOrderCommandService checkOrderCommandService;
    @Resource
    private StoreInventoryQueryService storeInventoryQueryService;
    @Resource
    private StoreInventoryCommandService storeInventoryCommandService;

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private MarketItemProvider marketItemProvider;


    @Resource
    private CheckOrderQueryService checkOrderQueryService;

    @Resource
    private MarketFacade marketFacade;

    @Resource
    private MarketItemService marketItemService;

    @Resource
    private MarketItemBizService marketItemBizService;

    @Resource
    private StoreInventoryBillQueryService billQueryService;

    @Resource
    private StoreInventoryBillCommandService billCommandService;
    @Override
    public StoreInventoryCheckOrderVO checkSubmit(StoreInventoryCheckInput input, LoginContextInfoDTO loginContextInfoDTO) {
        String inventoryCheckNo = checkOrderCommandService.checkSubmit(CheckOrderConvert.convert(input, loginContextInfoDTO));
        return new StoreInventoryCheckOrderVO(inventoryCheckNo);
    }

    @Override
    public StoreInventoryCheckOrderVO checkDelete(StoreInventoryCheckDeleteInput input, LoginContextInfoDTO loginContextInfoDTO) {
        boolean delFlag = checkOrderCommandService.checkDelete(input.getInventoryCheckNo(), loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getAccountName());
        if(!delFlag){
            throw new BizException("盘点记录删除失败");
        }
        return new StoreInventoryCheckOrderVO(input.getInventoryCheckNo());
    }

    @Override
    public StoreInventoryCheckOrderVO checkStaging(StoreInventoryCheckInput input, LoginContextInfoDTO loginContextInfoDTO) {
        String inventoryCheckNo = checkOrderCommandService.checkStaging(CheckOrderConvert.convert(input, loginContextInfoDTO));
        return new StoreInventoryCheckOrderVO(inventoryCheckNo);
    }

    @Override
    public void orderFinishStoreInventoryInboundWithBill(Long orderId) {
        OrderResp orderResp = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        if (orderResp == null) {
            log.error("订单不存在，orderId={}", orderId);
            return;
        }
        // 查看租户是否开启门店进销存
        Boolean storeInventorySwitch = userCenterTenantFacade.getStoreInventorySwitch(orderResp.getTenantId());
        if(!Boolean.TRUE.equals(storeInventorySwitch)){
            log.error("租户未开启门店进销存，不处理，tenantId={}, orderNo={}", orderResp.getTenantId(), orderResp.getOrderNo());
            return;
        }

        // 查看是否已经处理过
        boolean flag = storeInventoryQueryService.existStoreItemInventoryLog(orderResp.getOrderNo());
        if(flag){
            log.info("订单已经入库处理过，orderNo={}", orderResp.getOrderNo());
            return;
        }

        MerchantStoreAccountResultResp merchantStoreAccountInfo = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo (orderResp.getAccountId ());

        // 订单项列表
        List<OrderItemAndSnapshotResp> orderItemList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));

        List<Long> itemIds = orderItemList.stream().map(OrderItemAndSnapshotResp::getItemId).collect(Collectors.toList());

        // 查询配送前售后数量
        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.REFUNDING.getValue(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        orderAfterSaleQueryReq.setOrderIds(Lists.newArrayList(orderId));
        orderAfterSaleQueryReq.setAfterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType());
        List<OrderAfterSaleResp> orderAfterSaleRespList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(orderAfterSaleQueryReq));

        Map<Long, Integer> orderItemId2AmountMap = orderAfterSaleRespList.stream()
                .collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId, Collectors.summingInt(OrderAfterSaleResp::getAmount)));


        // 查询商品信息，库存单位
        StoreInventoryItemPageQueryDTO queryDTO =  new StoreInventoryItemPageQueryDTO ();
        queryDTO.setItemIds (itemIds);
        PageInfo<MarketItemStoreInventoryVO> pageResp = marketFacade.pageItemWithoutStore (queryDTO, orderResp.getTenantId ());
        Map<Long, MarketItemStoreInventoryVO> marketItemMap = pageResp.getList ().stream ().collect (Collectors.toMap (MarketItemStoreInventoryVO::getId, Function.identity (), (k1, k2) -> k1));

        // 更新库存，记录库存变动记录
        StoreInventoryBillDTO reqDTO = new StoreInventoryBillDTO ();
        reqDTO.setBillType(BillTypeEnum.IN_BUY.getCode ());
        reqDTO.setUserId(orderResp.getAccountId ());
        reqDTO.setUserName(merchantStoreAccountInfo==null?"门店":merchantStoreAccountInfo.getAccountName ());
        reqDTO.setRelatedDataId(orderResp.getId ());
        reqDTO.setRelatedDataNo(orderResp.getOrderNo ());

        List<StoreInventoryBillItemDTO> itemDTOs = orderItemList.stream().map(e -> {
            MarketItemStoreInventoryVO marketItem = marketItemMap.get(e.getItemId());
            if(marketItem == null){
                return null;
            }


            // 获取库存单位数量
            BigDecimal inventoryUnitMultiple = marketItem.getStoreOrderingInventoryUnitMultiple ();
            if(inventoryUnitMultiple == null){
                return null;
            }

            // 订货单位数量
            Integer changeQuantity = e.getAmount() - Optional.ofNullable(orderItemId2AmountMap.get(e.getOrderItemId())).orElse(0);
            if(changeQuantity <= 0){
                return null;
            }
            // 订货单位数量转换为库存单位数量
            changeQuantity = changeQuantity * inventoryUnitMultiple.intValue();

            StoreInventoryBillItemDTO item = new StoreInventoryBillItemDTO();
            item.setItemId(e.getItemId());
            item.setTitle(e.getTitle());
            item.setSpecification(marketItem.getSpecification ());
            item.setOptQuantity(new BigDecimal (changeQuantity));
            item.setStoreInventoryUnit(marketItem.getStoreInventoryUnit ());
            item.setUnitPrice(e.getTotalPrice ().divide (BigDecimal.valueOf (e.getAmount ()), RoundingMode.HALF_UP).divide (inventoryUnitMultiple, RoundingMode.HALF_UP));
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(itemDTOs)){
            log.info("订单更新门店库存的商品列表为空，orderNo={}", orderResp.getOrderNo());
            return;
        }
        reqDTO.setItemList(itemDTOs);
        billCommandService.billSubmit (reqDTO, orderResp.getTenantId(), orderResp.getStoreId());
    }

    @Override
    public void afterOrderFinishStoreInventoryOutboundWithBill(OrderAfterSaleResp orderAfterSaleDTO) {
        if(Objects.equals (OrderAfterSaleTypeEnum.NOT_SEND.getType (), orderAfterSaleDTO.getAfterSaleType ())){
            log.info ("orderAfterSaleDTO.gettype 不需要处理返回，aftersale={},orderAfterSaleDTO.getAfterSaleType={}",orderAfterSaleDTO.getAfterSaleOrderNo (),orderAfterSaleDTO.getAfterSaleType());
            return;
        }
        Integer billType;
        if (Arrays.asList (OrderAfterSaleServiceTypeEnum.RETURN_REFUND.getValue(),OrderAfterSaleServiceTypeEnum.RETURN_REFUND_ENTER_BILL.getValue(),OrderAfterSaleServiceTypeEnum.RETURN_REFUND_BALANCE.getValue()).contains ( orderAfterSaleDTO.getServiceType())) {
            billType = BillTypeEnum.OUT_AFTERSALE.getCode ();
        } else if (orderAfterSaleDTO.getServiceType().equals(OrderAfterSaleServiceTypeEnum.RESEND.getValue())) {
            billType = BillTypeEnum.IN_RESEND.getCode ();
        }else{
            log.info ("orderAfterSaleDTO.getServiceType 不需要处理返回，aftersale={},orderAfterSaleDTO.getServiceType()={}",orderAfterSaleDTO.getAfterSaleOrderNo (),orderAfterSaleDTO.getServiceType());
            return;
        }
        // 查看租户是否开启门店进销存
        Boolean storeInventorySwitch = userCenterTenantFacade.getStoreInventorySwitch(orderAfterSaleDTO.getTenantId());
        if(!Boolean.TRUE.equals(storeInventorySwitch)){
            log.error("租户未开启门店进销存，不处理，tenantId={}, afterSaleOrderNo={}", orderAfterSaleDTO.getTenantId(), orderAfterSaleDTO.getAfterSaleOrderNo ());
            return;
        }
        MerchantStoreAccountResultResp merchantStoreAccountInfo = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo (orderAfterSaleDTO.getAccountId ());

        OrderItemAndSnapshotResp orderResp = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById (orderAfterSaleDTO.getOrderItemId ()));

        // 查询商品信息，库存单位
        StoreInventoryItemPageQueryDTO queryDTO =  new StoreInventoryItemPageQueryDTO ();
        queryDTO.setItemIds (Collections.singletonList (orderResp.getItemId ()));
        PageInfo<MarketItemStoreInventoryVO> pageResp = marketFacade.pageItemWithoutStore (queryDTO, orderAfterSaleDTO.getTenantId ());
        Map<Long, MarketItemStoreInventoryVO> marketItemMap = pageResp.getList ().stream ().collect (Collectors.toMap (MarketItemStoreInventoryVO::getId, Function.identity (), (k1, k2) -> k1));

        // 更新库存，记录库存变动记录
        StoreInventoryBillDTO reqDTO = new StoreInventoryBillDTO ();
        reqDTO.setBillType(billType);

        reqDTO.setUserId(orderAfterSaleDTO.getAccountId ());
        reqDTO.setUserName(merchantStoreAccountInfo==null?"门店":merchantStoreAccountInfo.getAccountName ());
        reqDTO.setRelatedDataId(orderAfterSaleDTO.getId ());
        reqDTO.setRelatedDataNo(orderAfterSaleDTO.getAfterSaleOrderNo ());

        MarketItemStoreInventoryVO marketItem = marketItemMap.get(orderResp.getItemId());
        if(marketItem == null){
            log.info ("商品不存在，返回，aftersale={},orderResp.getItemId()={}",orderAfterSaleDTO.getAfterSaleOrderNo (),orderResp.getItemId());
            return;
        }


        // 获取库存单位数量
        BigDecimal inventoryUnitMultiple = marketItem.getStoreOrderingInventoryUnitMultiple ();
        if(inventoryUnitMultiple == null){
            return;
        }

        // 订货单位数量
        Integer changeQuantity = orderAfterSaleDTO.getAmount () * inventoryUnitMultiple.intValue();
        if(changeQuantity <= 0){
            return;
        }

        StoreInventoryBillItemDTO item = new StoreInventoryBillItemDTO();
        item.setItemId(marketItem.getId ());
        item.setTitle(marketItem.getTitle());
        item.setSpecification(marketItem.getSpecification ());
        item.setOptQuantity(BigDecimal.valueOf (changeQuantity));
        item.setStoreInventoryUnit(marketItem.getStoreInventoryUnit ());
        item.setUnitPrice(orderResp.getTotalPrice ().divide (BigDecimal.valueOf (orderResp.getAmount ()), RoundingMode.HALF_UP).divide (inventoryUnitMultiple, RoundingMode.HALF_UP));

        reqDTO.setItemList(Collections.singletonList(item));
        billCommandService.billSubmit (reqDTO, orderResp.getTenantId(), orderAfterSaleDTO.getStoreId());
    }

    @Override
    public List<StoreInventoryCheckRecordItemVO> listCheckRecordItem(StoreInventoryCheckRecordDetailQueryDTO dto, Long storeId, Long tenantId) {
        Long recordId = dto.getId ();
        StoreInventoryCheckRecordBaseVO storeInventoryCheckRecordBaseVO = checkOrderQueryService.queryById (recordId, storeId, tenantId);
        if(storeInventoryCheckRecordBaseVO == null){
            throw new BizException ("盘点单不存在");
        }
        List<StoreInventoryCheckRecordItemVO> vos = checkOrderQueryService.listCheckRecordItem (dto, storeId, tenantId);
        if(CollectionUtil.isEmpty (vos)){
            return Collections.emptyList ();
        }
        //如果是待提交，给出最新的数量
        List<Long> itemIds = vos.stream ().map (StoreInventoryCheckRecordItemVO::getItemId).collect (Collectors.toList ());
        Map<Long, MarketItemInfoResp> marketItemInfoRespMap =  marketFacade.queryItemWithUnit (itemIds, tenantId);
        if(Objects.equals (storeInventoryCheckRecordBaseVO.getRecordStatus (), CheckOrderStatusEnum.STAGING.getCode ())){
            List<StoreItemInventory> storeItemInventories = storeInventoryQueryService.listByItemIds (tenantId, storeId, itemIds);
            Map<Long, StoreItemInventory> storeItemInventoryMap = storeItemInventories.stream ().collect (Collectors.toMap (StoreItemInventory::getItemId, x -> x));
            vos.forEach (e-> e.setExpectedQuantity (storeItemInventoryMap.containsKey (e.getItemId ())?storeItemInventoryMap.get (e.getItemId ()).getExistingQuantity ():BigDecimal.ZERO));
        }
        vos.forEach (e-> {
            if(marketItemInfoRespMap.containsKey (e.getItemId ())){
                e.setMainPicture (marketItemInfoRespMap.get (e.getItemId ()).getMainPicture ());
            }
        });
        return vos;
    }

    @Override
    public String billSubmit(StoreInventoryBillDTO input, LoginContextInfoDTO requestContextInfoDTO) {
        List<StoreInventoryBillItemDTO> itemList = input.getItemList ();
        List<Long> itemIds = itemList.stream ().map (StoreInventoryBillItemDTO::getItemId).collect (Collectors.toList ());
        Map<Long, BigDecimal> priceMap = marketItemBizService.queryItemPriceWithDefaultPrice (requestContextInfoDTO.getTenantId (),requestContextInfoDTO.getStoreId (), itemIds);
        itemList.forEach (e-> e.setUnitPrice (priceMap.get (e.getItemId ())));
        input.setUserId (requestContextInfoDTO.getAccountId ());
        input.setUserName (requestContextInfoDTO.getAccountName ());
        return billCommandService.billSubmit (input, requestContextInfoDTO.getTenantId (),requestContextInfoDTO.getStoreId ());
    }

    @Override
    public String billDelete(StoreInventoryBillDeleteDTO input, LoginContextInfoDTO requestContextInfoDTO) {
        billCommandService.billDelete (input.getBillNo (), requestContextInfoDTO.getTenantId (),requestContextInfoDTO.getStoreId ());
        return input.getBillNo ();
    }

    @Override
    public String billStaging(StoreInventoryBillDTO input, LoginContextInfoDTO requestContextInfoDTO) {
        List<StoreInventoryBillItemDTO> itemList = input.getItemList ();
        List<Long> itemIds = itemList.stream ().map (StoreInventoryBillItemDTO::getItemId).collect (Collectors.toList ());
        Map<Long, BigDecimal> priceMap = marketItemBizService.queryItemPriceWithDefaultPrice (requestContextInfoDTO.getTenantId (),requestContextInfoDTO.getStoreId (), itemIds);
        itemList.forEach (e-> e.setUnitPrice (priceMap.get (e.getItemId ())));
        input.setUserId (requestContextInfoDTO.getAccountId ());
        input.setUserName (requestContextInfoDTO.getAccountName ());
        return billCommandService.billStaging (input, requestContextInfoDTO.getTenantId (),requestContextInfoDTO.getStoreId ());
    }

    @Override
    public PageInfo<StoreInventoryBillVO> pageBill(StoreInventoryBillPageQueryDTO dto, Long storeId, Long tenantId) {
        Page<StoreInventoryBillVO> page = billQueryService.pageByCondition (dto, storeId, tenantId);
        if(page!=null){
            List<StoreInventoryBillVO> records = page.getRecords ().stream ().filter(x -> CollectionUtil.isNotEmpty(x.getItemIds())).collect(Collectors.toList());
            records.forEach (e-> e.setPictureList (getPicMap(records.stream ().collect (Collectors.toMap (StoreInventoryBillVO::getBillNo, StoreInventoryBillVO::getItemIds)),tenantId).get (e.getBillNo ())));
        }
        return PageInfoConverter.toPageInfo(page, (StoreInventoryBillVO record) -> record);
    }

    @Override
    public List<StoreInventoryBillItemVO> listBillItem(StoreInventoryBillDetailQueryDTO dto, Long storeId, Long tenantId) {
        Long billId = dto.getId ();
        StoreInventoryBillBaseVO billBaseVO = billQueryService.queryById (billId, storeId, tenantId);
        if(billBaseVO == null){
            throw new BizException ("单据不存在");
        }
        List<StoreInventoryBillItemVO> vos = billQueryService.listBillItem (billId, storeId, tenantId);
        if(CollectionUtil.isEmpty (vos)){
            return Collections.emptyList ();
        }
        //如果是待提交，给出最新的数量
        List<Long> itemIds = vos.stream ().map (StoreInventoryBillItemVO::getItemId).collect (Collectors.toList ());
        Map<Long, MarketItemInfoResp> marketItemInfoRespMap =  marketFacade.queryItemWithUnit (itemIds, tenantId);
        if(Objects.equals (billBaseVO.getBillStatus (), StoreInventoryBillStatusEnum.STAGING.getCode ())){
            List<StoreItemInventory> storeItemInventories = storeInventoryQueryService.listByItemIds (tenantId, storeId, itemIds);
            Map<Long, StoreItemInventory> storeItemInventoryMap = storeItemInventories.stream ().collect (Collectors.toMap (StoreItemInventory::getItemId, x -> x));
            vos.forEach (e-> e.setActualQuantity (storeItemInventoryMap.containsKey (e.getItemId ())?storeItemInventoryMap.get (e.getItemId ()).getExistingQuantity ():BigDecimal.ZERO));
        }
        vos.forEach (e-> {
            if(marketItemInfoRespMap.containsKey (e.getItemId ())){
                e.setMainPicture (marketItemInfoRespMap.get (e.getItemId ()).getMainPicture ());
            }
        });
        return vos;
    }

    @Override
    public void stockConfigSave(StoreItemInventoryConfigDTO dto, Long storeId, Long tenantId) {
        storeInventoryCommandService.saveStockConfig (dto, storeId, tenantId);
    }

    @Override
    public StoreInventoryWarningVO warningCount(Long storeId, Long tenantId) {
        Long excessCount = storeInventoryQueryService.countByStatus(storeId,tenantId, InventoryStatusEnum.EXCESS.getCode ());
        Long outOfCount = storeInventoryQueryService.countByStatus(storeId,tenantId, InventoryStatusEnum.OUT_OF.getCode ());
        StoreInventoryWarningVO storeInventoryWarningVO = new StoreInventoryWarningVO ();
        storeInventoryWarningVO.setExcessCount(excessCount);
        storeInventoryWarningVO.setOutOfCount(outOfCount);
        return storeInventoryWarningVO;
    }

    @Override
    public PageInfo<MarketItemStoreInventoryVO> pageItem(StoreInventoryItemPageQueryDTO queryDTO, Long storeId, Long tenantId) {
        Integer inventoryStatus = queryDTO.getInventoryStatus ();
        List<StoreItemInventory> storeItemInventories = null;
        if(inventoryStatus != null){
            storeItemInventories = storeInventoryQueryService.listByItemStatus(tenantId, storeId,inventoryStatus);
            if(CollectionUtil.isEmpty (storeItemInventories)){
                return PageInfo.emptyPageInfo ();
            }else{
                List<Long> itemIds = storeItemInventories.stream ().map (StoreItemInventory::getItemId).collect (Collectors.toList ());
                queryDTO.setItemIds (itemIds);
            }
        }
        PageInfo<MarketItemStoreInventoryVO> pageResp = marketFacade.pageItemWithoutStore (queryDTO, tenantId);
        List<MarketItemStoreInventoryVO> list = pageResp.getList ();
        if(CollectionUtil.isNotEmpty (list)) {
            List<Long> itemIds = list.stream ().map (MarketItemStoreInventoryVO::getId).collect (Collectors.toList ());
            if(CollectionUtil.isEmpty (storeItemInventories)) {
                storeItemInventories = storeInventoryQueryService.listByItemIds (tenantId, storeId, itemIds);
            }
            Map<Long, StoreItemInventory> storeItemInventoryMap = storeItemInventories.stream ().collect (Collectors.toMap (StoreItemInventory::getItemId, x -> x));

            Map<Long, BigDecimal> priceMap = marketItemBizService.queryItemPriceWithDefaultPrice (tenantId, storeId, itemIds);

            list.forEach (e->{
                if(storeItemInventoryMap.containsKey (e.getId ())) {
                    StoreItemInventory storeItemInventory = storeItemInventoryMap.get (e.getId ());
                    e.setExistingQuantity (storeItemInventory.getExistingQuantity ());
                    e.setStockMin (storeItemInventory.getStockMin ());
                    e.setStockMax (storeItemInventory.getStockMax ());
                    e.setInventoryStatus (storeItemInventory.getInventoryStatus ());
                    e.setWarningSwitch (storeItemInventory.getWarningSwitch ());
                }else {
                    e.setExistingQuantity (BigDecimal.ZERO);
                    e.setInventoryStatus (InventoryStatusEnum.NOT_SET.getCode ());
                    e.setWarningSwitch (WarningSwitchEnum.CLOSE.getCode ());
                }
                if(e.getExistingQuantity () == null || e.getStoreOrderingInventoryUnitMultiple () == null || !priceMap.containsKey (e.getId ())) {
                    e.setTotalPrice (BigDecimal.ZERO);
                }else{
                    e.setTotalPrice (priceMap.get (e.getId ()).divide (e.getStoreOrderingInventoryUnitMultiple (), RoundingMode.HALF_UP).multiply (e.getExistingQuantity ()));
                }
            });
        }
        return pageResp;
    }

    @Override
    public PageInfo<StoreInventoryCheckRecordVO> pageCheckRecord(StoreInventoryCheckRecordPageQueryDTO dto, Long storeId, Long tenantId) {
        Page<StoreInventoryCheckRecordVO> page = checkOrderQueryService.pageByCondition (dto, storeId, tenantId);
        if(page!=null){
            List<StoreInventoryCheckRecordVO> records = page.getRecords ().stream ().filter(x -> CollectionUtil.isNotEmpty(x.getItemIds())).collect(Collectors.toList());
            records.forEach (e-> e.setPictureList (getPicMap(records.stream ().collect (Collectors.toMap (StoreInventoryCheckRecordVO::getRecordNo, StoreInventoryCheckRecordVO::getItemIds)),tenantId).get (e.getRecordNo ())));
        }
        return PageInfoConverter.toPageInfo(page, (StoreInventoryCheckRecordVO record) -> record);
    }

    private Map<String,List<String>> getPicMap(Map<String,List<Long>> map,Long tenantId) {
        Map<String, List<String>> itemPicMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty (map)){
            List<Long> itemList = map.values().stream().flatMap(List::stream).collect(Collectors.toList());

            List<MarketItemVO> marketItemVOS = marketItemService.batchByItemIds (itemList, tenantId);
            if(CollectionUtil.isNotEmpty (marketItemVOS)){

                Map<Long, String> marketItemMap = marketItemVOS.stream().filter (e->e.getMainPicture()!=null).collect(Collectors.toMap(MarketItemVO::getItemId,vo -> vo.getMainPicture().split(",")[0]));

                map.forEach((no,itemIds) -> {
                    List<String> picList = itemIds.stream ()
                            .filter(marketItemMap::containsKey)
                            .map(marketItemMap::get)
                            .collect(Collectors.toList());
                    itemPicMap.put (no,picList);
                });
            }
        }
        return itemPicMap;
    }
}
