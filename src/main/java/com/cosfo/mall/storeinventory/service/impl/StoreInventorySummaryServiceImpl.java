package com.cosfo.mall.storeinventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.mall.common.utils.PageInfoConverter;
import com.cosfo.mall.storeinventory.convert.StoreInventorySummaryConvert;
import com.cosfo.mall.storeinventory.service.StoreInventorySummaryService;
import com.cosfo.storeinventory.model.dto.StoreInventoryItemSummaryQueryDTO;
import com.cosfo.storeinventory.model.vo.StoreInventorySummaryVO;
import com.cosfo.storeinventory.offline.po.StoreInventorySummaryDay;
import com.cosfo.storeinventory.service.StoreInventorySummaryQueryService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class StoreInventorySummaryServiceImpl implements StoreInventorySummaryService {

    @Resource
    private StoreInventorySummaryQueryService summaryQueryService;
    @Override
    public PageInfo<StoreInventorySummaryVO> pageSummary(StoreInventoryItemSummaryQueryDTO dto, Long storeId, Long tenantId) {
        Page<StoreInventorySummaryDay> page = summaryQueryService.pageSummary (dto, storeId, tenantId);
        PageInfo<StoreInventorySummaryVO> result = PageInfoConverter.toPageInfo (page, StoreInventorySummaryConvert.INSTANCE::e2StoreInventorySummaryVO);
        return result;
    }

    @Override
    public StoreInventorySummaryVO detailSummary(StoreInventoryItemSummaryQueryDTO dto, Long storeId, Long tenantId) {
        StoreInventorySummaryDay detailSummary = summaryQueryService.detailSummary (dto, storeId, tenantId);
        return StoreInventorySummaryConvert.INSTANCE.e2StoreInventorySummaryVO (detailSummary);
    }
}
