package com.cosfo.mall.storeinventory.service;

import com.cosfo.mall.storeinventory.model.vo.StoreInventoryWarningVO;
import com.cosfo.storeinventory.model.dto.StoreInventoryItemSummaryQueryDTO;
import com.cosfo.storeinventory.model.vo.StoreInventorySummaryVO;
import com.github.pagehelper.PageInfo;

public interface StoreInventorySummaryService {

    PageInfo<StoreInventorySummaryVO> pageSummary(StoreInventoryItemSummaryQueryDTO dto, Long storeId, Long tenantId);

    StoreInventorySummaryVO detailSummary(StoreInventoryItemSummaryQueryDTO dto, Long storeId, Long tenantId);

}