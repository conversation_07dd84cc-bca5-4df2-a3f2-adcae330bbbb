package com.cosfo.mall.storeinventory.model.input;

import com.cosfo.mall.storeinventory.model.dto.MarketItemStoreInventoryCheckDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/8/1 上午10:21
 */
@Data
public class StoreInventoryCheckInput {

    /**
     * 盘点单号，修改时必传，新增时为空
     */
    private String inventoryCheckNo;

    /**
     * 单据名称
     */
    private String checkName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点商品列表
     */
    @Valid
    @NotEmpty(message = "商品列表不能为空")
    @Size(max = 100,message = "商品列表请在1~100条范围内")
    private List<MarketItemStoreInventoryCheckDTO> itemList;
}
