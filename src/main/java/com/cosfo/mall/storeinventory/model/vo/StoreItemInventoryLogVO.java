package com.cosfo.mall.storeinventory.model.vo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店商品库存log
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Getter
@Setter
public class StoreItemInventoryLogVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 商品sku id
     */
    private Long itemId;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 操作前库存数量
     */
    private BigDecimal beforeExistingQuantity;

    /**
     * 操作后库存数量
     */
    private BigDecimal afterExistingQuantity;

    /**
     * 操作类型,0=入库,1=出库
     */
    private Integer optType;

    /**
     * 关联数据类型,1=订单,2=售后,3=手动入库,4=手动出库
     */
    private Integer relatedDataType;

    /**
     * 关联数据id
     */
    private Long relatedDataId;

    /**
     * 关联数据编码,无编码的时候取产生数据的毫秒时间戳
     */
    private String relatedDataNo;

    /**
     * 商品sku 标题
     */
    private String title;

    /**
     * 商品sku 规格
     */
    private String specification;
    /**
     * 图片
     */
    private String mainPicture;
    /**
     * 商品库存单位
     */
    private String storeInventoryUnit;

    /**
     * 单据类型
     * 100-其他入库;101-采购入库;102-补发入库;103-盘盈入库;104-换货入库;200-销售出库;201-报损出库;201-采购退货出库(退货退款);203其他出库;204-盘亏出库;205-换货出库
     */
    private Integer billType;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
