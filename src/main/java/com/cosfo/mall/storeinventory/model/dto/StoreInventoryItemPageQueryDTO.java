package com.cosfo.mall.storeinventory.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 */
@Data
public class StoreInventoryItemPageQueryDTO {
    /**
     * 分类id
     */
    private Long classificationId;
    /**
     * 编码
     */
    private Long id;
    /**
     * 名称
     */
    private String title;
    /**
     * 库存状态,0=未开启库存状态计算,1=正常,2=堆积,3=缺货
     */
    private Integer inventoryStatus;
    /**
     * 商品ID
     */
    private List<Long> itemIds;

    private Integer pageIndex;
    private Integer pageSize;
}
