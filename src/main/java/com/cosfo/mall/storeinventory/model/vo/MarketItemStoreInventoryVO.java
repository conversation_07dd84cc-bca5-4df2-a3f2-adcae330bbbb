package com.cosfo.mall.storeinventory.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MarketItemStoreInventoryVO implements Serializable {
    /**
     * 编码 、主键id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 库存数量
     */
    private BigDecimal existingQuantity;

    /**
     * 商品库存单位
     */
    private String storeInventoryUnit;
    /**
     * 订货 库存 单位倍数
     */
    private BigDecimal storeOrderingInventoryUnitMultiple;
    /**
     * 标准单价
     */
    private BigDecimal standardUnitPrice;

    /**
     * 货值
     */
    private BigDecimal totalPrice;
    /**
     * 库存状态,0=未开启库存状态计算,1=正常,2=堆积,3=缺货
     */
    private Integer inventoryStatus;
    /**
     * 库存最低阈值
     */
    private BigDecimal stockMin;
    /**
     * 库存最高阈值
     */
    private BigDecimal stockMax;
    /**
     * 库存预警开关,0=关闭;1=开启
     */
    private Integer warningSwitch;
}
