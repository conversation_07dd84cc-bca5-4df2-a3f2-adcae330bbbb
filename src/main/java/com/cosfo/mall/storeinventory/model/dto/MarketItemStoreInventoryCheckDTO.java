package com.cosfo.mall.storeinventory.model.dto;

import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MarketItemStoreInventoryCheckDTO implements Serializable {

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空")
    private Long itemId;

    /**
     * 商品标题
     */
    @NotBlank(message = "商品标题不能为空")
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 预期盘点结果数量（盘点前库存数量）
     */
    @NotNull(message = "实时库存不能为空")
    private BigDecimal expectedQuantity;

    /**
     * 实际盘点得出的库存数量（盘点设置的库存数量）
     */
    @NotNull(message = "盘点库存不能为空")
    @Min(value = 0, message = "盘点库存不能小于0")
    @Digits(integer = 10, fraction = 0, message = "盘点库存不能为小数")
    private String actualQuantity;

    /**
     * 商品库存单位
     */
    @NotBlank(message = "商品库存单位不能为空")
    private String storeInventoryUnit;


}
