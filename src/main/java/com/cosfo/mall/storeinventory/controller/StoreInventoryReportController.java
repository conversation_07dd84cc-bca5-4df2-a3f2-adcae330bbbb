package com.cosfo.mall.storeinventory.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryWarningVO;
import com.cosfo.mall.storeinventory.model.vo.StoreItemInventoryLogVO;
import com.cosfo.mall.storeinventory.service.StoreInventoryLogService;
import com.cosfo.mall.storeinventory.service.StoreInventorySummaryService;
import com.cosfo.storeinventory.model.dto.StoreInventoryItemLogPageQueryDTO;
import com.cosfo.storeinventory.model.dto.StoreInventoryItemSummaryQueryDTO;
import com.cosfo.storeinventory.model.vo.StoreInventorySummaryVO;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述: 门店库存报表
 *
 */
@RestController
@RequestMapping("/store-inventory-report")
public class StoreInventoryReportController extends BaseController {
    @Resource
    private StoreInventoryLogService storeInventoryLogService;
    @Resource
    private StoreInventorySummaryService storeInventorySummaryService;
    /**
     * 库存流水记录
     * @return
     */
    @RequestMapping(value = "/item-inventory-log", method = RequestMethod.POST)
    public CommonResult<PageInfo<StoreItemInventoryLogVO>> pageLog(@RequestBody StoreInventoryItemLogPageQueryDTO dto) {
        return CommonResult.ok(storeInventoryLogService.pageLog(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }
    /**
     * 出入库汇总列表
     * @return
     */
    @RequestMapping(value = "/item-inventory-summary/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<StoreInventorySummaryVO>> pageSummary(@RequestBody StoreInventoryItemSummaryQueryDTO dto) {
        return CommonResult.ok(storeInventorySummaryService.pageSummary(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }
    /**
     * 出入库汇总详情
     * @return
     */
    @RequestMapping(value = "/item-inventory-summary/detail", method = RequestMethod.POST)
    public CommonResult<StoreInventorySummaryVO> detailSummary(@RequestBody StoreInventoryItemSummaryQueryDTO dto) {
        return CommonResult.ok(storeInventorySummaryService.detailSummary(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }
}
