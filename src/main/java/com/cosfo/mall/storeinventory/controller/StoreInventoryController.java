package com.cosfo.mall.storeinventory.controller;

import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.storeinventory.model.dto.StoreInventoryItemPageQueryDTO;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckDeleteInput;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckInput;
import com.cosfo.mall.storeinventory.model.vo.MarketItemStoreInventoryVO;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryCheckOrderVO;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryWarningVO;
import com.cosfo.storeinventory.model.dto.*;
import com.cosfo.storeinventory.model.vo.StoreInventoryCheckRecordItemVO;
import com.cosfo.storeinventory.model.vo.StoreInventoryBillItemVO;
import com.cosfo.mall.storeinventory.service.StoreInventoryService;
import com.cosfo.storeinventory.model.vo.StoreInventoryCheckRecordVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.cosfo.storeinventory.model.vo.StoreInventoryBillVO;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述: 门店库存
 *
 */
@RestController
@RequestMapping("/store-inventory")
public class StoreInventoryController extends BaseController {
    @Resource
    private StoreInventoryService storeInventoryService;

    /**
     * 查询门店商品库存列表
     */
    @RequestMapping(value = "/pageItem", method = RequestMethod.POST)
    public CommonResult<PageInfo<MarketItemStoreInventoryVO>> pageItem(@RequestBody StoreInventoryItemPageQueryDTO storeInventoryItemPageQueryDTO) {
        return CommonResult.ok(storeInventoryService.pageItem(storeInventoryItemPageQueryDTO,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }


    /**
     * 盘点提交
     */
    @RequestMapping(value = "/check/submit", method = RequestMethod.POST)
    public CommonResult<StoreInventoryCheckOrderVO> checkSubmit(@RequestBody @Valid StoreInventoryCheckInput input) {
        return CommonResult.ok(storeInventoryService.checkSubmit(input, getRequestContextInfoDTO()));
    }

    /**
     * 盘点删除
     */
    @RequestMapping(value = "/check/delete", method = RequestMethod.POST)
    public CommonResult<StoreInventoryCheckOrderVO> checkDelete(@RequestBody @Valid StoreInventoryCheckDeleteInput input) {
        return CommonResult.ok(storeInventoryService.checkDelete(input, getRequestContextInfoDTO()));
    }


    /**
     * 盘点暂存（保存草稿）
     */
    @RequestMapping(value = "/check/staging", method = RequestMethod.POST)
    public CommonResult<StoreInventoryCheckOrderVO> checkStaging(@RequestBody @Valid StoreInventoryCheckInput input) {
        return CommonResult.ok(storeInventoryService.checkStaging(input, getRequestContextInfoDTO()));
    }

    /**
     * 查询门店盘点单列表
     */
    @RequestMapping(value = "/pageCheckRecord", method = RequestMethod.POST)
    public CommonResult<PageInfo<StoreInventoryCheckRecordVO>> pageCheckRecord(@RequestBody StoreInventoryCheckRecordPageQueryDTO dto) {
        return CommonResult.ok (storeInventoryService.pageCheckRecord(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }

    /**
     * 查询门店盘点单详情列表
     */
    @RequestMapping(value = "/listCheckRecordItem", method = RequestMethod.POST)
    public CommonResult<List<StoreInventoryCheckRecordItemVO>> listCheckRecordItem(@RequestBody @Valid StoreInventoryCheckRecordDetailQueryDTO dto) {
        return CommonResult.ok (storeInventoryService.listCheckRecordItem(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }











    /**
     * 入库单/出库单提交
     */
    @RequestMapping(value = "/bill/submit", method = RequestMethod.POST)
    public CommonResult<String> checkSubmit(@RequestBody @Valid StoreInventoryBillDTO dto) {
        return CommonResult.ok(storeInventoryService.billSubmit(dto, getRequestContextInfoDTO()));
    }

    /**
     * 入库单/出库单删除
     */
    @RequestMapping(value = "/bill/delete", method = RequestMethod.POST)
    public CommonResult<String> billDelete(@RequestBody @Valid StoreInventoryBillDeleteDTO dto) {
        return CommonResult.ok(storeInventoryService.billDelete(dto, getRequestContextInfoDTO()));
    }


    /**
     * 入库单/出库单暂存（保存草稿）
     */
    @RequestMapping(value = "/bill/staging", method = RequestMethod.POST)
    public CommonResult<String> billStaging(@RequestBody @Valid StoreInventoryBillDTO dto) {
        return CommonResult.ok(storeInventoryService.billStaging(dto, getRequestContextInfoDTO()));
    }

    /**
     * 查询门店出入库列表
     */
    @RequestMapping(value = "/pageBill", method = RequestMethod.POST)
    public CommonResult<PageInfo<StoreInventoryBillVO>> pageBill(@RequestBody StoreInventoryBillPageQueryDTO dto) {
        return CommonResult.ok (storeInventoryService.pageBill(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }

    /**
     * 查询门店出入库详情列表
     */
    @RequestMapping(value = "/listBillItem", method = RequestMethod.POST)
    public CommonResult<List<StoreInventoryBillItemVO>> listBillItem(@RequestBody @Valid StoreInventoryBillDetailQueryDTO dto) {
        return CommonResult.ok (storeInventoryService.listBillItem(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }

    /**
     * 库存预警保存
     */
    @RequestMapping(value = "/stock/config/save", method = RequestMethod.POST)
    public CommonResult<Void> stockConfigSave(@RequestBody @Valid StoreItemInventoryConfigDTO dto) {
        storeInventoryService.stockConfigSave(dto,getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ());
        return CommonResult.ok ();
    }
    /**
     * 库存预警数量查询
     * @return
     */
    @RequestMapping(value = "/stock/warning/count", method = RequestMethod.POST)
    public CommonResult<StoreInventoryWarningVO> warningCount() {
        return CommonResult.ok(storeInventoryService.warningCount(getRequestContextInfoDTO ().getStoreId (),getRequestContextInfoDTO ().getTenantId ()));
    }
}
