package com.cosfo.mall.common.redission.core.strategy.impl;

import com.cosfo.mall.common.redission.core.strategy.RedissonConfigStrategy;
import com.cosfo.mall.common.redission.enums.GlobalConstant;
import com.cosfo.mall.common.redission.prop.RedissonProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;


/**
 * 单机方式Redisson配置
 *
 * <AUTHOR>
 * @date 2020-11-11
 */
@Slf4j
public class StandaloneRedissonConfigStrategyImpl implements RedissonConfigStrategy {

    @Override
    public Config createRedissonConfig(RedissonProperties redissonProperties) {
        // Validate input parameters
        if (redissonProperties == null) {
            log.error("RedissonProperties cannot be null");
            throw new IllegalArgumentException("RedissonProperties cannot be null");
        }

        String address = redissonProperties.getAddress();
        if (StringUtils.isBlank(address)) {
            log.error("Redis address cannot be null or empty");
            throw new IllegalArgumentException("Redis address cannot be null or empty");
        }

        Config config = new Config();
        try {
            String password = redissonProperties.getPassword();
            int database = redissonProperties.getDatabase();
            String redisAddr = GlobalConstant.REDIS_CONNECTION_PREFIX + address;

            SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(redisAddr)
                    .setDatabase(database);
            if (StringUtils.isNotBlank(password)) {
                singleServerConfig.setPassword(password);
            }
            log.info("初始化Redisson单机配置,连接地址:" + config.toYAML());
        } catch (Exception e) {
            log.error("单机Redisson初始化错误", e);
            throw new RuntimeException("Failed to create Redisson standalone configuration", e);
        }
        return config;
    }
}
