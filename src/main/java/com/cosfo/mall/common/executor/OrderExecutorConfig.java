package com.cosfo.mall.common.executor;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Configuration
public class OrderExecutorConfig {
    @Value("${spring.application.name}")
    private String applicationName;
    @Value("${executor.corePoolSize}")
    private int corePoolSize;
    @Value("${executor.maxPoolSize}")
    private int maxPoolSize;
    @Value("${executor.queueCapacity}")
    private int queueCapacity;
    @Value("${executor.keepAliveTime}")
    private long keepAliveTime;

    @Bean("orderExecutor")
    public ExecutorService createThreadPool() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat(applicationName + "-%d")
                .build();
        ExecutorService pool = new ThreadPoolExecutor(corePoolSize,
                maxPoolSize,
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(queueCapacity),
                threadFactory,
                new ThreadPoolExecutor.AbortPolicy());

        return pool;
    }
}
