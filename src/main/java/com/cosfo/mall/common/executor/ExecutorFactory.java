package com.cosfo.mall.common.executor;


import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description 线程池工厂
 * @date 2022/7/31 10:10
 */
public class ExecutorFactory {

    /**
     * supplierDeliveryExecutor
     */
    public static ExecutorService messageExecutor = new ThreadPoolExecutor(2, 8,
            1L, TimeUnit.MINUTES,
            new LinkedBlockingQueue<Runnable>(),
            new ThreadFactoryBuilder().setNameFormat("supplierDeliveryExecutor-").build());
}
