package com.cosfo.mall.common.result;

import lombok.*;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;

/**
 * 兼容返回类型，包含前后两个版本返回信息
 *
 * <AUTHOR>
 * @date 2022/9/7  17:29
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompatibleResultDTO<T> extends ResultDTO<T> {
    /**
     * 响应status，参考http状态码
     */
    private Integer status;
    /**
     * 错误码、开发排查用、非必填
     */
    private String errCode;
    /**
     * 响应msg，前端展示用
     */
    private String msg;

    public CompatibleResultDTO(ResultDTO<T> resultDTO, String errCode) {
        this.setCode(resultDTO.getCode());
        this.setMessage(resultDTO.getMessage());
        this.setData(resultDTO.getData());
        this.status = ResultStatusEnum.SERVER_ERROR.getStatus();
        this.msg = ResultStatusEnum.SERVER_ERROR.getMsg();
        this.errCode = errCode;
    }

    public CompatibleResultDTO(CommonResult<T> commonResult, String errCode) {
        this.setCode(commonResult.getStatus());
        this.setMessage(commonResult.getMsg());
        this.setData(commonResult.getData());
        this.status = ResultStatusEnum.SERVER_ERROR.getStatus();
        this.msg = ResultStatusEnum.SERVER_ERROR.getMsg();
        this.errCode = errCode;
    }
}
