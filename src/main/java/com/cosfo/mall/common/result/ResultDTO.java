package com.cosfo.mall.common.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/6  11:11
 */
@Data
@NoArgsConstructor
public class ResultDTO<D> implements Serializable {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应messag
     */
    private String message;

    /**
     * 返回数据
     */
    private D data;

    public ResultDTO(ResultDTOEnum resultDTOEnum) {
        this.code = resultDTOEnum.getCode();
        this.message = resultDTOEnum.getMessage();
    }

    public ResultDTO(ResultDTOEnum resultDTOEnum, D data) {
        this.code = resultDTOEnum.getCode();
        this.message = resultDTOEnum.getMessage();
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public D getData() {
        return data;
    }

    public void setData(D data) {
        this.data = data;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return this.code.equals(ResultDTOEnum.SUCCESS.getCode());
    }

    @JsonIgnore
    public boolean isFail() {
        return !isSuccess();
    }

    public static <D> ResultDTO<D> success(D data) {
        ResultDTO<D> resultDTO = new ResultDTO<>(ResultDTOEnum.SUCCESS);
        resultDTO.setData(data);
        return resultDTO;
    }

    public static <D> ResultDTO<D> success(ResultDTOEnum code, D data) {
        ResultDTO<D> resultDTO = new ResultDTO<>(code);
        resultDTO.setData(data);
        return resultDTO;
    }

    public static <D> ResultDTO<D> success() {
        return new ResultDTO<>(ResultDTOEnum.SUCCESS);
    }

    public static <D> ResultDTO<D> fail(ResultDTOEnum enumCode) {
        return new ResultDTO<>(enumCode);
    }

    public static <D> ResultDTO<D> fail(ResultDTOEnum enumCode, D data) {
        ResultDTO<D> resultDTO = new ResultDTO<>();
        resultDTO.setCode(enumCode.getCode());
        resultDTO.setMessage(enumCode.getMessage());
        resultDTO.setData(data);
        return resultDTO;
    }

    public static <D> ResultDTO<D> fail(String msg) {
        ResultDTO<D> resultDTO = new ResultDTO<>();
        resultDTO.setCode(ResultDTOEnum.SERVER_ERROR.getCode());
        resultDTO.setMessage(msg);
        return resultDTO;
    }

    public static <D> ResultDTO<D> fail(Integer code, String msg) {
        ResultDTO<D> resultDTO = new ResultDTO<>();
        resultDTO.setCode(code);
        resultDTO.setMessage(msg);
        return resultDTO;
    }

    public static <D> ResultDTO<D> fail(Integer code, String msg, D data) {
        ResultDTO<D> resultDTO = new ResultDTO<>();
        resultDTO.setCode(code);
        resultDTO.setMessage(msg);
        resultDTO.setData(data);
        return resultDTO;
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
