package com.cosfo.mall.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/6  11:08
 */
@Getter
@AllArgsConstructor
public enum ResultDTOEnum {
    /**
     * 200-成功
     */
    SUCCESS(200, "成功"),
    /**
     * 403-权限不足
     */
    NO_PERMISSION(403, "权限不足"),
    /**
     * 404-资源不存在
     */
    NOT_FOUND(404, "资源不存在"),
    /**
     * 500-服务器错误
     */
    SERVER_ERROR(500, "服务器错误"),
    /**
     *
     */
    PARAMETER_MISSING(501, "参数缺失"),
    /**
     * 用户名或密码错误
     */
    USER_OR_PASSWORD_WRONG(502, "用户名或密码错误"),
    /**
     * 账号失效
     */
    ACCOUNT_FAILURE(503,"账号失效"),
    /**
     * 参数错误
     */
    PARAMETER_ERROR(504,"参数错误"),

    /**
     * 1001-当前分类名称已存在
     */
    CLASSIFICATION_HAS_EXIST(1001, "当前分类名称已存在"),

    /**
     * 1002-当前分类下有商品
     */
    CLASSIFICATION_HAS_PRODUCT(1002, "当前分类下有商品，不可删除"),

    /**
     * 1003- 不存在此上传类型
     */
    UPLOAD_TYPE_NOT_EXIST(1003, "不存在此上传类型"),

    /**
     * 1004-最多不超过十个主图
     */
    MAIN_PICTURE_OVER(1004, "最多不超过十个主图"),

    /**
     * 1005-最多不超过十个详情图
     */
    DETAIL_PICTURE_OVER(1004, "最多不超过十个主图"),

    /**
     * 1005-一级分类图标不能为空
     */
    PRIMARY_CLASSIFICATION_ICON_EMPTY(1005, "一级分类图标不能为空"),

    /**
     * 1006-未查询到用户信息
     */
    MERCHANT_INFO_NOT_FOUND(1006, "未查询到用户信息"),

    /**
     * 1007-报价不可用
     */
    SUPPLY_NOT_AVAILABLE(1007, "报价不可用"),

    /**
     *
     */
    QUERY_STOCK_FAIL(1008, "库存信息获取失败，请联系管理员"),

    /**
     * 1008-订单状态不正确
     */
    ORDER_STATUS_ERROR(1009, "订单状态不正确"),

    /**
     * 没有默认收货地址
     */
    DEFAULT_ADDRESS_MISSING(1010,"请先新增收货地址信息"),

    /**
     * 1011 未注册
     */
    ACCOUNT_NOT_FOUND(1011, "门店未注册"),

    /**
     * 1012-该用户正在审核中
     */
    STORE_AUDITING(1012, "门店正在审核中"),

    /**
     * 1013-该用户审核被拒绝
     */
    STORE_REFUSE(1013, "门店审核被拒绝"),

    /**
     * 1014-短信验证码发送失败
     */
    SEND_CODE_FAIL(1014, "短信验证码发送失败"),

    /**
     * 1015-验证码错误
     */
    CODE_ERROR(1015, "验证码错误"),

    /**
     * 1016-门店名称不符合条件
     */
    STORE_NAME_ERROR(1016, "门店名称不符合条件"),

    /**
     * 1017-门店地址不符合条件
     */
    STORE_ADDRESS_ERROR(1017, "门店地址不符合条件"),

    /**
     * 1018-门店地址不符合条件
     */
    STORE_HOUSE_NUMBER_ERROR(1018, "门牌号不符合条件"),

    /**
     * 1019-二次校验验证码错误
     */
    DOUBLE_CHECK_CODE_ERROR(1019, "二次校验验证码错误，请重新进行注册"),

    /**
     * 1020-未查询该租户信息
     */
    TENANT_NOT_FOUND(1020, "未查询到该租户信息"),

    /**
     * 1021-未查询到门店信息
     */
    STORE_INFO_NOT_FOUND(1021, "未查询到门店信息"),

    /**
     * 1022-未查询到门店信息
     */
    ADDRESS_INFO_NOT_FOUND(1022, "未查询到地址信息"),
    /**
     * 1023-未查询到联系人信息
     */
    CONTACT_NOT_FOUND(1023,"未查询到联系人信息"),

    /**
     * 1024-至少需要一个联系人
     */
    CONTACT_AT_LEAST_ONE(1024,"至少需要一个联系人"),
    /**
     * 下单失败返回码
     */
    ORDER_ERROR(1025,"下单失败"),

    /**
     * 下单失败返回码
     */
    ORDER_AFTER_SALE_ERROR(1026,"售后单失败"),

    /**
     * 购物车失败
     */
    TROLLEY_ERROR(1027,"购物车失败"),
    /**
     * 用户信息已注册
     */
    PHONE_REGISTERED(1028,"您的微信号已经绑定别的手机号，请更改手机号登录"),

    /**
     * 微信已经注册
     */
    WEICHAT_REGISTERED(1029,"微信已经注册,请用注册的手机号登录"),

    /**
     * 账期权限
     */
    BILL_SWITCH(1030,"您没有账期权限，无法使用账期方式支付订单"),
    /**
     * 账期权限
     */
    ONLINE_PAYMENT(1031,"您没有在线支付权限，无法使用微信支付方式支付订单"),


    WECHAT_REGISTERED(1032,"微信已经注册,请用注册的手机号登录"),

    /**
     * 您的门店已关闭请联系品牌方处理
     */
    STORE_HAS_CLOSED(1033, "您的门店已关闭, 请联系品牌方处理"),

    /**
     * 门店名重复
     */
    STORE_NAME_REPEAT(1034, "门店名称重复"),

    /**
     * 未查询到售后订单
     */
    ORDER_AFTER_SALE_NOT_FOUND(1035,"未查询到售后订单"),

    /**
     * 分账处理器不存在
     */
    PROFIT_SHARING_HANDLE_NOT_EXIST(1036, "分账处理器不存在"),

    /**
     * 订单备注长度超过限制
     */
    ORDER_CREATE_REMARK_LIMIT(1037, "备注不能超过100字"),
    /**
     * 余额权限
     */
    BALANCE_SWITCH(1038,"您没有余额权限，无法使用余额方式支付订单"),
    /**
     * 线下支付权限
     */
    ENABLE_OFFLINE_PAYMENT(1039,"您没有线下支付权限，无法使用线下支付方式支付订单"),
    ENABLE_OFFLINE_PAYMENT_NO_PAYMENTRECEIPT(1040,"请输入支付凭证"),

    /**
     * 非现金支付权限
     */
    NON_CASH_SWITCH(1041, "您没有权限，无法使用该方式支付订单"),

    REGISTRY_CLOSE(1200, "商城未开启注册权限"),

    PHONE_NOT_EXIST(1201, "该手机号还不是店长或者店员手机号，请联系总部或店长添加"),

    PHONE_REGEX_ERROR(1202, "手机号格式有误"),

    PHONE_ALL_WAIT_AUDIT_EXIST(1203, "该手机号已注册门店，且处于待审核状态"),

    ITEM_ON_SALE_DOWN(1204, "该商品已下架"),
    ADDRESS_POI_MISSING(1205,"您的下单地址坐标不准确，请前往修改地址"),
    OPERATION_FAIL(1206, "操作失败"),

    OAAPPID_IS_NULL(1207,"公众号APPId未配置"),

    OFFICIAL_ACCOUNTS_CODE_MISSING(1028,"公众号授权code缺失"),

    REFUND_ID_PARARM_MISSING(1029,"退款Id参数缺失"),

    EXAMINE_CODE_FAIL(1050,"验证码已失效,请重新获取验证码"),

    USERNAME_PASSWORD_FAIL(1051,"用户名密码错误"),

    ACCOUNT_NOT_FOUND_FAIL(1052,"手机号对应的用户信息不存在"),


//    GET_WECHAT_PHONE_ERROR(1051,"获取微信用户手机号信息异常,请稍后再试"),
//    GET_WECHAT_OPENID_ERROR(1052,"获取微信用户openId信息异常,请稍后再试"),
//    CAN_LOGIN_ACCOUNT_NOT_EXIST(1053,"当前手机号不存在可登录的门店账户"),

    /**
     * 支付相关
     */
    ZERO_ORDER_PAYMENT_SUCCESS(2001, "0元订单支付成功"),
    ;
    private final Integer code;

    private final String message;

    @Override
    public String toString() {
        return "ResultEnum{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }

}
