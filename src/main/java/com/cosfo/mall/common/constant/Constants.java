package com.cosfo.mall.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 通用常量
 * @date 2022/5/21 15:54
 */
public class Constants {

    /**
     * 短信sceneId
     */
    public static final Long SCENE_ID = 2L;

    /**
     * 创蓝短信地址
     */
    public static final String CHUANGLAN_SEND_URL = "http://smssh1.253.com/msg/v1/send/json";

    /**
     * code标识
     */
    public static final String CODE = "code";

    /**
     * 验证码key前缀
     */
    public static final String CODE_PREFIX = "code_";

    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";

    /**
     * 过期时间
     */
    public static final long EXPIRATION_TIME = 60 * 15 * 1000L;

    /**
     * 下划线
     */
    public static final String UNDERLINE = "_";

    /**
     * 换行符
     */
    public static final String LINE = System.getProperty("line.separator");

    /**
     * 分号
     */
    public static final String SEMICOLON = "；";

    /**
     * 逗号
     */
    public static final String COMMA = ",";
//    /**
//     * 类目缓存
//     */
    public static final String CATEGORY_CACHE = "category_cache_hash";
    /**
     * 星号 asterisk
     */
    public static final String ASTERISK = "*";
    /**
     * 成功
     */
    public static final String OK = "/ok";

    /**
     * "application/json"
     */
    public static final String JSON_CONTENT_TYPE = "application/json";

    /**
     * one hundred
     */
    public static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    public static final BigDecimal ONE_THOUSAND = BigDecimal.valueOf(1000);

    /**
     * 售后群聊地址
     */
    public static final String AFTER_SALE_GROUP_CHAT = "after_sale_robot_url";

    /**
     * title
     */
    public static final String TITLE = "title";

    /**
     * text
     */
    public static final String TEXT = "text";

    /**
     * 系统
     */
    public static final String SYSTEM = "系统";

    /**
     * 系统自动通过
     */
    public static final String SYSTEM_PASS = "系统自动通过";

    /**
     * orderNo
     */
    public static final String ORDER_NO = "orderNo";

    /**
     * time
     */
    public static final String TIME = "time";

    /**
     * 回收正常模板
     */
    public static final String NORMAL_RECYCLE_TEMPLATE = "回收状态:正常；";

    /**
     * 配送正常模板
     */
    public static final String NORMAL_DELIVERY_TEMPLATE = "配送状态:正常；";

    /**
     * 回收异常模板
     */
    public static final String ABNORMAL_RECYCLE_TEMPLATE = "回收状态:异常；";

    /**
     * 配送异常模板
     */
    public static final String ABNORMAL_DELIVERY_TEMPLATE = "配送状态:异常；";

    /**
     * 汇付请求流水号前缀
     */
    public static final String HUIFU_PRE = "hp";

    /**
     * 汇付日期格式
     */
    public static final String HUIFU_DATE = "yyyyMMdd";


    /**
     * 汇付过期日期格式
     */
    public static final String HUIFU_EXPIRE_DATE = "yyyyMMddHHmmss";

    /**
     * 汇付商品描述
     */
    public static final String HUIFU_PRODUCTS_DESC = "的订单";

    /**
     * 汇付异步通知url
     */
    public static final String NOTIFY_URL = "/pay-notify/huifu-pay";

    /**
     * 微信支付类型
     */
//    public static final String WECHAT_TYPE = "0";

    /**
     * 汇付支付成功
     */
    public static final String HUIFU_SUCCESS = "S";
    /**
     * 汇付支付失败
     */
    public static final String HUIFU_FAIL = "F";

    /**
     * 汇付交易成功code
     */
    public static final String HUIFU_SUCCESS_CODE = "00000000";
    /**
     * 汇付交易处理中code
     */
    public static final String HUIFU_PROCESSING_CODE = "00000100";

    /**
     * 汇付退款发起查询时,原交易不存在错误码
     */
    public static final String HUIFU_REQ_ID_NOT_EXIST = "23000001";


    public static final String STARS = "*";

    /**
     * 汇付接收空数组格式
     */
    public static final String HUIFU_EMPTY_LIST = "[{}]";

    /**
     * 派送完成,缺货售后原因
     */
    public static final String AFTER_SALE_STOCK_OUT_REASON = "缺货";

    /**
     * 自动生成补货单日志前缀
     */
    public static final String AUTO_CREATE_AFTER_SALE_PREFIX = "自动生成补货单";
    
    /**
     * 用户中心业务异常前缀
     */
    public static final String USER_CENTER_BIZ_CODE_PREFIX = "BIZ";

    /**
     * 商城域名
     */
    public static final String mall_url = "https://mall.cosfo.cn";

    /************* 运费相关 start *************/
    /**
     * 运费生成备注 - 通过每单阶梯价生成
     */
    public static final String DELIVERY_FEE_STEP = "通过每单阶梯价生成";

    /**
     * 运费生成备注 - 每日第二单起运费免费
     */
    public static final String DELIVERY_FEE_DAILY = "每日第二单起运费免费";

    /**
     * 运费生成备注 - 通过随仓报价生成
     */
    public static final String DELIVERY_FEE_FOLLOW = "通过随仓报价生成";
    /************* 运费相关 end *************/

    /**
     * 租户权益缓存前缀
     */
    public static final String TENANT_PRIVILEGES = "cosfo_tenant_privileges_";

    public static final String LINE_SYMBOL = "-";
}
