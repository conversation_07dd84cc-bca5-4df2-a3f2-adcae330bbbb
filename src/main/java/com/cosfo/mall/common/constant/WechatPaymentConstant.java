package com.cosfo.mall.common.constant;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/17
 */
public class WechatPaymentConstant {
    /**
     * 直连-请求分账
     */
    public static final String DIRECT_PROFIT_SHARING_REQUEST = "/v3/profitsharing/orders";

    /**
     * 直连-查询分账结果
     */
    public static final String DIRECT_PROFIT_SHARING_QUERT = "/v3/profitsharing/orders/";

    /**
     * 直连-查询剩余待分金额
     */
    public static final String DIRECT_PROFIT_SHARING_AMOUNTS = "/v3/profitsharing/transactions/";

    /**
     * 关闭订单
     */
    public static final String JSAPI_CLOSE_PAY_ORDER= "/v3/pay/transactions/out-trade-no/{out_trade_no}/close";

    /**
     * 汇付税费比例 千分之23
     */
    public static final BigDecimal TAX_RATE = new BigDecimal(0.23);

    /**
     * 微信税费比例 千分之2
     */
    public static final BigDecimal WX_TAX_RATE = new BigDecimal(0.2);
}
