package com.cosfo.mall.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/19 18:57
 */
public class NumberConstant {

    /**
     * integer 0
     */
    public static final Integer ZERO = 0;

    /**
     * integer 1
     */
    public static final Integer ONE = 1;

    /**
     * integer 2
     */
    public static final Integer TWO = 2;

    /**
     * integer 3
     */
    public static final Integer THREE = 3;

    /**
     * integer 4
     */
    public static final Integer FOUR = 4;

    /**
     * integer 5
     */
    public static final Integer FIVE = 5;

    /**
     * integer 6
     */
    public static final Integer SIX = 6;

    /**
     * integer 10
     */
    public static final Integer TEN = 10;

    /**
     * integer 7
     */
    public static final Integer SEVEN = 7;

    /**
     * integer 8
     */
    public static final Integer EIGHT = 8;

    /**
     * integer 9
     */
    public static final Integer NINE = 9;

    /**
     * integer 16
     */
    public static final Integer SIXTEEN = 16;

    /**
     * integer 24
     */
    public static final Integer TWENTY_FOUR = 24;

    /**
     * integer 100
     */
    public static final Integer HUNDRED = 100;

    /**
     * integer 50
     */
    public static final Integer FIFTY = 50;

    /**
     * big decimal 100
     */
    public static final BigDecimal DECIMAL_HUNDRED = new BigDecimal(100);

    /**
     * integer 1000
     */
    public static final Integer ONE_THOUSAND = 1000;
}
