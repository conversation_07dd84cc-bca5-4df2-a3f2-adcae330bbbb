package com.cosfo.mall.common.controller;

import com.cosfo.mall.common.config.SystemTenantProperties;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;

import javax.annotation.Resource;

/**
 * 获取上下文信息控制类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
public abstract class BaseController {

    @Resource
    private SystemTenantProperties systemTenantProperties;

    public LoginContextInfoDTO getRequestContextInfoDTO() {
        LoginContextInfoDTO loginContextInfoDTO = ThreadTokenHolder.getLoginContextInfoDTO();
        if (loginContextInfoDTO == null) {
            loginContextInfoDTO = new LoginContextInfoDTO();
            // 设置默认租户
            loginContextInfoDTO.setTenantId(systemTenantProperties.getWurthTenantId());
        }
        return loginContextInfoDTO;
    }
}
