package com.cosfo.mall.common.decorator;


import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public final class MdcDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable task) {
        Map<String, String> threadContext = MDC.getCopyOfContextMap();
        return () -> {
            try {
                if (Objects.nonNull(threadContext)) {
                    // 将父线程的context set到子线程
                    MDC.setContextMap(threadContext);
                }
                task.run();
            } catch (Exception e) {
                log.error("异常信息：{}", e.getMessage(), e);
            } finally {
                MDC.clear();
            }
        };
    }

}
