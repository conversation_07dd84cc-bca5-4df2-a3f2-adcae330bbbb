package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述: 订单出库完成
 * 自营仓订单和三方仓订单都监听此消息
 * 无仓订单供应商发货完成模拟出库消息
 *
 * @author: xiaowk
 * @date: 2024/5/23 上午11:43
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_common_task_finish",
        consumerGroup = MqGroupConstant.GID_ORDER_WAREHOUSING_FINISHED,
        tag = "tag_ofc_outbound_task"
)
public class OfcCommonTaskFinishedListener extends AbstractMqListener<CommonFulfillmentFinishMessage> {
    @Resource
    private OrderService orderService;

    @Override
    public void process(CommonFulfillmentFinishMessage fulfillmentOrderResultMessageDTO) {
        log.info("rocketmq 收到订单出库完成消息，消息内容：{}", JSONObject.toJSONString(fulfillmentOrderResultMessageDTO));

        // 订单出库完成, 自提订单更新已完成和实际自提时间, 非自提更新状态 待收货
        orderService.orderOutOfStorage(fulfillmentOrderResultMessageDTO);
    }

}
