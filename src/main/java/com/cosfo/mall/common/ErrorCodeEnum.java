package com.cosfo.mall.common;

import net.xianmu.common.exception.error.code.ErrorCode;
import net.xianmu.common.result.ResultStatusEnum;

/**
 * @desc error code 枚举
 * <AUTHOR>
 * @date 2023/3/16 15:25
 */
public enum ErrorCodeEnum implements ErrorCode {
    UNKNOWN_ERROR("未知的错误"),

    PARAMETER_ERROR("参数错误"),

    VALIDATION_ERROR("业务校验异常错误"),

    QUERY_ERROR("查询错误"),

    UPDATE_ERROR("修改错误"),

    INSERT_ERROR("插入错误"),

    DELETE_ERROR("删除错误"),

    PARAMS_ERROR("参数错误"),


    PARAMS_MISSING("参数缺失"),

    READ_PEM_PRIVATE_KEY("支付环境异常"),

    MERCHANT_STORE_BALANCE_IS_NULL("余额不足", 3001),
    MERCHANT_STORE_BALANCE_INSUFFICIENT("余额不足", 3001),
    MARKET_ITEM_SUPPLY_PRICE_ERROR("余额支付异常"),
    MARKET_ITEM_SUPPLY_DELIVERY_ERROR("余额支付异常"),
    MERCHANT_STORE_BALANCE_CHANGE_FAIL("余额变更失败", 3001),
    AGENT_PREPAYMENT_INSUFFICIENT("代仓预付费用不足", 3001),
    SUPPLY_PREPAYMENT_INSUFFICIENT("供应价预付费用不足", 3001),
    SUPPLY_DELIVERY_FEE_PREPAYMENT_INSUFFICIENT("供应商运费费用不足", 3001),

    EXACTLY_ONCE_CREATE_REFUND("已存在对应退款单信息", 3100),

    ORDER_AFTER_SALE_FUND_NOT_FOUND("退款查询不到对应的售后单"),

    ORDER_AFTER_SALE_STATUS_ERROR("售后退款订单状态错误"),

    ORDER_AFTER_SALE_FUND_NOT_BALANCE("退款未查询到对应的余额账户信息"),

    /**
     * 订单业务开头
     */
    ORDER_QUANTITY_NOT_PASS("不满足起订量", 4001),

    ADDRESS_POI_MISSING("您的下单地址坐标不准确，请前往修改地址",1205)

    ;

    private String message;

    private Integer status;

    @Override
    public Integer getStatus() {
        return status == null ? ResultStatusEnum.SERVER_ERROR.getStatus() : status;
    }

    @Override
    public String getCode() {
        return name();
    }

    @Override
    public String getMessage() {
        return message;
    }

    ErrorCodeEnum(String message) {
        this.message = message;
    }

    ErrorCodeEnum(String message, Integer status) {
        this.message = message;
        this.status = status;
    }
}
