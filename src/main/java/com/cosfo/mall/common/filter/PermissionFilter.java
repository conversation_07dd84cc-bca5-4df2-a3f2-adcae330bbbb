package com.cosfo.mall.common.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.utils.JwtUtils;
import com.cosfo.mall.common.utils.RedisUtils;
import com.cosfo.mall.common.utils.SpringContextUtil;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.tenant.model.dto.TenantPrivilegesInfoDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/20
 */
@Slf4j
@Component
public class PermissionFilter extends FormAuthenticationFilter {

    public PermissionFilter() {
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        String token = WebUtils.toHttp(request).getHeader("token");
        if (StringUtils.isNotEmpty(token) && token.startsWith("cosfo-mall_ ")) {
            return true;
        }
        boolean result = ((HttpServletRequest) request).getMethod().equalsIgnoreCase("OPTIONS") || super.isAccessAllowed(request, response, mappedValue);
        if (!result) {
            return false;
        }
        try {
            return fillLoginContextInfo(request, token, response);
        } catch (BizException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("获取登录信息失败, token={}", token, ex);
            return false;
        }
    }

    private static String printLoginContext(LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(loginContextInfoDTO)) {
            return "<empty-login-context>";
        }
        return String.format("(tenantId:%d, storeId:%d, accountId:%d, storeName:%s)", loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getStoreName());
    }

    private boolean fillLoginContextInfo(ServletRequest request, String token, ServletResponse response) throws IOException {
        String url = ((HttpServletRequest) request).getRequestURI();
        Object principal = SecurityUtils.getSubject().getPrincipal();
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(principal));
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(Long.valueOf(jsonObject.get("tenantId").toString()));
        loginContextInfoDTO.setAccountId(Long.valueOf(jsonObject.get("bizUserId").toString()));
        Object loginCache = com.cosfo.mall.common.utils.SpringContextUtil.getBean("redisUtils", RedisUtils.class).get(JwtUtils.TOKEN_PREFIX + loginContextInfoDTO.getAccountId());
        if (loginCache == null || StringUtils.isBlank(loginCache.toString())) {
            return false;
        }
        String loginCacheJson = loginCache.toString();
        LoginContextInfoDTO contextInfoDTO = JSONObject.parseObject(loginCacheJson, LoginContextInfoDTO.class);
        loginContextInfoDTO.setStoreId(contextInfoDTO.getStoreId());
        loginContextInfoDTO.setJwtToken(contextInfoDTO.getJwtToken());
        loginContextInfoDTO.setPhone(contextInfoDTO.getPhone());
        loginContextInfoDTO.setStoreName(contextInfoDTO.getStoreName());
        loginContextInfoDTO.setAccountName(contextInfoDTO.getAccountName());
        loginContextInfoDTO.setOpenId(contextInfoDTO.getOpenId());
        if (!token.equals(loginContextInfoDTO.getJwtToken())) {
            log.warn("当前token:{}和登录token:{}不一致，需要重新登录", token, loginContextInfoDTO.getJwtToken());
            return false;
        }
        //获取版本过期时间
        Object saleVersionInfo = SpringContextUtil.getBean("redisUtils", RedisUtils.class).get(Constants.TENANT_PRIVILEGES + loginContextInfoDTO.getTenantId());
        if (saleVersionInfo != null) {
            TenantPrivilegesInfoDTO tenantPrivilegesInfoDTO = JSON.parseObject(saleVersionInfo.toString(), TenantPrivilegesInfoDTO.class);
            //是否过期
            if (LocalDate.now().isAfter(tenantPrivilegesInfoDTO.getExpireDate())) {
                log.warn("当前版本已过期，tenantId:{}", loginContextInfoDTO.getTenantId());
                ResultDTO<Void> resultDTO = ResultDTO.fail(ResultStatusEnum.SERVER_ERROR.getStatus(), "当前功能已过期，请联系品牌方续期");
                setResultToResponse(request, response, resultDTO);
                throw new BizException("当前功能已过期，请联系品牌方续期");
            }
        }
        ThreadTokenHolder.setToken(loginContextInfoDTO);
        log.info("店铺：{}请求了接口：{}", printLoginContext(loginContextInfoDTO), url);
        return true;
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {

        String requestURI = ((HttpServletRequest) request).getRequestURI();
        if (requestURI.equals("/")) {
            this.saveRequestAndRedirectToLogin(request, response);
            return false;
        } else {
//            CommonResult error = CommonResult.fail(ResultStatusEnum.FORBIDDEN, ResultStatusEnum.UNAUTHORIZED.getMsg(), ResultStatusEnum.UNAUTHORIZED.getStatus().toString());
            ResultDTO<Void> resultDTO = ResultDTO.fail(ResultStatusEnum.FORBIDDEN.getStatus(), ResultStatusEnum.UNAUTHORIZED.getMsg());
            setResultToResponse(request, response, resultDTO);
            return false;
        }
    }

    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        String loginUrl = this.getLoginUrl();
        WebUtils.issueRedirect(request, response, loginUrl, (Map) null, true, false);
    }

    private void setResultToResponse(ServletRequest request, ServletResponse response, ResultDTO<Void> resultDTO) throws IOException {
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setHeader("Access-Control-Allow-Origin", ((HttpServletRequest) request).getHeader("Origin"));
        httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/json");
        if (resultDTO != null) {
            httpServletResponse.setStatus(200);
            httpServletResponse.getWriter().write(JSON.toJSONString(resultDTO));
        }
    }

}