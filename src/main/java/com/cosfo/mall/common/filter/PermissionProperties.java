package com.cosfo.mall.common.filter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Component
@ConfigurationProperties(prefix = "permission")
public class PermissionProperties {

    private Set<String> allowedAnonymousUris = new HashSet<>();

    public Set<String> getAllowedAnonymousUris() {
        return allowedAnonymousUris;
    }

    public void setAllowedAnonymousUris(Set<String> allowedAnonymousUris) {
        this.allowedAnonymousUris = allowedAnonymousUris;
    }
}