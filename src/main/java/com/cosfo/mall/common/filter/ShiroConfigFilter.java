package com.cosfo.mall.common.filter;

import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Configuration
public class ShiroConfigFilter {
    /**
     * ShiroFilter是整个Shiro的入口点，用于拦截需要安全控制的请求进行处理
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        //校验失败自己的登陆页面
        shiroFilter.setLoginUrl("/merchant/store/login");
        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/merchant/store/login", "anon");
        filterMap.put("/merchant/store/sendCode", "anon");
        filterMap.put("/merchant/store/examineCode", "anon");
        filterMap.put("/merchant/store/smsCodeLogin", "anon");
        filterMap.put("/merchant/store/verifyUsernameAndPhoneMatch", "anon");
        filterMap.put("/merchant/store/examineCodeAndUpdatePassword", "anon");
        filterMap.put("/merchant/store/usernamePasswordLogin", "anon");
        filterMap.put("/sdk/user-base/queryAuthUserBase", "anon");
        filterMap.put("/getWeChatPhoneLogin", "anon");
        filterMap.put("/merchant/store/submitStoreInfo", "anon");
        filterMap.put("/merchant/store/resubmitStoreInfo", "anon");
        filterMap.put("/ok", "anon");
        filterMap.put("/swagger-ui.html", "anon");
        filterMap.put("/swagger-ui/**", "anon");
        filterMap.put("/swagger-ui/index.html", "anon");
        filterMap.put("/v3/api-docs", "anon");
        filterMap.put("/stock/after/sale/unlock/stock", "anon");
        filterMap.put("/pay/refund", "anon");
//        filterMap.put("/pay-notify/wx-direct", "anon");
//        filterMap.put("/pay-notify/huifu-pay", "anon");
//        filterMap.put("/pay-notify/huifu-refund", "anon");
        filterMap.put("/pay-notify/**", "anon");
        filterMap.put("/user/getOpenInfo", "anon");
        filterMap.put("/merchant/logo", "anon");
        filterMap.put("/home/<USER>/listAll/**", "anon");
        filterMap.put("/marketClassification/unLoginListAll", "anon");
        filterMap.put("/order/get/orderInfo", "anon");
        filterMap.put("/tenant/tenantInfo", "anon");
        filterMap.put("/tenant/query/use-phone-verification-flag", "anon");
        filterMap.put("/merchant/store/checkRegistered", "anon");
        filterMap.put("/getUserPhoneNumber", "anon");
        filterMap.put("/order/query", "anon");
        filterMap.put("/order/after/sale/queryAllAfterSaleOrder", "anon");
        filterMap.put("/order/after/sale/add", "anon");
        filterMap.put("/order/after/sale/query-item-info", "anon");
        filterMap.put("/product-sku/synchronized-supply-sku", "anon");
        filterMap.put("/push/result/receive", "anon");
        // 后门接口放过拦截
        filterMap.put("/supplier/delivery/upsert/having-order", "anon");
        filterMap.put("/merchant-store-account/query/list-belong-store-accounts", "anon");
        filterMap.put("/merchant-store-account/query/list-store-account", "anon");
        filterMap.put("/bill-profit-sharing-refund/**", "anon");
        filterMap.put("/payment/query-total-price", "anon");
        filterMap.put("/pay/execute-refund", "anon");
        filterMap.put("/bill-profit-sharing/**", "anon");
        filterMap.put("/order/notify/delivering", "anon");
        //外部对接，下单服务
        filterMap.put("/bill-profit-profit-sharing-refund/do-refund-sharing/**", "anon");
        filterMap.put("/**", "authc");

        shiroFilter.setFilterChainDefinitionMap(filterMap);

        Map<String, Filter> filterWonMap = new LinkedHashMap<>();
        filterWonMap.put("authc", new PermissionFilter());
        shiroFilter.setFilters(filterWonMap);
        return shiroFilter;
    }

}
