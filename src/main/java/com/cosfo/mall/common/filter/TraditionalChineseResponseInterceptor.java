package com.cosfo.mall.common.filter;

import com.cosfo.mall.common.result.ResultDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.houbb.opencc4j.util.ZhTwConverterUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;

/**
 * 响应体拦截器 - 将错误信息从简体转为繁体
 */
@Slf4j
@ControllerAdvice
public class TraditionalChineseResponseInterceptor implements ResponseBodyAdvice<Object> {


    @Resource
    private ObjectMapper objectMapper;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 只处理返回CommonResult的响应
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        try {
            if (body instanceof CommonResult) {
                CommonResult<?> result = (CommonResult<?>) body;
                // 转换错误信息
                String originalMsg = result.getMsg();
                if (originalMsg != null && !originalMsg.isEmpty()) {
                    String traditionalMsg = ZhTwConverterUtil.toTraditional(originalMsg);
                    log.info("traditionalMsg:{}", traditionalMsg);
                    result.setMsg(traditionalMsg);
                }
            } else if (body instanceof ResultDTO) {
                ResultDTO<?> result = (ResultDTO<?>) body;
                // 转换错误信息
                String originalMsg = result.getMessage();
                if (originalMsg != null && !originalMsg.isEmpty()) {
                    String traditionalMsg = ZhTwConverterUtil.toTraditional(originalMsg);
                    log.info("traditionalMsg:{}", traditionalMsg);
                    result.setMessage(traditionalMsg);
                }
            }
        } catch (Exception e) {
            log.error("转换响应体中的简体到繁体失败", e);
        }

        return body;
    }
}
