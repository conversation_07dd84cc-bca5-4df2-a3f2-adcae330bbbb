package com.cosfo.mall.common.filter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Set;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/20
 */
@Slf4j
@Data
public class AnonymousAllowedPermissionFilter extends PermissionFilter {

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        boolean result = super.isAccessAllowed(request, response, mappedValue);
        if (result) {
            return true;
        }
        log.info("判断是否可以匿名访问（兼容有token的情况）");
        return isAnonymousAllowed(((HttpServletRequest) request).getRequestURI());
    }


    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        return super.onAccessDenied(request, response);
    }

    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        super.redirectToLogin(request, response);
    }

    private Set<String> allowedAnonymousUris;

    public Set<String> getAllowedAnonymousUris() {
        return allowedAnonymousUris;
    }

    public void setAllowedAnonymousUris(Set<String> allowedAnonymousUris) {
        this.allowedAnonymousUris = allowedAnonymousUris;
    }

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 检查URI是否在允许匿名访问的列表中（支持**通配符）
    private boolean isAnonymousAllowed(String uri) {
        return allowedAnonymousUris.stream().anyMatch(pattern -> pathMatcher.match(pattern, uri));
    }
}