package com.cosfo.mall.common.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.utils.*;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 鉴权过滤器
 * @author: George
 * @Date: 2022/05/20
 */
@Slf4j
@Order(2)
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SystemTokenProcessor systemTokenGenerator;

    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        if (systemTokenGenerator.isEnabled()) {
            //增加system token的验证；
            String token = httpServletRequest.getParameter(JwtUtils.TOKEN_NAME);
            LoginContextInfoDTO loginContextInfoDTO = extractLoginContextInfoFromSystemToken(token);
            if (null != loginContextInfoDTO) {
                //系统token 登录；
                log.info("本次请求是用SystemToken发起的请求：{}", JSON.toJSONString(loginContextInfoDTO));
                addCredentialToSpring(loginContextInfoDTO, httpServletRequest, httpServletResponse, filterChain);
                return;
            }
        }
        // 默认都走原来的正常的用户登录：
        doJwtTokenFilter(httpServletRequest, httpServletResponse, filterChain);
    }

    private LoginContextInfoDTO extractLoginContextInfoFromSystemToken(String token) {
        LoginContextInfoDTO contextInfoDTO = null;
        if (org.apache.commons.lang3.StringUtils.isBlank(token)) {
            return null;
        }
        // 校验token是否合法/过期
        Map<String, Object> validateMap = JwtUtils.validateTokenAndGetClaims(token);
        if (CollectionUtils.isEmpty(validateMap)) {
            log.warn("token is invalid:{}", token);
            return null;
        }
        if (!JwtUtils.isSystemToken(token)) {
            log.warn("this is NOT a token generated by system:{}", token);
            return null;
        }

        log.info("information in system_token:{}, {}", token, JSON.toJSONString(validateMap));
        contextInfoDTO = new LoginContextInfoDTO();
        contextInfoDTO.setTenantId(Long.valueOf(validateMap.get(JwtUtils.TENANT_ID).toString()));
        contextInfoDTO.setStoreId(Long.valueOf(validateMap.get(JwtUtils.STORE_ID).toString()));
        contextInfoDTO.setAccountId(Long.valueOf(validateMap.get(JwtUtils.ACCOUNT_ID).toString()));
        contextInfoDTO.setStoreName("SystemToken");
        ThreadTokenHolder.setToken(contextInfoDTO);
        return contextInfoDTO;
    }

    protected void doJwtTokenFilter(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        String token = httpServletRequest.getHeader(JwtUtils.TOKEN_NAME);
        if (!httpServletRequest.getRequestURI().equals(Constants.OK)) {
            log.info("{}请求了接口：{} {}", token, httpServletRequest.getMethod(), httpServletRequest.getRequestURI());
        }

        // 清除线程中的缓存
        ThreadTokenHolder.clearMerchantInfo();
        ParameterRequestWrapper wrapper = new ParameterRequestWrapper(httpServletRequest, new HashMap<>());
        LoginContextInfoDTO contextInfoDTO = null;
        // 是否需要校验
        if (JwtUtils.isProtectedUrl(httpServletRequest)) {
            if (!StringUtils.isEmpty(token) && token.startsWith("cosfo-mall__")) {
                addCredentialToSpring(contextInfoDTO, wrapper, httpServletResponse, filterChain);
                return;
            } else {
                if (StringUtils.isEmpty(token) || !token.startsWith(JwtUtils.TOKEN_PREFIX)) {
                    log.info("JwtAuthenticationFilter token:{}", token);
                    filterChain.doFilter(httpServletRequest, httpServletResponse);
                    return;
                }

                // 校验token是否合法/过期
                Map<String, Object> validateMap = JwtUtils.validateTokenAndGetClaims(httpServletRequest);
                if (CollectionUtils.isEmpty(validateMap)) {
                    log.info("JwtAuthenticationFilter token:{}", token);
                    filterChain.doFilter(httpServletRequest, httpServletResponse);
                    return;
                }

                // 判断是否有缓存
                Object accountId = validateMap.get(JwtUtils.ACCOUNT_ID);
                Object loginCache = redisUtils.get(JwtUtils.TOKEN_PREFIX + accountId);
                if (loginCache == null) {
                    log.info("JwtAuthenticationFilter token:{},accountId:{}", token, accountId);
                    filterChain.doFilter(httpServletRequest, httpServletResponse);
                    return;
                }
                String loginCacheJson = loginCache.toString();
                contextInfoDTO = JSONObject.parseObject(loginCacheJson, LoginContextInfoDTO.class);

                // redis缓存中的token和携带token不一致
                if (!Objects.equals(token, contextInfoDTO.getJwtToken())) {
                    log.info("JwtAuthenticationFilter token:{},contextInfoDTO:{}", token, JSON.toJSONString(contextInfoDTO));
                    filterChain.doFilter(httpServletRequest, httpServletResponse);
                    return;
                }
                ThreadTokenHolder.setToken(contextInfoDTO);
            }

            // H5未登录前通过token注入tenantId
        } else {
            if (!StringUtils.isEmpty(token) && token.startsWith(JwtUtil.PREFIX_FLAG)) {
                // parma请求参数注入tenantId
                String substring = token.substring(JwtUtil.PREFIX_FLAG.length());
                Long tenantId = Long.valueOf(AESUtils.decrypt(substring));
                wrapper.addParameter("tenantId", tenantId);
                String body = wrapper.getBody();
                JSONObject jsonObject;
                if (!StringUtils.isEmpty(body)) {
                    jsonObject = JSONObject.parseObject(body);
                } else {
                    jsonObject = new JSONObject();
                }

                // body请求参数注入tenantId
                jsonObject.put("tenantId", tenantId);
                wrapper.setBody(JSON.toJSONString(jsonObject));
            }
        }

        if (!httpServletRequest.getRequestURI().equals(Constants.OK)) {
            log.info("登录信息{}请求了接口：{} {}", printLoginContext(ThreadTokenHolder.getLoginContextInfoDTO()), httpServletRequest.getMethod(),
                    httpServletRequest.getRequestURI());
        }
        addCredentialToSpring(contextInfoDTO, wrapper, httpServletResponse, filterChain);
    }

    private void addCredentialToSpring(LoginContextInfoDTO loginContextInfoDTO, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                       FilterChain filterChain) throws ServletException, IOException {
        //将缓存对象注入到SecurityContextHolder
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(
                loginContextInfoDTO, null, null));
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }

    @Override
    public void destroy() {
        ThreadTokenHolder.clearMerchantInfo();
        super.destroy();
    }

    /**
     * 处理拦截器中redis无法注入问题
     *
     * @param clazz
     * @param request
     * @param <T>
     * @return
     */
    public <T> T getBean(Class<T> clazz, HttpServletRequest request) {
        WebApplicationContext applicationContext = WebApplicationContextUtils.getRequiredWebApplicationContext(request.getServletContext());
        return applicationContext.getBean(clazz);
    }

    private static String printLoginContext(LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(loginContextInfoDTO)) {
            return "<empty-login-context>";
        }
        return String.format("(tenantId:%d, storeId:%d, accountId:%d, storeName:%s)", loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getStoreName());
    }
}
