package com.cosfo.mall.common.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.SystemTenantProperties;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.utils.JwtUtils;
import com.cosfo.mall.common.utils.RedisUtils;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Objects;

/**
 * @Description: 鉴权过滤器
 * @author: George
 * @Date: 2022/05/20
 */
@Slf4j
@Order(2)
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Resource
    private SystemTenantProperties systemTenantProperties;


    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        // 默认都走原来的正常的用户登录：
        doJwtTokenFilter(httpServletRequest, httpServletResponse, filterChain);
    }

    protected void doJwtTokenFilter(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        String token = httpServletRequest.getHeader(JwtUtils.TOKEN_NAME);
        if (!httpServletRequest.getRequestURI().equals(Constants.OK)) {
            log.info("{}请求了接口：{} {}", token, httpServletRequest.getMethod(), httpServletRequest.getRequestURI());
        }

        // 清除线程中的缓存
        ThreadTokenHolder.clearMerchantInfo();
        ParameterRequestWrapper wrapper = new ParameterRequestWrapper(httpServletRequest, new HashMap<>());
        LoginContextInfoDTO contextInfoDTO = null;
        // 是否需要校验
        if (JwtUtils.isProtectedUrl(httpServletRequest)) {
            if (!StringUtils.isEmpty(token) && token.startsWith("cosfo-mall__")) {
                addCredentialToSpring(contextInfoDTO, wrapper, httpServletResponse, filterChain);
                return;
            } else {
                log.warn("非法的token:{}，需要以cosfo-mall__开头", token);
            }
        } else {
            Long wurthTenantId = systemTenantProperties.getWurthTenantId();
            log.info("伍尔特不需要多租户，这里直接使用系统配置的租户ID:{}", wurthTenantId);
            String body = wrapper.getBody();
            JSONObject jsonObject;
            if (!StringUtils.isEmpty(body)) {
                jsonObject = JSONObject.parseObject(body);
            } else {
                jsonObject = new JSONObject();
            }
            // body请求参数注入tenantId
            jsonObject.put("tenantId", wurthTenantId);
            wrapper.setBody(JSON.toJSONString(jsonObject));
        }

        if (!httpServletRequest.getRequestURI().equals(Constants.OK)) {
            log.info("登录信息{}请求了接口：{} {}", printLoginContext(ThreadTokenHolder.getLoginContextInfoDTO()), httpServletRequest.getMethod(),
                    httpServletRequest.getRequestURI());
        }
        addCredentialToSpring(contextInfoDTO, wrapper, httpServletResponse, filterChain);
    }

    private void addCredentialToSpring(LoginContextInfoDTO loginContextInfoDTO, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                       FilterChain filterChain) throws ServletException, IOException {
        //将缓存对象注入到SecurityContextHolder
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(
                loginContextInfoDTO, null, null));
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }

    @Override
    public void destroy() {
        ThreadTokenHolder.clearMerchantInfo();
        super.destroy();
    }

    private static String printLoginContext(LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(loginContextInfoDTO)) {
            return "<empty-login-context>";
        }
        return String.format("(tenantId:%d, storeId:%d, accountId:%d, storeName:%s)", loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getStoreName());
    }
}
