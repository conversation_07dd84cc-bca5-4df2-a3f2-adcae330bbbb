package com.cosfo.mall.common.filter;

import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 描述: 操作人信息日志打印
 *
 * @创建时间: 2023/5/9
 */
@Slf4j
@Component
@Order(1000)
public class StoreInfoLoggerFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            LoginContextInfoDTO loginContextInfoDTO = ThreadTokenHolder.getLoginContextInfoDTO();
            log.info("店铺：{}请求了接口：{}", printLoginContext(loginContextInfoDTO), request.getRequestURI());
        } finally {
            filterChain.doFilter(request, response);
            // pagehelper清除分页信息
            PageHelper.clearPage();
        }
    }

    private static String printLoginContext(LoginContextInfoDTO loginContextInfoDTO) {
        if (null == loginContextInfoDTO) {
            return "<empty-login-context>";
        }
        return String.format("(tenantId:%d, storeId:%d, accountId:%d, storeName:%s)", loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId(),
                loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getStoreName());
    }
}
