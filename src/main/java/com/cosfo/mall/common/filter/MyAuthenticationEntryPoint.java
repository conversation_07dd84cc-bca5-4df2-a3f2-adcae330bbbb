package com.cosfo.mall.common.filter;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.result.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Description 通用返回
 * @author: George
 * @Date: 2022/05/20
 */
@Slf4j
@Component
public class MyAuthenticationEntryPoint implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        log.info("MyAuthenticationEntryPoint commence uri:{}", request.getRequestURI());
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().println(JSON.toJSONString(PageResultDTO.fail(ResultStatusEnum.FORBIDDEN.getStatus(), ResultStatusEnum.UNAUTHORIZED.getMsg())));
    }
}



