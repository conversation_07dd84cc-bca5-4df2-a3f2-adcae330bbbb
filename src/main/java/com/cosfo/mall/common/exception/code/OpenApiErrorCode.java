package com.cosfo.mall.common.exception.code;

import lombok.Getter;
import net.xianmu.common.exception.error.code.ErrorCode;

/**
 * @Author: fansongsong
 * @Date: 2023-09-25
 * @Description:
 */
@Getter
public enum OpenApiErrorCode implements ErrorCode {

    CREATE_AFTER_VALID_CODE(100001, "创建售后业务拦截异常"),
    CREATE_AFTER_SERVICE_TYPE_VALID_CODE(100002, "同一订单，批量售后不支持多种售后类型"),
    CREATE_AFTER_CONCURRENCE_VALID_CODE(100003, "批量售后并发限制异常"),
    AFTER_ORDER_NO_NO_EXIST_CODE(100004, "存在售后单已处理"),
    ORDER_STATUS_UN_SUPPORT_CODE(100005, "订单当前状态不支持发起配送后售后"),
    CREATE_AFTER_NOT_ENOUGH_AMOUNT_FOR_APPLY(100006, "售后可售后件数不足"),
    CREATE_AFTER_NOT_ENOUGH_PRICE_FOR_APPLY(100007, "售后可售后金额不足"),
    CREATE_AFTER_NOT_ENOUGH_TIME_FOR_APPLY(100008, "售后可发起售后时间已过"),
    CREATE_AFTER_NOT_AUTO_FINISH(100009, "批量售后订单无自动完成时间"),


    OR_EXIST_REPEAT_ORDER(200001, "存在重复的订单"),
    OR_EXIST_REPEAT_SKUCODE(200002, "存在重复的商品"),
    OR_ADDRESS_NOT_SUPPORTED_DELIVERY(200003, "门店地址不支持配送"),
    OR_EXIST_NOT_QUOTATION_ITEM(200004, "存在非鲜沐直供商品"),
    OR_PAY_ORDER_ERROR(200005, "订单记录支付账单失败"),
    OR_ITEM_ABSENT(200006, "下单的商品信息不存在"),
    OR_ITEM_PRICE_ABSENT(200007, "下单的商品报价不存在"),
    OR_ITEM_OUT_OF_STOCK(200008, "下单的商品库存不足"),
    OR_EXIST_REPEAT_ORDER_ITEM_ID(200009, "存在重复的外部子订单号"),
    OR_BUY_MULTIPLE_ERROR(200010, "倍数错误"),
    OR_DELIVERY_DATE_ERROR(200011, "期望配送日期不支持配送"),

    OR_NOT_EXIST_CUSTOMER(201000, "订单信息不存在"),
    OR_CLOSE_NOT_SUPPORT_STATUS(201001, "当前订单状态不可取消"),


    ;


    private final Integer status;

    private final String msg;

    private final String code;

    OpenApiErrorCode(Integer status, String msg, String code) {
        this.status = status;
        this.msg = msg;
        this.code = code;
    }

    OpenApiErrorCode(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
        this.code = "OPENAPI-DEFAULT_ERROR";
    }


    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
