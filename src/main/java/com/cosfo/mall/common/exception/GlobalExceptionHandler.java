package com.cosfo.mall.common.exception;

import com.cosfo.mall.common.exception.code.OpenApiErrorCode;
import com.cosfo.mall.common.result.CompatibleResultDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.*;
import net.xianmu.common.exception.error.code.ErrorCode;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import org.apache.catalina.connector.ClientAbortException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/6  14:31
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public CompatibleResultDTO exceptionHandler(Exception e) {
        // 处理业务异常
        ResultDTO<Object> fail = ResultDTO.fail(ResultDTOEnum.SERVER_ERROR);
        ErrorCode errorCode = null;
        if (e instanceof ClientAbortException) {
            log.info("【警告】message=[{}]", e.getMessage(), e);
        } else if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException) e;
            log.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            fail = ResultDTO.fail(exception.getErrorCode().getStatus(), exception.getMessage());
        } else if (e instanceof PayBizException) {
            PayBizException exception = (PayBizException) e;
            log.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            fail = ResultDTO.fail(exception.getErrorCode().getStatus(), exception.getMessage(), exception.getData());
        } else if (e instanceof BizException) {
            BizException exception = (BizException) e;
            log.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            errorCode = exception.getErrorCode();
            fail = ResultDTO.fail(exception.getErrorCode().getStatus(), exception.getMessage());
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException) e;
            log.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            fail = ResultDTO.fail(exception.getErrorCode().getStatus(), exception.getMessage());
        } else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException) e;
            log.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            fail = ResultDTO.fail(exception.getErrorCode().getStatus(), exception.getMessage());
        } else if (e instanceof DefaultServiceException) {
            log.error("DefaultServiceException：{}", e.getMessage(), e);
            if (((DefaultServiceException) e).getCode() != null) {
                fail = ResultDTO.fail(((DefaultServiceException) e).getCode(), e.getMessage());
                return new CompatibleResultDTO(fail, e.getMessage());
            }
            fail = ResultDTO.fail(e.getMessage());
            return new CompatibleResultDTO(fail, e.getMessage());
        } else {
            log.error("DefaultServiceException：{}", e.getMessage(), e);
            fail = ResultDTO.fail(ResultDTOEnum.SERVER_ERROR);
        }
        return new CompatibleResultDTO(fail, Optional.ofNullable(errorCode).map(ErrorCode::getCode).orElse(e.getMessage()));
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public CompatibleResultDTO bindExceptionHandler(MethodArgumentNotValidException e) {
        log.warn("Exception：{}", e.getMessage(), e);
        String message = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        ResultDTO<Object> fail = ResultDTO.fail(ResultDTOEnum.PARAMETER_MISSING.getCode(), message);
        return new CompatibleResultDTO(fail, message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public CompatibleResultDTO constraintViolationException(ConstraintViolationException ex) {
        log.warn("ConstraintViolationException:", ex);
        ResultDTO<Object> fail = ResultDTO.fail(ResultDTOEnum.PARAMETER_MISSING);
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        String message = violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(";"));

        return new CompatibleResultDTO(fail, message);
    }

    @Override
    public DubboResponse processError(Throwable throwable, ProceedingJoinPoint joinPoint) {
        if (throwable instanceof ConsumerException) {
            ConsumerException exception = (ConsumerException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof ParamsException) {
            ParamsException exception = (ParamsException)throwable;
            log.warn("调用方参数异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof BizException) {
            BizException exception = (BizException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            if(exception.getErrorCode() != null && (exception.getErrorCode() instanceof OpenApiErrorCode)){
                return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getErrorCode().getStatus(), exception.getMessage());
            }
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof CallerException) {
            CallerException exception = (CallerException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        }  else if (throwable instanceof DefaultServiceException) {
            DefaultServiceException exception = (DefaultServiceException)throwable;
            log.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getCode().toString(), exception.getMessage());
        }else if (throwable instanceof ProviderException) {
            ProviderException exception = (ProviderException)throwable;
            log.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof OpenApiProviderException) {
            OpenApiProviderException exception = (OpenApiProviderException) throwable;
            log.error("openApi提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getErrorCode().getStatus(), exception.getMessage());
        } else {
            log.error("提供方未知异常, 异常信息:{}", throwable.getMessage(), throwable);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return DubboResponse.getError(providerErrorCode.getCode(), throwable.getMessage());
        }
    }

}
