package com.cosfo.mall.common.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

@Data
public class PageDTO<T> implements Serializable {

    private static final long serialVersionUID = -1;

    private static final int DEFAULT_PAGE_SIZE = 10;

    private Integer pageNum = 1;

    /**
     * 存放当前页中的数据
     */
    private List<T> list;

    /**
     * 总记录数
     */
    private Integer total = 0;

    /**
     * 每页显示的数据量
     */
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    /**
     * 总页数
     */
    private Integer pages = 1;

    /**
     * 是否是最后一页
     */
    private Boolean isLastPage;

    /**
     * 一个结果都没
     * @param page
     * @param pageSize
     * @return
     */
    public static PageDTO getNullResult(Integer page, Integer pageSize) {
        PageDTO empty = new PageDTO();
        empty.setPageNum(page);
        empty.setPageSize(pageSize);
        empty.setList(Collections.emptyList());
        empty.setIsLastPage(true);
        return empty;
    }
    public static <T, R> PageDTO<R> page2PageDTO(PageDTO<T> page, Function<? super T, ? extends R> mapper) {
        PageDTO resp = new PageDTO();
        resp.setPageNum(new Long(page.getPageNum()).intValue());
        resp.setPageSize(new Long(page.getPageSize()).intValue());
        resp.setTotal(new Long(page.getTotal()).intValue());
        resp.setList(new ArrayList());
        resp.setIsLastPage(page.getIsLastPage());
        List<R> collect = page.getList().stream().map(mapper).collect(toList());
        resp.setList(collect);
        return resp;
    }

//    public static<T, R> PageDTO<R> pageResp2pageDTO(PageResp<T> pageResp, Function<? super T, ? extends R> mapper) {
//        PageDTO resp = new PageDTO();
//        resp.setPageNum(new Long(pageResp.getPageNum()).intValue());
//        resp.setPageSize(new Long(pageResp.getPageSize()).intValue());
//        resp.setTotal(new Long(pageResp.getTotal()).intValue());
//        resp.setList(new ArrayList());
//        resp.setIsLastPage(pageResp.getIsLastPage());
//        List<R> collect = pageResp.getData().stream().map(mapper).collect(toList());
//        resp.setList(collect);
//        return resp;
//    }
}
