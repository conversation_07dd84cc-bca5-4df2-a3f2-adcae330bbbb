package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Getter
@AllArgsConstructor
public enum TenantTypeEnum {
    /**
     * 品牌方
     */
    BRAND(0,"品牌方"),
    /**
     * 供应商
     */
    SUPPLIER(1,"供应商"),
    /**
     * 帆台
     */
    FANTAI(2,"帆台");

    /**
     * 类型
     */
    private Integer type;
    /**
     * 描述
     */
    private String desc;
}
