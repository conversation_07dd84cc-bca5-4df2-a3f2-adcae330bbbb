package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/25  14:07
 */
public class RefundEnum {
    @Getter
    @AllArgsConstructor
    public enum Status {
        CONFIRM_REFUND(200, "", "创建交易确认退款,待发起"),

        IN_CONFIRM_REFUND(201, "", "交易确认退款中"),
        /**
         * 100, "创建退款,待发起" (新增状态)
         */
        CREATE_REFUND(100, "", "创建退款,待发起"),
        /**
         * 1, "退款中"
         */
        IN_REFUND(1, "PROCESSING", "退款中"),
        /**
         * 2, "退款成功"
         */
        SUCCESS(2, "SUCCESS", "退款成功"),
        /**
         * 3, "退款失败"
         */
        FAIL(3, "", "退款失败");

        /**
         * 状态
         */
        private Integer status;
        /**
         * 微信支付CODE
         */
        private String wxCode;
        /**
         * 说明
         */
        private String desc;

        public static Status getByStatus(Integer code){
            for(Status status : Status.values()){
                if(status.getStatus().equals(code)){
                    return status;
                }
            }

            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum WxNotifyStatus {
        /**
         * "SUCCESS", "退款成功"
         */
        SUCCESS("SUCCESS", "退款成功"),
        /**
         * "CLOSED", "退款关闭"
         */
        CLOSED("CLOSED", "退款关闭"),
        /**
         * "ABNORMAL", "退款异常"
         */
        ABNORMAL("ABNORMAL", "退款异常");

        /**
         * 微信支付CODE
         */
        private String wxCode;
        /**
         * 说明
         */
        private String desc;
    }
}
