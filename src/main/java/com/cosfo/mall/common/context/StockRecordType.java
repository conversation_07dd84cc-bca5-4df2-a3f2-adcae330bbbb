package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/19  11:09
 */
@Getter
@AllArgsConstructor
public enum StockRecordType {
    /**
     *  0、下单
     */
    ORDER(0, "下单"),
    /**
     * 1、取消订单
     */
    ORDER_CANCEL(1, "取消订单"),
    /**
     * 2、售后
     */
    AFTER_SALE(2, "售后");

    private Integer type;
    private String desc;
}
