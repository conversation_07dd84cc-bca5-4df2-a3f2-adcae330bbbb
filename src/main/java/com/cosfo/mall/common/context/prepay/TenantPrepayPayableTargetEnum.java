package com.cosfo.mall.common.context.prepay;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 9:30
 */
@Getter
@AllArgsConstructor
public enum TenantPrepayPayableTargetEnum {

    /**
     * 供应商直供
     */
    DIRECT_SUPPLY(0, "供应商直供"),
    /**
     * 代仓费用
     */
    AGENT_WAREHOUSE_EXPENSE(1, "代仓费用"),
    /**
     * 供应商直供和代仓费用
     */
    DIRECT_SUPPLY_AND_AGENT(2, "供应商直供和代仓费用"),
    ;

    /**
     * 可支付目标
     */
    private Integer payableTarget;

    /**
     * 描述
     */
    private String desc;

}
