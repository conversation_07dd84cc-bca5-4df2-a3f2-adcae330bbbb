package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/1 10:54
 */
@Getter
@AllArgsConstructor
public enum SummerfarmDeliveryTypeEnum {

    /**
     * 配送
     */
    DELIVERY(0, "配送"),

    /**
     * 回收
     */
    RECYCLE(1, "回收");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;
}
