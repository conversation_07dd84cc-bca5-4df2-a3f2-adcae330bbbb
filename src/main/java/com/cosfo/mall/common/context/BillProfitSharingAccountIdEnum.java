package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-07-25
 **/
@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum BillProfitSharingAccountIdEnum {

    /**
     * 非默认
     */
    FT(0L, "帆台"),

    /**
     * 供应商
     */
    SUPPLIER(1L, "供应商");


    private Long id;
    private String desc;
}
