package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2022/12/26 11:17
 * 支付类型
 */
@Getter
@AllArgsConstructor
public enum BillPayEnum {
    /**
     * 微信
     */
    INCOME(1,1,0,"微信支付"),

    /**
     * 账期
     */
    PAY(2,2,null,"账期支付"),
    /**
     * 汇付
     */
    HUIFU(3,1,1,"汇付支付");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 支付方式 1,线上支付 2,账期
     */
    private Integer payType;

    /**
     *支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 状态描述
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (BillPayEnum billPayEnum : BillPayEnum.values()) {
            if (Objects.equals(code, billPayEnum.getCode())) {
                return billPayEnum.getDesc();
            }
        }
        return null;
    }


    /**
     * 获取支付方式
     * @param code
     * @return
     */
    public static Integer getPayType(Integer code) {
        for (BillPayEnum billPayEnum : BillPayEnum.values()) {
            if (Objects.equals(code, billPayEnum.getCode())) {
                return billPayEnum.getPayType();
            }
        }
        return null;
    }


    /**
     * 获取支付渠道
     * @param code
     * @return
     */
    public static Integer getPayChannel(Integer code) {
        for (BillPayEnum billPayEnum : BillPayEnum.values()) {
            if (Objects.equals(code, billPayEnum.getCode())) {
                return billPayEnum.getOnlinePayChannel();
            }
        }
        return null;
    }
}
