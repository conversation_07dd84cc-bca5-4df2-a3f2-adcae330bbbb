package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/30 9:36
 */
@Getter
@AllArgsConstructor
public enum DeliveryStateEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 异常
     */
    ABNORMAL(1, "异常");

    /**
     * 状态
     */
    private Integer state;

    /**
     * 描述
     */
    private String desc;


}
