package com.cosfo.mall.common.context.prepay;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 9:35
 */
@Getter
@AllArgsConstructor
public enum TenantPrepayRecordStatusEnum {

    /**
     * 待审核
     */
    WAIT_AUDIT(0, "待审核"),
    /**
     * 审核通过
     */
    AUDIT_SUCCESS(0, "审核通过"),
    /**
     * 审核失败
     */
    AUDIT_FAIL(2, "审核失败"),
    ;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;
}
