package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/21 12:01
 */
@Getter
@AllArgsConstructor
public enum MerchantStoreStatusEnum {

    /**
     * 审核中
     */
    AUDITING(0, "审核中"),

    /**
     * 审核通过
     */
    AUDIT_SUCCESS(1, "审核通过"),

    /**
     * 审核拒绝
     */
    AUDIT_REFUSE(2,"审核拒绝"),

    /**
     * 关店
     */
    CLOSE(3, "已关店");

    private Integer status;
    private String desc;
}
