package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/29
 */
public class MerchantDeliveryFeeRuleEnum {

    @Getter
    @AllArgsConstructor
    public enum MerchantDeliveryFeeRuleTypeEnum {

        /**
         * 每单
         */
        SINGLE(0,"每单"),
        /**
         * 每日
         */
        DAILY(1,"每日"),
        /**
         * 基于仓报价
         */
        FOLLOW_WAREHOUSE(2,"基于仓报价");

        /**
         * 售后订单状态编码
         */
        private Integer code;
        /**
         * 售后订单状态描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum MerchatDeliveryFeeRulePriceTypeEnum {
        /**
         * 固定价
         */
        FIXED_PRICE(0,"固定价"),
        /**
         * 实时加价
         */
        REAL_TIME_PRICE(1,"实时加价"),
        /**
         * 实时上浮
         */
        REAL_TIME_FLOATING(2,"实时上浮");

        /**
         * 售后订单状态编码
         */
        private Integer code;
        /**
         * 售后订单状态描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum DeliveryThresholdType {
        // 门槛类型  0-金额 1-数量
        /**
         * 金额
         */
        PRICE(0,"金额"),
        /**
         * 数量
         */
        COUNT(1,"数量");

        private Integer code;

        private String desc;
    }
}
