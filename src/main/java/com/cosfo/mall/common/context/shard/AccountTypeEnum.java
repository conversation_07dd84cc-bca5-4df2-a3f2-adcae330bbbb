package com.cosfo.mall.common.context.shard;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-01-04
 **/
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {


    PLATFORM(0, "平台"),
    SUPPLIER(1, "供应商"),
    TENANT(2, "租户"),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static Integer getByTenantId(Long tenantId) {

        if (tenantId == null) {
            return null;
        }
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (Objects.equals(tenantId, Long.valueOf(accountTypeEnum.type))) {
                return accountTypeEnum.type;
            }
        }
        return TENANT.getType();
    }
}
