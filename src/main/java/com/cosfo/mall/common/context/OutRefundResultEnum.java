package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-06
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum OutRefundResultEnum {

    /**
     * 明确第三方退款成功
     */
    SUCCESS("SUCCESS", "退款成功"),

    /**
     * 明确第三方退款失败。可以进行再次退款的重试
     */
    FAIL("FAIL", "退款失败"),

    /**
     * 第三方不确定的状态,卡住等待回调或者再次回查处理，或者最终人工排查
     */
    PROCESSING("PROCESSING", "进行中");

    /**
     * code
     */
    private String code;
    /**
     * 描述
     */
    private String desc;
}
