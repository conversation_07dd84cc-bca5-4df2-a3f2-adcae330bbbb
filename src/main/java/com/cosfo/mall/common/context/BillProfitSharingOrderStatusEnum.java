package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-08-01
 **/
@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum BillProfitSharingOrderStatusEnum {
    /**
     * 待分账
     */
    WAITING(0, "待分账"),

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 部分分账
     */
    PART(2, "部分分账"),

    /**
     * 已完成
     */
    FINISHED(3, "已完成"),

    /**
     * 初始化
     */
    INIT(4, "初始化");

    private Integer status;
    private String desc;
}
