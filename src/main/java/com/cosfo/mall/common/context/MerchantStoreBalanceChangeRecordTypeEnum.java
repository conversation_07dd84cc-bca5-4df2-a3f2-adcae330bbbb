package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 19:21
 */
@Getter
@AllArgsConstructor
public enum MerchantStoreBalanceChangeRecordTypeEnum {

    /**
     * 预付
     */
    PREPAY(0, "预付"),
    /**
     * 消费
     */
    CONSUMPTION(1, "消费"),
    /**
     * 消费退款
     */
    REFUND(2, "消费退款"),

    /**
     * 冻结
     */
    FROZEN(3, "冻结"),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;
}
