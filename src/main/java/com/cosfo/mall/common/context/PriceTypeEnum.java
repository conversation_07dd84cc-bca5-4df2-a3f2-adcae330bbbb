package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 商品指定价
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/13
 */
@Getter
@AllArgsConstructor
public enum PriceTypeEnum {

    /**
     * 指定价
     */
    SPECIFIED_PRICE(0, "指定价"),

    /**
     * 鲜沐报价单
     */
    SUMMERFATM_PRICE(1,"鲜沐报价单");
    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;

    public static PriceTypeEnum getByCode(Integer code){
        for(PriceTypeEnum priceTypeEnum: PriceTypeEnum.values()){
            if (priceTypeEnum.code.equals(code)) {
                return priceTypeEnum;
            }
        }

        return null;
    }
}
