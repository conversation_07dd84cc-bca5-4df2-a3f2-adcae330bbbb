package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public class OrderItemFeeEnum {

    @Getter
    @AllArgsConstructor
    public enum FeeType {

        /**
         * 代仓费
         */
        AGENT_FEE(1, "代仓费"),
        ;
        private final Integer code;

        private final String desc;

    }

    @Getter
    @AllArgsConstructor
    public enum TransactionType {
        /**
         * 支付
         */
        PAY(0, "支付"),
        REFUND(1, "退款")
        ;
        private final Integer code;

        private final String desc;
    }
}
