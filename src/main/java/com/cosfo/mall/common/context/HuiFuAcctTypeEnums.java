package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/20
 */
@Getter
@AllArgsConstructor
public enum HuiFuAcctTypeEnums {
    BASIC_ACCOUNT("01","基本户"),

    CASH_ACCOUNT("02","现金户"),

    DELAY_ACCOUNT("03", "延时户"),

    MONEY_ACCOUNT("04","钱包户"),

    TOP_UP_ACCOUNT("05","充值户"),

    MARKETING_ACCOUNT("06","营销户"),
    ;


    /**
     * 状态编码
     */
    private String code;
    /**
     * 状态描述
     */
    private String desc;
}
