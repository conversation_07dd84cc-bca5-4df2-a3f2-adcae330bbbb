//package com.cosfo.mall.common.mq;
//
//import com.cosfo.mall.common.constant.MQDelayConstant;
//
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//
//
///**
// * @Package: com.manageSystem.mq
// * @Description:
// * @author: <EMAIL>
// * @Date: 2017/3/13
// */
//@Deprecated
//@Service
//public class Producer {
//
//
//
//    public void sendDataToQueue(String queueKey, String data) {
//        rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    public void sendDataToQueue(String queueKey, Object data) {
//        rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    /**
//     * 顺序消息
//     * @param queueKey
//     * @param data
//     * @param hashKey
//     */
//    public void sendOrderlyDataToQueue(String queueKey, String data, String hashKey) {
//        this.rocketMQTemplate.syncSendOrderly(queueKey, data, hashKey);
//    }
//
//    /**
//     * 发送延时消息
//     * @param queueKey
//     * @param data
//     * @param delayLevel
//     */
//    public void sendDelayDataToQueue(String queueKey, String data, int delayLevel) {
//        Message message = MessageBuilder.withPayload(data).build();
//        this.rocketMQTemplate.syncSend(queueKey, message, MQDelayConstant.TIMEOUT, delayLevel);
//    }
//
//}
