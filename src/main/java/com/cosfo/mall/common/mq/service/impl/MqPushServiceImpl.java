//package com.cosfo.mall.common.mq.service.impl;
//
//import com.cosfo.mall.common.mq.service.MqPushService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.rocketmq.client.producer.SendResult;
//import org.apache.rocketmq.client.producer.SendStatus;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@Service
//public class MqPushServiceImpl implements MqPushService {
//    @Resource
//    private RocketMQTemplate rocketMQTemplate;
//
//    @Override
//    public SendResult sendMessage(String topic, String key, Map<String, Object> body) {
//        return this.sendDelayMessage(topic, "", key, 0, body);
//    }
//
//    @Override
//    public SendResult sendMessage(String topic, String tag, String key, Map<String, Object> body) {
//        return this.sendDelayMessage(topic, tag, key, 0, body);
//    }
//
//    @Override
//    public SendResult sendDelayMessage(String topic, String key, Integer delayLevel, Map<String, Object> body) {
//        return this.sendDelayMessage(topic, "", key, delayLevel, body);
//    }
//
//    @Override
//    public SendResult sendDelayMessage(String topic, String tag, String key, Integer delayLevel, Map<String, Object> body) {
//        Message<?> message = MessageBuilder.withPayload(body).setHeader(RocketMQHeaders.KEYS, key).build();
//        String destination = StringUtils.isBlank(tag) ? topic : topic.concat(":").concat(tag);
//        SendResult sendResult = rocketMQTemplate.syncSend(destination, message, 3000L, delayLevel);
//        if (sendResult.getSendStatus() == SendStatus.SEND_OK) {
//            log.info("sendMessage ok,result:{}", sendResult);
//        } else {
//            log.info("sendMessage fail,result:{}", sendResult);
//        }
//        return sendResult;
//    }
//
//    @Override
//    public <T> SendResult sendMessageList(String topic, List<Message<T>> messageList) {
//        return rocketMQTemplate.syncSend(topic, messageList);
//    }
//}
