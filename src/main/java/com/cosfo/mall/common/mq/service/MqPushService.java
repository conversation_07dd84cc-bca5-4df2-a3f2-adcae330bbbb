//package com.cosfo.mall.common.mq.service;
//
//import org.apache.rocketmq.client.producer.SendResult;
//import org.springframework.messaging.Message;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 描述:
// *
// * @author: <EMAIL>
// * @创建时间: 2022/5/24
// */
//public interface MqPushService {
//
//    SendResult sendMessage(String topic, String key, Map<String, Object> body);
//
//    SendResult sendMessage(String topic, String tag, String key, Map<String, Object> body);
//
//    SendResult sendDelayMessage(String topic, String key, Integer delayLevel, Map<String, Object> body);
//
//    SendResult sendDelayMessage(String topic, String tag, String key, Integer delayLevel, Map<String, Object> body);
//
//    <T> SendResult sendMessageList(String topic, List<Message<T>> messageList);
//}
