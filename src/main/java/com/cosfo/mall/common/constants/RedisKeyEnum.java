package com.cosfo.mall.common.constants;

import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-03-30
 * @Description: 定义redis的key枚举
 */
@Getter
public enum RedisKeyEnum {
    C00001("普通订单生成售后单分布式锁"),
    C00002("普通订单生成售后单幂等键"),
    C00003("售后订单生成售后单分布式锁"),
    C00004("售后订单生成售后单幂等键"),
    C00005("订单支付分布式锁"),
    C00006("开放平台订单下单分布式锁"),
    C00007("刷新订单金额分布式锁"),
    C00008("订单配送完成，通知第三方缓存key"),
    C00009("支付单创建交易流水分布式锁"),
    C00010("支付单创建交易流水幂等键"),
    C00011("退款单创建交易流水分布式锁"),
    C00012("退款单创建交易流水幂等键"),
    C00013("手机号用户选择账户去登录的标识"),
    C00014("发送短信验证码分布式锁"),
    C00015("短信验证码发送标记"),
    C00016("计划单下单分布式锁"),
    C00017("支付回调处理分布式锁"),

    ;

    /**
     * 锁系统前缀
     */
    private static final String SPACE = "cosfo-mall";

    /**
     * 连接符
     */
    public static final String SEPARATOR = "_";

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    RedisKeyEnum(String desc) {

    }

}
