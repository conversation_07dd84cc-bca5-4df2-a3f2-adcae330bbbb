package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-06-25
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum ItemSaleModeEnum {
    NORMAL_SALE(0, "可独售"),
    TYING_ADD_ON_SALE(1,"搭售可凑单"),
    TYING_CANNOT_ADD_ON_SALE(2,"搭售不可凑单");

    /**
     * 订单项状态编码
     */
    private Integer mode;
    /**
     * 订单项状态描述
     */
    private String desc;
}
