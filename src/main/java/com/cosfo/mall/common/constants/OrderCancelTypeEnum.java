package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-09-25
 **/
@Getter
@AllArgsConstructor
public enum OrderCancelTypeEnum {

    /**
     * 手动取消
     */
    MANUALLY_CANCEL(0, "手动取消"),

    /**
     * 订单创建时投递的延时取消
     */
    NORMAL_CANCEL(1, "正常延时取消"),


    /**
     * 超时的延时取消
     */
    TIME_OUT_CANCEL(2, "超时延时取消"),
    ;

    /**
     * 状态类型编码
     */
    private Integer type;
    /**
     * 状态类型描述
     */
    private String desc;
}
