package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/15
 */
public interface TenantDeliveryFeeRuleEnums {

    @Getter
    @AllArgsConstructor
    enum Type {
        /**
         * 跟随供应商
         */
        FOLLOW_SUPPLIER(0, "跟随供应商"),
        /**
         * 免运费
         */
        FREE(1, "免运费"),
        /**
         * 自定义
         */
        CUSTOMIZE(2,"自定义");

        /**
         * 类型
         */
        private Integer type;
        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum FreeType{
        /**
         * 按金额
         */
        MONEY(0,"按金额"),
        /**
         * 按件数
         */
        NUMBER(1,"按件数");
        /**
         * 类型
         */
        private Integer type;
        /**
         * 描述
         */
        private String desc;
    }
}
