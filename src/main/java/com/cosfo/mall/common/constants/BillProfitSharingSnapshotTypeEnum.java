package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/4
 */
@Getter
@AllArgsConstructor
public enum BillProfitSharingSnapshotTypeEnum {
    /**
     * 部分分账给品牌方
     */
    PART(0,"部分分账给品牌方"),

    /**
     * 全部分账给品牌方
     */
    ALL(1,"全部分账给品牌方");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}
