package com.cosfo.mall.common.constants;

import com.cosfo.summerfarm.enums.SummerfarmProductLocationEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/12 19:55
 */
public class ProductSkuEnum {

    /**
     * 仓库类型
     */
    @Getter
    @AllArgsConstructor
    public enum WarehouseType {

        /**
         * 自营仓
         */
        SELF,
        /**
         * 三方仓
         */
        THIRD
    }

    /**
     * 定价类型
     */
    public enum PriceType {

        /**
         * 指定价
         */
        SPECIFIED_PRICE
    }

    /**
     * 供价方式
     */
    public enum SupplyType {

        /**
         * 0 成本供价
         */
        COST_SUPPLY,

        /**
         * 1 报价单供价
         */
        QUOTATION_SUPPLY
    }

    @Getter
    @AllArgsConstructor
    public enum STORAGE_LOCATION {

        /**
         * 常温
         */
        NORMAL(0, "常温", "18-22"),

        /**
         * 冷藏
         */
        COLD(1, "冷藏","4-10"),

        /**
         * 冷冻
         */
        FROZEN(2, "冷冻","-18");

        /**
         * type
         */
        private Integer type;

        /**
         * desc
         */
        private String desc;

        /**
         * 建议储存温度
         */
        private String storageTemperature;

        /**
         * get desc
         *
         * @param type
         * @return
         */
        public static String getDesc(Integer type) {
            for (STORAGE_LOCATION storageLocationEnum : STORAGE_LOCATION.values()) {
                if (storageLocationEnum.type.equals(type)) {
                    return storageLocationEnum.desc;
                }
            }
            return null;
        }

        /**
         * get storageTemperature         *
         * @param type
         * @return
         */
        public static String getStorageTemperature(Integer type){
            for (STORAGE_LOCATION storageLocationEnum : STORAGE_LOCATION.values()) {
                if (storageLocationEnum.type.equals(type)) {
                    return storageLocationEnum.getStorageTemperature();
                }
            }
            return null;
        }

        /**
         * getType
         * @param storageLocation
         * @return
         */
        public static Integer getType(String storageLocation) {
            for (STORAGE_LOCATION storageLocationEnum : STORAGE_LOCATION.values()) {
                if (storageLocationEnum.desc.equals(storageLocation)) {
                    return storageLocationEnum.type;
                }
            }
            return null;
        }

        /**
         * 转换到鲜沐的 Location
         * @param type
         * @return
         */
        public static Integer convertToSummerfarmLocation(Integer type) {
            if (Objects.equals(STORAGE_LOCATION.NORMAL.getType(), type)) {
                return SummerfarmProductLocationEnum.NORMAL.getType();
            }
            if (Objects.equals(STORAGE_LOCATION.COLD.getType(), type)) {
                return SummerfarmProductLocationEnum.COLD.getType();
            }
            if (Objects.equals(STORAGE_LOCATION.FROZEN.getType(), type)) {
                return SummerfarmProductLocationEnum.FROZEN.getType();
            }
            return STORAGE_LOCATION.NORMAL.getType();
        }
    }

    @Getter
    @AllArgsConstructor
    public enum GUARANTEE_UNIT {

        /**
         * 天
         */
        DAY(0, "天"),

        /**
         * 月
         */
        MONTH(1, "月"),

        /**
         * 年
         */
        YEAR(2, "年");

        /**
         * type
         */
        private Integer type;

        /**
         * desc
         */
        private String desc;

        /**
         * get desc
         *
         * @param type
         * @return
         */
        public static String getDesc(Integer type) {
            for (GUARANTEE_UNIT guaranteeUnitEnum : GUARANTEE_UNIT.values()) {
                if (guaranteeUnitEnum.type.equals(type)) {
                    return guaranteeUnitEnum.desc;
                }
            }
            return null;
        }

        public static Integer getType(String guaranteeUnit) {
            for (GUARANTEE_UNIT guaranteeUnitEnum : GUARANTEE_UNIT.values()) {
                if (guaranteeUnitEnum.desc.equals(guaranteeUnit)) {
                    return guaranteeUnitEnum.type;
                }
            }
            return null;
        }
    }
}
