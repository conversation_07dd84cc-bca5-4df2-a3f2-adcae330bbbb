package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysParamKeyEnum {

    //微信第三方平台参数在DB中的key名称
    SYS_PARAM_KEY_TP_APP_ID("tp_appid","第三方平台appid"),
    SYS_PARAM_KEY_TP_APP_SECRET("tp_app_secret","第三方平台appsecret"),
    SYS_PARAM_KEY_TP_TOKEN("tp_token","第三方平台解密token"),
    SYS_PARAM_KEY_TP_ENCODING_AES_KEY("tp_encoding_aes_key","第三方平台解密key"),
    SYS_PARAM_KEY_TP_APP_TICKET("tp_app_ticket","第三方平台ticket"),
    SYS_PARAM_KEY_TP_ACCESS_TOKEN_JSON("tp_access_token_json","第三方平台accessTokenJson串"),

    //系统参数
    SYS_PARAM_KEY_BASE_URL("baseUrl","系统基础域名"),
    SYS_PARAM_KEY_SCHEME("scheme","请求前缀"),

    /**
     * 线上测试手机号
     */
    PRODUCT_TEST_PHONE("product_test_phone", "线上测试手机号"),
    /**
     * 线上测试验证码
     */
    PRODUCT_TEST_CODE("product_test_code", "线上测试验证码"),

    TIMEOUT_CANCEL_ORDER_LEVEL("timeout_cancel_order_level", "超时关单延迟等级,兜底30分钟"),
    ;

    /**
     * 状态类型编码
     */
    private String key;
    /**
     * 状态类型描述
     */
    private String desc;

}
