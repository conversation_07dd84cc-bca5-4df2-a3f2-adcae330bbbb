package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-08-24
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum RefundSourceEnum {

    /**
     * 处理中
     */
    DEFAULT(1, "默认来源(消息或HTTP)"),

    /**
     * binlog消息
     */
    BINLOG(2, "binlog消息"),

    /**
     * 定时任务
     */
    TASK(3, "定时任务");

    /**
     * 状态
     */
    private Integer status;
    /**
     * 说明
     */
    private String desc;
}
