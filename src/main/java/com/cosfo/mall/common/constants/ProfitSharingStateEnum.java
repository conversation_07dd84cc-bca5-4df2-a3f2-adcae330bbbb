package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 分账单状态
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingStateEnum {

    /**
     * 处理中
     */
    PROCESSING(0,"PROCESSING","待分账"),
    /**
     * 分账完成
     */
    FINISHED(1,"FINISHED","分账完成"),
    /**
     * 分账失败
     */
    FAIL(2,"FAIL","失败"),
    /**
     * 取消
     */
    CANCEL(3,"CANCEL","取消");

    /**
     * 状态
     */
    private Integer status;
    /**
     * 微信支付CODE
     */
    private String wxCode;
    /**
     * 说明
     */
    private String desc;
}
