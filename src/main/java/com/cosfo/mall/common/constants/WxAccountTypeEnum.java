package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/24
 */
@Getter
@AllArgsConstructor
public enum WxAccountTypeEnum {
    /**
     * 商户号
     */
    PROPRIETARY(0,"MERCHANT_ID"),
    /**
     * 个人openId
     */
    THREE_PARTIES(1,"PERSONAL_OPENID");

    /**
     * 配送仓类型编码
     */
    private Integer code;
    /**
     * 配送仓类型描述
     */
    private String desc;
}
