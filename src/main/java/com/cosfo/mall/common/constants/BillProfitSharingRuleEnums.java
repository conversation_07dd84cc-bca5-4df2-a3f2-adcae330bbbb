package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/18
 */
public interface BillProfitSharingRuleEnums {

    @AllArgsConstructor
    @Getter
    enum RoleCode{
        /**
         * 分账方
         */
        SUB_BANK(1,"分账方"),
        /**
         * 接收方
         */
        RECEIVER(0,"接收方");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;
    }
}
