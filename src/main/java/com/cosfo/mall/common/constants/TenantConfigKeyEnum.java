package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * @author: xiaowk
 * @date: 2023/3/19 下午5:43
 */
@Getter
@AllArgsConstructor
public enum TenantConfigKeyEnum {

    /**
     * 无库存商品展示规则
     */
    GOODS_SHOW_RULE_OUTOFSTOCK("goods_show_rule", "无库存商品展示规则 0-收起 1-展开 2-隐藏", "0"),

    /**
     * 品牌客服电话配置
     */
    CUSTOMER_PHONE("customer_phone", "品牌客服电话配置", ""),

    /**
     * 小程序注册功能 0:关闭 1开启,默认关闭
     */
    REGISTRY_SWITCH("registry_switch", "小程序注册功能配置 0:关闭 1开启", "0"),

    /**
     * 配送完成回调，是否自动生成待审核售后单 0:关闭 1:开启(自动生成)
     */
    AUTO_CREATE_RESEND_AFTER_SALE_RULE("auto_create_resend_after_sale_rule", "配送完成回调，是否自动生成补发单售后单 0:关闭 1:开启(自动生成)", "1"),

    /**
     * 门店下单有效期倒计时
     */
    PLACE_ORDER_PERMISSION_EXPIRY_TIME("place_order_permission_expiry_time", "门店下单有效期倒计时", "14"),

    ;
    /**
     * 租户配置key
     */
    private String configKey;

    /**
     * 租户配置描述
     */
    private String configDesc;

    /**
     * 租户配置key默认值
     */
    private String defaultValue;


    public static TenantConfigKeyEnum getConfigEnum(String configKey) {
        for (TenantConfigKeyEnum tenantConfigEnum : TenantConfigKeyEnum.values()) {
            if (tenantConfigEnum.configKey.equals(configKey)) {
                return tenantConfigEnum;
            }
        }

        return null;
    }
}
