package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-21
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum MessageRefundStatusEnum {

    /**
     * 退款成功
     */
    SUCCESS("退款成功", "S", "退款成功"),

    /**
     * 退款失败
     */
    FAIL("因为退款账户资金不足，退款失败", "F", "退款失败"),
    ;

    private String subTitle;
    private String uniquePrefix;
    private String title;

    /**
     * 连接符
     */
    public static final String SEPARATOR = "_";

    public String getUnique(String id) {
        return this.getUniquePrefix() + SEPARATOR + id;
    }
}
