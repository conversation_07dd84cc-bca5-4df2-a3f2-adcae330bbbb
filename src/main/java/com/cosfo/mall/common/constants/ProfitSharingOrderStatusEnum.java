package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingOrderStatusEnum {
    /**
     * 待分账
     */
    WAITING(0,"待分账"),
    /**
     * 分账处理中
     */
    CALCULATE_FINISHED(1,"金额计算完成"),
    /**
     * 实际分账中
     */
    PROCESSING(2, "分账中"),
    /**
     * 分账完成
     */
    FINISHED(3,"分账完成"),
    /**
     * 分账失败
     */
    FAILED(4,"分账失败"),
    /**
     * 部分分账
     */
    PART_FINISHED(5,"部分完成"),
    /**
     * 取消分账
     */
    CANCEL(6,"取消分账"),;

    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;
}
