package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/5
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    /**
     * 订单
     */
    ORDER(0,"订单"),
    /**
     * 售后单
     */
    ORDER_AFTER_SALE(1,"售后单");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}
