package com.cosfo.mall.common.constants;

import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/21
 */
@Getter
@AllArgsConstructor
public enum OrderAfterSaleFlagEnum {

    /**
     * 退款
     */
    REFUND(0,"退款"),
    /**
     * 退款详情
     */
    REFUND_DETAIL(1,"退款详情"),
    /**
     * 退款成功
     */
    REFUND_SUCCESS(2,"退款成功"),
    /**
     * 配送后售后
     */
    DELIVERED(3,"配送后售后"),
    /**
     * 不能发起售后
     */
    UNABLE(4,"不能发起售后");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 获取售后按钮
     *
     * @param orderStatus
     * @param orderAfterSaleDTOS
     * @return
     */
    public static OrderAfterSaleFlagEnum get(Integer orderStatus, List<OrderAfterSaleResp> orderAfterSaleDTOS, boolean flag){
        if(!flag){
            return UNABLE;
        }

        if(OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderStatus)){
            return UNABLE;
        }

        if(OrderStatusEnum.ableApplyNotSendAfterSale(orderStatus)){
            return REFUND;
//            if(CollectionUtils.isEmpty(orderAfterSaleDTOS)){
//                return REFUND;
//            }else {
//                List<com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO> afterSaleDTOS = orderAfterSaleDTOS.stream().filter(e -> !OrderAfterSaleStatusEnum.AUDITED_FAILED.getCode().equals(e.getStatus())).collect(Collectors.toList());
//                if(CollectionUtils.isEmpty(afterSaleDTOS)){
//                    return REFUND;
//                }
//
//                boolean anyMatch = afterSaleDTOS.stream().filter(e -> OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(e.getAfterSaleType())).anyMatch(e -> OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode().equals(e.getStatus()));
//                if(anyMatch){
//                    return REFUND_SUCCESS;
//                }else {
//                    return REFUND_DETAIL;
//                }
//            }
        }else if(OrderStatusEnum.ableApplyDeliveredAfterSale(orderStatus)){
            return DELIVERED;
        }

        return UNABLE;
    }

//    public static OrderAfterSaleFlagEnum getOld(Integer orderStatus, List<OrderAfterSaleDTO> orderAfterSaleDTOS, boolean flag){
//        if(!flag){
//            return UNABLE;
//        }
//
//        if(OrderStatusEnum.ableApplyNotSendAfterSale(orderStatus)){
//            if(CollectionUtils.isEmpty(orderAfterSaleDTOS)){
//                return REFUND;
//            }else {
//                List<OrderAfterSaleDTO> afterSaleDTOS = orderAfterSaleDTOS.stream().filter(e -> !OrderAfterSaleStatusEnum.AUDITED_FAILED.getCode().equals(e.getStatus())).collect(Collectors.toList());
//                if(CollectionUtils.isEmpty(afterSaleDTOS)){
//                    return REFUND;
//                }
//
//                boolean anyMatch = afterSaleDTOS.stream().filter(e -> OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(e.getAfterSaleType())).anyMatch(e -> OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode().equals(e.getStatus()));
//                if(anyMatch){
//                    return REFUND_SUCCESS;
//                }else {
//                    return REFUND_DETAIL;
//                }
//            }
//        }else if(OrderStatusEnum.ableApplyDeliveredAfterSale(orderStatus)){
//            return DELIVERED;
//        }
//
//        return UNABLE;
//    }
}
