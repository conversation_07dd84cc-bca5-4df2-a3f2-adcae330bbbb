package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分账方式
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingRuleMappingTypeEnum {
    ERROR(-1, "错误"),
    PRICE(0, "按价格"),
    SELF_RATIO(1, "按比例"),
    AVERAGE_RATIO(2, "按分账比例均摊"),
    ;

    /**
     * 编码
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public static ProfitSharingRuleMappingTypeEnum getByCode(Integer code){
        for(ProfitSharingRuleMappingTypeEnum profitSharingRuleMappingTypeEnum: ProfitSharingRuleMappingTypeEnum.values()){
            if (profitSharingRuleMappingTypeEnum.code.equals(code)) {
                return profitSharingRuleMappingTypeEnum;
            }
        }
        return ERROR;
    }
}
