package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 支付枚举
 */
@Getter
@AllArgsConstructor
public enum PayTypeEnum {
    /**
     * 微信支付
     */
    WECHAT_PAY(1, "微信支付"),
    /**
     * 账期支付
     */
    BILL(2, "账期支付"),
    /**
     * 余额支付
     */
    BALANCE(3, "余额支付"),
    /**
     * 支付宝支付
     */
    ALI_PAY(4, "支付宝支付"),
    /**
     * 无需支付
     */
    ZERO_PRICE_PAY(5, "无需支付"),

    /**
     * 线下支付
     */
    OFFLINE_PAY(6, "线下支付"),

    /**
     * 非现金支付
     */
    NON_CASH_PAY(7, "非现金支付"),

    /**
     * 组合支付
     */
    COMBINED_PAY(8, "组合支付"),
    ;


    /**
     * 状态类型编码
     */
    private final Integer type;

    /**
     * 状态类型描述
     */
    private final String desc;

    /**
     * 根据支付类型获取支付描述
     *
     * @param payType 支付方式
     * @return 支付方式描述
     */
    public static String getDesc(Integer payType) {
        return Arrays.stream(PayTypeEnum.values()).filter(el -> Objects.equals(el.getType(), payType)).findFirst().orElseThrow(() -> new BizException("不合法的支付类型")).getDesc();
    }

    /**
     * 判断是否是现结支付
     *
     * @param payType
     * @return
     */
    public static boolean isCurrentPay(Integer payType) {
        return Objects.equals(payType, WECHAT_PAY.getType()) || Objects.equals(payType, ALI_PAY.getType());
    }

    /**
     * 将payType转换成组合支付中的本地支付类型，如余额、非现金支付，售后的时候都是退款于此
     * @param payType
     * @return
     */
    public static boolean usableCombinedNativePayType(Integer payType) {
        return Objects.equals(payType, BALANCE.getType()) || Objects.equals(payType, NON_CASH_PAY.getType());
    }


    /**
     * 判断是否是非现结支付
     *
     * @param payType
     * @return
     */
    public static boolean isNotCurrentPay(Integer payType) {
        return !isCurrentPay(payType);
    }
}
