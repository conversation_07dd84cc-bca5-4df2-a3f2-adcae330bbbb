package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * @author: xiaowk
 * @date: 2023/3/19 下午5:43
 */
@Getter
@AllArgsConstructor
public enum TenantCommonConfigKeyEnum {

    /**
     * 无库存商品展示规则
     */
    GOODS_SHOW_RULE_OUTOFSTOCK("goods_show_rule", "无库存商品展示规则 0-收起 1-展开 2-隐藏", "0"),

    /**
     * 品牌客服电话配置
     */
    CUSTOMER_PHONE("customer_phone", "品牌客服电话配置", ""),

    /**
     * 客服咨询入口开关0:关闭 1开启
     */
    CONSULTATION_ENTRANCE("consultation_entrance", "客服咨询入口开关0:关闭 1开启", "0"),

    ;
    /**
     * 租户配置key
     */
    private String configKey;

    /**
     * 租户配置描述
     */
    private String configDesc;

    /**
     * 租户配置key默认值
     */
    private String defaultValue;


    public static TenantCommonConfigKeyEnum getConfigEnum(String configKey) {
        for (TenantCommonConfigKeyEnum tenantConfigEnum : TenantCommonConfigKeyEnum.values()) {
            if (tenantConfigEnum.configKey.equals(configKey)) {
                return tenantConfigEnum;
            }
        }

        return null;
    }
}
