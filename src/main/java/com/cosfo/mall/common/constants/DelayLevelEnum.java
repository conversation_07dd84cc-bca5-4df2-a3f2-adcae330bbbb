package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-04-26
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum DelayLevelEnum {
    ONE_DELAY_LEVEL(1, "延迟1s"),
    TWO_DELAY_LEVEL(2, "延迟5s"),
    THREE_DELAY_LEVEL(3, "延迟10s"),
    FOUR_DELAY_LEVEL(4, "延迟30s"),
    FIVE_DELAY_LEVEL(5, "延迟1m"),
    SIX_DELAY_LEVEL(6, "延迟2m"),
    SEVEN_DELAY_LEVEL(7, "延迟3m"),
    EIGHT_DELAY_LEVEL(8, "延迟4m"),
    NIGHT_DELAY_LEVEL(9, "延迟5m"),
    TEN_DELAY_LEVEL(10, "延迟6m"),
    ELEVEN_DELAY_LEVEL(11, "延迟7m"),
    TWELVE_DELAY_LEVEL(12, "延迟8m"),
    THIRTEEN_DELAY_LEVEL(13, "延迟9m"),
    FOURTEEN_DELAY_LEVEL(14, "延迟10m"),
    FIFTEEN_DELAY_LEVEL(15, "延迟20m"),
    SIXTEEN_DELAY_LEVEL(16, "延迟30m"),
    SEVENTEEN_DELAY_LEVEL(17, "延迟1h"),
    EIGHTEEN_DELAY_LEVEL(18, "延迟2h"),
    ;

    /**
     * 延迟等级
     */
    private Integer level;
    /**
     * 描述
     */
    private String desc;

    public static DelayLevelEnum getByLevel(Integer level) {
        for (DelayLevelEnum delayLevelEnum : DelayLevelEnum.values()) {
            if (delayLevelEnum.level.equals(level)) {
                return delayLevelEnum;
            }
        }
        return null;
    }
}
