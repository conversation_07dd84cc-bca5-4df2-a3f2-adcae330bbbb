package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 17:46
 * @Description:
 */
@Deprecated
public interface MarketItemEnums {


    /**
     * 弃用，商品相关以后统一使用item-center-client的枚举类
     */
    @Getter
    @AllArgsConstructor
    @Deprecated
    enum GoodsType{
        /**
         * 虚拟货品
         */
        VIRTUAL(0,"无货"),
        /**
         * 报价货品
         */
        QUOTATION(1,"报价货品"),
        /**
         * 自营货品
         */
        SELF_SUPPORT(2,"自营货品");

        /**
         * 类型编码
         */
        private Integer code;
        /**
         * 类型描述
         */
        private String desc;

        /**
         * 根据code获取
         *
         * @param code
         * @return
         */
        public static GoodsType getByCode(Integer code){
            for(GoodsType goodsType: GoodsType.values()){
                if(goodsType.getCode().equals(code)){
                    return goodsType;
                }
            }

            return null;
        }

    }

    @Getter
    @AllArgsConstructor
    enum ItemType{
        /**
         * 实物商品
         */
        PHYSICAL_GOODS(0,"实物商品"),
        /**
         * 虚拟商品
         */
        VIRTUAL(1,"虚拟商品"),
        /**
         * 组合包
         */
        COMBINATION_PACKAGE(2,"组合包");
        /**
         * 类型编码
         */
        private Integer code;
        /**
         * 类型描述
         */
        private String desc;
    }
}
