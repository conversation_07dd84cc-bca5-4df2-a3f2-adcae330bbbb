package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务场景
 * <AUTHOR>
 * @date : 2022/12/23 11:00
 */
@Getter
@AllArgsConstructor
public enum PaySceneEnum {


    ON_LINE("01","标准费率线上"),
    /**
     * 对应微信支付场景3
     */
    DOWN_LINE("02","标准费率线下"),

    FEE_NO("03","非盈利费率"),

    FEE_PAY("04","缴费费率"),

    FEE_INSURANCE("05","保险费率"),

    FEE_ACTIVITY("06","业活动费率"),

    FEE_SCHOOL("07","校园餐饮费率"),

    FEE_PRIMARY("08","中小幼费率"),

    OFF_LINE("09","非在线教培");

    /**
     * 状态类型编码
     */
    private String code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 获取支付类型
     *
     * @param code
     * @return
     */
    public static PaySceneEnum getPayType(Integer code){
        for (PaySceneEnum payTypeEnum : PaySceneEnum.values()){
            if(payTypeEnum.getCode().equals(code)){
                return payTypeEnum;
            }
        }

        return null;
    }
}
