package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/19 9:45
 */
@Getter
@AllArgsConstructor
public enum EnableOfflinePaymentEnum {

    CLOSE(0, "关闭"),

    /**
     * 开通
     */
    OPEN(1, "开启");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static String getDesc(Integer type) {
        for (EnableOfflinePaymentEnum value : EnableOfflinePaymentEnum.values()) {
            if (Objects.equals(value.getType (), type)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
