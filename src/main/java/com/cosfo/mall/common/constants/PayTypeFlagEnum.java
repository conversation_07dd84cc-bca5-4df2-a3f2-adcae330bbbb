package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:支付类型标记：用于支付时，前端根据该决定如何拉去相关支付
 *
 * @author: fss
 */
@Getter
@AllArgsConstructor
public enum PayTypeFlagEnum {

    /**
     * 小程序汇付插件支付
     */
    APPLET_HUI_FU_PLUGIN_FLAG(1,"小程序汇付插件支付"),

    UNKNOWN(0,"未明确"),
    ;


    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}
