package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 代仓分账方式
 */
@Getter
@AllArgsConstructor
public enum ProductAgentSkuFeeRuleTypeEnum {
    ERROR(-1, "错误"),
    ACCOUNT(1, "按件数"),
    SELF_RATIO(0, "按比例"),
            ;

    /**
     * 编码
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;

    public static ProductAgentSkuFeeRuleTypeEnum getByCode(Integer code){
        for(ProductAgentSkuFeeRuleTypeEnum productAgentSkuFeeRuleTypeEnum: ProductAgentSkuFeeRuleTypeEnum.values()){
            if (productAgentSkuFeeRuleTypeEnum.code.equals(code)) {
                return productAgentSkuFeeRuleTypeEnum;
            }
        }
        return ERROR;
    }
}
