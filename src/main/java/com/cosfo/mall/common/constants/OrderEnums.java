package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单枚举类
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/12
 */
public interface OrderEnums {

    /**
     * 描述: 配送仓类型
     *
     * @author: <EMAIL>
     * @创建时间: 2022/5/18
     */
    @Getter
    @AllArgsConstructor
    enum WarehouseTypeEnum {
        PROPRIETARY(0,"无仓订单","品牌自营仓"),
        THREE_PARTIES(1,"三方优选仓", "三方优选仓"),
        SELF_SUPPLY(2,"自营仓", "品牌自营仓");

        /**
         * 配送仓类型编码
         */
        private Integer code;
        /**
         * 配送仓类型描述
         */
        private String desc;
        /**
         * 订单名称
         */
        private String name;

        public static WarehouseTypeEnum getByCode(Integer code){
            if(code == null){
                return null;
            }
            for(WarehouseTypeEnum warehouseTypeEnum : WarehouseTypeEnum.values()){
                if(warehouseTypeEnum.getCode().equals(code)){
                    return warehouseTypeEnum;
                }
            }

            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    enum OrderType{
        /**
         * 组合订单
         */
        COMBINE(1,"组合订单"),
        /**
         * 普通订单
         */
        COMMON(0,"普通订单"),

        /**
         * 组合订单
         */
        PRESALE(2,"预售订单"),

        ;

        /**
         * 类型编码
         */
        private Integer code;
        /**
         * 类型描述
         */
        private String desc;
    }
}
