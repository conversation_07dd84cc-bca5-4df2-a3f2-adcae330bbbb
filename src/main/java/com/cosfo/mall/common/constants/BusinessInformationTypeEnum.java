package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-05-30
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum BusinessInformationTypeEnum {

    /**
     * 类型：0-品牌用户
     */
    BRAND_USER_TYPE(0,"品牌用户"),

    /**
     * 类型：1-单店用户
     */
    SINGLE_USER_TYPE(1,"单店用户"),
    ;
    /**
     * 状态类型编码
     */
    private Integer type;
    /**
     * 状态类型描述
     */
    private String desc;

}
