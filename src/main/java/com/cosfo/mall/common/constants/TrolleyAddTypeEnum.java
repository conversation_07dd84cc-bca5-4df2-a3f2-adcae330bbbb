package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 购物车加购类型
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
@Getter
@AllArgsConstructor
public enum TrolleyAddTypeEnum {
    PRODUCT_DETAIL(1,"商品详情页加购"),
    TROLLEY_PAGE(2,"购物车页面加购");

    /**
     * 订单状态编码
     */
    private Integer code;
    /**
     * 订单状态描述
     */
    private String desc;
}
