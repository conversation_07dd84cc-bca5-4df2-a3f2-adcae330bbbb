package com.cosfo.mall.common.constants;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * 支付途径枚举
 * <AUTHOR>
 * @date 2022/12/23  17:14
 */
@Getter
@AllArgsConstructor
public enum OnlinePayChannelEnum {
    WECHAT_PAY(0, "微信支付"),
    HUIFU_PAY(1, "汇付支付"),
    DIN_PAY(2, "智付支付"),
    ;

    /**
     * 支付渠道
     */
    private Integer channel;
    /**
     * 渠道描述
     */
    private String desc;

    /**
     * 可分账的渠道
     *
     * @return
     */
    public static Set<Integer> UsableProfitSharingChannel() {
        return Sets.newHashSet(HUIFU_PAY.channel, DIN_PAY.channel);
    }

    public static String getDesc(Integer channel) {
        for (OnlinePayChannelEnum payChannelEnum : OnlinePayChannelEnum.values()) {
            if (Objects.equals(channel, payChannelEnum.getChannel())) {
                return payChannelEnum.getDesc();
            }
        }
        return null;
    }
}
