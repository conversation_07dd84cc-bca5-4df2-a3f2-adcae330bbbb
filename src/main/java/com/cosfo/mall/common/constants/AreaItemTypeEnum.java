package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/28
 */
@Getter
@AllArgsConstructor
public enum AreaItemTypeEnum {
    PERCENTAGE(0,"百分比上浮"),

    QUOTA(1,"固定价上浮"),

    FIXED_PRICE(2,"固定价");

    /**
     * 优选仓商品上架价格策略
     */
    private Integer code;
    /**
     * 优选仓商品上架价格策略描述
     */
    private String desc;
}
