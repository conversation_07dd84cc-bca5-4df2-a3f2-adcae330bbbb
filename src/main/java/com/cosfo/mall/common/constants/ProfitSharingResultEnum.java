package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 微信分账状态
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingResultEnum {
    /**
     * 待分账
     */
    WAITING(0, "PENDING", "P", "待分账"),
    /**
     * 分账完成
     */
    FINISHED(1, "SUCCESS", "S", "分账完成"),
    /**
     * 分账失败
     */
    FAILED(2, "CLOSED", "F", "分账失败"),

    /**
     * 处理中
     */
    PROCESSING(0, "P", "P", "处理中"),

    FINISHED_DETAIL(1, "C", "C", "完成参见明细");


    /**
     * 状态
     */
    private Integer status;
    /**
     * 微信支付CODE
     */
    private String wxCode;

    /**
     * 汇付分账code
     */
    private String huiFuCode;
    /**
     * 说明
     */
    private String desc;

    public static ProfitSharingResultEnum getByWxCode(String wxCode) {
        for (ProfitSharingResultEnum profitSharingResultEnum : ProfitSharingResultEnum.values()) {
            if (profitSharingResultEnum.getWxCode().equals(wxCode)) {
                return profitSharingResultEnum;
            }
        }

        return null;
    }

    public static ProfitSharingResultEnum getByCode(String code) {
        for (ProfitSharingResultEnum profitSharingResultEnum : ProfitSharingResultEnum.values()) {
            if (profitSharingResultEnum.getWxCode().equals(code)) {
                return profitSharingResultEnum;
            }

            if (profitSharingResultEnum.getHuiFuCode().equals(code)) {
                return profitSharingResultEnum;
            }
        }

        return null;
    }

    public static ProfitSharingResultEnum getByHuiFuCode(String huiFuCode) {
        for (ProfitSharingResultEnum profitSharingResultEnum : ProfitSharingResultEnum.values()) {
            if (profitSharingResultEnum.getHuiFuCode().equals(huiFuCode)) {
                return profitSharingResultEnum;
            }
        }

        return null;
    }

    public static ProfitSharingResultEnum getByStatus(Integer status) {
        for (ProfitSharingResultEnum profitSharingResultEnum : ProfitSharingResultEnum.values()) {
            if (profitSharingResultEnum.getStatus().equals(status)) {
                return profitSharingResultEnum;
            }
        }

        return null;
    }
}
