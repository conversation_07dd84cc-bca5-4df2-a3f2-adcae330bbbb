package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 0:全部，1:自营，2代仓，3供应商
 */
@Getter
@AllArgsConstructor
public enum GoodsSourceEnum {

    //0:全部，1:自营，2代仓，3供应商
    ALL(0, "全部"),
    SELF(1, "自营"),
    AGENT(2, "代仓"),
    SUPPLIER(3, "供应商"),
    ;


    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}
