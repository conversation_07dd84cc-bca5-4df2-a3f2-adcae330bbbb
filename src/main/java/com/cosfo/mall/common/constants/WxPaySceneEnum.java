package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/11
 */
@Getter
@AllArgsConstructor
public enum WxPaySceneEnum {
    /**
     * 线下反扫
      */
    REVERSE_SCAN_BELOW_LINE("1","线下反扫"),
    /**
     * 线下公众号
     */
    OFFLINE_PUBLIC_ACCOUNT("2","线下公众号"),
    /**
     * 线下小程序
     */
    OFFLINE_MIMI_PROGRAM("3","线下小程序");

    /**
     *     1：线下反扫
     * 2：线下公众号
     * 3：线下小程序
     * 4：线上公众号
     * 5：线上小程序
     * 6：教培行业
     * 7：校园餐饮（需要调商户活动报名接口）
     *             8：教育K12
     * 9：非在线教培（需要调商户活动报名接口）
     *             10：非盈利费率
     * 11：保险费率
     */

    /**
     * 状态类型编码
     */
    private String code;
    /**
     * 状态类型描述
     */
    private String desc;
}
