package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 智付退款状态枚举
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Getter
@AllArgsConstructor
public enum DinPayRefundStatusEnum {

    /**
     * 等待处理
     */
    BEFORE_RECEIVE("BEFORERECEIVE", "等待处理"),

    /**
     * 接收成功
     */
    RECEIVE("RECEIVE", "接收成功"),

    /**
     * 初始化
     */
    INIT("INIT", "初始化"),

    /**
     * 处理中
     */
    DOING("DOING", "处理中"),

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 失败
     */
    FAIL("FAIL", "失败"),

    /**
     * 关闭
     */
    CLOSE("CLOSE", "关闭"),

    /**
     * 未知状态
     */
    UNKNOWN("", "未知状态");

    /**
     * 状态编码
     */
    private String code;

    /**
     * 状态描述
     */
    private String desc;

    /**
     * 根据状态码获取枚举
     */
    public static DinPayRefundStatusEnum getByCode(String code) {
        if (code == null) {
            return UNKNOWN;
        }

        for (DinPayRefundStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否为最终状态（成功或失败）
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAIL || this == CLOSE;
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailure() {
        return this == FAIL || this == CLOSE;
    }

    /**
     * 是否为处理中状态
     */
    public boolean isProcessing() {
        return this == BEFORE_RECEIVE || this == RECEIVE || this == INIT || this == DOING;
    }
}
