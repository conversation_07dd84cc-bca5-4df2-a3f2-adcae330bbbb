package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 汇付状态枚举类 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/7
 */
@Getter
@AllArgsConstructor
public enum HuiFuStatusEnums {
    /**
     * 处理中
     */
    PROCESSING("P", "处理中"),
    /**
     * 成功
     */
    SUCCESS("S","成功"),
    /**
     * 失败
     */
    FAILED("F","失败");

    /**
     * 编码
     */
    private String code;

    /**
     * 状态描述
     */
    private String desc;

    public static HuiFuStatusEnums getByCode(String code){
        for(HuiFuStatusEnums huiFuStatusEnums: HuiFuStatusEnums.values()){
            if(huiFuStatusEnums.code.equals(code)){
                return huiFuStatusEnums;
            }
        }

        return null;
    }
}
