package com.cosfo.mall.common.mapper;

import com.cosfo.mall.common.model.dto.CommonLocationCityDTO;
import com.cosfo.mall.common.model.po.CommonLocationCity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/25
 */
@Mapper
public interface CommonLocationCityMapper {
    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(CommonLocationCity record);

    /**
     * 新增数据
     *
     * @param record
     * @return
     */
    int insertSelective(CommonLocationCity record);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    CommonLocationCity selectByPrimaryKey(Long id);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CommonLocationCity record);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(CommonLocationCity record);

    /**
     * 根据城市名称查询
     *
     * @param cityName
     * @return
     */
    CommonLocationCityDTO selectByCityName(@Param("cityName") String cityName);
}