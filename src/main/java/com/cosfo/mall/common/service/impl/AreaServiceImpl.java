package com.cosfo.mall.common.service.impl;

import com.cosfo.mall.common.mapper.CommonLocationCityMapper;
import com.cosfo.mall.common.model.dto.CommonLocationCityDTO;
import com.cosfo.mall.common.service.AreaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/25
 */
@Service
public class AreaServiceImpl implements AreaService {

    @Resource
    private CommonLocationCityMapper commonLocationCityMapper;

    @Override
    public CommonLocationCityDTO selectByCityName(String cityName) {
        return commonLocationCityMapper.selectByCityName(cityName);
    }
}
