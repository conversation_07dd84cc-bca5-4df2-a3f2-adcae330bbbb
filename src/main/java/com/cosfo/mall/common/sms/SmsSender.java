package com.cosfo.mall.common.sms;

import com.cosfo.mall.common.sms.model.Sms;

/**
 * <AUTHOR>
 */
public interface SmsSender {
    /**
     * 创建短信验证码.
     *
     * @param length 长度
     * @return 验证码
     */
    static int buildRandom(int length) {
        int num = 1;
        double random = Math.random();
        if (random < 0.1) {
            random = random + 0.1;
        }
        for (int i = 0; i < length; i++) {
            num = num * 10;
        }
        return (int) ((random * num));
    }

    /**
     * 发送短信
     *
     * @param sms 短信对象
     * @return 是否发送成功
     */
    boolean sendSms(Sms sms);
}
