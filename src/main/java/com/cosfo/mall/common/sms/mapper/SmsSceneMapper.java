package com.cosfo.mall.common.sms.mapper;

import com.cosfo.mall.common.sms.model.SmsScene;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmsSceneMapper {

    /**
     * 查询
     * @param templateId
     * @return
     */
    SmsScene getByTemplateId(@Param("templateId") String templateId);

    /**
     * 查询
     * @param sceneId
     * @param platform
     * @return
     */
    SmsScene selectByScenePlatform(@Param("sceneId") Long sceneId, @Param("platform") Integer platform);

}
