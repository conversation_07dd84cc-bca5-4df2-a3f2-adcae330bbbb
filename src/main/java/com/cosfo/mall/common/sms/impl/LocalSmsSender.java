package com.cosfo.mall.common.sms.impl;

import com.cosfo.mall.common.sms.SmsSender;
import com.cosfo.mall.common.sms.model.Sms;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 本地发送器，只打印日志不会实际发送短信
 * <AUTHOR>
 */
@Component
public class LocalSmsSender implements SmsSender {
    private static final Logger logger = LoggerFactory.getLogger(LocalSmsSender.class);

    @Override
    public boolean sendSms(Sms sms) {
        logger.info("sms:{}", sms);
        return true;
    }
}
