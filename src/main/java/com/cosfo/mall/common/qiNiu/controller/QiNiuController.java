package com.cosfo.mall.common.qiNiu.controller;

import com.cosfo.mall.common.constant.QiNiuConstant;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.UploadTokenFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/11 10:18
 */
@RestController
@RequestMapping(value = "/qiniu")
public class QiNiuController {

    /**
     * 获取上传图片到七牛的token和key值（前端自由上传）
     * fileName
     * @return
     */
    @RequestMapping(value = "upload-token/one", method = RequestMethod.POST)
    public ResultDTO uploadTokenByFileName(String fileName) {
        Map<String, String> data = UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);
        if (data == null) {
            return ResultDTO.fail(ResultDTOEnum.UPLOAD_TYPE_NOT_EXIST);
        }
        return ResultDTO.success(data);
    }
}
