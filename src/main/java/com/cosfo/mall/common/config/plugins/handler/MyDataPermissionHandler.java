package com.cosfo.mall.common.config.plugins.handler;

import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.schema.Column;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据权限
 */
@Slf4j
@Component
public class MyDataPermissionHandler implements DataPermissionHandler {

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        try {
            Class<?> clazz = Class.forName(mappedStatementId.substring(0, mappedStatementId.lastIndexOf(".")));
            Method[] methods = clazz.getDeclaredMethods();
//            LoginContextInfoDTO loginInfoDTO = ThreadTokenHolder.getMerchantInfoDTO();
            LoginContextInfoDTO loginInfoDTO = new LoginContextInfoDTO();
//            TentPermission annotation = clazz.getAnnotation(TentPermission.class);
//            if (ObjectUtils.isNotEmpty(annotation) || loginInfoDTO == null) {
//                return where;
//            }
            for (Method method : methods) {
//                annotation = method.getAnnotation(TentPermission.class);
//                if (ObjectUtils.isNotEmpty(annotation)) {
//                    continue;
//                }
                Column column;
                EqualsTo equalsTo = new EqualsTo();
                if (where != null){
                    if(where.toString().contains(".")) {
                        int i = where.toString().indexOf(".");
                        StringBuffer stringBuffer = new StringBuffer(where.toString());
                        String colomName = stringBuffer.substring(0, i);
                        column = new Column(colomName + ".tenant_id");
                    }else{
                        column = new Column("tenant_id");
                    }
                    equalsTo.setLeftExpression(column);
                    equalsTo.setRightExpression(new StringValue(loginInfoDTO.getTenantId()+""));
                    // 创建 AND 表达式 拼接Where 和 = 表达式
                    return new AndExpression(where, equalsTo);
                }else{
                    column = new Column("tenant_id");
                    equalsTo.setLeftExpression(column);
                    equalsTo.setRightExpression(new StringValue(loginInfoDTO.getTenantId()+""));
                    return equalsTo;
                }

            }
        } catch (ClassNotFoundException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }
        return where;
    }
}
