package com.cosfo.mall.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @Author: zach
 * @Date: 2025-04-15
 * @Description: 霸王茶姬配置信息
 */
@Slf4j
@Configuration
@Data
public class ChageeConfig implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    @NacosValue(value = "${open.api.force.expectedDeliveryDateSwitch:false}", autoRefreshed = true)
    public Boolean forceExpectedDeliveryDateSwitch;

    public Boolean getForceExpectedDeliveryDateSwitch() {
        return forceExpectedDeliveryDateSwitch;
    }


    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }
}
