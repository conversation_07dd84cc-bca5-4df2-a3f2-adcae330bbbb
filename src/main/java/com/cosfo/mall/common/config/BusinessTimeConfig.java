package com.cosfo.mall.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @description: 时间配置
 * @author: <PERSON>
 * @date: 2023-09-25
 **/
@Component
@Data
public class BusinessTimeConfig {

    @NacosValue(value = "${payment.expire.time:5}", autoRefreshed = true)
    private long paymentExpireTime;

    @NacosValue(value = "${order.cancel.time:30}", autoRefreshed = true)
    private Long orderCancelTime;

    @NacosValue(value = "${order.cancel.timeout:5}", autoRefreshed = true)
    private Long orderCancelTimeOut;
}
