package com.cosfo.mall.common.config;

import com.cosfo.mall.common.decorator.MdcDecorator;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;


/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ExecutorConfig {

    @Primary
    @Bean
    public Executor asyncServiceExecutor(){
        log.info("开始配置异步线程池");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(4);
        //配置最大线程数
        executor.setMaxPoolSize(10);
        //配置队列大小
        executor.setQueueCapacity(100);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service-");
        // 配置Mdc装饰
        executor.setTaskDecorator(new MdcDecorator());
        executor.initialize();
        return executor;
    }

    @Bean("orderNotifyExecutorService")
    public ExecutorService createThreadPool() {
        log.info("开始配置订单通知异步线程池");
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("order-notify-service-")
                .build();
        // 线程池线程不够，兜底当前线程执行
        ExecutorService pool = new ThreadPoolExecutor(4,
                12,
                60,
                TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy());

        return pool;
    }
}
