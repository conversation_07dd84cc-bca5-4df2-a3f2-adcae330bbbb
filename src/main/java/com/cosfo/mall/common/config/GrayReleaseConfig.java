package com.cosfo.mall.common.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.mall.common.utils.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Data
@Slf4j
public class GrayReleaseConfig {

    @NacosValue(value = "${gray.ordercenter.tenant:2}", autoRefreshed = true)
    private String orderCenterGrayTenantIdsStr;
    @NacosValue(value = "${gray.ordercenter.release:false}", autoRefreshed = true)
    private boolean orderCenterGrayRelease;
    @NacosValue(value = "${gray.auth.tenant:2}", autoRefreshed = true)
    private String authGrayTenantIdsStr;
    @NacosValue(value = "${gray.auth.release:false}", autoRefreshed = true)
    private boolean authGrayRelease;
    @NacosValue(value = "${gray.goodscenter.release:false}", autoRefreshed = true)
    private boolean goodsCenterGrayRelease;
    /**
     * 旧登录接口是否开启拦截限制
     */
    @NacosValue(value = "${old.login.url.intercept:false}", autoRefreshed = true)
    private boolean oldLoginUrlIntercept;

    /**
     * 测试环境返回手机号验证码的自动化账号
     */
    @NacosValue(value = "${test.return.code.phone:13988867666,13900087676,13751454374,13866608522,13655546555}", autoRefreshed = true)
    private Set<String> returnCodePhoneSet;

    /**
     * 发送短信间隔时间
     */
    @NacosValue(value = "${sms.send.interval.time:60000}", autoRefreshed = true)
    private Long smsSendIntervalTime;

    public List<Long> getOrderCenterGrayTenantIds() {
        if (StringUtils.isEmpty(orderCenterGrayTenantIdsStr)) {
            return Collections.emptyList();
        }
        List<Long> list = Arrays.stream(orderCenterGrayTenantIdsStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        return list;
    }

    public boolean executeAuthGray(Long tenantId) {
        if (authGrayRelease) {
            log.info("全量开启Auth灰度");
            return true;
        }
        if (getAuthGrayTenantIds().contains(tenantId)) {
            log.info("租户[{}]命中Auth灰度", tenantId);
            return true;
        }
        return false;
    }

    public List<Long> getAuthGrayTenantIds() {
        if (StringUtils.isEmpty(authGrayTenantIdsStr)) {
            return Collections.emptyList();
        }
        List<Long> list = Arrays.stream(authGrayTenantIdsStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        return list;
    }

//    public boolean executeOrderCenterGray(Long tenantId) {
//        if (orderCenterGrayRelease) {
//            return true;
//        }
//        return getOrderCenterGrayTenantIds().contains(tenantId);
//    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}
