package com.cosfo.mall.common.config;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2024-01-12
 * @Description: 订单根据门店分组配置进行拦截
 */
@Slf4j
@Configuration
@Data
public class OrderInterceptStoreGroupConfig {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    /**
     * 订单根据门店分组配置进行拦截配置
     */
    @NacosValue(value = "${order.intercept.storeGruop:{4:1086}}", autoRefreshed = true)
    public String orderInterceptStoreGroup;

    /**
     * 订单拦截基于租户的生效时间配置
     */
    @NacosValue(value = "${order.intercept.beginTime:{4:1705420800000}}", autoRefreshed = true)
    public String orderInterceptBeginTime;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

    /**
     * 是否需要拦截订单
     * @param tenantId
     * @param storeGroupId
     * @return
     */
    public boolean interceptOrder(Long tenantId, Long storeGroupId) {
        try {
            Map<Long, Long> classificationMap = JSON.parseObject(orderInterceptStoreGroup, new TypeReference<Map<Long, Long>>() {
            });
            Map<Long, Long> tenantTimeMap = JSON.parseObject(orderInterceptBeginTime, new TypeReference<Map<Long, Long>>() {
            });

            log.info("interceptOrder classificationMap:{},tenantTimeMap:{},tenantId:{},storeGroupId:{}", classificationMap, tenantTimeMap, tenantId, storeGroupId);
            if (CollectionUtil.isEmpty(classificationMap) || CollectionUtil.isEmpty(tenantTimeMap)) {
                return false;
            }
            Long configStoreGroupId = classificationMap.get(tenantId);
            Long beginTime = tenantTimeMap.get(tenantId);
            if (Objects.isNull(configStoreGroupId) || Objects.isNull(beginTime)) {
                return false;
            }
            // 租户是否配置门店分组、生效时间
            boolean groupConfig = configStoreGroupId.equals(storeGroupId);
            boolean timeConfig = System.currentTimeMillis() > beginTime;
            return groupConfig && timeConfig;
        } catch (Exception e) {
            log.error("订单根据门店分组配置进行拦截 异常,默认不拦截订单", e);
            return false;
        }
    }
}
