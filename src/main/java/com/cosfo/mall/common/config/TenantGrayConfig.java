package com.cosfo.mall.common.config;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.system.model.po.SystemParameters;
import com.cosfo.mall.system.service.SystemParameterService;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 白名单配置
 */
@Slf4j
@Component
public class TenantGrayConfig {

    @Resource
    private SystemParameterService systemParameterService;

    public LoadingCache<String, SystemParameters> SYSTEM_CONFIG_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .maximumSize(20)
            .build(new CacheLoader<String, SystemParameters>() {
                @Override
                public SystemParameters load(String key) {
                    SystemParameters systemParameters = systemParameterService.selectByKey(key);
                    log.info("SYSTEM_CACHE systemParameters:{}", JSON.toJSONString(systemParameters));
                    return systemParameters;
                }
            });

    /**
     * 汇付是否全量开启退款重试机制
     */
    public static final String HUIFU_REFUND_RETRY_TENANT = "huifu_refund_retry_tenant";

    /**
     * 开启全量标志
     */
    public static final Long OPEN_TENANT_FLAG = -1L;

    /**
     * 商品中心是否可以使用新功能 true-是 false-否
     * @param tenantId
     * @return
     */
//    public boolean enableUseNewFeature(Long tenantId){
//        return true;
//    }

    /**
     * 是否开启汇付退款新方式
     * @param tenantId
     * @return
     */
    public boolean enableHuiFuRefundRetry(Long tenantId) {
        if (tenantId == null) {
            return false;
        }

        SystemParameters systemParameters = SYSTEM_CONFIG_CACHE.get(HUIFU_REFUND_RETRY_TENANT);
        if (systemParameters == null) {
            return false;
        }

        String value = systemParameters.getParamValue();

        try {
            if (StringUtils.isEmpty(value)) {
                return false;
            }
            Long configId = Long.valueOf(value);
            if (OPEN_TENANT_FLAG.equals(configId)) {
                return true;
            }

            if (configId.equals(tenantId)) {
                return true;
            }
        } catch (Exception e) {
            log.error("判断gray_scale_tenant配置报错, tenantId={}", tenantId, e);
            return false;
        }

        log.info("tenantId={} 使用旧功能...", tenantId);
        return false;
    }

    /**
     * 灰度支付租户
     *
     * @param tenantId
     * @return
     */
//    public boolean enableUsePayNewFeature(Long tenantId){
//        if(tenantId == null){
//            return false;
//        }
//
//        SystemParameters systemParameters = systemParameterService.selectByKey("gray_scale_pay_tenant");
//        if(systemParameters == null){
//            return false;
//        }
//
//        String value = systemParameters.getParamValue();
//
//        try {
//            if(value == null || JSONArray.parseArray(value, Long.class).contains(tenantId)){
//                return true;
//            }
//        } catch (Exception e) {
//            log.error("判断gray_scale_tenant配置报错, tenantId={}", tenantId, e);
//        }
//
//        log.info("tenantId={} 使用旧功能...", tenantId);
//        return false;
//    }
}