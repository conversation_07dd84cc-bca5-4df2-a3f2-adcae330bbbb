package com.cosfo.mall.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-09-19
 * @Description: 飞书群配置信息
 */
@Slf4j
@Configuration
@Data
public class OpenApiConfig implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    /**
     * openApi飞书告警群url
     */
    @NacosValue(value = "${open.api.warn.url:https://open.feishu.cn/open-apis/bot/v2/hook/538aca4f-cc25-4313-aad2-5fb034e901fa}", autoRefreshed = true)
    public String openApiWarnUrl;

    /**
     * openApi飞书告警群url签名
     */
    @NacosValue(value = "${open.api.warn.url.sign:z0pf6AYKrKiLJv2lekOOXf}", autoRefreshed = true)
    public String openApiWarnUrlSign;

    @NacosValue(value = "${open.api.itemcode.tenantid:59}", autoRefreshed = true)
    public Set<Long> itemCodeOpenApiTenantIds;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }
}
