package com.cosfo.mall.common.utils.binlog.observer;

import java.util.Map;

/**
 * binlog观察者, 用于处理binlog事件. 通过实现该接口, 可以在binlog事件发生时, 执行自定义的逻辑.
 * 单个binlog事件可能会触发多个观察者的处理逻辑, 因此观察者的处理逻辑应该尽量简单, 且不应该有顺序依赖.
 * 通常情况下,请不要直接实现该接口, 而是以表为单位新建接口继承该接口,以实现不同的观察者处理不同的数据库表.
 * 例如,如果是处理payment表的变动, 则新建PaymentObserver接口继承该接口, 并实现多个PaymentObserver接口.
 */
public interface BinlogObserver {

    default void onInsert(Map<String, String> newData) {
        // default implementation (do nothing)
    }

    default void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        // default implementation (do nothing)
    }

    default void onDelete(Map<String, String> oldData) {
        // default implementation (do nothing)
    }
}
