package com.cosfo.mall.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;

import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PageInfoConverter<T,R> {

    private PageInfoConverter() {
        // 无需实现
    }

    public static <T,R> PageInfo<R> toPageInfo(Page<T> page, Function<T,R> dataConvert) {
        PageInfo<R> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(Long.valueOf(page.getCurrent()).intValue());
        pageInfo.setPageSize(Long.valueOf(page.getSize()).intValue());
        pageInfo.setPages(Long.valueOf(page.getPages()).intValue());
        pageInfo.setTotal(Long.valueOf(page.getTotal()).intValue());
        pageInfo.setList(page.getRecords().stream().map(dataConvert).collect(Collectors.toList()));
        pageInfo.setIsLastPage(pageInfo.getPageNum() == pageInfo.getPages() || pageInfo.getPages() == 0);
        return pageInfo;
    }

    public static <T,R> PageInfo<R> toPageInfo(PageInfo<T> page, Function<T,R> dataConvert) {
        PageInfo<R> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setIsFirstPage(page.getPageNum() == 1);
        pageInfo.setIsLastPage(page.getPageNum() == page.getPages() || page.getPages() == 0);
        pageInfo.setHasPreviousPage(page.getPageNum() > 1);
        pageInfo.setHasNextPage(page.getPageNum() < page.getPages());
        pageInfo.setList(page.getList().stream().map(dataConvert).collect(Collectors.toList()));
        return pageInfo;
    }
}
