package com.cosfo.mall.common.utils.binlog.impl;

import cn.hutool.core.lang.Pair;
import com.cosfo.mall.common.context.binlog.BinlogEventEnum;
import com.cosfo.mall.common.context.binlog.DbTableName;
import com.cosfo.mall.common.mq.model.DtsModelBO;
import com.cosfo.mall.common.utils.binlog.DbTableDmlService;
import com.cosfo.mall.common.utils.binlog.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * order表监听
 * @author: xiaowk
 * @time: 2023/7/11 下午4:27
 */
@Slf4j
@Component
public class OrderDmlServiceImpl implements DbTableDmlService {

    @Override
    public String getTableDmlName() {
        return DbTableName.COSFO_TABLE_ORDER;
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (!Objects.equals(BinlogEventEnum.UPDATE.getEvent(), dtsModelBo.getType())) {
            return;
        }
        if (CollectionUtils.isEmpty(dtsModelBo.getData())) {
            return;
        }

        List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelBo);
        for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
            Map<String, String> dataMap = pair.getKey();
            Map<String, String> oldMap = pair.getValue();
        }
    }
}
