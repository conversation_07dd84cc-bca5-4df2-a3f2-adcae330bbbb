package com.cosfo.mall.common.utils.binlog.impl;

import com.cosfo.mall.common.context.binlog.DbTableName;
import com.cosfo.mall.common.utils.binlog.observer.BinlogObserver;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> fss
 * create at:  2023/05/18
 */
@Slf4j
@Component
public class OrderAfterSaleDmlServiceImpl extends DbTableDmlServiceImpl {

    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    @Override
    public String getTableDmlName() {
        return DbTableName.ORDER_AFTER_SALE;
    }

    @Override
    protected List<? extends BinlogObserver> getObservers() {
        return Collections.emptyList();
    }

    @Override
    protected void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        String orderAfterSaleId = newData.get("id");
        if (StringUtils.isEmpty(orderAfterSaleId)) {
            log.info("监听order_after_sale变动传入id为空，过滤消息");
            return;
        }
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(Long.valueOf(orderAfterSaleId))));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            log.error("售后单不存在 orderAfterSaleId:{}", orderAfterSaleId, new BizException("退款单不存在"));
            return;
        }

        OrderAfterSaleResp orderAfterSale = afterSaleDTOList.get(0);
        // 售后单完成，更新门店库存
        storeInventoryOutboundByAftersaleOrder(orderAfterSale);

        Integer oldStatus = Optional.ofNullable(oldData.get("status")).map(Integer::valueOf).orElse(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        if (OrderAfterSaleStatusEnum.REFUNDING.getValue().equals(oldStatus)) {
            log.info("监听order_after_sale,之前已经是退款中状态，立即终止,orderAfterSaleId:{}", orderAfterSaleId);
            return;
        }

        // 新数据为退款中状态才需要发起审核
        Integer status = Optional.ofNullable(newData.get("status")).map(Integer::valueOf).orElse(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        if (!OrderAfterSaleStatusEnum.REFUNDING.getValue().equals(status)) {
            log.info("监听order_after_sale,当前状态不是退款中状态，立即终止,orderAfterSaleId:{}", orderAfterSaleId);
            return;
        }

        Integer serviceType = orderAfterSale.getServiceType();
        // 校验售后单类型
        if (OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue().equals(serviceType) ||
                OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(serviceType)) {
            log.info("换货、补发类型售后单，无需执行退款 orderAfterSaleId:{}", orderAfterSaleId);
            return;
        }
    }

    private void storeInventoryOutboundByAftersaleOrder(OrderAfterSaleResp afterSaleDTO) {
        log.info ("售后消息，开始进行门店出库，no={}",afterSaleDTO.getAfterSaleOrderNo ());
    }
}
