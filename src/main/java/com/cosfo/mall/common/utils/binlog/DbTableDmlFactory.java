package com.cosfo.mall.common.utils.binlog;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2022/10/21  16:54
 */
@Component
public class DbTableDmlFactory implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * 获取实现
     */
    private static Collection<DbTableDmlService> dbTableDmlList;

    /**
     * 通过注册好的表名,获取实现类对象
     *
     * @param dbTableNameEnum 表名
     * @return 实现类对象
     */
    public DbTableDmlService creator(String dbTableNameEnum) {
        if (CollectionUtils.isEmpty(dbTableDmlList)) {
            dbTableDmlList = applicationContext
                    .getBeansOfType(DbTableDmlService.class)
                    .values();
        }
        return dbTableDmlList.stream().filter(x -> Objects.equals(x.getTableDmlName(), dbTableNameEnum))
                .findFirst()
                .orElse(null);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        dbTableDmlList = applicationContext.getBeansOfType(DbTableDmlService.class).values();
    }
}
