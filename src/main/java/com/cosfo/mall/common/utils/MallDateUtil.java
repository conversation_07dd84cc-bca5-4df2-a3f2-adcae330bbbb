package com.cosfo.mall.common.utils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 */
public class MallDateUtil {
    /**
     * 获取 day 当周开始时间
     * @param day
     * @return
     */
    public static LocalDateTime startOfWeek(LocalDateTime day) {
        if (day == null) {
            return null;
        }
        return day.with(DayOfWeek.MONDAY).with(LocalTime.MIN);
    }

    /**
     * 获取day当月开始时间
     * @param day
     * @return
     */
    public static LocalDateTime startOfMonth(LocalDateTime day) {
        if (day == null) {
            return null;
        }
        return day.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }
}
