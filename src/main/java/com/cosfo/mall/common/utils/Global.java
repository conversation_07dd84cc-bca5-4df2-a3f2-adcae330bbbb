package com.cosfo.mall.common.utils;


import cn.hutool.core.util.IdUtil;

/**
 * @Package: com.manageSystem.contexts
 * @Description: 系统常用参数
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
public class Global {

    /**
     * 普通订单业务编号
     */
    public static final String NORMAL_ORDER_CODE = "OR";

    /**
     * 售后订单业务编号
     */
    public static final String NORMAL_ORDER_AFTER_SALE_CODE = "AS";

    /**
     * 特殊订单业务编号
     */
    public static final String EXCEPTIONAL_ORDER_CODE = "EX";


    /**
     * 汇付支付流水号
     */
    public static final String HUIFU_PAY_CODE = "HP";

    /**
     * 汇付关单流水号
     */
    public static final String HUIFU_PAY_CLOSE = "HP_CLOSE";
    /**
     * 汇付退款流水号
     */
    public static final String HUIFU_REFUND = "HP_REFUND";

    /**
     * 汇付用户信息查询流水号前缀
     */
    public static final String HUIFU_USER_CODE = "HPU";

    /**
     * 汇付余额信息查询流水号前缀
     */
    public static final String HUIFU_BALANCE_INFO_CODE = "BI";

    /**
     * 汇付交易确认退款前缀
     */
    private static final String HUIFU_CONFIRM_REFUND = "CR";

    /**
     * 汇付流水号时间格式
     */
    public static final String HUIFU_FORMAT = "yyyyMMddHHmmss";

    /**
     * 生成订单号
     *
     * @param orderCode
     * @return
     */
    public static String createOrderNo(String orderCode) {
        return orderCode + System.currentTimeMillis() + StringUtils.orderRandomNum();
    }

    /**
     * 生成汇付流水号
     *
     * @param huifuCode
     * @return
     */
    public static String createHuiFuNo(String huifuCode) {
        return huifuCode + System.currentTimeMillis() + StringUtils.orderRandomNum();
    }

    /**
     * 生成支付单号
     *
     * @return 支付单号
     */
    public static String generatePaymentNo() {
        return "P" + IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 生成支付单号
     *
     * @return 支付单号
     */
    public static String generateRefundNo() {
        return "R" + IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 生成交易确认退款单号
     *
     * @param afterSaleId
     * @return
     */
    public static String generateConfirmRefundNo(Long afterSaleId) {
        return HUIFU_CONFIRM_REFUND + System.currentTimeMillis() + afterSaleId;
    }

    /**
     * 生成异常订单号
     */
//    public static String generateExceptionalOrderNo() {
//        return EXCEPTIONAL_ORDER_CODE + System.currentTimeMillis() + StringUtils.orderRandomNum();
//    }
}
