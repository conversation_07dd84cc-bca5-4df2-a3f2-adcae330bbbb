package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.Map;
import java.util.TreeMap;

public abstract class SignatureUtil {

	private static Logger logger = LoggerFactory.getLogger(com.cosfo.mall.common.utils.SignatureUtil.class);
	
	/**
	 * 生成sign HMAC-SHA256 或 MD5 签名
	 * @param map map
	 * @param paternerKey paternerKey
	 * @return sign
	 */
	public static String generateSign(Map<String, String> map,String paternerKey){
		return generateSign(map, null, paternerKey);
	}
	
	/**
	 * 生成sign HMAC-SHA256 或 MD5 签名
	 * @param map map
	 * @param sign_type HMAC-SHA256 或 MD5
	 * @param paternerKey paternerKey
	 * @return sign
	 */
	public static String generateSign(Map<String, String> map,String sign_type,String paternerKey){
		Map<String, String> tmap = MapUtil.order(map);
		if(tmap.containsKey("sign")){
			tmap.remove("sign");
		}
		String str = MapUtil.mapJoin(tmap, false, false);
		if(sign_type == null){
			sign_type = tmap.get("sign_type");
		}
		if("HMAC-SHA256".equalsIgnoreCase(sign_type)){
			try {
				  Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
				  SecretKeySpec secret_key = new SecretKeySpec(paternerKey.getBytes("UTF-8"), "HmacSHA256");
				  sha256_HMAC.init(secret_key);
				  return Hex.encodeHexString(sha256_HMAC.doFinal((str+"&key="+paternerKey).getBytes("UTF-8"))).toUpperCase();
			} catch (Exception e) {
				logger.error("", e);
			}
			return null;
		}else{//default MD5
			return DigestUtils.md5Hex(str+"&key="+paternerKey).toUpperCase();
		}
	}
	
	/**
	 * 生成事件消息接收签名
	 * @param token token
	 * @param timestamp timestamp
	 * @param nonce nonce
	 * @return str
	 */
	public static String generateEventMessageSignature(String token, String timestamp,String nonce) {
		String[] array = new String[]{token,timestamp,nonce};
		Arrays.sort(array);
		String s = StringUtils.arrayToDelimitedString(array, "");
		return DigestUtils.shaHex(s);
	}

	/**
	 * mch 支付、代扣异步通知签名验证
	 * @param map 参与签名的参数
	 * @param key mch key
	 * @return boolean
	 */
	public static boolean validateSign(Map<String,String> map,String key){
		return validateSign(map, null, key);
	}
	
	/**
	 * mch 支付、代扣API调用签名验证
	 * 
	 * @param map 参与签名的参数
	 * @param sign_type HMAC-SHA256 或 MD5 
	 * @param key mch key
	 * @return boolean
	 */
	public static boolean validateSign(Map<String,String> map,String sign_type,String key){
		if(map.get("sign") == null){
			return false;
		}
		return map.get("sign").equals(generateSign(map,sign_type,key));
	}


	/**
	 * 汇付调用签名
	 */
	public static String bodySign(Object obj, String privateKey){
		String s = JSONObject.toJSONString(obj);
		String sign = sign(s, privateKey);
		logger.info("签名信息，obj：{}, treeMap：{}, sign：{}", obj, s, sign);
		return sign;
	}

	/**
	 * 汇付RSA私钥签名：签名方式SHA256WithRSA
	 * @param data 待签名字符串
	 * @param privateKeyBase64 私钥（Base64编码）
	 * @return 签名byte[]
	 * @throws Exception
	 */
	public static String sign(String data, String privateKeyBase64) {
		// Base64 --> Key
		try {
			byte[] bytes = Base64.getDecoder().decode(privateKeyBase64);
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(bytes);
			KeyFactory keyFactory;
			keyFactory = KeyFactory.getInstance("RSA");
			PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
			// Sign
			Signature signature = Signature.getInstance("SHA256WithRSA");
			signature.initSign(privateKey);
			signature.update(data.getBytes("UTF-8"));
			return Base64.getEncoder().encodeToString(signature.sign());
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 使用汇付RSA公钥验签
	 * @param data 待签名字符串
	 * @param publicKeyBase64 公钥（Base64编码）
	 * @return 验签结果
	 * @throws Exception
	 */
	public static boolean verify(String data, String publicKeyBase64, String sign) {
		// Base64 --> Key
		try {
			byte[] bytes = Base64.getDecoder().decode(publicKeyBase64);
			X509EncodedKeySpec keySpec = new X509EncodedKeySpec(bytes);
			KeyFactory keyFactory;
			keyFactory = KeyFactory.getInstance("RSA");
			PublicKey publicKey = keyFactory.generatePublic(keySpec);
			// verify
			Signature signature = Signature.getInstance("SHA256WithRSA");
			signature.initVerify(publicKey);
			signature.update(data.getBytes("UTF-8"));
			return signature.verify(Base64.getDecoder().decode(sign));
		} catch (Exception e) {
			logger.error("Exception", e);
			return false;
		}

	}
}
