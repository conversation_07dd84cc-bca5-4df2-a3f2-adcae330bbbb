package com.cosfo.mall.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;

/**
 * 文件util
 *
 * <AUTHOR>
 * @date 2020/08/29
 */
@Slf4j
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    private static final int BUFFER_SIZE = 8192;

    private FileUtil() {
    }

    /**
     * 获得文件缓存地址
     *
     * @param suffixName 文件后缀 例如.jpg
     * @return 文件缓存地址
     */
    public static String tempFilePath(String suffixName) {
        return System.getProperty("user.dir") + File.separator + tempFileName(suffixName);
    }

    /**
     * 获得文件临时名称
     *
     * @param suffixName 文件后缀 例如.jpg
     * @return 文件名称
     */
    public static String tempFileName(String suffixName) {
        return System.currentTimeMillis() + suffixName;
    }

    /**
     * 文件删除
     *
     * @param filePath 文件路径
     */
    public static void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (Exception e) {
            logger.error("删除文件失败");
        }
    }

    /**
     * 获得文件后缀
     *
     * @param fileUrl 文件url 例如 http://a3.att.hudong.com/14/75/01300000164186121366756803686.jpg
     * @return 文件后缀 例如 .jpg
     */
    public static String getFileSuffix(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            return null;
        }

        int index = fileUrl.lastIndexOf(".");
        if(index == -1){
            return null;
        }

        return fileUrl.substring(index);
    }

    /**
     * 获得文件流
     *
     * @param fileUrl 文件url 例如 http://a3.att.hudong.com/14/75/01300000164186121366756803686.jpg
     * @return 文件流
     */
    public static InputStream getInputStreamFromFile(CloseableHttpClient httpClient, String fileUrl) {

        if (StringUtils.isEmpty(fileUrl)) {
            logger.info("fileUrl is null or empty");
            return null;
        }

        if (httpClient == null) {
            logger.info("httpClient is null or empty");
            return null;
        }

        try {
            RequestConfig timeoutConfig = RequestConfig.custom()
                    .setConnectTimeout(5000)
                    .setConnectionRequestTimeout(1000)
                    .setSocketTimeout(5000)
                    .build();

            HttpGet httpGet = new HttpGet(fileUrl);
            httpGet.setConfig(timeoutConfig);

            HttpResponse httpResponse = httpClient.execute(httpGet);

            if (httpResponse.getStatusLine().getStatusCode() != 200) {
            }

            return httpResponse.getEntity().getContent();
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 将inputStream转化为file
     *
     * @param is   输入流
     * @param file 要输出的文件
     */
    public static void inputStream2File(InputStream is, File file) throws Exception {
//        OutputStream os = null;
        try (OutputStream os = new FileOutputStream(file);){
//            os = new FileOutputStream(file);
            int len = 0;
            byte[] buffer = new byte[BUFFER_SIZE];

            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
        } finally {
//            os.close();
            is.close();
        }
    }

    /**
     * MultipartFile 转 File
     *
     * @param multipartFile
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile multipartFile) throws Exception {

        File toFile = null;
        if (multipartFile == null || "".equals(multipartFile) || multipartFile.getSize() <= 0) {
            multipartFile = null;
        } else {
            InputStream ins = null;
            ins = multipartFile.getInputStream();
            toFile = new File(multipartFile.getOriginalFilename());
            inputStream2File(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    /**
     * File转MultipartFile
     *
     * @param file
     * @return
     */
    public static MultipartFile fileToMultipartFile(File file) {
        final DiskFileItem item = new DiskFileItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true, file.getName(), 100000000, file.getParentFile());
        try {
            OutputStream os = item.getOutputStream();
            os.write(FileUtils.readFileToByteArray(file));
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }
        return new CommonsMultipartFile(item);
    }

    /**
     * 根据文件url获得File
     *
     * @param fileUrl 文件url 例如 http://a3.att.hudong.com/14/75/01300000164186121366756803686.jpg
     * @return File
     */
    public static File getFileFromUrl(String fileUrl,String fileSuffix) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault();) {
            InputStream inputStream = FileUtil.getInputStreamFromFile(httpClient, fileUrl);

            String fileTempPath = FileUtil.tempFilePath(fileSuffix);

            File file = new File(fileTempPath);

            FileUtil.inputStream2File(inputStream, file);

            return file;
        } catch (Exception e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 根据图片url得到base64字符串
     *
     * @param fileUrl 文件url 例如 https://td-dev-public.oss-cn-hangzhou.aliyuncs.com/freight/1598237300898530036.jpg
     * @return 图片base64字符串
     */
    public static String getBase64FromUrl(String fileUrl) {

        ByteArrayOutputStream data = new ByteArrayOutputStream();
        try (CloseableHttpClient httpClient = HttpClients.createDefault();) {
            InputStream inputStream = FileUtil.getInputStreamFromFile(httpClient, fileUrl);

            if (inputStream == null) {
                logger.info("inputStream is null");
                return null;
            }


            byte[] byteArray = new byte[BUFFER_SIZE];
            int length = -1;
            while ((length = inputStream.read(byteArray)) != -1) {
                data.write(byteArray, 0, length);
            }
            // 关闭流
            inputStream.close();
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        // 对字节数组Base64编码
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(data.toByteArray());
    }
}
