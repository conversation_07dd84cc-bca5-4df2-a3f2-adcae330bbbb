package com.cosfo.mall.common.utils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class PageInfoHelper {
    public PageInfoHelper() {
    }

    public static <T> PageInfo<T> createPageInfo(List<T> data, Integer pageSize) {
        PageInfo pageInfo = new PageInfo(data);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSize(pageInfo.getTotal() != 0 ? ((new Long(pageInfo.getTotal())).intValue() / pageSize) + 1 : 0);
        return pageInfo;
    }

    public static <T> PageInfo<T> createPageInfo(int pageIndex, int pageSize, Supplier<List<T>> query) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo((List)query.get());
    }
}
