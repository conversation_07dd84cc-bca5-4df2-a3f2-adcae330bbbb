package com.cosfo.mall.common.utils;

import com.cosfo.mall.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.util.Objects;
import java.util.Random;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/17 19:23
 */
@Slf4j
public class StringUtils extends org.springframework.util.StringUtils {

    private static Logger logger = LoggerFactory.getLogger(StringUtils.class);

    private static final Charset UTF8 = Charset.forName("UTF-8");

    /**
     * 用于截断字符串，数据库的varchar字段的长度是用Byte[] 数组的长度来计算的， 因此varchar(127) 表示最多byte[127], 而不是 127个字符，因为中文字符占用了3个byte；emoji占用了4个byte；
     *
     * @param str
     * @param sizeOfUtfBytes
     * @return
     */
    public static String trimToLengthUTF8(String str, int sizeOfUtfBytes) {
        if (sizeOfUtfBytes <= 3) {
            sizeOfUtfBytes = 3;
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(str)) {
            return str;
        }
        byte[] byteArray = str.getBytes(UTF8);
        if (byteArray.length <= sizeOfUtfBytes) {
            return str;
        }

        int charsToRemove = (byteArray.length - sizeOfUtfBytes) / 3;
        //至少移除一位；
        charsToRemove = Math.max(1, charsToRemove);
        return trimToLengthUTF8(str.substring(0, str.length() - charsToRemove), sizeOfUtfBytes);
    }

    /**
     * 截断字符串；截断到byte数组的长度为最多 sizeOfUtfBytes，且保留trailingStrToKeep；
     *
     * @param str               要截断的字符串
     * @param trailingStrToKeep 要保留的结尾字符串
     * @param sizeOfUtfBytes    最终的byte数组大小
     * @return 截断后的字符串+要保留的结尾字符串
     */
    public static String trimToLengthUTF8(String str, String trailingStrToKeep, int sizeOfUtfBytes) {
        if (!org.apache.commons.lang3.StringUtils.isEmpty(trailingStrToKeep)) {
            byte[] keepingBytes = trailingStrToKeep.getBytes(UTF8);
            if (sizeOfUtfBytes <= keepingBytes.length) {
                throw new IllegalArgumentException("unable to keep trailing string:" + str + ", trailingStrToKeep:" + trailingStrToKeep);
            }
            sizeOfUtfBytes -= keepingBytes.length;
        }

        return trimToLengthUTF8(str, sizeOfUtfBytes) + trailingStrToKeep;
    }

    public static final String orderRandomNum() {
        Random random = new Random();
        int rs = random.nextInt(99);
        return rs < 10 ? "0" + rs : rs + "";
    }

    public static boolean isBlank(Object... objects) {
        boolean result = false;
        if (null == objects) {
            return true;
        } else {
            Object[] var2 = objects;
            int var3 = objects.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                Object object = var2[var4];
                if (null == object || "".equals(object.toString().trim()) || "null".equals(object.toString().trim())) {
                    result = true;
                    break;
                }
            }

            return result;
        }
    }

    /**
     * 校验门店名称是否规范
     *
     * @param storeName
     * @return
     */
    public static boolean isStoreName(String storeName) {
        return isEmpty(storeName) ? false : storeName.matches("[\\u4e00-\\u9fa5a-zA-Z0-9]{1,20}");
    }

    /**
     * 校验地址是否规范
     *
     * @param storeAddress
     * @return
     */
    public static boolean isAddress(String storeAddress) {
        return isEmpty(storeAddress) ? false : storeAddress.length() > 100 ? false : true;
    }

    /**
     * 校验门牌号是否规范
     *
     * @return
     */
    public static boolean isHouseNumber(String houseNumber) {
        return houseNumber.matches("[\\u4e00-\\u9fa5a-zA-Z0-9-]{1,50}");
    }

    /**
     * 用户中心错误提示语处理
     * @param response
     * @param defaultMsg
     * @return
     */
    public static String builderErrorMsg(DubboResponse response, String defaultMsg) {
        if (Objects.nonNull(response) && !response.isSuccess()) {
            if (!StringUtils.isEmpty(response.getCode()) && response.getCode().startsWith(Constants.USER_CENTER_BIZ_CODE_PREFIX)) {
                return response.getMsg();
            }
        }
        return defaultMsg;
    }
}
