package com.cosfo.mall.common.utils.binlog;

import cn.hutool.core.lang.Pair;
import com.cosfo.mall.common.mq.model.DtsModelBO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DtsModelHandler {

    /**
     * 获取DtsModel对齐的数据
     * @return pair集合（pair左边是data，右边是old）
     */
    public static List<Pair<Map<String, String>, Map<String, String>>> getAlignedData(DtsModelBO dtsModelEvent){
        List<Pair<Map<String, String>, Map<String, String>>> pairList = new ArrayList<>();
        int size = dtsModelEvent.getData() == null ? 0 : dtsModelEvent.getData().size();
        int oldSize = dtsModelEvent.getOld() == null ? 0 : dtsModelEvent.getOld().size();
        if (size != oldSize){
            return pairList;
        }
        for (int i = 0; i < size; i++) {
            pairList.add(new Pair<>(dtsModelEvent.getData().get(i), dtsModelEvent.getOld().get(i)));
        }
        return pairList;
    }

    /**
     * 只获取新数据(此方法仅供新增事件使用)
     */
    public static List<Map<String, String>> getOnlyNewData(DtsModelBO dtsModelEvent) {
        return dtsModelEvent.getData();
    }

}
