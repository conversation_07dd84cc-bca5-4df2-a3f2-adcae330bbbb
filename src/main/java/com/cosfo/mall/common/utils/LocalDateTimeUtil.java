package com.cosfo.mall.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/31
 */
@Slf4j
public class LocalDateTimeUtil {

    private LocalDateTimeUtil() {
    }

    /**
     * 时间格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String TIME_FROMAT_SIMPLE1 = "yyyy-MM-dd HH:mm:ss";
    /**
     * 时间格式：yyyy/MM/dd HH:mm:ss
     */
    public static final String TIME_FROMAT_SIMPLE2 = "yyyy/MM/dd HH:mm:ss";
    /**
     * 时间格式：yyyyMMddHHmmss
     */
    public static final String TIME_FROMAT_SIMPLE3 = "yyyyMMddHHmmss";
    /**
     * 时间格式：yyyy年MM月dd日 HH点mm分ss秒
     */
    public static final String TIME_FROMAT_SIMPLE4 = "yyyy年MM月dd日 HH点mm分ss秒";

    /**
     * 时间格式：yyyyMMddHH
     */
    public static final String TIME_FROMAT_SIMPLE5 = "yyyyMMddHH";
    /**
     * 时间格式：HH:mm:ss
     */
    public static final String TIME_FROMAT_HHMMSS = "HH:mm:ss";
    /**
     * 日期格式：yyyyMMdd
     */
    public static final String DATE_FROMAT_YYYYMMDD1 = "yyyyMMdd";
    /**
     * 日期格式：yyyy-MM-dd
     */
    public static final String DATE_FROMAT_YYYYMMDD2 = "yyyy-MM-dd";
    /**
     * 日期格式：yyyymm
     */
    public static final String DATE_FROMAT_YYYYMM = "yyyyMM";

    public static final String DATE_FROMAT_SIMPLE6 = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String TIME_FROMAT_MIN = "yyyy-MM-dd HH:mm";

    /**
     * 日期格式：MM月dd日
     */
    public static final String DATE_FROMAT_MMdd = "MM月dd日";

    /**
     * 日期格式：HH:mm
     */
    public static final String TIME_FROMAT_HHMM = "HH:mm";


    /**
     * 时间转成 yyyy-MM-dd HH:mm:ss.SSS 格式
     *
     * @return yyyy-MM-dd HH:mm:ss.SSS格式字符串
     */
    public static String localDateTimeToString(LocalDateTime localDateTime) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(DATE_FROMAT_SIMPLE6);
        return formatDate.format(localDateTime);
    }

    /**
     * 时间转成 yyyy/MM/dd HH:mm:ss格式
     *
     * @return yyyy/MM/dd HH:mm:ss 字符串
     */
    public static String localDateTimeToString2(LocalDateTime localDateTime) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(TIME_FROMAT_SIMPLE2);
        return formatDate.format(localDateTime);
    }

    /**
     *  LocalDateTime时间转成 yyyyMMdd格式
     *
     * @return yyyyMMdd 字符串
     */
    public static String localDateTimeToString1(LocalDateTime localDateTime) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD1);
        return formatDate.format(localDateTime);
    }

    /**
     * LocalDate  转换成yyyyMMdd 类型的字符串
     *
     * @param localDate
     * @return
     */
    public static String localDateToString(LocalDate localDate) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD1);
        return formatDate.format(localDate);
    }

    /**
     * 获取当前日期
     *
     * @return yyyyMMdd 格式字符串
     */
    public static final String getCurrentDay() {
        return dateToString(new Date(), DATE_FROMAT_YYYYMMDD1);
    }

    /**
     * DateToString
     *
     * @param date
     * @param formatStr
     * @return
     */
    public static final String dateToString(Date date, String formatStr) {
        SimpleDateFormat sf = new SimpleDateFormat(formatStr);
        return sf.format(date);
    }


    /**
     * yyyy-MM-dd格式字符串 转换为LocalDate
     * @param date
     * @return
     */
    public static final LocalDate stringToDate(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD2));
    }

    /**
     * 按指定格式对LocalDate类型进行格式化输出（返回类型：String）
     *
     * @param date
     * @return  yyyy-MM-dd格式字符串
     */
    public static String localDateFormat(LocalDate date) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD2);
        return date.format(formatDate);
    }

    /**
     * 按指定格式对LocalTime类型进行格式化输出（返回类型：String）
     * HH:mm:ss
     * @param date
     * @return 返回时分秒
     */
    public static String localTimeFormat(LocalTime date) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(TIME_FROMAT_HHMMSS);
        return date.format(formatDate);
    }

    /**
     * LocalDateTime 类型 转换成yyyy-MM-dd HH:mm:ss 格式字符串
     *
     * @param date 时间
     * @return
     */
    public static String localTimeFormat(LocalDateTime date) {
        DateTimeFormatter formatDate = DateTimeFormatter.ofPattern(TIME_FROMAT_SIMPLE1);
        return date.format(formatDate);
    }

    /**
     * 时间字符串转化为指定格式的时间
     *
     * @param dateStr    被转换的时间字符串
     * @param dateFormat 指定格式的字符串
     * @return LocalDate
     */
    public static LocalDate parseString2LocalDate(String dateStr, String dateFormat) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(dateFormat);
        try {
            return LocalDate.parse(dateStr, dtf);
        } catch (Exception e) {
            log.error("-----parse String to LocalDate error!------dateStr:{}", dateStr, e);
            return null;
        }
    }

    /**
     * 时间字符串转化为指定格式的时间
     *
     * @param dateStr    被转换的时间字符串
     * @param dateFormat 指定格式的字符串
     * @return LocalDate
     */
    public static LocalDateTime parseString2LocalDateTime(String dateStr, String dateFormat) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(dateFormat);
        try {
            return LocalDateTime.parse(dateStr, dtf);
        } catch (Exception e) {
            log.error("-----parse String to LocalDateTime error!------dateStr:{}", dateStr, e);
            return null;
        }
    }


    /**
     * 时间字符串转换成yyyy-MM-dd HH:mm:ss格式的LocalDateTime
     *
     * @param dateTime 时间参数
     * @return LocalDateTime
     */
    public static LocalDateTime parseDateTime(String dateTime) {
        try {
            return LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(TIME_FROMAT_SIMPLE1));
        } catch (Exception e) {
            log.error("-----parse String to LocalDateTime error!------dateTimeStr:{}", dateTime, e);
        }
        return null;
    }

    /**
     * LocalDateTime转换为所需格式的String类型时间
     *
     * @param localDateTime 被格式化的时间
     * @param formatStr     时间格式
     * @return String
     */
    public static String formatLocalDateTime(LocalDateTime localDateTime, String formatStr) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(formatStr);
        return localDateTime.format(dtf);
    }

    /**
     * 计算两LocalDateTime间相差的秒数
     *
     * @param startDateTime 起始时间
     * @param endDateTime   结束时间
     * @return 总秒数
     */
    public static Long toSeconds(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return Duration.between(startDateTime, endDateTime).getSeconds();
    }

    /**
     * 计算两LocalDateTime间相差的秒数
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return 总秒数
     */
    public static int toDays(LocalDate startDate, LocalDate endDate) {
        return Period.between(startDate, endDate).getDays();
    }

    /**
     * 比较所给时间dateTime是否在两时间点(startDateTime,endDateTime)之间
     *
     * @param dateTime      被比较时间
     * @param startDateTime 时间点上限
     * @param endDateTime   时间点下限
     * @return Boolean true/false  若startDateTime<dateTime<endDateTime 则返回true，否则返回false
     */
    public static Boolean isBetween(LocalDateTime dateTime, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return dateTime.isAfter(startDateTime) && dateTime.isBefore(endDateTime);
    }

    /**
     * LocalDate转换为LocalDateTime
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime localDateToLocalDateTime(LocalDate localDate) {
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Instant instant = date.toInstant();//An instantaneous point on the time-line.(时间线上的一个瞬时点。)
        ZoneId zoneId = ZoneId.systemDefault();//A time-zone ID, such as {@code Europe/Paris}.(时区)
        return instant.atZone(zoneId).toLocalDateTime();
    }

    /**
     * LocalDate类型转换成yyyy-MM-dd HH:mm:ss 格式字符串
     *
     * @param localDate localDate
     * @return 字符串结果
     */
    public static String dateStrToLocalDateTime(LocalDate localDate) {
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        return localDateTime.format(DateTimeFormatter.ofPattern(TIME_FROMAT_SIMPLE1));
    }

    /**
     * yyyy-MM-dd 格式字符串转换为LocalDateTime
     *
     * @param source
     * @return
     */
    public static LocalDateTime parseStringToLocalDateTime(String source) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD2);
        LocalDate localDate = LocalDate.parse(source, dateTimeFormatter);
        return localDate.atStartOfDay();
    }

    //" 02:03"
    public static final LocalDateTime stringMinToLocalDateTime(String min, String date) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(TIME_FROMAT_MIN);
        try {
            if (min.length() > 6) {
                return LocalDateTime.parse(min, dtf);
            } else {
                String dated = LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD1)).
                        format(DateTimeFormatter.ofPattern(DATE_FROMAT_YYYYMMDD2));
                return LocalDateTime.parse(dated + " " + min, dtf);
            }
        } catch (Exception e) {
            log.error("-----parse String to LocalDateTime error!------dateStr:{}", min, e);
            return null;
        }
    }
    /**
     * 毫秒级时间戳转 LocalDateTime
     * @param epochMilli 毫秒级时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime ofEpochMilli(long epochMilli){
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneOffset.of("+8"));
    }

}


