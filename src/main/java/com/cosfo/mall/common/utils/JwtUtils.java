package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSON;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.SignatureException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @discription Jwt工具类
 * @date 2022/5/20 10:12
 */
@Slf4j
public class JwtUtils {

    /**
     * 令牌环有效期
     */
    public static final long EXPIRATION_TIME = 12 * 60 * 60 * 1000;
    public static final long NEW_EXPIRATION_TIME = 24 * 60 * 60 * 1000;

    /**
     * 令牌环密钥
     */
    private static final String SECRET_WEAK = "abc123456def";

    private static final String SECRET_STRONG = "YcFnPzHY(5ANfbxpL&@3Q8>6J9zElABy";

    /**
     * 令牌环头标识
     */
    public static final String TOKEN_PREFIX = "cosfo-mall_";

    /**
     * 配置令牌环在http heads中的键值
     */
    public static final String TOKEN_NAME = "token";

    /**
     * 租户id
     */
    public static final String TENANT_ID = "tenantId";

    /**
     * 门店id
     */
    public static final String STORE_ID = "storeId";

    /**
     * 账户id
     */
    public static final String ACCOUNT_ID = "accountId";

    /**
     * 生成system token的机器；
     */
    public static final String SYSTEM_TOKEN_HOST = "systemTokenHost";

    /**
     * 哪些请求不需要进行安全校验
     */
    public static final String[] protectUrlPattern = {
            "/merchant/store/login",
            "/merchant/store/verifyUsernameAndPhoneMatch",
            "/merchant/store/examineCodeAndUpdatePassword",
            "/merchant/store/usernamePasswordLogin",
            "/sdk/user-base/queryAuthUserBase",
            "/merchant/store/sendCode",
            "/merchant/store/examineCode",
            "/merchant/store/smsCodeLogin",
            "/getWeChatPhoneLogin",
            "/merchant/store/submitStoreInfo",
            "/merchant/store/resubmitStoreInfo",
            "/ok",
            "/swagger-ui.html",
            "/swagger-ui/*",
            "/swagger-ui/index.html",
            "/v3/api-docs",
            "/stock/after/sale/unlock/stock",
            "/pay/refund",
            "/pay-notify/wx-direct",
            "/pay-notify/huifu-pay",
            "/pay-notify/huifu-refund",
            "/user/getOpenInfo",
            "/merchant/logo",
            "/home/<USER>/listAll/{pageIndex}/{pageSize}",
            "/marketClassification/unLoginListAll",
            "/order/get/orderInfo",
            "/tenant/tenantInfo",
            "/merchant/store/checkRegistered",
            "/getUserPhoneNumber",
            "/order/query",
            "/order/after/sale/queryAllAfterSaleOrder",
            "/order/after/sale/add",
            "/order/after/sale/query-item-info",
            "/product-sku/synchronized-supply-sku",
            "/supplier/delivery/upsert/having-order",
            "/merchant-store-account/query/list-belong-store-accounts",
            "/merchant-store-account/query/list-store-account",
            "/product-sku/synchronized-supply-sku",
            "/bill-profit-sharing-refund/calculate/{id}",
            "/bill-profit-sharing-refund/do-refund-sharing/{id}",
            "/push/result/receive",
            "/payment/query-total-price",
            "/pay/execute-refund",
            "/bill-profit-sharing/do-profit-sharing/{profitSharingNo}",
            "/bill-profit-sharing/save-snapshot/{orderId}",
            "/bill-profit-sharing/calculate/{profitSharingNo}",
            "/bill-profit-sharing/do-profit-sharing/{profitSharingNo}",
            "/bill-profit-sharing/profit-sharing-flow/{orderId}",
            "/bill-profit-sharing/profit-sharing-flow/by-no/{profitSharingNo}",
            "/bill-profit-sharing/update/snapshots",
            "/order/notify/delivering",
            "/tenant/query/use-phone-verification-flag",
            "/sap/**"
    };

    /**
     * 路径matcher
     */
    private static final PathMatcher pathmatcher = new AntPathMatcher();


    private static String generateTokenInternal(HashMap<String, Object> map, long expirationTime) {
        expirationTime = Math.max(EXPIRATION_TIME, expirationTime);
        String jwt = Jwts.builder()
                .setClaims(map)
                .setExpiration(new Date(System.currentTimeMillis() + Math.max(EXPIRATION_TIME, expirationTime)))
                .signWith(SignatureAlgorithm.HS512, SECRET_STRONG)
                .compact();
        return jwt;
    }

    /**
     * 令牌校验
     *
     * @param request
     * @return
     */
    public static Map<String, Object> validateTokenAndGetClaims(HttpServletRequest request) {
        return validateTokenAndGetClaims(request.getHeader(TOKEN_NAME));
    }

    public static Map<String, Object> validateTokenAndGetClaims(String token) {
        try {
            Map<String, Object> body = Jwts.parser()
                    .setSigningKey(SECRET_STRONG)//默认使用强密码；
                    .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                    .getBody();
            if (!body.containsKey(TENANT_ID) || !body.containsKey(ACCOUNT_ID) || !body.containsKey(STORE_ID)) {
                throw new RuntimeException("token doesn't contain necessary fields:" + JSON.toJSONString(body));
            }
            return body;
        } catch (SignatureException signatureException) {
            log.warn("SignatureException:" + token, signatureException);
            //可能是旧的secret生成的token；
            return validateTokenAndGetClaimsUsingWeakSecret(token);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    private static Map<String, Object> validateTokenAndGetClaimsUsingWeakSecret(String token) {
        try {
            Map<String, Object> body = Jwts.parser()
                    .setSigningKey(SECRET_WEAK)
                    .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                    .getBody();
            if (!body.containsKey(TENANT_ID) || !body.containsKey(ACCOUNT_ID) || !body.containsKey(STORE_ID)) {
                throw new RuntimeException("token doesn't contain necessary fields:" + JSON.toJSONString(body));
            }
            return body;
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    /**
     * 是否过滤接口
     *
     * @param request
     * @return
     */
    public static boolean isProtectedUrl(HttpServletRequest request) {
        for (int i = 0; i < protectUrlPattern.length; i++) {
            if (pathmatcher.match(protectUrlPattern[i], request.getServletPath())) {
                return false;
            }
        }
        return true;
    }
}
