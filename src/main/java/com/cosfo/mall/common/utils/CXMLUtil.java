package com.cosfo.mall.common.utils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.StringWriter;

/**
 * cxml转换工具类
 *
 * @author: xiaowk
 * @date: 2025/4/22 上午11:21
 */
public class CXMLUtil {

    /**
     * 将给定的 XML 字符串解析为指定类型的 Java Bean 对象
     *
     * @param xml   XML 字符串
     * @param clazz 需要转换的 Java Bean 类型
     * @param <T>   目标 Java Bean 类型
     * @return 转换后的 Java Bean 对象
     * @throws JAXBException 如果解析过程中发生错误
     */
    public static <T> T fromXML(String xml, Class<T> clazz) throws JAXBException {
        JAXBContext jc = JAXBContext.newInstance(clazz);
        Unmarshaller unmarshaller = jc.createUnmarshaller();
        InputStream in = new ByteArrayInputStream(xml.getBytes());
        System.setProperty("javax.xml.accessExternalDTD", "all");//设置该参数主要是为了避免解析出错 <!DOCTYPE cXML SYSTEM 'http://xml.cXML.org/schemas/cXML/1.2.008/cXML.dtd'>
        T object = (T)unmarshaller.unmarshal(in);
        return object;
    }

    public static String beanConvertXml(Object obj) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = jaxbContext.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
        StringWriter writer = new StringWriter();
        marshaller.marshal(obj, writer);
        return writer.toString();
    }
}
