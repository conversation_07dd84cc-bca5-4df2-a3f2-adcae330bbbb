package com.cosfo.mall.common.utils;

import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTOEnum;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.error.code.ParamsErrorCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 参数校验断言  不会告警
 * <AUTHOR>
 */
public interface AssertCheckParams {

    static ParamsException buildParamsException(int code, String msg) {
        ParamsErrorCode paramsErrorCode = new ParamsErrorCode();
        paramsErrorCode.setStatus(code);
        return new ParamsException(msg, paramsErrorCode);
    }


    /**
     * 校验字符串参数
     *
     * @param s       许校验的字符串
     * @param message 校验失败的错误消息
     * @return trim后的字符串
     */
    static String assertString(String s, String message) {
        if (s == null || (s = s.trim()).isEmpty()) {
            throw buildParamsException(ResultDTOEnum.PARAMETER_MISSING.getCode(), message);
        }

        return s;
    }

    static void isTrue(boolean expression, int code, String message) {
        if (!expression) {
            throw buildParamsException(code, message);
        }
    }

    static void notNull(Object object, int code, String message) {
        if (object == null) {
            throw buildParamsException(code, message);
        }
    }

    static void isNull(Object object, int code, String message) {
        if (null != object) {
            throw buildParamsException(code, message);
        }
    }

    static void hasText(String value, String nameField) {
        if (value == null || value.length() == 0) {
            throw buildParamsException(ResultDTOEnum.PARAMETER_MISSING.getCode(), nameField);
        }
    }

    static void hasText(String text, int code, String message) {
        if (StringUtils.isEmpty(text)) {
            throw buildParamsException(code, message);
        }
    }

    static void notEmpty(Map<?, ?> map, int code, String message) {
        if (CollectionUtils.isEmpty(map)) {
            throw buildParamsException(code, message);
        }
    }

    static void notEmpty(List<?> data, int code, String message) {
        if (CollectionUtils.isEmpty(data)) {
            throw buildParamsException(code, message);
        }
    }
}
