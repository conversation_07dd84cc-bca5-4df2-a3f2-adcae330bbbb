package com.cosfo.mall.common.utils;

import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTOEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.error.code.BizErrorCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 业务校验断言  不会告警
 * <AUTHOR>
 */
public interface AssertCheckBiz {

    /**
     * 构建业务异常
     * @param code
     * @param msg
     * @return
     */
    static BizException buildBizException(int code, String msg) {
        BizErrorCode bizErrorCode = new BizErrorCode();
        bizErrorCode.setStatus(code);
        return new BizException(msg, bizErrorCode);
    }

    /**
     * 校验字符串参数
     *
     * @param s       许校验的字符串
     * @param message 校验失败的错误消息
     * @return trim后的字符串
     */
    static String assertString(String s, String message) {
        if (s == null || (s = s.trim()).isEmpty()) {
            throw buildBizException(ResultDTOEnum.PARAMETER_MISSING.getCode(), message);
        }

        return s;
    }

    static void isTrue(boolean expression, int code, String message) {
        if (!expression) {
            throw buildBizException(code, message);
        }
    }

    static void notNull(Object object, int code, String message) {
        if (object == null) {
            throw buildBizException( code, message);
        }
    }

    static void isNull(Object object, int code, String message) {
        if (null != object) {
            throw buildBizException(code, message);
        }
    }

    static void hasText(String value, String nameField) {
        if (value == null || value.length() == 0) {
            throw buildBizException(ResultDTOEnum.PARAMETER_MISSING.getCode(), nameField);
        }
    }

    static void hasText(String text, int code, String message) {
        if (StringUtils.isEmpty(text)) {
            throw buildBizException(code, message);
        }
    }

    static void notEmpty(Map<?, ?> map, int code, String message) {
        if (CollectionUtils.isEmpty(map)) {
            throw buildBizException(code, message);
        }
    }

    static void notEmpty(List<?> data, int code, String message) {
        if (CollectionUtils.isEmpty(data)) {
            throw buildBizException(code, message);
        }
    }
}
