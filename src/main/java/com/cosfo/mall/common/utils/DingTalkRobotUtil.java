package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 10:38
 */
@Slf4j
public class DingTalkRobotUtil {

    /**
     * 消息类型-text
     */
    public final static String TEXT = "text";
    /**
     * 消息类型-link
     */
    public final static String LINK = "link";
    /**
     * 消息类型-markdown
     */
    public final static String MARKDOWN = "markdown";
    /**
     * 消息类型-ActionCard
     */
    public final static String ACTION_CARD = "ActionCard";
    /**
     * 消息类型-FeedCard
     */
    public final static String FEED_CARD = "FeedCard";

    /**
     * 发送消息at指定人
     *
     * @param type      消息类型
     * @param url       地址
     * @param msg       消息体
     * @param atMobiles at手机号
     */
    public static String sendMsg(String type, String url, Supplier<Map<String, String>> msg, boolean isAtAll, Supplier<List<String>> atMobiles) {
        JSONObject atJson = new JSONObject();
        atJson.put("isAtAll", isAtAll);
        if (!isAtAll) {
            if (atMobiles != null && !CollectionUtils.isEmpty(atMobiles.get())) {
                atJson.put("atMobiles", atMobiles.get());
            }
        }

        JSONObject msgJson = new JSONObject();
        msgJson.put("msgtype", type);
        msgJson.put(type, msg.get());
        msgJson.put("at", atJson);

        log.info("正在发送钉钉消息...");
        String res = HttpUtil.sendPost(url, msgJson.toJSONString());
        log.info("发送钉钉消息返回结果：{}", res);

        return res;
    }

    /**
     * 发送markDown类型消息at指定人
     *
     * @param url       地址
     * @param msg       消息体
     * @param atMobiles at手机号
     */
    public static String sendMarkDownMsg(String url, Supplier<Map<String, String>> msg, Supplier<List<String>> atMobiles) {
        return sendMsg(MARKDOWN, url, msg, false, atMobiles);
    }

    /**
     * 发送消息@全部人
     *
     * @param type 消息类型
     * @param url  地址
     * @param msg  消息体
     */
    public static String sendMsgAndAtAll(String type, String url, Supplier<Map<String, String>> msg) {
        return sendMsg(type, url, msg, true, null);
    }

    public static String sign(Long timestamp, String secret) throws Exception {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        return sign;
    }

}
