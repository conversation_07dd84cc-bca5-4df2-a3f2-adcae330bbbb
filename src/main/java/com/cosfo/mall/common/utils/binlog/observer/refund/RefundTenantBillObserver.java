package com.cosfo.mall.common.utils.binlog.observer.refund;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class RefundTenantBillObserver implements RefundObserver {

    // 退款状态没有枚举,所以在这里设定
    private static final Integer REFUND_SUCCESS = 2;

    @Override
    public void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        Long refundId = Long.valueOf(newData.get("id"));
        Integer newStatus = Optional.ofNullable(newData.get("refund_status")).map(Integer::valueOf).orElse(null);

        // 当退款状态由退款中转变为退款成功后,更新tenant bill表
        if (REFUND_SUCCESS.equals(newStatus)) {
            updateTenantBillTable(refundId);
        }
    }

    /**
     * 监听refund表.
     * 当退款状态由退款中转变为退款成功后,更新tenant bill表
     */
    private void updateTenantBillTable(Long refundId) {
        log.info("开始更新tenant bill表，refundId: {}", refundId);
//        tenantBillService.createTenantBillForRefund(refundId);
    }
}
