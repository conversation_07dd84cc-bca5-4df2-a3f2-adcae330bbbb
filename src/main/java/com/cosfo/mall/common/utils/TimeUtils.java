package com.cosfo.mall.common.utils;

import cn.hutool.core.date.DateUtil;
import com.cosfo.mall.common.exception.DefaultServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

public class TimeUtils {
    public static final Logger log = LoggerFactory.getLogger(TimeUtils.class);

    /**
     * 东八区
     */
    public static final String GMT8 = "GMT+8";

    public static final String FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_TIME_STAMP = "yyyyMMddHHmmss";
    public static final String FORMAT_UTC = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String FORMAT_BEIJING = "yyyy-MM-dd'T'HH:mm:ss'+08:00'";
    public static final String FORMAT_DATE = "yyyy-MM-dd";
    public static final String FORMAT_HOUSE = "yyyy-MM-dd HH";
    public static final String FORMAT_DATE_CN = "yyyy年MM月dd日";
    public static final String FORMAT_MONTH = "yyyy-MM";
    public static final String FORMAT_STRING = "yyyyMMdd";
    public static final String FORMAT_TIME_TYPE = "yyyyMMdd/HHmmss";
    public static final String FORMAT_SHORT_MONTH = "yyMM";
    public static final String FORMAT_DAY_ONLY = "dd";
    public static final String FORMAT_MONTH_ONLY = "MM";
    public static final String FORMAT_YEAR_ONLY = "YYYY";
    public static final String DAY_TYPE = "d";
    public static final String MONTH_TYPE = "M";
    public static final String YEAR_TYPE = "y";
    public static final String HOUR_TYPE = "h";
    public static final String REGEX_YYYYMMDD = "\\d{8}";
    public static final String REGEX_YYYY_MM_DD = "\\d{4}-\\d{2}-\\d{2}";
    public static final String REGEX_YYYY_MM_DD_HH_MI_SS = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
    public static final String REGEX_NUMBER_CHARACTER = "\\d+[a-z]";
    public static final int WEEK_TYPE_XINGQI = 0;
    public static final int WEEK_TYPE_ZHOU = 1;
    public static final String[] WEEK_ARRAY_XINGQI = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
    public static final String[] WEEK_ARRAY_ZHOU = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    private TimeUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static Date CST2UTC(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 8);
        return calendar.getTime();
    }

    public static String CST2UTC(String dateString, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(changeString2Date(dateString, format));
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 8);
        return changeDate2String(calendar.getTime(), TimeUtils.FORMAT_UTC);
    }

    public static Date UTC2CST(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
        return calendar.getTime();
    }

    public static String UTC2CST(String dateString, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(changeString2Date(dateString, format));
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
        return changeDate2String(calendar.getTime(), TimeUtils.FORMAT);
    }

    public static Date changeString2Date(String timeString) {
        return changeString2Date(timeString, TimeUtils.FORMAT_DATE);
    }

    public static Date changeString2Date(String timeString, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = formatter.parse(timeString);
        } catch (ParseException e) {
        }

        return date;
    }

    public static String changeDate2String(Date date, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    public static String changeDate2String(Date date) {
        return changeDate2String(date, FORMAT_DATE);
    }

    public static String getDateStringByYearMonthDay(Integer year, Integer month, Integer day, String format) {
        Calendar calendar = Calendar.getInstance();
        // 月份是从0到11,与传统日期之间差1个月
        month--;
        calendar.set(year, month, day);
        return changeDate2String(calendar.getTime(), format);
    }

    public static String getDateStringByYearMonth(Integer year, Integer month, String format) {
        // 不传某天默认为第一天
        return getDateStringByYearMonthDay(year, month, 1, format);
    }

    public static String getDateStringByYear(Integer year, String format) {
        // 不传某天默认为第一个月第一天
        return getDateStringByYearMonthDay(year, 1, 1, format);
    }

    public static String format(String date, String formatBefore, String formatAfter) {
        return changeDate2String(changeString2Date(date, formatBefore), formatAfter);
    }

    public static String makeTimeString(String dateString, Integer hour) {
        Date date = TimeUtils.changeString2Date(dateString, TimeUtils.FORMAT_DATE);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, hour);
        date = calendar.getTime();

        SimpleDateFormat df = new SimpleDateFormat(TimeUtils.FORMAT);
        return df.format(date);
    }

    public static String getBeforeTimeString(String dateString, Integer value, String type, String format) {
        Date date = TimeUtils.changeString2Date(dateString, format);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        switch (type) {
            case TimeUtils.DAY_TYPE:
                calendar.add(Calendar.DATE, -value);
                break;
            case TimeUtils.MONTH_TYPE:
                calendar.add(Calendar.MONTH, -value);
                break;
            case TimeUtils.YEAR_TYPE:
                calendar.add(Calendar.YEAR, -value);
                break;
            case TimeUtils.HOUR_TYPE:
                calendar.add(Calendar.HOUR, -value);
                break;
        }

        date = calendar.getTime();

        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.format(date);
    }

    public static String getBeforeTimeString(String dateString, Integer value, String type) {
        return getBeforeTimeString(dateString, value, type, TimeUtils.FORMAT_DATE);
    }

    public static String getBeforeTimeString(String dateString, String dateValue) {
        return getBeforeTimeString(dateString, dateValue, TimeUtils.FORMAT_DATE);
    }

    public static Date getBeforeTime(Date date, Integer value) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -value);
        date = calendar.getTime();
        return date;
    }

    public static String getBeforeTimeString(Date date, Integer value) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -value);
        date = calendar.getTime();
        SimpleDateFormat formatter = new SimpleDateFormat(TimeUtils.FORMAT_DATE);
        return formatter.format(date);
    }

    public static String getBeforeTimeString(String dateString, String dateValue, String format) {
        String regex = REGEX_NUMBER_CHARACTER;
        Pattern pattern = Pattern.compile(regex);
        if (pattern.matcher(dateValue).matches()) {

            String type = dateValue.split("")[dateValue.length() - 1];
            int value = Integer.parseInt(dateValue.replace(type, ""));
            return getBeforeTimeString(dateString, value, type, format);
        }
        return null;
    }

    public static String getTodayString(String format) {

        if (StringUtils.isEmpty(format)) {
            format = FORMAT_DATE;
        }
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(format);

        return sdf.format(date);
    }

    public static String getTomorrowString(String format) {
        return getBeforeTimeString(getTodayString(format), -1, TimeUtils.DAY_TYPE, format);
    }

    public static List<String> getPerDaysByStartAndEndDate(String startDate, String endDate, String dateFormat) {
        return getPerDaysByStartAndEndDate(startDate, endDate, dateFormat, Calendar.DAY_OF_MONTH);
    }

    public static List<String> getPerDaysByStartAndEndDate(String startDate, String endDate, String dateFormat, int calendarType) {
        DateFormat format = new SimpleDateFormat(dateFormat);
        try {
            Date sDate = format.parse(startDate);
            Date eDate = format.parse(endDate);
            long start = sDate.getTime();
            long end = eDate.getTime();
            if (start > end) {
                return null;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(eDate);
            List<String> res = new ArrayList<String>();
            while (end >= start) {
                res.add(format.format(calendar.getTime()));
                calendar.add(calendarType, -1);
                end = calendar.getTimeInMillis();
            }
            return res;
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    public static String getFormat(String date) {
        String format = null;
        // 这里正则使用数字位数与格式的简单匹配，且有日期转换的双重校验
        if (changeString2Date(date, TimeUtils.FORMAT) != null && Pattern.compile(REGEX_YYYY_MM_DD_HH_MI_SS).matcher(date).matches()) {
            format = TimeUtils.FORMAT;
        } else if (changeString2Date(date, TimeUtils.FORMAT_STRING) != null && Pattern.compile(REGEX_YYYYMMDD).matcher(date).matches()) {
            format = TimeUtils.FORMAT_STRING;
        } else if (changeString2Date(date) != null && Pattern.compile(REGEX_YYYY_MM_DD).matcher(date).matches()) {
            format = TimeUtils.FORMAT_DATE;
        }
        return format;
    }

    public static List<Date> getDatesBetweenTwoDate(Date beginDate, Date endDate) {
        List<Date> dateList = new ArrayList<Date>();
        dateList.add(beginDate);// 把开始时间加入集合
        Calendar cal = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        cal.setTime(beginDate);
        boolean bContinue = true;
        while (bContinue) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            cal.add(Calendar.DAY_OF_MONTH, 1);
            // 测试此日期是否在指定日期之后
            if (endDate.after(cal.getTime())) {
                dateList.add(cal.getTime());
            } else {
                break;
            }
        }
        if (beginDate.compareTo(endDate) != 0) {
            dateList.add(endDate);// 把结束时间加入集合
        }
        return dateList;
    }

    public static boolean checkDate(String date, String format) {
        if (changeString2Date(date, format) == null) {
            return false;
        }
        return true;
    }

    /**
     * 得到指定月的天数
     */
    public static int getMonthDay(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);//把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);//日期回滚一天，也就是最后一天
        return a.get(Calendar.DATE);
    }

    /**
     * 获取该周的第一天
     *
     * @param year
     * @param week
     * @return
     */
    public static String getWeekFirstDate(int year, int week) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.WEEK_OF_YEAR, week);
        calendar.set(Calendar.DAY_OF_WEEK, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该周的最后一天
     *
     * @param year
     * @param week
     * @return
     */
    public static String getWeekLastDate(int year, int week) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.WEEK_OF_YEAR, week);
        calendar.set(Calendar.DAY_OF_WEEK, 7);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该月的第一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getMonthFirstDate(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该月的最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getMonthLastDate(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DATE, 1);
        calendar.roll(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该年的第一天
     *
     * @param year
     * @return
     */
    public static String getYearFirstDate(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该年的最后一天
     *
     * @param year
     * @return
     */
    public static String getYearLastDate(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, 11);
        calendar.set(Calendar.DATE, 1);
        calendar.roll(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该日期当年的第一天
     *
     * @param dateString
     * @return
     */
    public static String getYearStartDate(String dateString) {
        Date date = changeString2Date(dateString);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该日期当年的最后一天
     *
     * @param dateString
     * @return
     */
    public static String getYearEndDate(String dateString) {
        Date date = changeString2Date(dateString);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, 11);
        calendar.set(Calendar.DATE, 1);
        calendar.roll(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该日期本周的第一天
     * 注意:MONDAY为第一天
     *
     * @param dateString yyyy-MM-dd 格式
     * @return
     */
    public static String getWeekStartDate(String dateString) {
        Date date = changeString2Date(dateString);
        Calendar calendar = Calendar.getInstance();
//        calendar.setMinimalDaysInFirstWeek(Calendar.MONDAY);
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.DAY_OF_WEEK, 1);
        calendar.add(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取该日期本周的最后一天
     * 注意:MONDAY为第一天
     *
     * @param dateString yyyy-MM-dd 格式
     * @return
     */
    public static String getWeekEndDate(String dateString) {
        Date date = changeString2Date(dateString);
        Calendar calendar = Calendar.getInstance();
//        calendar.setMinimalDaysInFirstWeek(Calendar.MONDAY);
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.DAY_OF_WEEK, 1);
        calendar.roll(Calendar.DAY_OF_WEEK, -1);
        calendar.add(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    public static Calendar str2Calendar(String strDate, String formatStr) throws ParseException {

        if (StringUtils.isEmpty(formatStr)) {
            formatStr = FORMAT_DATE;
        }

        SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        Date date = sdf.parse(strDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * 判断是否是相同月份
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean sampleMonth(Calendar a, Calendar b) {
        return a.get(Calendar.YEAR) == b.get(Calendar.YEAR) && a.get(Calendar.MONTH) == b.get(Calendar.MONTH);
    }

    /**
     * 判断是否是同一天
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean sameDay(Date a, Date b) {
        String sa = changeDate2String(a, FORMAT_DATE);
        String sb = changeDate2String(b, FORMAT_DATE);
        return sa.equals(sb);
    }

    /**
     * 根据yyyy-MM格式的日期获取当前月第一天
     *
     * @param startDate
     * @return
     */
    public static String getMonthStartDate(String startDate) {
        if (startDate == null) {
            return null;
        }

        Date date = changeString2Date(startDate, FORMAT_MONTH);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    /**
     * 根据yyyy-MM格式的日期获取当前月最后一天
     *
     * @param endDate
     * @return
     */
    public static String getMonthEndDate(String endDate) {
        if (endDate == null) {
            return null;
        }

        Date date = changeString2Date(endDate, FORMAT_MONTH);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, 1);
        calendar.roll(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(calendar.getTime());
    }

    public static boolean checkStartDateAndEndDate(String startDate, String endDate) {

        if (startDate != null && !checkDate(startDate, FORMAT_DATE) || endDate != null && !checkDate(endDate, FORMAT_DATE)) {
            return false;
        }

        return true;
    }

    public static boolean checkStartMonthAndEndMonth(String startDate, String endDate) {

        if (startDate != null && !checkDate(startDate, FORMAT_MONTH) || endDate != null && !checkDate(endDate, FORMAT_MONTH)) {
            return false;
        }

        return true;
    }

    public static Boolean checkBeforeTime(Date date1, Date date2, Integer value, String type) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        switch (type) {
            case TimeUtils.DAY_TYPE:
                calendar1.add(Calendar.DATE, value);
                break;
            case TimeUtils.MONTH_TYPE:
                calendar1.add(Calendar.MONTH, value);
                break;
            case TimeUtils.YEAR_TYPE:
                calendar1.add(Calendar.YEAR, value);
                break;
            case TimeUtils.HOUR_TYPE:
                calendar1.add(Calendar.HOUR, value);
                break;
        }

        return calendar1.before(calendar2);
    }

    /**
     * 获得日期格式化字符串
     *
     * @param dateStr yyyy-MM-dd格式
     * @return yyyy-MM-dd HH:mm:ss格式
     */
    public static String getFullDateTime(String dateStr) {
        Date date = DateUtil.parse(dateStr, FORMAT_DATE);

        return DateUtil.format(date, FORMAT);
    }

    /**
     * 将yyyy-MM-dd格式的日期转换为星期几
     *
     * @param datetime 日期 yyyy-MM-dd 格式
     * @param weekType
     * @return weekDay
     */
    public static String date2Week(String datetime, int weekType) {
        if (StringUtils.isEmpty(datetime)) {
            return null;
        }

        SimpleDateFormat f = new SimpleDateFormat(FORMAT_DATE);

        Calendar calendar = Calendar.getInstance();
        Date date = null;
        try {
            date = f.parse(datetime);
            calendar.setTime(date);
        } catch (ParseException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }
        int w = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }

        if (WEEK_TYPE_XINGQI == weekType) {
            return WEEK_ARRAY_XINGQI[w];
        } else if (WEEK_TYPE_ZHOU == weekType) {
            return WEEK_ARRAY_ZHOU[w];
        }

        return null;
    }

    /**
     * 将日期转换为星期几
     *
     * @param date     日期
     * @param weekType
     * @return weekDay
     */
    public static String date2Week(Date date, int weekType) {
        if (date == null) {
            return null;
        }

        Calendar calendar = getCalendar(date);

        int w = calendar.get(Calendar.DAY_OF_WEEK) - 1;

        if (w < 0) {
            w = 0;
        }

        if (WEEK_TYPE_XINGQI == weekType) {
            return WEEK_ARRAY_XINGQI[w];
        } else if (WEEK_TYPE_ZHOU == weekType) {
            return WEEK_ARRAY_ZHOU[w];
        }

        return null;
    }

    /**
     * 获取两个日期(yyyy-MM-dd)之间的月份列表
     *
     * @param startData 开始日期
     * @param endDate   结束日期
     * @return list
     */
    public static List<String> getMonthListBetweenDays(String startData, String endDate) {
        List<String> monthList = new ArrayList<>();
        SimpleDateFormat f = new SimpleDateFormat(FORMAT_MONTH);
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        try {
            min.setTime(f.parse(startData));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

            max.setTime(f.parse(endDate));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        } catch (ParseException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        Calendar curr = min;
        while (curr.before(max)) {
            monthList.add(f.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }

        return monthList;
    }

    /**
     * 获取年份的开始时间和结束时间
     *
     * @param year 年份
     * @return String[]
     */
    public static String[] getYearStartAndEndTimeStr(Integer year) {
        String[] yearArray = new String[2];

        if (year == null || year <= 0) {
            return yearArray;
        }

        String yearFirstDateStr = getYearFirstDate(year);
        Date yearFirstDate = changeString2Date(yearFirstDateStr);
        Date yearFirstDayTime = DateUtil.beginOfDay(yearFirstDate);
        String yearStartTime = changeDate2String(yearFirstDayTime, TimeUtils.FORMAT);

        yearArray[0] = yearStartTime;

        String yearLastDateStr = getYearLastDate(year);
        Date yearLastDate = changeString2Date(yearLastDateStr);
        Date yearLastDayTime = DateUtil.endOfDay(yearLastDate);
        String yearEndTime = changeDate2String(yearLastDayTime, TimeUtils.FORMAT);

        yearArray[1] = yearEndTime;

        return yearArray;
    }

    /**
     * 获取月份的开始时间和结束时间
     *
     * @param monthDate yyyy-MM格式
     * @return String[]
     */
    public static String[] getMonthStartAndEndTimeStr(String monthDate) {
        String[] monthArray = new String[2];

        Date date = DateUtil.parse(monthDate, TimeUtils.FORMAT_MONTH);

        Date beginOfMonth = DateUtil.beginOfMonth(date);

        // 月份开始日期 yyyy-MM-dd 00:00:00
        String beginOfMonthStr = DateUtil.format(beginOfMonth, TimeUtils.FORMAT);

        monthArray[0] = beginOfMonthStr;

        Date endOfMonth = DateUtil.endOfMonth(date);

        // 月份结束日期 yyyy-MM-dd 23:59:59
        String endOfMonthStr = DateUtil.format(endOfMonth, TimeUtils.FORMAT);

        monthArray[1] = endOfMonthStr;

        return monthArray;
    }

    /**
     * 获取当前月份份的开始时间和结束时间
     *
     * @return String[]
     */
    public static String[] getThisMonthStartAndEndTimeStr() {

        Calendar calendar = getCalendar(null);

        String date = changeDate2String(calendar.getTime(), TimeUtils.FORMAT_MONTH);

        return getMonthStartAndEndTimeStr(date);
    }

    /**
     * 获取当前年份的开始时间和结束时间
     *
     * @return String[]
     */
    public static String[] getThisYearStartAndEndTimeStr() {

        Calendar calendar = getCalendar(null);
        int year = calendar.get(Calendar.YEAR);

        return getYearStartAndEndTimeStr(year);
    }

    /**
     * 获取一天的开始时间和结束时间
     *
     * @param DayDate yyyy-MM-dd格式
     * @return String[]
     */
    public static String[] getDayStartAndEndTimeStr(String DayDate) {
        String[] dayArray = new String[2];

        Date date = DateUtil.parse(DayDate, TimeUtils.FORMAT_DATE);

        Date beginOfDay = DateUtil.beginOfDay(date);

        // 开始时间 yyyy-MM-dd 00:00:00
        String beginOfDayStr = DateUtil.format(beginOfDay, TimeUtils.FORMAT);

        dayArray[0] = beginOfDayStr;

        Date endOfDay = DateUtil.endOfDay(date);

        // 结束时间 yyyy-MM-dd 23:59:59
        String endOfDayStr = DateUtil.format(endOfDay, TimeUtils.FORMAT);

        dayArray[1] = endOfDayStr;

        return dayArray;
    }

    /**
     * 获取当日的开始时间和结束时间
     *
     * @return String[]
     */
    public static String[] getThisDayStartAndEndTimeStr() {

        Calendar calendar = getCalendar(null);

        return getDayStartAndEndTimeStr(TimeUtils.changeDate2String(calendar.getTime(), FORMAT_DATE));
    }

    /**
     * 获取一周的开始时间和结束时间
     *
     * @param DayDate yyyy-MM-dd格式
     * @return String[]
     */
    public static String[] getWeekStartAndEndTimeStr(String DayDate) {
        String[] weekArray = new String[2];

        Date date = DateUtil.parse(DayDate, TimeUtils.FORMAT_DATE);

        Date beginOfWeek = DateUtil.beginOfWeek(date);

        // 开始时间 yyyy-MM-dd 00:00:00
        String beginOfWeekStr = DateUtil.format(beginOfWeek, TimeUtils.FORMAT);

        weekArray[0] = beginOfWeekStr;

        Date endOfWeek = DateUtil.endOfWeek(date);

        // 结束时间 yyyy-MM-dd 23:59:59
        String endOfWeekStr = DateUtil.format(endOfWeek, TimeUtils.FORMAT);

        weekArray[1] = endOfWeekStr;

        return weekArray;
    }

    /**
     * 获取本周的开始时间和结束时间
     *
     * @return String[]
     */
    public static String[] getThisWeekStartAndEndTimeStr() {

        Calendar calendar = getCalendar(null);

        return getWeekStartAndEndTimeStr(TimeUtils.changeDate2String(calendar.getTime(), FORMAT_DATE));
    }

    /**
     * 获得日历类
     *
     * @param time
     * @return
     */
    public static Calendar getCalendar(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone(GMT8));

        if (time != null) {
            calendar.setTime(time);
        }

        return calendar;
    }

    /**
     * 获得年份
     *
     * @param time
     * @return
     */
    public static int getYear(Date time) {
        Calendar calendar = TimeUtils.getCalendar(time);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取一天的开始时间
     *
     * @param dateStr yyyy-MM-dd 格式日期
     * @return yyyy-MM-dd HH:mm:ss 格式日期
     */
    public static String getBeginTimeOfDay(String dateStr) {

        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }

        Date date = changeString2Date(dateStr);

        Date beginOfDay = DateUtil.beginOfDay(date);

        return changeDate2String(beginOfDay, FORMAT);
    }

    /**
     * 获取一天的结束时间
     *
     * @param dateStr yyyy-MM-dd 格式日期
     * @return yyyy-MM-dd HH:mm:ss 格式日期
     */
    public static String getEndTimeOfDay(String dateStr) {

        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }

        Date date = changeString2Date(dateStr);

        Date endOfDay = DateUtil.endOfDay(date);

        return changeDate2String(endOfDay, FORMAT);
    }

    /**
     * 获得之前几个月份的list
     *
     * @param currMonth 起始月份
     * @param count     月份数量
     * @return yyyy-MM格式的日期列表
     */
    public static List<String> getBeforeMonthList(String currMonth, Integer count) {
        List<String> monthList = new ArrayList<>();

        while (count > 0) {
            monthList.add(currMonth);
            currMonth = TimeUtils.getBeforeTimeString(currMonth, 1, TimeUtils.MONTH_TYPE, TimeUtils.FORMAT_MONTH);
            count--;
        }
        Collections.reverse(monthList);
        return monthList;
    }

    /**
     * 获得之后几个月份的list
     *
     * @param currMonth 起始月份
     * @param count     月份数量
     * @return yyyy-MM格式的日期列表
     */
    public static List<String> getAfterMonthList(String currMonth, Integer count) {
        List<String> monthList = new ArrayList<>();

        while (count > 0) {
            currMonth = TimeUtils.getBeforeTimeString(currMonth, -1, TimeUtils.MONTH_TYPE, TimeUtils.FORMAT_MONTH);
            monthList.add(currMonth);
            count--;
        }
        return monthList;
    }

    /**
     * 获得之后几天的list
     *
     * @param curDay 起始日期
     * @param count  日期数量
     * @return yyyy-MM-dd格式的日期列表
     */
    public static List<String> getAfterDayList(String curDay, Integer count) {
        List<String> dayList = new ArrayList<>();

        while (count > 0) {
            curDay = TimeUtils.getBeforeTimeString(curDay, -1, TimeUtils.DAY_TYPE);
            dayList.add(curDay);
            count--;
        }

        return dayList;
    }

    public static Date convertTODate(LocalDate date) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = date.atStartOfDay().atZone(zone).toInstant();
        java.util.Date da = Date.from(instant);
        return da;
    }

    public static LocalDate convertTOLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        LocalDate localDate = localDateTime.toLocalDate();
        return localDate;
    }

    public static LocalDate convertTOLocalDate(LocalDateTime dateTime) {
        if(dateTime == null){
            return null;
        }
        LocalDate date = dateTime.toLocalDate();
        return date;
    }

    public static LocalDateTime convertTOLocalDate(LocalDate localDate) {
        LocalDateTime ldt = localDate.atStartOfDay();
        return ldt;
    }

    /**
     * 获取三个月前的时间
     *
     * @return
     */
    public static String getThreeMonthBeforeTime() {
        Date dNow = new Date(); //当前时间
        Date dBefore = new Date();
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(dNow);//把当前时间赋给日历
        calendar.add(Calendar.MONTH, -3); //设置为前3月
        dBefore = calendar.getTime(); //得到前3月的时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //设置时间格式
        String defaultStartDate = sdf.format(dBefore); //格式化前3月的时间
        return defaultStartDate;
    }

    /**
     * LocalDateTime转Date
     *
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeConvertDate(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        // 将此日期时间与时区相结合以创建 ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());
        // 本地时间线LocalDateTime到即时时间线Instant时间戳
        Instant instant = zonedDateTime.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        Date date = Date.from(instant);
        return date;
    }

    public static LocalDateTime dateConvertLocalDateTime(Date date){
        // 转为时间戳
        Instant instant = date.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return  localDateTime;
    }

    public static LocalDateTime convertToLocalDate(LocalDate localDate) {
        LocalDateTime ldt = localDate.atStartOfDay();
        return ldt;
    }

    /**
     * LocalDateTime 转 时间戳(秒级别)
     *
     * @param localDateTime
     * @return Long
     * <AUTHOR>
     * @updateTime 2020/9/8 10:17
     */
    public static Long localDateTimeToSecond(LocalDateTime localDateTime) {
        Long epochSecond = localDateTime.toEpochSecond(ZoneOffset.ofHours(8));
        return epochSecond;
    }

    /**
     * LocalDateTime 转 时间戳(毫秒级别)
     *
     * @param localDateTime
     * @return Long
     * <AUTHOR>
     * @updateTime 2020/9/8 10:17
     */
    public static Long localDateTimeToMilliseconds(LocalDateTime localDateTime) {
        long milliseconds = localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        return milliseconds;
    }

    /**
     * 时间戳(秒) 转 LocalDateTime
     *
     * @param second 时间戳(秒)
     * @return LocalDateTime
     * <AUTHOR>
     * @updateTime 2020/9/8 10:20
     */
    public static LocalDateTime secondToLocalDateTime(Long second) {
        LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(second, 0, ZoneOffset.ofHours(8));
        return localDateTime;
    }

    /**
     * 时间戳(毫秒) 转 LocalDateTime
     *
     * @param milliseconds 时间戳(毫秒)
     * @return LocalDateTime
     * <AUTHOR>
     * @updateTime 2020/9/8 10:20
     */
    public static LocalDateTime millisecondsToLocalDateTime(Long milliseconds) {
        LocalDateTime localDateTime = Instant.ofEpochMilli(milliseconds).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
        return localDateTime;
    }

    public static LocalDate parseStringToLocalDate(String localDateString){
        if (null == localDateString || localDateString.isEmpty()){
            return null;
        }
        // 把localDateString转成LocalDate
        return LocalDate.parse(localDateString, DateTimeFormatter.ofPattern(FORMAT_DATE));
    }
}
