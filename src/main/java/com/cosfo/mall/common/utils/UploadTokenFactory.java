package com.cosfo.mall.common.utils;

import com.cosfo.mall.common.constant.QiNiuConstant;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import java.util.HashMap;
import java.util.Map;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/11 10:18
 */
public class UploadTokenFactory {

    public static Map<String, String> createToken(String fileName, long expires) {
        Map<String, String> result = new HashMap();
        Auth auth = Auth.create(QiNiuConstant.ACCESS_KEY, QiNiuConstant.SECRET_KEY);
        String token = auth.uploadToken(QiNiuConstant.DEFAULT_BUCKET, fileName, expires, (StringMap)null);
        result.put("token", token);
        result.put("key", fileName);
        return result;
    }
}
