package com.cosfo.mall.common.utils.binlog.impl;

import com.cosfo.mall.common.context.binlog.BinlogEventEnum;
import com.cosfo.mall.common.mq.model.DtsModelBO;
import com.cosfo.mall.common.utils.binlog.DbTableDmlService;
import com.cosfo.mall.common.utils.binlog.DtsModelHandler;
import com.cosfo.mall.common.utils.binlog.observer.BinlogObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public abstract class DbTableDmlServiceImpl implements DbTableDmlService {

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (CollectionUtils.isEmpty(dtsModelBo.getData())) {
            return;
        }

        switch (BinlogEventEnum.valueOf(dtsModelBo.getType())) {
            case INSERT:
                DtsModelHandler.getOnlyNewData(dtsModelBo).forEach(this::onInsert);
                break;
            case UPDATE:
                DtsModelHandler.getAlignedData(dtsModelBo).forEach(pair -> onUpdate(pair.getKey(), pair.getValue()));
                break;
            default:
                break;
        }
    }

    /**
     * 使用观察者模式, 通知所有观察者.
     * 在一个变动事务中可能有多个业务逻辑需要处理, 因此使用观察者模式, 以实现不同业务之间的隔离.
     * 使用方法: 在该类的子类中,注入对应表的对应观察者接口的List, Spring会自动注入所有实现了该BinlogObserver接口的类.
     * 例子 {@see PaymentDmlServiceImpl}
     *
     * @return binlog表对应的观察者列表
     */
    protected abstract List<? extends BinlogObserver> getObservers();

    protected void onInsert(Map<String, String> newData) {
        getObservers().forEach(observer -> {
            try {
                observer.onInsert(newData);
            } catch (Exception e) {
                log.error("处理新增异常,observer:{}", observer.getClass().getSimpleName(), e);
            }
        });
    }


    protected void onUpdate(Map<String, String> newData, Map<String, String> oldData) {
        getObservers().forEach(observer -> {
            try {
                observer.onUpdate(newData, oldData);
            } catch (Exception e) {
                log.error("处理更新异常,observer:{}", observer.getClass().getSimpleName(), e);
            }
        });
    };

    // dml为delete操作时的逻辑. 暂时没人用,所以先不实现
}
