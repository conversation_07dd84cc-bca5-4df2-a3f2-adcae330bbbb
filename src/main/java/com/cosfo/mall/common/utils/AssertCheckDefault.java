package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.exception.DefaultServiceException;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-11-27
 * @Description:
 */
@Slf4j
public class AssertCheckDefault {

    public static void expectNotNull(Object object, String message) {
        log.info("expectNotNull object:{}", JSON.toJSONString(object));
        if (Objects.isNull(object)) {
            throw new DefaultServiceException(message);
        }
    }
}
