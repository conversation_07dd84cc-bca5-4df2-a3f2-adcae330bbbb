package com.cosfo.mall.common.task;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.facade.sap.SapApiFacade;
import com.cosfo.mall.facade.sap.dto.SapPushOrderDTO;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.ordercenter.client.common.OrderItemStatusEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * sap推送订单
 *
 * @author: xiaowk
 * @date: 2025/5/21 下午3:09
 */
@Component
@Slf4j
public class OrderPushSapTask extends XianMuJavaProcessorV2 {

    @Resource
    private OrderService orderService;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;

    @Resource
    private SapApiFacade sapApiFacade;

    /**
     * sap推送订单时间段，3天内的1-待确认订单
     */
    @NacosValue(value = "${sap.order.push.start.days:3}", autoRefreshed = true)
    private Long orderPushStartDays;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("sap推送订单 task start, 参数：{}", context);

        OrderQueryReq queryReq = new OrderQueryReq();
        // 1-待确认
        queryReq.setStatusList(Lists.newArrayList(OrderStatusEnum.CREATING_ORDER.getCode()));
        queryReq.setCreateEndTime(LocalDateTime.now());
        queryReq.setCreateStartTime(LocalDate.now().atStartOfDay().minusDays(orderPushStartDays));
        List<OrderResp> orderDTOList = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
        if (CollectionUtils.isEmpty(orderDTOList)) {
            log.info("sap待推送订单为空");
            return new ProcessResult(true);
        }

        List<SapPushOrderDTO.OrderPO> orderPOList = new ArrayList<>();


        List<List<OrderResp>> partitionList = Lists.partition(orderDTOList, 100);

        for (List<OrderResp> orderResps : partitionList) {

            List<Long> orderIdList = orderResps.stream().map(OrderResp::getId).collect(Collectors.toList());

            OrderItemQueryReq req = new OrderItemQueryReq();
            req.setOrderIds(orderIdList);
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(req));
            Map<Long, List<OrderItemAndSnapshotResp>> orderItemMap = orderItemAndSnapshotRespList.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getOrderId));

            for (OrderResp orderResp : orderResps) {
                Long orderId = orderResp.getId();

                try {
                    OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                    updateReq.setOrderId(orderId);
                    updateReq.setOriginStatus(OrderStatusEnum.CREATING_ORDER.getCode());
                    updateReq.setStatus(OrderStatusEnum.NO_PAYMENT.getCode());
                    Boolean flag = RpcResultUtil.handle(orderCommandProvider.updateStatus(updateReq));
                    if (!flag) {
                        continue;
                    }
                } catch (Exception e) {
                    log.error("更新订单状态异常", e);
                    continue;
                }

                List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = orderItemMap.get(orderId);
                SapPushOrderDTO.OrderPO orderPO = buildSapOrderDTO(orderResp, orderItemAndSnapshotDTOList);
                orderPOList.add(orderPO);
            }

        }

        if(!CollectionUtils.isEmpty(orderPOList)){
            SapPushOrderDTO sapPushOrderDTO = new SapPushOrderDTO();
            sapPushOrderDTO.setOrderList(orderPOList);
            sapApiFacade.pushOrder(sapPushOrderDTO);
        }

        log.info("sap推送订单 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }


    private SapPushOrderDTO.OrderPO buildSapOrderDTO(OrderResp orderResp, List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList) {
        SapPushOrderDTO.OrderPO orderPO = new SapPushOrderDTO.OrderPO();
        orderPO.setOrderNo(orderResp.getOrderNo());
        // sap客户编码
        orderPO.setSoldTo(orderResp.getSapCustomerNo());
        // sap地址编码
        orderPO.setShipTo(orderResp.getSapAddressNo());
        orderPO.setOrderDate(orderResp.getCreateTime().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        orderPO.setOrderTime(orderResp.getCreateTime().format(DateTimeFormatter.ofPattern("HHmmss")));
        orderPO.setTaxAmount(orderResp.getTaxAmount());
        orderPO.setRemark(orderResp.getRemark());
        List<SapPushOrderDTO.OrderItemPO> itemList = new ArrayList<>();
        orderPO.setItemList(itemList);

        List<OrderItemAndSnapshotResp> orderItemList = orderItemAndSnapshotDTOList.stream().filter(e -> !OrderItemStatusEnum.CANCELED.getCode().equals(e.getStatus())).collect(Collectors.toList());

        int i = 0;
        for (OrderItemAndSnapshotResp orderItemAndSnapshotResp : orderItemList) {
            SapPushOrderDTO.OrderItemPO orderItemPO = new SapPushOrderDTO.OrderItemPO();
            itemList.add(orderItemPO);

            i++;
            orderItemPO.setItemNo(10 * i); // ItemNo 从10开始，每个物料加10
            orderItemPO.setMaterial(orderItemAndSnapshotResp.getSapSkuCode());
            orderItemPO.setQuantity(orderItemAndSnapshotResp.getAmount());
            orderItemPO.setNetPrice(orderItemAndSnapshotResp.getPayablePrice());
            orderItemPO.setUnit(1);
            orderItemPO.setNetValue(orderItemAndSnapshotResp.getTotalPrice()); // NetValue = NetPrice * Quantity
        }
        
        return orderPO;
    }

}
