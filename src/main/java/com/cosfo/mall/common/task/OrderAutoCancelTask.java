package com.cosfo.mall.common.task;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.facade.sap.dto.SapPushOrderDTO;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 24小时SAP没有回告，自动取消订单
 *
 * @author: xiaowk
 * @date: 2025/5/21 下午3:09
 */
@Component
@Slf4j
public class OrderAutoCancelTask extends XianMuJavaProcessorV2 {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    /**
     * 自动取消超时时间。单位：分钟
     */
    @NacosValue(value = "${sap.order.cancel.timeout:1440}", autoRefreshed = true)
    private Long orderCancelTimeOut;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("自动取消订单 task start, 参数：{}", context);
        
        OrderQueryReq queryReq = new OrderQueryReq();
        // 2-待确认-推送中
        queryReq.setStatusList(Lists.newArrayList(OrderStatusEnum.NO_PAYMENT.getCode()));
        queryReq.setCreateEndTime(LocalDateTime.now());
        queryReq.setCreateStartTime(LocalDate.now().atStartOfDay().minusDays(10));
        List<OrderResp> orderDTOList = RpcResultUtil.handle(orderQueryProvider.queryOrderList(queryReq));
        if (CollectionUtils.isEmpty(orderDTOList)) {
            log.info("sap推送中的订单为空");
            return new ProcessResult(true);
        }

        List<SapPushOrderDTO.OrderPO> orderPOList = new ArrayList<>();

        LocalDateTime nowTime = LocalDateTime.now();
        for (OrderResp orderResp : orderDTOList) {
            LocalDateTime compareTime = Optional.ofNullable(orderResp.getUpdateTime()).orElse(orderResp.getCreateTime());
            if(nowTime.minusMinutes(orderCancelTimeOut).isAfter(compareTime)){
                Long orderId = orderResp.getId();

                try {
                    OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                    updateReq.setOrderId(orderId);
                    updateReq.setOriginStatus(OrderStatusEnum.NO_PAYMENT.getCode());
                    updateReq.setStatus(OrderStatusEnum.CANCELED.getCode());
                    RpcResultUtil.handle(orderCommandProvider.updateStatus(updateReq));
                } catch (Exception e) {
                    log.error("更新订单状态异常", e);
                }
            }
        }


        log.info("自动取消订单 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }

}
