package com.cosfo.mall.warehouse.model.vo;

import lombok.Data;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WarehouseStorageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库地址
     */
    private String address;
    /**
     * 联系人 仓库负责人
     */
    private String personContact;

    /**
     * 仓库负责人手机号
     */
    private String phone;

    /**
     * 所属租户0鲜沐
     */
    private Long tenantId;
    /**
     * 仓库来源类型
     */
    private WarehouseSourceEnum warehouseSourceEnum;
}
