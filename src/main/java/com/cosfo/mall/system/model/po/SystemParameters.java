package com.cosfo.mall.system.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * system_parameters
 * <AUTHOR>
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemParameters implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 参数key
     */
    private String paramKey;

    /**
     * 参数value
     */
    private String paramValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
