package com.cosfo.mall.system.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信三方平台信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SystemWechantTpInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 微信三方平台appId
     */
    private String tpAppId;

    /**
     * 微信三方平台appsecret
     */
    private String tpAppSecret;

    /**
     * 微信三方平台解密token
     */
    private String tpToken;

    /**
     * 微信三方平台解密key
     */
    private String tpEncodingAesKey;

    /**
     * 微信三方基础域名
     */
    private String baseUrl;

    /**
     * 第三方token获取票据
     */
    private String tpAppTicket;

    /**
     * 第三方accessToken
     */
    private String tpAccessTokenJson;

    /**
     * downloadFile合法域名
     */
    private String liteRequestDownloadDomain;

    /**
     * request合法域名
     */
    private String liteRequestRequestDomain;

    /**
     * socket合法域名
     */
    private String liteRequestSocketDomain;

    /**
     * uploadFile合法域名
     */
    private String liteRequestUploadDomain;

    /**
     * 业务域名
     */
    private String liteWebviewWebViewDomain;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


}
