package com.cosfo.mall.system.controller;

import com.cosfo.mall.common.result.ResultDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 17:54
 */
@RestController
public class HeartBeatController {

    /**
     * 检测服务是否正常
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/ok", method = RequestMethod.GET)
    public ResultDTO OK() {
        return ResultDTO.success("SUCCESS");
    }
}
