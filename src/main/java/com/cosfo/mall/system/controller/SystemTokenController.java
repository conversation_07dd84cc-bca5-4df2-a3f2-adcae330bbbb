package com.cosfo.mall.system.controller;

import com.cosfo.mall.common.filter.SystemTokenProcessor;
import com.cosfo.mall.common.result.ResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system/token")
public class SystemTokenController {

    @Autowired
    private SystemTokenProcessor systemTokenGenerator;

    @PostMapping("/exchange")
    public ResultDTO<String> exchangeToken(@RequestParam("token") String token,
      @RequestParam(required = false) Long accountId, @RequestParam(required = false) Long storeId) throws Exception {
        if (!systemTokenGenerator.isEnabled()) {
            return ResultDTO.fail("system token is disabled");
        }
        return ResultDTO.success(systemTokenGenerator.exchangeToken(token, storeId, accountId));
    }
}
