package com.cosfo.mall.system.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.constants.AppType;
import com.cosfo.mall.system.mapper.SystemParametersMapper;
import com.cosfo.mall.system.mapper.SystemWechantTpInfoMapper;
import com.cosfo.mall.system.model.dto.SystemAuthorizerDto;
import com.cosfo.mall.system.model.dto.TpAccessTokenJsonDto;
import com.cosfo.mall.system.model.po.SystemWechantTpInfo;
import com.cosfo.mall.system.service.SystemService;
import com.cosfo.mall.system.service.SystemWechantTpInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class SystemServiceImpl implements SystemService {

    @Resource
    private SystemWechantTpInfoService systemWechantTpInfoService;

    /**
     * 获取三方平台授权信息
     *
     * @return
     * @
     */
    @Override
    public SystemAuthorizerDto getAuthorizer() {
//        SystemAuthorizerDto systemAuthorizerDto = new SystemAuthorizerDto();
//        systemAuthorizerDto.setAppId(appId);
//        log.info("mall的appId:{}", appId);
//        SystemWechantTpInfo systemWechantTpInfo = systemWechantTpInfoService.getOne(new LambdaQueryWrapper<SystemWechantTpInfo>().eq(SystemWechantTpInfo::getTpAppId, appId));
//        log.info("mall的systemWechantTpInfo:{}", JSONObject.toJSONString(systemWechantTpInfo));
//        if (StringUtils.isNotBlank(systemWechantTpInfo.getTpAccessTokenJson())) {
//            TpAccessTokenJsonDto tokenJsonDto = JSON.parseObject(systemWechantTpInfo.getTpAccessTokenJson(), TpAccessTokenJsonDto.class);
//            systemAuthorizerDto.setAccessToken(tokenJsonDto.getAccessToken());
//            systemAuthorizerDto.setAccessTokenExpiretime(tokenJsonDto.getAccessTokenExpiretime());
//        }
//        systemAuthorizerDto.setAppType(AppType.WEIXIN_TP.getCode());
//        return systemAuthorizerDto;
        return null;
    }

}
