package com.cosfo.mall.system.service.Impl;

import com.cosfo.mall.system.mapper.SystemParametersMapper;
import com.cosfo.mall.system.model.po.SystemParameters;
import com.cosfo.mall.system.service.SystemParameterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 11:16
 */
@Service
public class SystemParameterServiceImpl implements SystemParameterService {

    @Resource
    private SystemParametersMapper systemParametersMapper;

    @Override
    public SystemParameters selectByKey(String key) {
        return systemParametersMapper.selectByKey(key);
    }
}
