package com.cosfo.mall.product.model.dto;

import lombok.Data;
import net.summerfarm.goods.client.resp.ProductsMappingResp;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/29
 */
@Data
public class ProductSkuDTO {
    /**
     * skuId
      */
    private Long skuId;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * spuId
     */
    private Long spuId;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * itemId
     */
    private Long itemId;
    /**
     * 商城价
     */
    private BigDecimal price;
    /**
     * 上下架
     */
    private Integer onSale;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 配送方式
     */
    private Integer deliveryType;
    /**
     * 定价方式
     */
    private Integer mappingType;
    /**
     * 价格配置数额
     */
    private BigDecimal mappingNumber;
    /**
     * 供应商skuId
     */
    private Long supplySkuId;
    /**
     * 供应商租户Id
     */
    private Long supplyTenantId;
    /**
     * 供应商报价方式
     */
    private Integer supplyPriceType;
    /**
     * 供应类型
     */
    private Integer supplyType;
    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    /**
     * 城市售卖主键Id
     */
    private Long areaItemId;
    /**
     * 一级类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目
     */
    private String firstCategoryName;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目
     */
    private String secondCategoryName;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目
     */
    private String thirdCategoryName;

    private ProductsMappingResp skuMapping;
}
