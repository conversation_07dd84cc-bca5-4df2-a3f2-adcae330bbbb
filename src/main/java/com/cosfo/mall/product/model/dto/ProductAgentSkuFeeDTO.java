package com.cosfo.mall.product.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
public class ProductAgentSkuFeeDTO {

    /**
     * @see com.cosfo.mall.common.constants.ProductAgentSkuFeeRuleTypeEnum
     */
    private Integer type;

    /**
     * 百分比加价后的金额
     */
    private BigDecimal afterPercentPrice;

    /**
     * 件数加价规则
     */
    private List<ProductAgentSkuFeeCountRuleDTO> countRuleDTOList;
}
