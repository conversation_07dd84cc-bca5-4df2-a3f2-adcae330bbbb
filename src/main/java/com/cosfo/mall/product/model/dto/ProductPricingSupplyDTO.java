package com.cosfo.mall.product.model.dto;

import com.cosfo.mall.product.model.po.ProductPricingSupply;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/18  10:39
 */
@Data
public class ProductPricingSupplyDTO extends ProductPricingSupply {
    /**
     * itemId
     */
    private Long itemId;

    /**
     * 城市报价单主键Id
     */
    private Integer productPricingSupplyCityMappingId;

    /**
     * 城市Id
     */
    private Integer cityId;

    /**
     * 报价方式 0、指定价  1,随鲜沐商城价
     */
    private Integer type;

    /**
     * 0、成本供价 1、报价单供价
     */
    private Integer supplyType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;
}
