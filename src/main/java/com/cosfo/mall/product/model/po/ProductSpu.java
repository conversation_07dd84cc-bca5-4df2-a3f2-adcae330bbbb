package com.cosfo.mall.product.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * product_spu
 * <AUTHOR>
@Data
public class ProductSpu implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 主标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;

    /**
     * 存储温度
     */
    private String storageTemperature;

    /**
     * 保质期
     */
    private Integer guaranteePeriod;

    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;

    /**
     * 产地
     */
    private String origin;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}