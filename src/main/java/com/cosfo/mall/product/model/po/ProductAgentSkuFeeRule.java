package com.cosfo.mall.product.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * product_agent_sku_fee_rule
 * <AUTHOR>
@Data
public class ProductAgentSkuFeeRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 规则类型0按比例1按件数
     */
    private Integer type;

    /**
     * 规则
     */
    private String rule;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @see com.cosfo.mall.common.constants.ProductAutomaticIncreasePriceFlagEnum
     */
    private Integer automaticIncreasePriceFlag;
}
