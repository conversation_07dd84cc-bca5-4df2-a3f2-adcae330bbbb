package com.cosfo.mall.product.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description 品牌实体
 * <AUTHOR>
 * @date 2022/5/11 10:42
 */
@Data
public class ProductBrand implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
