package com.cosfo.mall.product.model.po;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * product_sku
 * <AUTHOR>
@Data
public class ProductSku implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 鲜沐sku
     */
    private String sku;

    /**
     * 是否代仓
     */
    @TableField("agent_type")
    @Deprecated
    private Integer agentType;

    /**
     * spu id
     */
    private Long spuId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 体积
     */
    private String volume;

    /**
     * 体积单位
     */
    private Long volumeUnit;

    /**
     * 地点类型0进口1国产
     */
    private Integer placeType;

    private static final long serialVersionUID = 1L;
}