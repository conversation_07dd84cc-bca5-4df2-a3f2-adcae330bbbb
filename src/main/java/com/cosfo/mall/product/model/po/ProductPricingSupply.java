package com.cosfo.mall.product.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * product_pricing_supply
 * <AUTHOR>
@Data
public class ProductPricingSupply implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * fantai
     */
    private Long supplySkuId;

    /**
     * xm
     */
    private Long skuId;

    /**
     * 供应商tenantId
     */
    private Long supplyTenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
