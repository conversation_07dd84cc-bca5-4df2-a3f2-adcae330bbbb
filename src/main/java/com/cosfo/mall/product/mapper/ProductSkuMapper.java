package com.cosfo.mall.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.product.model.dto.ProductSkuDTO;
import com.cosfo.mall.product.model.po.ProductSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface ProductSkuMapper extends BaseMapper<ProductSku> {
    int deleteByPrimaryKey(Long id);

    int insert(ProductSku record);

    int insertSelective(ProductSku record);

    ProductSku selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSku record);

    int updateByPrimaryKey(ProductSku record);
}