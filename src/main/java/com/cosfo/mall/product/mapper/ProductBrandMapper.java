package com.cosfo.mall.product.mapper;

import com.cosfo.mall.product.model.po.ProductBrand;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/16 20:02
 */
@Mapper
public interface ProductBrandMapper {

    /**
     * 查询
     * @param id
     * @return
     */
    ProductBrand selectByPrimaryKey(Long id);

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(ProductBrand record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(ProductBrand record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductBrand record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductBrand record);

    /**
     * 根据名称查询
     * @param brandName
     * @return
     */
    ProductBrand selectByName(String brandName);
}
