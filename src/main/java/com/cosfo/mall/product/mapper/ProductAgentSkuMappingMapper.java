package com.cosfo.mall.product.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.model.po.ProductAgentSkuMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface ProductAgentSkuMappingMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAgentSkuMapping record);

    int insertSelective(ProductAgentSkuMapping record);

    ProductAgentSkuMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAgentSkuMapping record);

    int updateByPrimaryKey(ProductAgentSkuMapping record);

    /**
     * 查询代仓sku信息
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuDTO> queryAgentSkuInfo(@Param("skuIds") List<Long> skuIds,
                                               @Param("tenantId") Long tenantId);

    /**
     * 根据代仓skuId查询代仓sku信息
     *
     * @param agentSkuIds
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    List<ProductAgentSkuDTO> queryAgentSkuInfoByAgentSkuIds(@Param("agentSkuIds") List<Long> agentSkuIds,
            @Param("agentTenantId") Long agentTenantId,
            @Param("tenantId") Long tenantId);

    /**
     * 查询
     *
     * @param skuId
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "on")
    ProductAgentSkuDTO selectBySkuIdAndTenantId(@Param("skuId") Long skuId,
                                                @Param("tenantId") Long tenantId);


    /**
     * 根据代仓和代仓供应商租户Id查询商品信息
     * @param agentTenantId
     * @param agentSkuId
     * @param tenantId
     * @return
     */
    ProductAgentSkuMapping selectByAgentSkuIdAndAgentTenantIdAndTenantId(@Param("agentSkuId") Long agentSkuId, @Param("agentTenantId") Long agentTenantId, @Param("tenantId") Long tenantId);

    /**
     * 根据tenantId和skucode查询商品信息
     * @param tenantId
     * @param agentSkuCodes
     * @return
     */
    List<ProductAgentSkuMapping> listBySkuCodes(@Param("tenantId") Long tenantId, @Param("agentSkuCodes") List<String> agentSkuCodes);

    /**
     * 根据tenantId和skucode查询代仓商品信息
     * @param tenantId
     * @param agentSkuCodes
     * @return
     */
    List<ProductAgentSkuMapping> selfAgentListBySkuCodes(@Param("tenantId") Long tenantId, @Param("agentSkuCodes") List<String> agentSkuCodes);
}