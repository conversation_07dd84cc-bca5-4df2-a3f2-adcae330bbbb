package com.cosfo.mall.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.product.model.po.ProductSpu;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ProductSpuMapper extends BaseMapper<ProductSpu> {
    int deleteByPrimaryKey(Long id);

    int insert(ProductSpu record);

    int insertSelective(ProductSpu record);

    ProductSpu selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSpu record);

    int updateByPrimaryKey(ProductSpu record);
}