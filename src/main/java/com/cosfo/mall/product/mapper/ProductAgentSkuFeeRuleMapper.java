package com.cosfo.mall.product.mapper;

import com.cosfo.mall.product.model.po.ProductAgentSkuFeeRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 描述:
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Mapper
public interface ProductAgentSkuFeeRuleMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(ProductAgentSkuFeeRule record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(ProductAgentSkuFeeRule record);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    ProductAgentSkuFeeRule selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductAgentSkuFeeRule record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductAgentSkuFeeRule record);

    /**
     * 根据品牌方查询代仓商品收费规则
     *
     * @param tenantId
     * @return
     */
    ProductAgentSkuFeeRule selectByTenantId(@Param("tenantId") Long tenantId);
}