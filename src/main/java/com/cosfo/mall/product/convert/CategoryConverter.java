//package com.cosfo.mall.product.convert;
//
//import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
//import com.cosfo.erp.client.category.resp.CategoryLevelResultResp;
//import com.cosfo.mall.common.utils.TimeUtils;
//import com.cosfo.mall.facade.dto.ProductCategoryDTO;
//import com.cosfo.mall.product.model.po.Category;
//
///**
// * <AUTHOR>
// * @date : 2023/2/2 10:16
// */
//public class CategoryConverter {
//
//    public static Category toCategory(CategoryDetailResultResp cateGoryResultResp){
//
//        if (cateGoryResultResp == null) {
//            return null;
//        }
//        Category category = new Category();
//        category.setId(cateGoryResultResp.getId());
//        category.setName(cateGoryResultResp.getName());
//        category.setParentId(cateGoryResultResp.getParentId());
//        category.setCreateTime(TimeUtils.localDateTimeConvertDate(cateGoryResultResp.getCreateTime()));
//        category.setUpdateTime(TimeUtils.localDateTimeConvertDate(cateGoryResultResp.getUpdateTime()));
//        return category;
//    }
//    public static CategoryQueryReq toCateGoryQueryReq(Category category){
//
//        if (category == null) {
//            return null;
//        }
//        CategoryQueryReq cateGoryQueryReq = new CategoryQueryReq();
//        cateGoryQueryReq.setId(category.getId());
//        cateGoryQueryReq.setName(category.getName());
//        cateGoryQueryReq.setParentId(category.getParentId());
//// Not mapped TO fields:
//// createTime
//// updateTime
//// Not mapped FROM fields:
//// createTime
//// updateTime
//        cateGoryQueryReq.setCreateTime(TimeUtils.dateConvertLocalDateTime(category.getCreateTime()));
//        cateGoryQueryReq.setUpdateTime(TimeUtils.dateConvertLocalDateTime(category.getUpdateTime()));
//        return cateGoryQueryReq;
//    }
//
//    /**
//     * 转化为ProductCategoryDTO
//     *
//     * @param categoryLevelResultResp
//     * @return
//     */
//    public static ProductCategoryDTO convertToProductCategoryDTO(CategoryLevelResultResp categoryLevelResultResp){
//        if (categoryLevelResultResp == null) {
//            return null;
//        }
//
//        ProductCategoryDTO productCategoryDTO = new ProductCategoryDTO();
//        productCategoryDTO.setFirstCategoryId(categoryLevelResultResp.getFirstCategoryId());
//        productCategoryDTO.setFirstCategoryName(categoryLevelResultResp.getFirstCategoryName());
//        productCategoryDTO.setSecondCategoryId(categoryLevelResultResp.getSecondCategoryId());
//        productCategoryDTO.setSecondCategoryName(categoryLevelResultResp.getSecondCategoryName());
//        productCategoryDTO.setThirdCategoryId(categoryLevelResultResp.getThirdCategoryId());
//        productCategoryDTO.setThirdCategoryName(categoryLevelResultResp.getThirdCategoryName());
//        productCategoryDTO.setCategoryStr(categoryLevelResultResp.getCategoryStr());
//        return productCategoryDTO;
//    }
//}
