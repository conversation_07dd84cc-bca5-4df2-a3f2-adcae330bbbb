package com.cosfo.mall.product.convert;

import com.cosfo.mall.product.model.dto.ProductPricingSupplyDTO;
import com.cosfo.mall.product.model.po.ProductPricingSupply;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
/**
 * @author: monna.chen
 * @Date: 2023/11/13 16:46
 * @Description:
 */
@Mapper
public interface ProductPricingConvert {
    ProductPricingConvert INSTANCE =  Mappers.getMapper(ProductPricingConvert.class);

    List<ProductPricingSupplyDTO> convert2Dtos(List<ProductPricingSupply> pricingSupplyList);

    ProductPricingSupplyDTO convert2Dto(ProductPricingSupply pricingSupply);

    ProductPricingSupplyDTO convertCopy(ProductPricingSupplyDTO dto);
}