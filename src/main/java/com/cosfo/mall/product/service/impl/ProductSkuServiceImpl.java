package com.cosfo.mall.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.mall.facade.ProductQueryFacade;
import com.cosfo.mall.facade.converter.ProductFacadeConvert;
import com.cosfo.mall.product.model.dto.ProductSkuDTO;
import com.cosfo.mall.product.service.ProductSkuService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/15
 */
@Slf4j
@Service
public class ProductSkuServiceImpl implements ProductSkuService {

    @Resource
    private ProductQueryFacade productQueryFacade;


    @Override
    public ProductSkuDTO queryById(Long skuId) {
        if (ObjectUtil.isEmpty(skuId)) {
            return null;
        }
        List<ProductSkuDetailResp> skuDetailResps = productQueryFacade.querySkuInfo(Collections.singletonList(skuId));
        if (CollectionUtil.isNotEmpty(skuDetailResps)) {
            return ProductFacadeConvert.INSTANCE.convert2Sku(skuDetailResps.get(0));
        }
        return null;
    }

}
