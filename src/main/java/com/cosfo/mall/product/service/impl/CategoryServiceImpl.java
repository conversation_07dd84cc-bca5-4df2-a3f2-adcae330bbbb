//package com.cosfo.mall.product.service.impl;
//
//import com.cosfo.mall.common.utils.StringUtils;
//import com.cosfo.mall.facade.CategoryServiceFacade;
//import com.cosfo.mall.facade.SummerFarmInterfaceServiceFacade;
//import com.cosfo.mall.product.mapper.CategoryMapper;
//import com.cosfo.mall.product.model.po.Category;
//import com.cosfo.mall.product.service.CategoryService;
//import net.summerfarm.manage.client.saas.resp.SummerFarmStockResp;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Set;
//
//@Service
//public class CategoryServiceImpl implements CategoryService {
//    @Resource
//    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
//    @Autowired
//    private CategoryServiceFacade categoryServiceFacade;
//
//    @Override
//    public void syncFromSummerForm(Set<Long> ids) {
//
//        List<Category> resps = summerFarmInterfaceServiceFacade.queryCategory(ids);
//        // 类目信息
//        if (!CollectionUtils.isEmpty(resps)) {
//            resps.stream().forEach(e->{
//                Category categoryDb = categoryServiceFacade.selectByPrimaryKey(e.getId());
//                if (categoryDb == null) {
//                    categoryServiceFacade.add(e);
//                } else {
//                    // 更新类目
//                    categoryServiceFacade.edit(e);
//                }
//            });
//        }
//    }
//}
