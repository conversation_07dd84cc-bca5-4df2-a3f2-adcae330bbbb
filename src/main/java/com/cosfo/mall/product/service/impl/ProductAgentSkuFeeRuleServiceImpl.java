package com.cosfo.mall.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProductAgentSkuFeeRuleTypeEnum;
import com.cosfo.mall.common.constants.ProductAutomaticIncreasePriceFlagEnum;
import com.cosfo.mall.common.constants.ProfitSharingMerberCodeEnum;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.order.service.OrderAgentSkuFeeRuleService;
import com.cosfo.mall.product.mapper.ProductAgentSkuFeeRuleMapper;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;
import com.cosfo.mall.product.model.po.ProductAgentSkuFeeRule;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Service
public class ProductAgentSkuFeeRuleServiceImpl implements ProductAgentSkuFeeRuleService {

    @Resource
    private ProductAgentSkuFeeRuleMapper productAgentSkuFeeRuleMapper;
    @Resource
    private OrderAgentSkuFeeRuleService orderAgentSkuFeeRuleService;

    @Override
    public ProductAgentSkuFeeRuleDTO queryByTenantId(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        ProductAgentSkuFeeRule productAgentSkuFeeRule = productAgentSkuFeeRuleMapper.selectByTenantId(tenantId);
        ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDTO = new ProductAgentSkuFeeRuleDTO();
        if (Objects.isNull(productAgentSkuFeeRule)) {
            return null;
        }
        BeanUtils.copyProperties(productAgentSkuFeeRule, productAgentSkuFeeRuleDTO);
        return productAgentSkuFeeRuleDTO;
    }

    @Override
    public ProductAgentSkuFeeDTO buildAgentSkuFeeRuleList(Long tenantId, BigDecimal basePrice) {
        ProductAgentSkuFeeDTO data = new ProductAgentSkuFeeDTO();
        // 查询当前租户的加价规则
        ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDTO = queryByTenantId(tenantId);
        // 如果没有配置 或者 配置关闭 就不处理
        if (Objects.isNull(productAgentSkuFeeRuleDTO)
                || Objects.equals(productAgentSkuFeeRuleDTO.getAutomaticIncreasePriceFlag(), ProductAutomaticIncreasePriceFlagEnum.CLOSE.getFlag())) {
            return null;
        }

        List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS = JSON.parseArray(productAgentSkuFeeRuleDTO.getRule(), ProductAgentSkuFeeRuleDetailDTO.class);
        if (CollectionUtil.isEmpty(productAgentSkuFeeRuleDetailDTOS)) {
            return null;
        }
        data.setType(productAgentSkuFeeRuleDTO.getType());

        // 按照比例
        if (Objects.equals(productAgentSkuFeeRuleDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
            ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO = productAgentSkuFeeRuleDetailDTOS.stream().filter(el -> Objects.equals(el.getMemberCode(), ProfitSharingMerberCodeEnum.BRAND.getCode())).findFirst().get();
            BigDecimal percentage = productAgentSkuFeeRuleDetailDTO.getPercentage();
            if (Objects.isNull(percentage) || percentage.compareTo(BigDecimal.ZERO) == 0) {
                throw new DefaultServiceException("价格查询有误，请联系管理员");
            }
            BigDecimal percentageRate = percentage.divide(NumberConstant.DECIMAL_HUNDRED);
            BigDecimal price = basePrice.divide(percentageRate, 2, ROUND_HALF_UP);
            data.setAfterPercentPrice(price);
            return data;
        }

        // 按照加价规则
        List<ProductAgentSkuFeeCountRuleDTO> ruleList = productAgentSkuFeeRuleDetailDTOS.stream().sorted(Comparator.comparing(ProductAgentSkuFeeRuleDetailDTO::getCount)).map(el -> {
            ProductAgentSkuFeeCountRuleDTO ruleDTO = new ProductAgentSkuFeeCountRuleDTO();
            ruleDTO.setAmount(el.getAmount());
            ruleDTO.setCount(el.getCount());
            ruleDTO.setPrice(basePrice.add(el.getAmount()));
            return ruleDTO;
        }).collect(Collectors.toList());
        data.setCountRuleDTOList(ruleList);
        return data;
    }

    @Override
    public ProductAgentSkuFeeCountRuleDTO buildNextStepSkuFeeRule(Long tenantId, BigDecimal basePrice, Integer itemCount) {
        ProductAgentSkuFeeDTO productAgentSkuFeeDTO = buildAgentSkuFeeRuleList(tenantId, basePrice);
        if (Objects.isNull(productAgentSkuFeeDTO) || !Objects.equals(productAgentSkuFeeDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode())) {
            return null;
        }
        List<ProductAgentSkuFeeCountRuleDTO> countRuleDTOList = productAgentSkuFeeDTO.getCountRuleDTOList();
        Optional<ProductAgentSkuFeeCountRuleDTO> optionalRule = countRuleDTOList.stream().filter(el -> el.getCount() > itemCount).findFirst();
        if (Objects.isNull(optionalRule) || !optionalRule.isPresent()) {
            return null;
        }
        return optionalRule.get();
    }

    @Override
    public BigDecimal calculateAgentAmountByTenant(Long tenantId, BigDecimal price, Integer amount) {
        ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDTO = queryByTenantId(tenantId);
        if (Objects.isNull(productAgentSkuFeeRuleDTO)) {
            return BigDecimal.ZERO;
        }
        List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS = JSON.parseArray(productAgentSkuFeeRuleDTO.getRule(), ProductAgentSkuFeeRuleDetailDTO.class);
        if (CollectionUtil.isEmpty(productAgentSkuFeeRuleDetailDTOS)) {
            return BigDecimal.ZERO;
        }
        if (Objects.equals(productAgentSkuFeeRuleDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
            ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO = productAgentSkuFeeRuleDetailDTOS.stream().filter(el -> Objects.equals(el.getMemberCode(), ProfitSharingMerberCodeEnum.SUPPLIER.getCode())).findFirst().get();
            BigDecimal percentage = productAgentSkuFeeRuleDetailDTO.getPercentage();
            BigDecimal agentFee = NumberUtil.mul(price, NumberUtil.div(percentage, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            return agentFee;
        }
        if (Objects.equals(productAgentSkuFeeRuleDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode())) {
            // 临界点
            ProductAgentSkuFeeRuleDetailDTO recentProductAgentSkuFeeRule = null;
            Integer maxCount = NumberConstant.ZERO;
            for (ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO : productAgentSkuFeeRuleDetailDTOS) {
                if (productAgentSkuFeeRuleDetailDTO.getCount().compareTo(amount) <= NumberConstant.ZERO && productAgentSkuFeeRuleDetailDTO.getCount().compareTo(maxCount) > NumberConstant.ZERO) {
                    recentProductAgentSkuFeeRule = productAgentSkuFeeRuleDetailDTO;
                    maxCount = productAgentSkuFeeRuleDetailDTO.getCount();
                }
            }
            if (Objects.isNull(recentProductAgentSkuFeeRule)) {
                return BigDecimal.ZERO;
            }
            return recentProductAgentSkuFeeRule.getAmount();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public ProductAgentSkuFeeRuleDetailDTO getHitAgentRule(Long tenantId, Integer amount) {

        ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDTO = queryByTenantId(tenantId);
        if (Objects.isNull(productAgentSkuFeeRuleDTO)) {
            return null;
        }
        List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS = JSON.parseArray(productAgentSkuFeeRuleDTO.getRule(), ProductAgentSkuFeeRuleDetailDTO.class);
        if (CollectionUtil.isEmpty(productAgentSkuFeeRuleDetailDTOS)) {
            return null;
        }
        if (Objects.equals(productAgentSkuFeeRuleDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode())) {
            ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO = productAgentSkuFeeRuleDetailDTOS.stream().filter(el -> Objects.equals(el.getMemberCode(), ProfitSharingMerberCodeEnum.SUPPLIER.getCode())).findFirst().get();
            return productAgentSkuFeeRuleDetailDTO;
        }
        if (Objects.equals(productAgentSkuFeeRuleDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode())) {
            // 临界点
            ProductAgentSkuFeeRuleDetailDTO recentProductAgentSkuFeeRule = null;
            Integer maxCount = NumberConstant.ZERO;
            for (ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO : productAgentSkuFeeRuleDetailDTOS) {
                if (productAgentSkuFeeRuleDetailDTO.getCount().compareTo(amount) <= NumberConstant.ZERO && productAgentSkuFeeRuleDetailDTO.getCount().compareTo(maxCount) > NumberConstant.ZERO) {
                    recentProductAgentSkuFeeRule = productAgentSkuFeeRuleDetailDTO;
                    maxCount = productAgentSkuFeeRuleDetailDTO.getCount();
                }
            }
            return recentProductAgentSkuFeeRule;
        }
        return null;
    }
}
