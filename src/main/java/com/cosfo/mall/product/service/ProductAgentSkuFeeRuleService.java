package com.cosfo.mall.product.service;

import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
public interface ProductAgentSkuFeeRuleService {

    /**
     * 查询品牌方代仓品收费规则
     *
     * @param tenantId
     * @return
     */
    ProductAgentSkuFeeRuleDTO queryByTenantId(Long tenantId);

    /**
     * 计算加价后的金额
     * @param tenantId 租户
     * @param basePrice 商品基础价
     * @return
     */
    ProductAgentSkuFeeDTO buildAgentSkuFeeRuleList(Long tenantId, BigDecimal basePrice);

    /**
     * 根据商品数量查询下一个规则配置
     * @param tenantId
     * @param basePrice
     * @param itemCount
     * @return
     */
    ProductAgentSkuFeeCountRuleDTO buildNextStepSkuFeeRule(Long tenantId, BigDecimal basePrice, Integer itemCount);

    /**
     * 计算代仓费用
     * @param tenantId
     * @param price
     * @param amount
     * @return
     */
    BigDecimal calculateAgentAmountByTenant(Long tenantId, BigDecimal price, Integer amount);


    /**
     * 获取命中规则
     * @param tenantId
     * @param amount
     * @return
     */
    ProductAgentSkuFeeRuleDetailDTO getHitAgentRule(Long tenantId, Integer amount);


}
