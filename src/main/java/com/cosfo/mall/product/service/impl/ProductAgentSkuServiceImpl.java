package com.cosfo.mall.product.service.impl;

import com.cosfo.mall.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.mall.product.model.dto.ProductAgentSkuDTO;
import com.cosfo.mall.product.service.ProductAgentSkuService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/19
 */
@Service
public class ProductAgentSkuServiceImpl implements ProductAgentSkuService {

    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;

    @Override
    public List<ProductAgentSkuDTO> queryAgentSkuInfo(List<Long> skuIds, Long tenantId) {
        if(CollectionUtils.isEmpty(skuIds) || tenantId == null){
            return Collections.emptyList();
        }
        List<ProductAgentSkuDTO> productAgentSkuDTOS = productAgentSkuMappingMapper.queryAgentSkuInfo(skuIds, tenantId);
        return productAgentSkuDTOS;
    }

}
