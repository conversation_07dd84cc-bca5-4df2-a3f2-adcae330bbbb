package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.tenant.dao.TenantPrepaymentTransactionItemDao;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;
import com.cosfo.mall.tenant.service.TenantPrepaymentTransactionItemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @desc 预付交易明细服务层
 * <AUTHOR>
 * @date 2023/3/22 17:03
 */
@Service
public class TenantPrepaymentTransactionItemServiceImpl implements TenantPrepaymentTransactionItemService {

    @Resource
    private TenantPrepaymentTransactionItemDao tenantPrepaymentTransactionItemDao;

    @Override
    public void batchInsertTransactionItem(List<TenantPrepaymentTransactionItem> tenantPrepaymentTransactionItems) {
        tenantPrepaymentTransactionItemDao.saveBatch(tenantPrepaymentTransactionItems);
    }

    @Override
    public List<TenantPrepaymentTransactionItem> queryByOrderId(Long tenantId, Long orderId) {
        return tenantPrepaymentTransactionItemDao.queryByOrderId(tenantId, orderId);
    }

    @Override
    public List<TenantPrepaymentTransactionItem> queryByOrderItemId(Long tenantId, Long orderItemId, Integer transactionType) {
        return tenantPrepaymentTransactionItemDao.queryByOrderItemId(tenantId, orderItemId, transactionType);
    }

    @Override
    public void batchUpdateTransactionItem(List<TenantPrepaymentTransactionItem> list) {
        tenantPrepaymentTransactionItemDao.updateBatchById(list);
    }

    @Override
    public List<TenantPrepaymentTransactionItem> queryByOrderIds(Long tenantId, List<Long> orderIds) {
        if (Objects.isNull(orderIds) || orderIds.isEmpty()) {
            return Collections.emptyList();
        }
        return tenantPrepaymentTransactionItemDao.queryByOrderIds(tenantId, orderIds);
    }
}
