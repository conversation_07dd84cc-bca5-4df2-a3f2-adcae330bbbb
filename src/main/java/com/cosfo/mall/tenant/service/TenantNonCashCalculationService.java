package com.cosfo.mall.tenant.service;

import com.cosfo.mall.order.model.bo.OrderNonCashCalculationParamBO;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationResultBO;
import com.cosfo.manage.client.order.resp.OrderResp;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-04-22
 **/
public interface TenantNonCashCalculationService {

    /**
     * 计算非现金账户可用和不可用金额
     *
     * @param paramBO
     * @return
     */
    OrderNonCashCalculationResultBO calculateNonCashAmount(OrderNonCashCalculationParamBO paramBO);

    /**
     * 根据订单计算非现金账户可用和不可用金额
     *
     * @param orderNos
     * @return
     */
    OrderNonCashCalculationResultBO calculateNonCashAmount(List<String> orderNos);

}
