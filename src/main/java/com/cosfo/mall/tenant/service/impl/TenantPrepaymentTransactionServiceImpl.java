package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.tenant.dao.TenantPrepaymentTransactionDao;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.mall.tenant.service.TenantPrepaymentTransactionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @desc 预付交易服务层
 * <AUTHOR>
 * @date 2023/3/20 10:47
 */
@Service
@Slf4j
public class TenantPrepaymentTransactionServiceImpl implements TenantPrepaymentTransactionService {

    @Resource
    private TenantPrepaymentTransactionDao tenantPrepaymentTransactionDao;

    @Override
    public int batchInsertTransaction(List<TenantPrepaymentTransaction> transactionList) {
        boolean result = tenantPrepaymentTransactionDao.saveBatch(transactionList);
        return result ? 1 : 0;
    }

    @Override
    public List<TenantPrepaymentTransaction> queryByAssociatedOrderNos(Long tenantId, List<String> associatedOrderNos) {
        if (CollectionUtils.isEmpty(associatedOrderNos)) {
            return Lists.newArrayList();
        }
        return tenantPrepaymentTransactionDao.queryByAssociatedOrderNos(tenantId, associatedOrderNos);
    }
}
