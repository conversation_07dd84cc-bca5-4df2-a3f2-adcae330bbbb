package com.cosfo.mall.tenant.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.context.DecreaseTypeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceEnums;
import com.cosfo.mall.common.context.prepay.TenantPrepayPayableTargetEnum;
import com.cosfo.mall.common.context.prepay.TenantPrepayTransactionEnum;
import com.cosfo.mall.common.exception.PayBizException;
import com.cosfo.mall.order.model.bo.OrderBalanceBO;
import com.cosfo.mall.order.model.bo.OrderItemNeedPrepayBO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.order.service.OrderAgentSkuFeeRuleService;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.mall.supplier.SupplierDeliveryInfoService;
import com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo;
import com.cosfo.mall.tenant.model.bo.TenantPrepaymentCalculateBO;
import com.cosfo.mall.tenant.model.bo.TenantPrepaymentTransactionBO;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;
import com.cosfo.mall.tenant.service.TenantPrepaymentAccountService;
import com.cosfo.mall.tenant.service.TenantPrepaymentService;
import com.cosfo.mall.tenant.service.TenantPrepaymentTransactionItemService;
import com.cosfo.mall.tenant.service.TenantPrepaymentTransactionService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @desc 预付服务层
 * <AUTHOR>
 * @date 2023/3/20 20:32
 */
@Service
@Slf4j
public class TenantPrepaymentServiceImpl implements TenantPrepaymentService {

    @Resource
    private TenantPrepaymentAccountService tenantPrepaymentAccountService;
    @Resource
    private SupplierDeliveryInfoService supplierDeliveryInfoService;
    @Resource
    private TenantPrepaymentTransactionService tenantPrepaymentTransactionService;
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private OrderAgentSkuFeeRuleService orderAgentSkuFeeRuleService;
    @Resource
    private TenantPrepaymentTransactionItemService tenantPrepaymentTransactionItemService;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;


    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealThreePartiesOrderPrepayment(OrderBalanceBO threePartiesOrderPayBO) {
        Order order = threePartiesOrderPayBO.getOrder();
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(order.getId()));

        // 计算每个订单项的预付应付
        List<OrderItemNeedPrepayBO> needPrepaySupplyList = calculateSupplyFee(orderItemAndSnapshotRespList);
        List<OrderItemNeedPrepayBO> needPrepayAgentList = calculateAgentFee(orderItemAndSnapshotRespList);

        // 计算对应的供应运费费用
        BigDecimal needPrepaySupplyDeliveryFee = calculateSupplyDeliveryFee(order.getOrderNo());

        // 如果无需预付则流程结束
        Boolean supplyPrepaymentFlag = !CollectionUtils.isEmpty(needPrepaySupplyList) && needPrepaySupplyList.stream().map(OrderItemNeedPrepayBO::getNeedPrepayAmount).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) > 0;
        Boolean agentWarehousePrepaymentFlag = !CollectionUtils.isEmpty(needPrepayAgentList) && needPrepayAgentList.stream().map(OrderItemNeedPrepayBO::getNeedPrepayAmount).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) > 0;
        Boolean supplyDeliveryFeePrepaymentFlag = needPrepaySupplyDeliveryFee.compareTo(BigDecimal.ZERO) > 0;
        if (!supplyPrepaymentFlag && !agentWarehousePrepaymentFlag && !supplyDeliveryFeePrepaymentFlag) {
            log.info("订单编号：[{}] 本次无需预付，流程结束", order.getOrderNo());
            return;
        }

        // 查询给供应商的预付 加排它锁
        List<TenantPrepaymentAccount> tenantPrepaymentAccounts = tenantPrepaymentAccountService.queryTenant4SupplierPrepay(order.getTenantId(), order.getSupplierTenantId());
//        String orderPriceString = threePartiesOrderPayBO.getNeedPayPrice().setScale(2, RoundingMode.DOWN).toPlainString();
        if (CollectionUtils.isEmpty(tenantPrepaymentAccounts)) {
            log.info("未查询到品牌方：{}给供应商：{}的预付信息", order.getTenantId(), order.getSupplierTenantId());
            throw new PayBizException("当前订单存在部分商品，需要现结支付", ErrorCodeEnum.MERCHANT_STORE_BALANCE_INSUFFICIENT, Arrays.asList(order.getOrderNo()));
        }


        // 每种类型的预付账户
        Map<Integer, TenantPrepaymentAccount> tenantPrepaymentAccountMap = tenantPrepaymentAccounts.stream().collect(Collectors.toMap(TenantPrepaymentAccount::getPayableTarget, item -> item));
        TenantPrepaymentAccount supplyPayableAccount = tenantPrepaymentAccountMap.getOrDefault((TenantPrepayPayableTargetEnum.DIRECT_SUPPLY.getPayableTarget()), new TenantPrepaymentAccount());
        TenantPrepaymentAccount agentPayableAccount = tenantPrepaymentAccountMap.getOrDefault((TenantPrepayPayableTargetEnum.AGENT_WAREHOUSE_EXPENSE.getPayableTarget()), new TenantPrepaymentAccount());
        TenantPrepaymentAccount supplyAndAgentPayableAccount = tenantPrepaymentAccountMap.getOrDefault((TenantPrepayPayableTargetEnum.DIRECT_SUPPLY_AND_AGENT.getPayableTarget()), new TenantPrepaymentAccount());
        BigDecimal supplyPayableAmount = Optional.ofNullable(supplyPayableAccount.getAvailableAmount()).orElse(BigDecimal.ZERO);
        BigDecimal agentPayableAmount = Optional.ofNullable(agentPayableAccount.getAvailableAmount()).orElse(BigDecimal.ZERO);
        BigDecimal supplyAndAgentPayableAmount = Optional.ofNullable(supplyAndAgentPayableAccount.getAvailableAmount()).orElse(BigDecimal.ZERO);

        TenantPrepaymentTransactionBO prepaymentTransactionBO = TenantPrepaymentTransactionBO.builder()
                .tenantId(order.getTenantId())
                .orderId(order.getId())
                .orderNo(order.getOrderNo())
                .supplyPayableAmount(supplyPayableAmount)
                .agentPayableAmount(agentPayableAmount)
                .supplyAndAgentPayableAmount(supplyAndAgentPayableAmount)
                .supplyPayableAccount(supplyPayableAccount)
                .agentPayableAccount(agentPayableAccount)
                .supplyAndAgentPayableAccount(supplyAndAgentPayableAccount)
                .needPrepaySupplyList(needPrepaySupplyList)
                .needPrepayAgentList(needPrepayAgentList)
                .needPrepaySupplyDeliveryFee(needPrepaySupplyDeliveryFee)
                .tenantPrepaymentTransactionItems(new ArrayList<>())
                .build();
        // 校验并扣减对应的预付
        calculateNeedDecreasePrepayment(prepaymentTransactionBO);

        // 订单维度扣减的预付金额
        BigDecimal decreaseSupplyPayableAmount = NumberUtil.sub(supplyPayableAmount, prepaymentTransactionBO.getSupplyPayableAmount());
        BigDecimal decreaseAgentPayableAmount = NumberUtil.sub(agentPayableAmount, prepaymentTransactionBO.getAgentPayableAmount());
        BigDecimal decreaseSupplyAndAgentPayableAmount = NumberUtil.sub(supplyAndAgentPayableAmount, prepaymentTransactionBO.getSupplyAndAgentPayableAmount());
        Map<Integer, BigDecimal> accountDecreaseMap = new HashMap<>(NumberConstant.THREE);
        accountDecreaseMap.put(TenantPrepayPayableTargetEnum.DIRECT_SUPPLY.getPayableTarget(), decreaseSupplyPayableAmount);
        accountDecreaseMap.put(TenantPrepayPayableTargetEnum.AGENT_WAREHOUSE_EXPENSE.getPayableTarget(), decreaseAgentPayableAmount);
        accountDecreaseMap.put(TenantPrepayPayableTargetEnum.DIRECT_SUPPLY_AND_AGENT.getPayableTarget(), decreaseSupplyAndAgentPayableAmount);

        Integer decreaseType = threePartiesOrderPayBO.getDecreaseType();
        if (Objects.equals(decreaseType, DecreaseTypeEnum.FREEZE.getType())) {
            // 如果是冻结，直接冻结金额，不生成流水
            for (TenantPrepaymentAccount tenantPrepaymentAccount : tenantPrepaymentAccounts) {
                BigDecimal decreaseAmount = accountDecreaseMap.get(tenantPrepaymentAccount.getPayableTarget());
                if (decreaseAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                int result = tenantPrepaymentAccountService.freezeAvailableAmount(tenantPrepaymentAccount.getId(), decreaseAmount);
                if (result != 1) {
                    throw new PayBizException("当前订单存在部分商品，需要现结支付", ErrorCodeEnum.MERCHANT_STORE_BALANCE_INSUFFICIENT, Collections.singletonList(order.getOrderNo()));
                }
                log.info("订单[{}]冻结预付成功, 冻结金额[{}]", order.getOrderNo(), decreaseAmount);
            }
        }
        if (Objects.equals(decreaseType, DecreaseTypeEnum.DECREASE.getType())) {
            for (TenantPrepaymentAccount tenantPrepaymentAccount : tenantPrepaymentAccounts) {
                BigDecimal decreaseAmount = accountDecreaseMap.get(tenantPrepaymentAccount.getPayableTarget());
                if (decreaseAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                Long id = tenantPrepaymentAccount.getId();
                BigDecimal availableAmount = tenantPrepaymentAccount.getAvailableAmount();
                if (availableAmount.compareTo(decreaseAmount) >= 0) {
                    int result = tenantPrepaymentAccountService.decreaseAvailableAmount(id, decreaseAmount);
                    if (result != 1) {
                        throw new PayBizException("当前订单存在部分商品，需要现结支付", ErrorCodeEnum.MERCHANT_STORE_BALANCE_INSUFFICIENT, Collections.singletonList(order.getOrderNo()));
                    }
                    log.info("订单[{}]预付成功, 扣减金额[{}]", order.getOrderNo(), decreaseAmount);
                }
            }
            // 预付交易记录
            BigDecimal supplyNeedPayAmount = needPrepaySupplyList.stream().map(OrderItemNeedPrepayBO::getNeedPrepayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal agentNeedPayAmount = needPrepayAgentList.stream().map(OrderItemNeedPrepayBO::getNeedPrepayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<TenantPrepaymentTransaction> transactionList = buildPrepaymentTransactionList(order, supplyNeedPayAmount, needPrepaySupplyDeliveryFee, agentNeedPayAmount);
            tenantPrepaymentTransactionService.batchInsertTransaction(transactionList.stream().filter(el -> el.getTransactionAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()));
        }

        List<TenantPrepaymentTransactionItem> tenantPrepaymentTransactionItems = prepaymentTransactionBO.getTenantPrepaymentTransactionItems();
        tenantPrepaymentTransactionItems.stream().forEach(el -> el.setDecreaseType(decreaseType));
        tenantPrepaymentTransactionItemService.batchInsertTransactionItem(tenantPrepaymentTransactionItems.stream().filter(el -> el.getTransactionAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()));
    }

    private List<TenantPrepaymentTransaction> buildPrepaymentTransactionList(Order order, BigDecimal supplyNeedPayAmount, BigDecimal needPrepaySupplyDeliveryFee, BigDecimal agentNeedPayAmount) {
        String transactionNo = IdUtil.getSnowflakeNextIdStr();
        TenantPrepaymentTransaction supplyTransaction = buildPrepaymentTransactionByOrder(order, transactionNo, TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY.getType(), supplyNeedPayAmount);
        TenantPrepaymentTransaction supplyDeliveryTransaction = buildPrepaymentTransactionByOrder(order, transactionNo, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), needPrepaySupplyDeliveryFee);
        TenantPrepaymentTransaction agentTransaction = buildPrepaymentTransactionByOrder(order, transactionNo, TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE.getType(), agentNeedPayAmount);
        return Arrays.asList(supplyTransaction, supplyDeliveryTransaction, agentTransaction);
    }


    /**
     * 计算对应的代仓费用
     * @param orderItemAndSnapshotRespList
     */
    private List<OrderItemNeedPrepayBO> calculateAgentFee(List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList) {
        List<OrderItemAndSnapshotResp> agentItemSnapshots = orderItemAndSnapshotRespList.stream().filter(el -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(el.getGoodsType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentItemSnapshots)) {
            return Collections.emptyList();
        }
        OrderItemAndSnapshotResp orderItemSnapshot = agentItemSnapshots.get(NumberConstant.ZERO);
        Long tenantId = orderItemSnapshot.getTenantId();
        List<OrderItemNeedPrepayBO> orderItemNeedPrepayBOS = new ArrayList<>(agentItemSnapshots.size());

        Integer totalAmount = 0;
        for (OrderItemAndSnapshotResp agentItemSnapshot : agentItemSnapshots) {
            totalAmount += agentItemSnapshot.getAmount();
        }
        for (OrderItemAndSnapshotResp agentItemSnapshot : agentItemSnapshots) {
            BigDecimal agentAmount = productAgentSkuFeeRuleService.calculateAgentAmountByTenant(tenantId, agentItemSnapshot.getPayablePrice(), totalAmount);
            OrderItemNeedPrepayBO orderItemNeedPrepayBO = new OrderItemNeedPrepayBO();
            orderItemNeedPrepayBO.setOrderItemId(agentItemSnapshot.getOrderItemId());
            orderItemNeedPrepayBO.setNeedPrepayAmount(NumberUtil.mul(agentAmount, agentItemSnapshot.getAmount()));
            orderItemNeedPrepayBOS.add(orderItemNeedPrepayBO);
        }
        return orderItemNeedPrepayBOS;
    }

    /**
     * 计算供应商供价
     * @param orderItemAndSnapshotRespList
     */
    private List<OrderItemNeedPrepayBO> calculateSupplyFee(List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList) {
        List<OrderItemAndSnapshotResp> supplierItemSnapshots = orderItemAndSnapshotRespList.stream().filter(el -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(el.getGoodsType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierItemSnapshots)) {
            return Collections.emptyList();
        }

        List<OrderItemNeedPrepayBO> orderItemNeedPrepayBOS = new ArrayList<>(supplierItemSnapshots.size());
        for (OrderItemAndSnapshotResp supplierItemSnapshot : supplierItemSnapshots) {
            if (Objects.isNull(supplierItemSnapshot.getSupplyPrice())) {
                log.error("订单项：{}未查询到对应的供应价", supplierItemSnapshot.getOrderItemId());
                throw new ProviderException(ErrorCodeEnum.MARKET_ITEM_SUPPLY_PRICE_ERROR);
            }
            BigDecimal supplyFee = NumberUtil.mul(supplierItemSnapshot.getSupplyPrice(), supplierItemSnapshot.getAmount());
            OrderItemNeedPrepayBO orderItemNeedPrepayBO = new OrderItemNeedPrepayBO();
            orderItemNeedPrepayBO.setOrderItemId(supplierItemSnapshot.getOrderItemId());
            orderItemNeedPrepayBO.setNeedPrepayAmount(supplyFee);
            orderItemNeedPrepayBOS.add(orderItemNeedPrepayBO);
        }
        return orderItemNeedPrepayBOS;
    }

    private TenantPrepaymentTransaction buildPrepaymentTransactionByOrder(Order order, String transactionNo, Integer transactionType, BigDecimal amount) {
        TenantPrepaymentTransaction transaction = new TenantPrepaymentTransaction();
        transaction.setTenantId(order.getTenantId());
        transaction.setSupplierTenantId(order.getSupplierTenantId());
        transaction.setTransactionNo(transactionNo);
        transaction.setType(TenantPrepayTransactionEnum.Type.EXPENDITURE.getType());
        transaction.setTransactionType(transactionType);
        transaction.setTransactionAmount(amount);
        transaction.setAssociatedOrderNo(order.getOrderNo());
        return transaction;
    }

    private TenantPrepaymentTransaction buildPrepaymentTransactionByOrderAfterSale(OrderResp order, String orderAfterSaleNo, String transactionNo, Integer transactionType, BigDecimal amount) {
        TenantPrepaymentTransaction transaction = new TenantPrepaymentTransaction();
        transaction.setTenantId(order.getTenantId());
        transaction.setSupplierTenantId(order.getSupplierTenantId());
        transaction.setTransactionNo(transactionNo);
        transaction.setType(TenantPrepayTransactionEnum.Type.INCOME.getType());
        transaction.setTransactionType(transactionType);
        transaction.setTransactionAmount(amount);
        transaction.setAssociatedOrderNo(orderAfterSaleNo);
        return transaction;
    }

    /**
     * 计算需要扣减的预付
     * @param tenantPrepaymentTransactionBO
     */
    private void calculateNeedDecreasePrepayment(TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO) {
        // 计算扣减供应价费用
        calculateSupplyFeeDecreasePrepayment(tenantPrepaymentTransactionBO);
        // 计算扣减供应商运费费用
        calculateSupplyDeliveryFeeDecreasePrepayment(tenantPrepaymentTransactionBO);
        // 计算扣减代仓费用
        calculateAgentFeeDecreasePrepayment(tenantPrepaymentTransactionBO);
    }

    /**
     * 计算代仓费用扣减逻辑
     * @param tenantPrepaymentTransactionBO
     */
    private void calculateAgentFeeDecreasePrepayment(TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO) {
        List<OrderItemNeedPrepayBO> needPrepayAgentList = tenantPrepaymentTransactionBO.getNeedPrepayAgentList();
        BigDecimal agentPayableAmount = tenantPrepaymentTransactionBO.getAgentPayableAmount();
        BigDecimal supplyAndAgentPayableAmount = tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAmount();
        for (OrderItemNeedPrepayBO orderItemNeedPrepayBO : needPrepayAgentList) {
            BigDecimal needPrepayAgentFee = orderItemNeedPrepayBO.getNeedPrepayAmount();
            if (needPrepayAgentFee.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (needPrepayAgentFee.compareTo(agentPayableAmount) > 0) {
                BigDecimal totalAgentPayableAmount = NumberUtil.add(agentPayableAmount, supplyAndAgentPayableAmount);
                if (needPrepayAgentFee.compareTo(totalAgentPayableAmount) > 0) {
                    throw new PayBizException("当前订单存在部分商品，需要现结支付", ErrorCodeEnum.AGENT_PREPAYMENT_INSUFFICIENT, Arrays.asList(tenantPrepaymentTransactionBO.getOrderNo()));
                }
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), orderItemNeedPrepayBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE.getType(), tenantPrepaymentTransactionBO.getAgentPayableAccount().getId(), agentPayableAmount);
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), orderItemNeedPrepayBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE.getType(), tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAccount().getId(), NumberUtil.sub(needPrepayAgentFee, agentPayableAmount));
                supplyAndAgentPayableAmount = NumberUtil.sub(supplyAndAgentPayableAmount, NumberUtil.sub(needPrepayAgentFee, agentPayableAmount));
                agentPayableAmount = BigDecimal.ZERO;
            } else {
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), orderItemNeedPrepayBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE.getType(), tenantPrepaymentTransactionBO.getAgentPayableAccount().getId(), needPrepayAgentFee);
                agentPayableAmount = NumberUtil.sub(agentPayableAmount, needPrepayAgentFee);
            }
        }
        tenantPrepaymentTransactionBO.setSupplyAndAgentPayableAmount(supplyAndAgentPayableAmount);
        tenantPrepaymentTransactionBO.setAgentPayableAmount(agentPayableAmount);
    }

    /**
     * 计算供应商运费扣减了逻辑
     * @param tenantPrepaymentTransactionBO
     */
    private void calculateSupplyDeliveryFeeDecreasePrepayment(TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO) {
        BigDecimal needPrepaySupplyDeliveryFee = tenantPrepaymentTransactionBO.getNeedPrepaySupplyDeliveryFee();
        BigDecimal supplyPayableAmount = tenantPrepaymentTransactionBO.getSupplyPayableAmount();
        BigDecimal supplyAndAgentPayableAmount = tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAmount();
        BigDecimal agentPayableAmount = tenantPrepaymentTransactionBO.getAgentPayableAmount();
        if (needPrepaySupplyDeliveryFee.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        if (needPrepaySupplyDeliveryFee.compareTo(supplyPayableAmount) > 0) {
            BigDecimal totalSupplyPayableAmount = NumberUtil.add(supplyPayableAmount, supplyAndAgentPayableAmount);
            if (needPrepaySupplyDeliveryFee.compareTo(totalSupplyPayableAmount) > 0) {
                BigDecimal totalPayableAmount = NumberUtil.add(supplyPayableAmount, supplyAndAgentPayableAmount, agentPayableAmount);
                if (needPrepaySupplyDeliveryFee.compareTo(totalPayableAmount) > 0) {
                    throw new PayBizException("当前订单存在部分商品，需要现结支付", ErrorCodeEnum.SUPPLY_DELIVERY_FEE_PREPAYMENT_INSUFFICIENT, Arrays.asList(tenantPrepaymentTransactionBO.getOrderNo()));

                }
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), null, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), tenantPrepaymentTransactionBO.getSupplyPayableAccount().getId(), supplyPayableAmount);
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), null, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAccount().getId(), supplyAndAgentPayableAmount);
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), null, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), tenantPrepaymentTransactionBO.getAgentPayableAccount().getId(), NumberUtil.sub(needPrepaySupplyDeliveryFee, totalSupplyPayableAmount));

                supplyPayableAmount = BigDecimal.ZERO;
                supplyAndAgentPayableAmount = BigDecimal.ZERO;
                agentPayableAmount = NumberUtil.sub(agentPayableAmount, NumberUtil.sub(needPrepaySupplyDeliveryFee, totalSupplyPayableAmount));

            } else {
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), null, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), tenantPrepaymentTransactionBO.getSupplyPayableAccount().getId(), supplyPayableAmount);
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), null, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAccount().getId(), NumberUtil.sub(needPrepaySupplyDeliveryFee, supplyPayableAmount));

                supplyAndAgentPayableAmount = NumberUtil.sub(supplyAndAgentPayableAmount, NumberUtil.sub(needPrepaySupplyDeliveryFee, supplyPayableAmount));
                supplyPayableAmount = BigDecimal.ZERO;
            }
        } else {
            buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), null, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE.getType(), tenantPrepaymentTransactionBO.getSupplyPayableAccount().getId(), needPrepaySupplyDeliveryFee);
            supplyPayableAmount = NumberUtil.sub(supplyPayableAmount, needPrepaySupplyDeliveryFee);
        }
        tenantPrepaymentTransactionBO.setSupplyPayableAmount(supplyPayableAmount);
        tenantPrepaymentTransactionBO.setSupplyAndAgentPayableAmount(supplyAndAgentPayableAmount);
        tenantPrepaymentTransactionBO.setAgentPayableAmount(agentPayableAmount);
    }

    /**
     * 计算供应价扣减逻辑
     * @param tenantPrepaymentTransactionBO
     */
    private void calculateSupplyFeeDecreasePrepayment(TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO) {
        BigDecimal supplyPayableAmount = tenantPrepaymentTransactionBO.getSupplyPayableAmount();
        BigDecimal supplyAndAgentPayableAmount = tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAmount();
        List<OrderItemNeedPrepayBO> needPrepaySupplyList = tenantPrepaymentTransactionBO.getNeedPrepaySupplyList();

        for (OrderItemNeedPrepayBO orderItemNeedPrepayBO : needPrepaySupplyList) {
            BigDecimal supplierTotalAmount = NumberUtil.add(supplyPayableAmount, supplyAndAgentPayableAmount);
            BigDecimal needPrepaySupplyFee = orderItemNeedPrepayBO.getNeedPrepayAmount();
            if (needPrepaySupplyFee.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (needPrepaySupplyFee.compareTo(supplyPayableAmount) > 0) {
                if (needPrepaySupplyFee.compareTo(supplierTotalAmount) > 0) {
                    throw new PayBizException("当前订单存在部分商品，需要现结支付", ErrorCodeEnum.SUPPLY_PREPAYMENT_INSUFFICIENT, Arrays.asList(tenantPrepaymentTransactionBO.getOrderNo()));
                }
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), orderItemNeedPrepayBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY.getType(), tenantPrepaymentTransactionBO.getSupplyPayableAccount().getId(), supplyPayableAmount);
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), orderItemNeedPrepayBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY.getType(), tenantPrepaymentTransactionBO.getSupplyAndAgentPayableAccount().getId(), NumberUtil.sub(needPrepaySupplyFee, supplyPayableAmount));
                supplyAndAgentPayableAmount = NumberUtil.sub(supplyAndAgentPayableAmount, NumberUtil.sub(needPrepaySupplyFee, supplyPayableAmount));
                supplyPayableAmount = BigDecimal.ZERO;
            } else {
                buildTransactionItemList(tenantPrepaymentTransactionBO, tenantPrepaymentTransactionBO.getTenantId(), tenantPrepaymentTransactionBO.getOrderId(), orderItemNeedPrepayBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY.getType(), tenantPrepaymentTransactionBO.getSupplyPayableAccount().getId(), needPrepaySupplyFee);
                supplyPayableAmount = NumberUtil.sub(supplyPayableAmount, needPrepaySupplyFee);
            }
        }
        tenantPrepaymentTransactionBO.setSupplyPayableAmount(supplyPayableAmount);
        tenantPrepaymentTransactionBO.setSupplyAndAgentPayableAmount(supplyAndAgentPayableAmount);
    }

    /**
     * 构建交易明细
     * @param orderId
     * @param orderItemId
     * @param transactionType
     * @param prepaymentAccountId
     * @param transactionAmount
     * @return
     */
    private void buildTransactionItemList(TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO, Long tenantId, Long orderId, Long orderItemId, Integer transactionType, Long prepaymentAccountId, BigDecimal transactionAmount) {
        List<TenantPrepaymentTransactionItem> tenantPrepaymentTransactionItems = tenantPrepaymentTransactionBO.getTenantPrepaymentTransactionItems();
        TenantPrepaymentTransactionItem transactionItem = new TenantPrepaymentTransactionItem();
        transactionItem.setTenantId(tenantId);
        transactionItem.setOrderId(orderId);
        transactionItem.setOrderItemId(orderItemId);
        transactionItem.setTransactionType(transactionType);
        transactionItem.setPrepaymentAccountId(prepaymentAccountId);
        transactionItem.setTransactionAmount(transactionAmount);
        tenantPrepaymentTransactionItems.add(transactionItem);
    }

    /**
     * 计算订单收取的供应商运费
     * @param orderNo
     * @return
     */
    private BigDecimal calculateSupplyDeliveryFee(String orderNo) {
        SupplierDeliveryInfo supplierDeliveryInfo = supplierDeliveryInfoService.querySupplierDeliveryInfo(orderNo);
        if (Objects.nonNull(supplierDeliveryInfo) && Objects.nonNull(supplierDeliveryInfo.getSupplierDeliveryFee())) {
            return supplierDeliveryInfo.getSupplierDeliveryFee();
        }
        log.error("订单：{}未查询到对应的运费供应价", orderNo);
        throw new ProviderException(ErrorCodeEnum.MARKET_ITEM_SUPPLY_DELIVERY_ERROR);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean dealThreePartiesOrderPrepaymentRefund(OrderAfterSaleResp orderAfterSale) {
        Long tenantId = orderAfterSale.getTenantId();
        Long orderId = orderAfterSale.getOrderId();
        String orderAfterSaleNo = orderAfterSale.getAfterSaleOrderNo();
        Long orderItemId = orderAfterSale.getOrderItemId();

//        OrderItem orderItem = orderItemService.queryById(orderItemId);
//        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotService.selectByOrderItemId(tenantId, orderItemId);
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        OrderItemAndSnapshotResp orderItemAndSnapshotResp = RpcResultUtil.handle(orderItemQueryProvider.queryDetailById(orderItemId));

        // 只处理三方仓订单
        if (!OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType())) {
            return false;
        }

        List<TenantPrepaymentTransactionItem> transactionItemList = tenantPrepaymentTransactionItemService.queryByOrderId(tenantId, orderId);
        if (CollectionUtils.isEmpty(transactionItemList)) {
            log.info("订单：{}, 售后单：{} 没对应的预付交易记录，无需处理", orderId, orderAfterSaleNo);
            return false;
        }
        // 幂等
        Long prepaymentAccountId = transactionItemList.get(NumberConstant.ZERO).getPrepaymentAccountId();
        TenantPrepaymentAccount tenantPrepaymentAccount = tenantPrepaymentAccountService.selectByIdForUpdate(prepaymentAccountId);
        List<TenantPrepaymentTransaction> orderAfterSaleNoTransactionsList = tenantPrepaymentTransactionService.queryByAssociatedOrderNos(tenantId, Collections.singletonList(orderAfterSaleNo));
        if (!CollectionUtils.isEmpty(orderAfterSaleNoTransactionsList)) {
            log.info("幂等控制,订单：{}, 售后单：{} 已生成退款预付数据，无需处理,{}", orderId, orderAfterSaleNo, JSON.toJSONString(orderAfterSaleNoTransactionsList));
            return true;
        }


        // 交易流水号
        String transactionNo = IdUtil.getSnowflakeNextIdStr();
        List<TenantPrepaymentTransaction> tenantPrepaymentTransactions = new ArrayList<>(NumberConstant.TWO);

        // 组装计算预付业务对象
        TenantPrepaymentCalculateBO tenantPrepaymentCalculateBO = buildCalculateBO(orderDTO, orderAfterSale, orderItemAndSnapshotResp);

        // 运费费用计算
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        BigDecimal supplierRefundDeliveryFee = calculateSupplyDeliveryFeeRefund(tenantPrepaymentCalculateBO);
        if (supplierRefundDeliveryFee.compareTo(BigDecimal.ZERO) > 0) {
            TenantPrepaymentTransaction supplierDeliveryFeeTransaction  = buildPrepaymentTransactionByOrderAfterSale(orderDTO, orderAfterSaleNo, transactionNo, TenantPrepayTransactionEnum.TransactionType.DELIVERY_FEE_REFUND.getType(), supplierRefundDeliveryFee);
            totalRefundAmount = totalRefundAmount.add(supplierRefundDeliveryFee);
            tenantPrepaymentTransactions.add(supplierDeliveryFeeTransaction);
        }

        // 代仓费用计算
        BigDecimal agentFeeRefund = BigDecimal.ZERO;
        if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(orderItemAndSnapshotResp.getGoodsType())) {
            // 以该订单当时记录的代仓规则计算需要退多少
            agentFeeRefund = calculateAgentFeeRefund(orderAfterSale, tenantPrepaymentCalculateBO.getLastOrderAfterSaleFlag(), tenantPrepaymentCalculateBO.getTransactionList());
            if (agentFeeRefund.compareTo(BigDecimal.ZERO) > 0) {
                TenantPrepaymentTransaction agentTransaction = buildPrepaymentTransactionByOrderAfterSale(orderDTO, orderAfterSaleNo, transactionNo, TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE_REFUND.getType(), agentFeeRefund);
                tenantPrepaymentTransactions.add(agentTransaction);
                totalRefundAmount = totalRefundAmount.add(agentFeeRefund);
            }
        }

        // 供应价费用计算
        BigDecimal refundSupplyPrice = BigDecimal.ZERO;
        if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItemAndSnapshotResp.getGoodsType())) {
            refundSupplyPrice = calculateSupplyFeeRefund(tenantPrepaymentCalculateBO);
            TenantPrepaymentTransaction supplyTransaction = buildPrepaymentTransactionByOrderAfterSale(orderDTO, orderAfterSaleNo, transactionNo, TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY_REFUND.getType(), refundSupplyPrice);
            tenantPrepaymentTransactions.add(supplyTransaction);
            totalRefundAmount = totalRefundAmount.add(refundSupplyPrice);
        }

        // 根据预付账户分组并将预付金额求和
        Boolean finalNeedRefundDeliveryFee = supplierRefundDeliveryFee.compareTo(BigDecimal.ZERO) > 0;
        Boolean finalNeedRefundAgentFee = agentFeeRefund.compareTo(BigDecimal.ZERO) > 0;
        Boolean finalNeedRefundSupplyFee = refundSupplyPrice.compareTo(BigDecimal.ZERO) > 0;
        if (!finalNeedRefundDeliveryFee && !finalNeedRefundAgentFee && !finalNeedRefundSupplyFee) {
            log.info("订单：{}, 售后单：{} 本次无需退款预付", orderId, orderAfterSaleNo);
            return false;
        }

        // 过滤出相关的交易明细
        List<TenantPrepaymentTransactionItem> associatedTransactionItemList = transactionItemList.stream()
                .filter(el -> (Objects.equals(el.getOrderItemId(), orderItemId) && (finalNeedRefundAgentFee || finalNeedRefundSupplyFee) )
                        || (Objects.isNull(el.getOrderItemId()) && finalNeedRefundDeliveryFee))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(associatedTransactionItemList)) {
            log.info("订单：{}, 售后单：{} 没有相关的预付交易记录，本次无需退款预付", orderId, orderAfterSaleNo);
            return false;
        }

        TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO = new TenantPrepaymentTransactionBO();
        tenantPrepaymentTransactionBO.setTotalRefundAmount(totalRefundAmount);
        tenantPrepaymentTransactionBO.setTenantPrepaymentTransactionItems(associatedTransactionItemList);
        tenantPrepaymentTransactionBO.setTenantPrepaymentTransactions(tenantPrepaymentTransactions);
        increaseNeedRefundPrepayment(tenantPrepaymentTransactionBO);
        return false;
    }

    private BigDecimal calculateAgentFeeRefund(OrderAfterSaleResp orderAfterSale, Boolean lastOrderAfterSaleFlag, List<TenantPrepaymentTransaction> refundTransactionList) {
        // 如果不是供应商责任 则不处理
        if (!ResponsibilityTypeEnum.SUPPLIER.getType().equals(orderAfterSale.getResponsibilityType()) && Objects.equals(orderAfterSale.getAfterSaleType(), OrderAfterSaleTypeEnum.DELIVERED.getType())) {
            return BigDecimal.ZERO;
        }

        if (!lastOrderAfterSaleFlag) {
            return orderAgentSkuFeeRuleService.calculateAgentFeeByOrder(orderAfterSale);
        }
        //查询出历史代仓支付、退款的预付费用
        BigDecimal historyTotalRefund = refundTransactionList.stream().filter(el -> Objects.equals(el.getTransactionType(), TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE_REFUND.getType())).map(TenantPrepaymentTransaction::getTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<TenantPrepaymentTransactionItem> transactionItemList = tenantPrepaymentTransactionItemService.queryByOrderItemId(orderAfterSale.getTenantId(), orderAfterSale.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.AGENT_WAREHOUSE_EXPENSE.getType());
        BigDecimal historyTotalPay = transactionItemList.stream().map(TenantPrepaymentTransactionItem::getTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return NumberUtil.sub(historyTotalPay, historyTotalRefund);
    }

    private TenantPrepaymentCalculateBO buildCalculateBO(OrderResp order, OrderAfterSaleResp orderAfterSale, OrderItemAndSnapshotResp orderItemAndSnapshotResp) {
        TenantPrepaymentCalculateBO calculateBO = new TenantPrepaymentCalculateBO();
        calculateBO.setTenantId(order.getTenantId());
        calculateBO.setOrderId(order.getId());
        calculateBO.setOrderNo(order.getOrderNo());
        calculateBO.setOrderDeliveryFee(order.getDeliveryFee());
        calculateBO.setOrderItemId(orderItemAndSnapshotResp.getOrderItemId());
        calculateBO.setOrderItemAmount(orderItemAndSnapshotResp.getAmount());
        calculateBO.setOrderItemPrice(orderItemAndSnapshotResp.getTotalPrice());
        calculateBO.setSupplyPrice(orderItemAndSnapshotResp.getSupplyPrice());
        calculateBO.setRefundDeliveryFee(orderAfterSale.getDeliveryFee());
        calculateBO.setRefundPrice(orderAfterSale.getTotalPrice());
        calculateBO.setMaxAfterSaleAmount(orderItemAndSnapshotResp.getMaxAfterSaleAmount());

        Boolean lastOrderItemOrderAfterSaleFlag = orderAfterSaleService.verifyLastOrderItemOrderAfterSaleFlag(orderAfterSale.getId());
//        OrderAfterSaleInput input = OrderAfterSaleInput.builder().tenantId(orderAfterSale.getTenantId()).orderItemId(orderAfterSale.getOrderItemId()).status(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode()).build();
//        List<OrderAfterSaleResultDTO> successOrderAfterSales = orderAfterSaleServiceFacade.queryAfterSaleOrderByOrderId(input);
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setTenantId(orderAfterSale.getTenantId());
        queryReq.setOrderItemIds(Lists.newArrayList(orderAfterSale.getOrderItemId()));
        queryReq.setStatusList(Lists.newArrayList(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(queryReq));
        List<String> orderAfterSaleNos = afterSaleDTOList.stream().map(OrderAfterSaleResp::getAfterSaleOrderNo).collect(Collectors.toList());
        calculateBO.setOrderAfterSaleNos(orderAfterSaleNos);
        calculateBO.setLastOrderAfterSaleFlag(lastOrderItemOrderAfterSaleFlag);

        List<TenantPrepaymentTransaction> transactionList = tenantPrepaymentTransactionService.queryByAssociatedOrderNos(order.getTenantId(), orderAfterSaleNos);
        calculateBO.setTransactionList(transactionList);
        return calculateBO;
    }

    private void increaseNeedRefundPrepayment(TenantPrepaymentTransactionBO tenantPrepaymentTransactionBO) {
        List<TenantPrepaymentTransactionItem> transactionItemList = tenantPrepaymentTransactionBO.getTenantPrepaymentTransactionItems();
        BigDecimal totalRefundAmount = tenantPrepaymentTransactionBO.getTotalRefundAmount();
        Map<Long, BigDecimal> prepaymentAccountMap = transactionItemList.stream().collect(Collectors.groupingBy(TenantPrepaymentTransactionItem::getPrepaymentAccountId, Collectors.mapping(TenantPrepaymentTransactionItem::getTransactionAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        BigDecimal totalPrepayAmount = transactionItemList.stream().map(TenantPrepaymentTransactionItem::getTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal rate = NumberUtil.div(totalRefundAmount, totalPrepayAmount);

        // 末尾倒减计算每个预付账户会退款的金额
        Integer prepayAccounts = prepaymentAccountMap.size();
        for (Map.Entry<Long, BigDecimal> accountPrepayEntry : prepaymentAccountMap.entrySet()) {
            if (totalRefundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                // 有可能会有不需要退款的场景
                continue;
            }
            Long accountId = accountPrepayEntry.getKey();
            BigDecimal transactionAmount = accountPrepayEntry.getValue();
            BigDecimal refundAmount;
            if (prepayAccounts == 1) {
                refundAmount = totalRefundAmount;
            } else {
                refundAmount = transactionAmount.multiply(rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                totalRefundAmount = NumberUtil.sub(totalRefundAmount, refundAmount);
                prepayAccounts--;
            }
            log.info("accountId:{},refundAmount:{}", accountId, refundAmount);
            tenantPrepaymentAccountService.increaseAvailableAmount(accountId, refundAmount);
        }

        // 插入预付交易信息
        List<TenantPrepaymentTransaction> tenantPrepaymentTransactions = tenantPrepaymentTransactionBO.getTenantPrepaymentTransactions();
        tenantPrepaymentTransactionService.batchInsertTransaction(tenantPrepaymentTransactions);
    }

    /**
     * 计算供应价退款
     * @param tenantPrepaymentCalculateBO
     */
    private BigDecimal calculateSupplyFeeRefund(TenantPrepaymentCalculateBO tenantPrepaymentCalculateBO) {
        Boolean lastOrderAfterSaleFlag = tenantPrepaymentCalculateBO.getLastOrderAfterSaleFlag();
        if (!lastOrderAfterSaleFlag) {
            BigDecimal supplyPrice = NumberUtil.mul(tenantPrepaymentCalculateBO.getSupplyPrice(), tenantPrepaymentCalculateBO.getOrderItemAmount());
            BigDecimal deliveryFee = Optional.ofNullable(tenantPrepaymentCalculateBO.getRefundDeliveryFee()).orElse(BigDecimal.ZERO);
            BigDecimal refundPrice = NumberUtil.sub(tenantPrepaymentCalculateBO.getRefundPrice(), deliveryFee);
            BigDecimal totalPrice = tenantPrepaymentCalculateBO.getOrderItemPrice();
            BigDecimal rate = NumberUtil.div(refundPrice, totalPrice);
            return NumberUtil.mul(supplyPrice, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        }

        //查询出历史代仓支付、退款的预付费用
        List<TenantPrepaymentTransaction> refundTransactionList = tenantPrepaymentCalculateBO.getTransactionList();
        BigDecimal historyTotalRefund = refundTransactionList.stream().filter(el -> Objects.equals(el.getTransactionType(), TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY.getType())).map(TenantPrepaymentTransaction::getTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<TenantPrepaymentTransactionItem> transactionItemList = tenantPrepaymentTransactionItemService.queryByOrderItemId(tenantPrepaymentCalculateBO.getTenantId(), tenantPrepaymentCalculateBO.getOrderItemId(), TenantPrepayTransactionEnum.TransactionType.DIRECT_SUPPLY_REFUND.getType());
        BigDecimal historyTotalPay = transactionItemList.stream().map(TenantPrepaymentTransactionItem::getTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return NumberUtil.sub(historyTotalPay, historyTotalRefund);
    }


    private BigDecimal calculateSupplyDeliveryFeeRefund(TenantPrepaymentCalculateBO tenantPrepaymentCalculateBO) {
        BigDecimal refundDeliveryFee = tenantPrepaymentCalculateBO.getRefundDeliveryFee();
        BigDecimal orderDeliveryFee = tenantPrepaymentCalculateBO.getOrderDeliveryFee();
        if (Objects.isNull(refundDeliveryFee)) {
            return BigDecimal.ZERO;
        }
        SupplierDeliveryInfo supplierDeliveryInfo = supplierDeliveryInfoService.querySupplierDeliveryInfo(tenantPrepaymentCalculateBO.getOrderNo());
        BigDecimal supplierDeliveryFee = supplierDeliveryInfo.getSupplierDeliveryFee();
        BigDecimal supplierRefundDeliveryFee = supplierDeliveryInfo.getSupplierDeliveryFee();
        if (refundDeliveryFee.compareTo(orderDeliveryFee) < 0) {
            BigDecimal rate = NumberUtil.div(refundDeliveryFee, orderDeliveryFee);
            supplierRefundDeliveryFee = NumberUtil.mul(supplierDeliveryFee, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        }
        return supplierRefundDeliveryFee;
    }
}
