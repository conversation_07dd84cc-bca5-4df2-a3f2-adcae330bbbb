package com.cosfo.mall.tenant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.TenantCommonConfigConstant;
import com.cosfo.mall.common.constants.TenantConfigKeyEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.facade.converter.TenantInfoMapper;
import com.cosfo.mall.facade.usercenter.UserCenterBusinessInfoFacade;
import com.cosfo.mall.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.mall.system.model.po.SystemParameters;
import com.cosfo.mall.system.service.SystemParameterService;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.model.po.Tenant;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.tenant.model.po.TenantCommonConfig;
import com.cosfo.mall.tenant.model.vo.TenantCustomerPhoneVO;
import com.cosfo.mall.tenant.service.TenantService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/18
 */
@Service
@Slf4j
public class TenantServiceImpl implements TenantService {
//    @Resource
//    private TenantCompanyMapper tenantCompanyMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterBusinessInfoFacade userCenterBusinessInfoFacade;
    @Resource
    private SystemParameterService systemParameterService;

    /**
     * 关闭手机号快速验证组件租户
     */
    private static final String CLOSE_PHONE_QUICK_VERIFICATION_COMPONENT_TENANTS = "close_phone_quick_verification_component_tenants";

    @Override
    public List<Tenant> querySupplierInfoBySupplierTenantIds(List<Long> supplierTenantIds) {
        AssertCheckParams.notNull(supplierTenantIds, ResultDTOEnum.PARAMETER_MISSING.getCode(), "supplierTenantIds不能为空");
        List<TenantResultResp> tenantResultRespList = userCenterTenantFacade.getTenantsByIds(supplierTenantIds);
        if(CollectionUtils.isEmpty(tenantResultRespList)){
            return Collections.EMPTY_LIST;
        }
        List<Tenant> tenantList = tenantResultRespList.stream().map(tenantResultResp -> TenantInfoMapper.INSTANCE.respToTenant(tenantResultResp)).collect(Collectors.toList());
        return tenantList;
//        return tenantMapper.querySupplierInfoBySupplierTenantIds(supplierTenantIds);
    }

    @Override
    public TenantAuthConnectionDTO queryTenantAuthConnection(Long tenantId) {
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        TenantAuthConnectionDTO dto = new TenantAuthConnectionDTO();
        BeanUtil.copyProperties(tenantAuthConnection, dto);
        return dto;
    }

    @Override
    public TenantAuthConnectionDTO queryByAppId(String appId) {
        TenantAuthConnection connection = tenantAuthConnectionMapper.selectByAppId(appId);
        TenantAuthConnectionDTO dto = new TenantAuthConnectionDTO();
        BeanUtil.copyProperties(connection, dto);
        return dto;
    }

    @Override
    public ResultDTO<TenantDTO> selectTenantInfo(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户Id不能为空");
        TenantResultResp tenantResultResp = userCenterTenantFacade.getTenantById(tenantId);
        Tenant tenant = TenantInfoMapper.INSTANCE.respToTenant(tenantResultResp);
        AssertCheckParams.notNull(tenant, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户信息不存在");
//        Tenant tenant = tenantMapper.selectByPrimaryKey(tenantId);
        BusinessInformationResultResp businessInfo = userCenterBusinessInfoFacade.getBusinessInfo(tenantId);
//        TenantCompany company = tenantCompanyMapper.selectByTenantId(tenantId);

        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setId(tenantId);
        tenantDTO.setAdminId(tenant.getAdminId());
        tenantDTO.setTenantName(tenant.getTenantName());
        //tenantDTO.setProfitSharingSwitch(tenant.getProfitSharingSwitch());
        if (Objects.nonNull(businessInfo)) {
            tenantDTO.setCompanyName(businessInfo.getCompanyName());
            tenantDTO.setProvince(businessInfo.getProvince());
            tenantDTO.setCity(businessInfo.getCity());
            tenantDTO.setArea(businessInfo.getArea());
            tenantDTO.setAddress(businessInfo.getAddress());
        }
        tenantDTO.setOnlinePayChannel(tenant.getOnlinePayChannel());

        // 默认值 0-收起
        tenantDTO.setGoodsShowRule("0");
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, "goods_show_rule");
        if (tenantCommonConfig != null) {
            tenantDTO.setGoodsShowRule(tenantCommonConfig.getConfigValue());
        }
        // 默认小程序注册功能关闭-0
        TenantCommonConfig registryConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigKeyEnum.REGISTRY_SWITCH.getConfigKey());
        String registrySwitch = Optional.ofNullable(registryConfig).map(TenantCommonConfig::getConfigValue).orElse(TenantConfigKeyEnum.REGISTRY_SWITCH.getDefaultValue());
        tenantDTO.setRegistrySwitch(Integer.valueOf(registrySwitch));
        return ResultDTO.success(tenantDTO);
    }

    @Override
    public Tenant selectByPrimaryKey(Long tenantId) {
        TenantResultResp tenantResultResp = userCenterTenantFacade.getTenantById(tenantId);
        return TenantInfoMapper.INSTANCE.respToTenant(tenantResultResp);
//        return tenantMapper.selectByPrimaryKey(tenantId);
    }

    @Override
    public TenantDTO selectByType(Integer type) {
//        return tenantMapper.selectByType(type);
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setType(type);
        List<TenantResultResp> tenantInfoList = userCenterTenantFacade.getTenantsByQuery(tenantQueryReq);
        if (CollectionUtils.isEmpty(tenantInfoList)) {
            return null;
        }
        TenantResultResp tenantResultResp = tenantInfoList.get(NumberConstant.ZERO);
        return TenantInfoMapper.INSTANCE.respToDto(tenantResultResp);
    }

    @Override
    public List<TenantCustomerPhoneVO> queryTenantCustomerPhone(Long tenantId) {
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantCommonConfigConstant.CUSTOMER_PHONE_CONFIG_KEY);
        if (tenantCommonConfig == null || StringUtils.isEmpty(tenantCommonConfig.getConfigValue())) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(tenantCommonConfig.getConfigValue(), TenantCustomerPhoneVO.class);
    }
    @Override
    public boolean getSaveWorrySwitchByTenantId(Long tenantId) {
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantCommonConfigConstant.SAVE_WORRY);
        if (tenantCommonConfig == null) {
            return false;
        }
        return Integer.valueOf (tenantCommonConfig.getConfigValue ())==1;
    }

    @Override
    public Boolean queryUsePhoneVerificationFlag(Long tenantId) {
        SystemParameters systemParameters = systemParameterService.selectByKey(CLOSE_PHONE_QUICK_VERIFICATION_COMPONENT_TENANTS);
        if (systemParameters == null) {
            return true;
        }
        String paramValue = systemParameters.getParamValue();
        if (StringUtils.isEmpty(paramValue)) {
            return true;
        }
        List<Long> closeTenantIds = JSON.parseArray(paramValue, Long.class);
        return !closeTenantIds.contains(tenantId);
    }
}
