package com.cosfo.mall.tenant.service;

import com.cosfo.mall.order.model.bo.OrderBalanceBO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;

/**
 * @desc 预付服务层
 * <AUTHOR>
 * @date 2023/3/20 20:32
 */
public interface TenantPrepaymentService {

    /**
     * 校验、扣减三方仓订单的预付
     *
     * @param threePartiesOrderPayBO
     * @return
     */
    void dealThreePartiesOrderPrepayment(OrderBalanceBO threePartiesOrderPayBO);

    /**
     * 处理三方仓售后订单的预付
     * @param orderAfterSale
     * @return 是否命中并发拦截
     */
    boolean dealThreePartiesOrderPrepaymentRefund(OrderAfterSaleResp orderAfterSale);
}
