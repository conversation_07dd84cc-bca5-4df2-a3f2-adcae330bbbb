package com.cosfo.mall.tenant.service;

import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;

/**
 * <AUTHOR>
 * @date 2022/12/29  14:12
 */
public interface TenantAuthConnectionService {
    /**
     * 查询汇付公钥
     * @param huifuId 汇付商户Id
     * @return 公钥
     */
    String selectPublicKey(String huifuId);

    /**
     * 查询支付配置信息
     *
     * @param tenantId
     * @return
     */
    TenantAuthConnectionDTO selectByTenantId(Long tenantId);

    /**
     * 查询历史支付配置信息
     *
     * @param tenantId
     * @param huifuId
     * @Param payMchId
     * @return
     */
    TenantAuthConnectionDTO queryHistoryPayConfig(Long tenantId, String huifuId, String payMchId);
}
