package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.common.context.TenantFundAccountConfigEnum;
import com.cosfo.mall.common.exception.PayBizException;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationParamBO;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationResultBO;
import com.cosfo.mall.order.model.bo.OrderItemNonCashCalculationParamBO;
import com.cosfo.mall.tenant.model.po.TenantFundAccountConfig;
import com.cosfo.mall.tenant.service.TenantNonCashCalculationService;
import com.cosfo.mall.tenant.service.TenantFundAccountConfigService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2025-04-22
 **/
@Slf4j
@Service
public class TenantNonCashCalculationServiceImpl implements TenantNonCashCalculationService {

    @Resource
    private TenantFundAccountConfigService tenantFundAccountConfigService;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @Resource
    private MarketItemService marketItemService;


    /**
     * 计算非现金账户可用和不可用金额
     *
     * @param paramBO 计算参数
     * @return 计算结果
     */
    @Override
    public OrderNonCashCalculationResultBO calculateNonCashAmount(OrderNonCashCalculationParamBO paramBO) {
        Long tenantId = paramBO.getTenantId();
        Long storeId = paramBO.getStoreId();

        OrderNonCashCalculationResultBO.OrderNonCashCalculationResultBOBuilder resultBuilder = OrderNonCashCalculationResultBO.builder()
                .usableNonCashBalance(BigDecimal.ZERO)
                .unusableNonCashBalance(BigDecimal.ZERO)
                .totalOrderAmount(paramBO.getTotalOrderAmount());

        // 查询租户资金账户配置
        TenantFundAccountConfig config = tenantFundAccountConfigService.getByTenantId(tenantId);
        if (config == null) {
            log.info("租户:{}，资金账户配置不存在，不支持非现金支付", tenantId);
            resultBuilder.unusableNonCashBalance(paramBO.getTotalOrderAmount());
            return resultBuilder.build();
        }

        // 获取限制商品组和运费配置
        String limitGoodsGroup = config.getLimitGoodsGroup();
        boolean allowShippingFee = Objects.equals(config.getAllowShippingFee(), TenantFundAccountConfigEnum.AllowShippingFeeEnum.YES.getCode());

        // 解析商品分组
        Set<Long> limitClassificationIds = parseLimitGoodsGroup(limitGoodsGroup);

        // 初始化可用和不可用金额
        BigDecimal usableNonCashBalance = BigDecimal.ZERO;
        BigDecimal unusableNonCashBalance = BigDecimal.ZERO;

        // 处理运费
        for (Map.Entry<Long, BigDecimal> entry : paramBO.getDeliveryFeeMap().entrySet()) {
            BigDecimal deliveryFee = entry.getValue() != null ? entry.getValue() : BigDecimal.ZERO;
            if (!allowShippingFee) {
                // 不支持付运费，加入不可用金额
                unusableNonCashBalance = unusableNonCashBalance.add(deliveryFee);
                log.info("租户:{}，非现金账户不支持运费支付，运费：{}", tenantId, deliveryFee);
            } else {
                // 支持付运费，加入可用金额
                usableNonCashBalance = usableNonCashBalance.add(deliveryFee);
            }
        }

        // 处理订单商品项
        for (OrderItemNonCashCalculationParamBO orderItem : paramBO.getOrderItemDetails()) {
            BigDecimal itemAmount = orderItem.getItemAmount();
            Long classificationId = orderItem.getClassificationId();

            // 判断商品是否在限制列表中
            if (limitClassificationIds.isEmpty()) {
                // 没有限制列表，所有商品都可用
                usableNonCashBalance = usableNonCashBalance.add(itemAmount);
            } else if (classificationId != null) {
                // 有限制列表，根据商品分类判断
                if (limitClassificationIds.contains(classificationId)) {
                    // 在限制列表中，可以使用非现金账户
                    usableNonCashBalance = usableNonCashBalance.add(itemAmount);
                    log.info("租户:{}, 商品ID:{}, 分类:{} 在限制列表中，可使用非现金账户支付，金额:{}",
                            tenantId, orderItem.getItemId(), classificationId, itemAmount);
                } else {
                    // 不在限制列表中，不可使用非现金账户
                    unusableNonCashBalance = unusableNonCashBalance.add(itemAmount);
                    log.info("租户:{}, 商品ID:{}, 分类:{} 不在限制列表中，不可使用非现金账户支付，金额:{}",
                            tenantId, orderItem.getItemId(), classificationId, itemAmount);
                }
            } else {
                // 分类ID为空，默认不可用
                unusableNonCashBalance = unusableNonCashBalance.add(itemAmount);
                log.info("租户:{}, 商品ID:{}, 分类为空，默认不可使用非现金账户支付，金额:{}",
                        tenantId, orderItem.getItemId(), itemAmount);
            }
        }

        // 构建结果
        resultBuilder.usableNonCashBalance(usableNonCashBalance)
                .unusableNonCashBalance(unusableNonCashBalance)
                .hasUnusableItems(unusableNonCashBalance.compareTo(BigDecimal.ZERO) > 0);

        log.info("租户:{}, 门店:{}, 非现金账户计算完成, 可用金额:{}, 不可用金额:{}, 订单总金额:{}",
                tenantId, storeId, usableNonCashBalance, unusableNonCashBalance,
                paramBO.getTotalOrderAmount());

        return resultBuilder.build();
    }

    private Set<Long> parseLimitGoodsGroup(String limitGoodsGroup) {
        try {
            if (StringUtils.isBlank(limitGoodsGroup)) {
                return Collections.emptySet();
            }

            // 格式是这样的[1,2,3]
            String replaced = limitGoodsGroup.replaceAll("[\\[\\]]", "");
            if (StringUtils.isBlank(replaced)) {
                return Collections.emptySet();
            }
            return Arrays.stream(replaced.split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("解析限制商品组失败，数据格式错误，数据:{}", limitGoodsGroup, e);
            return Collections.emptySet();
        }
    }

    @Override
    public OrderNonCashCalculationResultBO calculateNonCashAmount(List<String> orderNos) {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        Long storeId = loginContextInfoDTO.getStoreId();
        Long tenantId = loginContextInfoDTO.getTenantId();
        List<OrderResp> orders = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));

        // 查询订单明细
        List<Long> orderIds = orders.stream().map(OrderResp::getId).collect(Collectors.toList());
        OrderItemQueryReq req = new OrderItemQueryReq();
        req.setOrderIds(orderIds);
        List<OrderItemAndSnapshotResp> orderItems = RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(req));

        // 查询商品信息
        List<Long> itemIds = orderItems.stream().map(OrderItemAndSnapshotResp::getItemId).collect(Collectors.toList());
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(itemIds, tenantId);

        // 商品ID到分类ID的映射
        Map<Long, Long> itemIdToClassificationMap = marketItemVOList.stream()
                .filter(item -> item.getClassificationId() != null)
                .collect(Collectors.toMap(MarketItemVO::getItemId, MarketItemVO::getClassificationId, (v1, v2) -> v1));

        // 构建运费信息
        Map<Long, BigDecimal> deliveryFeeMap = orders.stream()
                .collect(Collectors.toMap(OrderResp::getId, OrderResp::getDeliveryFee));

        // 构建商品明细信息
        List<OrderItemNonCashCalculationParamBO> orderItemDetails = new ArrayList<>();
        for (OrderItemAndSnapshotResp item : orderItems) {
            orderItemDetails.add(OrderItemNonCashCalculationParamBO.builder()
                    .orderId(item.getOrderId())
                    .itemId(item.getItemId())
                    .itemAmount(item.getTotalPrice())
                    .classificationId(itemIdToClassificationMap.get(item.getItemId()))
                    .build());
        }

        // 计算订单总金额
        BigDecimal totalOrderAmount = orders.stream()
                .map(OrderResp::getPayablePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 构建参数BO
        OrderNonCashCalculationParamBO paramBO = OrderNonCashCalculationParamBO.builder()
                .tenantId(tenantId)
                .storeId(storeId)
                .deliveryFeeMap(deliveryFeeMap)
                .orderItemDetails(orderItemDetails)
                .totalOrderAmount(totalOrderAmount)
                .build();

        return calculateNonCashAmount(paramBO);
    }
}
