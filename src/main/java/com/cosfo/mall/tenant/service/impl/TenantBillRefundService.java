package com.cosfo.mall.tenant.service.impl;

import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.TenantBillTypeEnum;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.payment.mapper.HuiFuRefundMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.HuiFuRefund;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.repository.HuiFuRefundRepository;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.tenant.model.po.TenantBill;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TenantBillRefundService {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private HuiFuRefundMapper huiFuRefundMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @Resource
    private HuiFuRefundRepository huiFuRefundRepository;
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    public TenantBill createTenantBillForRefund(Long refundId) {
        Refund refund = refundMapper.selectByPrimaryKey(refundId);
        if (refund == null) {
            log.error("退款单不存在, refundId: {}", refundId);
            return null;
        }

        // 获取关联的支付单信息，用于判断支付渠道
        Payment payment = paymentService.queryByRefundId(refund.getId());
        if (payment == null) {
            log.error("退款单{}未找到关联的支付单", refundId);
            return null;
        }

        // 根据支付渠道处理退款手续费
        if (isChannelNeedFee(payment.getOnlinePayChannel())) {
            return handleRefundWithFee(refund, payment);
        } else {
            return handleNormalRefund(refund);
        }
    }

    /**
     * 判断支付渠道是否需要计算退款手续费
     */
    private boolean isChannelNeedFee(Integer onlinePayChannel) {
        return OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(onlinePayChannel) ||
                OnlinePayChannelEnum.DIN_PAY.getChannel().equals(onlinePayChannel);
    }

    /**
     * 统一处理需要手续费的退款渠道
     */
    private TenantBill handleRefundWithFee(Refund refund, Payment payment) {
        log.info("开始处理需要手续费的退款单，refundId: {}, channel: {}",
                refund.getId(), payment.getOnlinePayChannel());

        TenantBill bill = createBill(refund, payment);

        // 根据渠道获取退款手续费
        BigDecimal feeAmount = calculateRefundFeeForChannel(refund, payment);
        if (feeAmount != null && feeAmount.compareTo(BigDecimal.ZERO) != 0) {
            bill.setFeeAmount(feeAmount);
            log.info("退款单{}设置手续费: {}", refund.getId(), feeAmount);
        }

        return bill;
    }

    /**
     * 根据支付渠道计算退款手续费
     */
    private BigDecimal calculateRefundFeeForChannel(Refund refund, Payment payment) {
        Integer channel = payment.getOnlinePayChannel();

        if (OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(channel)) {
            return calculateHuiFuRefundFee(refund);
        } else if (OnlinePayChannelEnum.DIN_PAY.getChannel().equals(channel)) {
            return calculateDinPayRefundFee(refund);
        }

        log.warn("不支持的退款手续费渠道: {}", channel);
        return BigDecimal.ZERO;
    }

    /**
     * 计算汇付退款手续费
     */
    private BigDecimal calculateHuiFuRefundFee(Refund refund) {
        HuiFuRefund huiFuRefund = huiFuRefundRepository.queryLastByRefundId(refund.getId());
        if (huiFuRefund == null) {
            log.warn("退款单{}没有汇付退款记录", refund.getId());
            return BigDecimal.ZERO;
        }

        String feeAmountStr = Optional.ofNullable(huiFuRefund.getFeeAmount()).orElse("0");
        BigDecimal feeAmount = new BigDecimal(feeAmountStr);

        log.info("汇付退款单{}手续费: {}", refund.getId(), feeAmount);
        return feeAmount;
    }

    /**
     * 计算智付退款手续费
     */
    private BigDecimal calculateDinPayRefundFee(Refund refund) {
        // 智付退款直接使用 refund 表的 feeAmount 字段
        BigDecimal feeAmount = Optional.ofNullable(refund.getFeeAmount()).orElse(BigDecimal.ZERO);

        log.info("智付退款单{}手续费: {}", refund.getId(), feeAmount);
        return feeAmount;
    }



    private TenantBill handleNormalRefund(Refund refund) {
        return createBill(refund);
    }


    private TenantBill createBill(Refund refund) {
        Payment payment = paymentService.queryByRefundId(refund.getId());
        if (payment == null) {
            throw new BizException("记录交易流水，未查询到支付单信息");
        }
        return createBill(refund, payment);
    }

    private TenantBill createBill(Refund refund, Payment payment) {
        TenantBill bill = new TenantBill();
        bill.setType(TenantBillTypeEnum.EXPENSES.getType());
        bill.setTenantId(refund.getTenantId());
        bill.setStoreId(payment.getStoreId());
        bill.setBillPrice(refund.getRefundPrice());

        // 组合支付退款交易流水转换支付类型
        if (TradeTypeEnum.COMBINED_PAY.getDesc().equals(payment.getTradeType())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.selectByCombinedPaymentNo(payment.getPaymentNo());
            List<Integer> combinedPayTypes = combinedDetails.stream().map(detail -> TradeTypeEnum.getPayTypeByTradeType(detail.getTradeType())).collect(Collectors.toList());
            bill.setPaymentType(combinedPayTypes.stream().filter(PayTypeEnum::usableCombinedNativePayType).findFirst().orElse(TradeTypeEnum.COMBINED_PAY.getPayType()));

        }else{
            bill.setPaymentType(TradeTypeEnum.getPayTypeByTradeType(payment.getTradeType()));
        }

        List<OrderAfterSaleResp> afterSaleDTOList = fetchAfterSaleDTOList(refund);

        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            bill.setRecordNo(refund.getRefundNo());
        } else {
            OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);
            bill.setRecordNo(afterSaleDTO.getAfterSaleOrderNo());
        }
        return bill;
    }

    private List<OrderAfterSaleResp> fetchAfterSaleDTOList(Refund refund) {
        if (refund.getAfterSaleId() == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(
                orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(refund.getAfterSaleId())));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            return Collections.emptyList();
        }
        return afterSaleDTOList;
    }

}
