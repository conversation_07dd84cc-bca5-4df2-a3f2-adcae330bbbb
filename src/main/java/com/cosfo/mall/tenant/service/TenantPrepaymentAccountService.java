package com.cosfo.mall.tenant.service;

import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc 品牌预付
 * <AUTHOR>
 * @date 2023/3/17 10:50
 */
public interface TenantPrepaymentAccountService {

    /**
     * 查询品牌给供应商预付的账户
     * @param tenantId
     * @param supplierTenantId
     * @return
     */
    List<TenantPrepaymentAccount> queryTenant4SupplierPrepay(Long tenantId, Long supplierTenantId);

    /**
     * 扣减可用金额
     * @param id
     * @param changeAmount
     * @return
     */
    int decreaseAvailableAmount(Long id, BigDecimal changeAmount);

    /**
     * 冻结可用金额
     *
     * @param id
     * @param changeAmount
     * @return
     */
    int freezeAvailableAmount(Long id, BigDecimal changeAmount);

    /**
     * 增加可用金额
     * @param id
     * @param changeAmount
     * @return
     */
    int increaseAvailableAmount(Long id, BigDecimal changeAmount);

    /**
     * 根据主键ID查询排他锁
     * @param id
     * @return
     */
    TenantPrepaymentAccount selectByIdForUpdate(Long id);

    /**
     * 扣减冻结金额
     *
     * @param accountId
     * @param changeAmount
     * @return
     */
    int decreaseFreezeBalance(Long accountId, BigDecimal changeAmount);
}
