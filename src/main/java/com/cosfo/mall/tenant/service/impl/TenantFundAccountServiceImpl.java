package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.tenant.mapper.TenantFundAccountMapper;
import com.cosfo.mall.tenant.model.po.TenantFundAccount;
import com.cosfo.mall.tenant.service.TenantFundAccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2025-05-29
 **/
@Service
public class TenantFundAccountServiceImpl implements TenantFundAccountService {

    @Resource
    private TenantFundAccountMapper tenantFundAccountMapper;

    @Override
    public List<TenantFundAccount> queryByIds(List<Long> ids) {
        return tenantFundAccountMapper.queryByIds(ids);
    }
}
