package com.cosfo.mall.tenant.service;

import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;

import java.util.List;

/**
 * @desc 预付交易服务层
 * <AUTHOR>
 * @date 2023/3/20 10:47
 */
public interface TenantPrepaymentTransactionService {

    /**
     * 批量插入交易流水
     * @param transactionList
     * @return
     */
    int batchInsertTransaction(List<TenantPrepaymentTransaction> transactionList);


    /**
     * 根据相关单号查询
     * @param tenantId
     * @param associatedOrderNos
     * @return
     */
    List<TenantPrepaymentTransaction> queryByAssociatedOrderNos(Long tenantId, List<String> associatedOrderNos);
}
