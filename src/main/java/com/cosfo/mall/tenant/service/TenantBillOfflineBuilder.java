//package com.cosfo.mall.tenant.service;
//
//import com.cosfo.mall.payment.model.po.Payment;
//import com.cosfo.mall.payment.model.po.Refund;
//import com.cosfo.mall.payment.service.PaymentService;
//import com.cosfo.mall.payment.service.RefundService;
//import java.util.List;
//import java.util.Set;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//@Component
//@Slf4j
//public class TenantBillOfflineBuilder {
//
//    @Resource
//    private PaymentService paymentService;
//    @Resource
//    private RefundService refundService;
//
//    public void initTenantBill(Set<Long> tenantIds, String startTime, String endTime) {
//        // 查询已成功的支付单
//        List<Payment> payments = paymentService.querySuccessPayments(tenantIds, startTime, endTime);
//        log.info("查询已成功的支付单:{}, tenantIds:{}", payments.size(), tenantIds);
//        for (Payment payment : payments) {
//            paymentService.addTenantBill(payment);
//        }
//        // 查询已成功的退款单
//        List<Refund> refunds = refundService.querySuccessRefunds(tenantIds, startTime, endTime);
//        log.info("查询已成功的退款单:{}, tenantIds:{}", refunds.size(), tenantIds);
//        for (Refund refund : refunds) {
//            refundService.addTenantBill(refund);
//        }
//    }
//}
