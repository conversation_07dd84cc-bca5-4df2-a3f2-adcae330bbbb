package com.cosfo.mall.tenant.service;

import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.tenant.model.dto.DeliveryFeeResultDTO;
import com.cosfo.mall.tenant.model.po.TenantDeliveryFeeRule;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
public interface DeliveryFeeStrategy {
    /**
     * 根据类型获取具体执行策略
     *
     * @param type
     * @return
     */
    Boolean support(Integer type);

    /**
     * 计算运费
     *
     * @param orderDTO
     * @param tenantDeliveryFeeRule
     * @return
     */
    DeliveryFeeResultDTO calculateDeliveryFee(OrderDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule);
}
