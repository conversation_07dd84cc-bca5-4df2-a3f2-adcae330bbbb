package com.cosfo.mall.tenant.service;

import com.cosfo.mall.tenant.model.po.TenantBill;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/26  14:23
 */
public interface TenantBillService {

    /**
     * 批量插入收支明细
     *
     * @param bills 明细
     */
    void batchInsert(List<TenantBill> bills);

//    /**
//     * 查询交易流水记录
//     *
//     * @param tenantId
//     * @param recordNo
//     * @return
//     */
//    TenantBill queryByRecordNo(Long tenantId, String recordNo);

    /**
     * 为支付单创建账单. 支付单可能对应多个订单,因此可能会创建多个账单
     *
     * @param paymentId 支付单id
     */
    void createTenantBillForPayment(Long paymentId);


    /**
     * 为退款单创建账单
     *
     * @param refundId 退款单id
     */
    void createTenantBillForRefund(Long refundId);

//    /**
//     * 初始化账单
//     * @param tenantId
//     * @param startTime
//     * @param endTime
//     */
//    void initTenantBill(Long tenantId, String startTime, String endTime);
}
