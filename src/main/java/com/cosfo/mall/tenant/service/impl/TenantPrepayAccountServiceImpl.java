package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.tenant.dao.TenantPrepaymentAccountDao;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;
import com.cosfo.mall.tenant.service.TenantPrepaymentAccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @desc 品牌预付
 * <AUTHOR>
 * @date 2023/3/17 10:50
 */
@Service
public class TenantPrepayAccountServiceImpl implements TenantPrepaymentAccountService {

    @Resource
    private TenantPrepaymentAccountDao tenantPrepaymentAccountDao;

    @Override
    public List<TenantPrepaymentAccount> queryTenant4SupplierPrepay(Long tenantId, Long supplierTenantId) {
        return tenantPrepaymentAccountDao.queryTenant4SupplierPrepay(tenantId, supplierTenantId);
    }

    @Override
    public int decreaseAvailableAmount(Long id, BigDecimal amount) {
        return tenantPrepaymentAccountDao.decreaseAvailableAmount(id, amount);
    }

    @Override
    public int freezeAvailableAmount(Long id, BigDecimal changeAmount) {
        return tenantPrepaymentAccountDao.freezeAvailableAmount(id, changeAmount);
    }

    @Override
    public int increaseAvailableAmount(Long id, BigDecimal changeAmount) {
        return tenantPrepaymentAccountDao.increaseAvailableAmount(id, changeAmount);
    }

    @Override
    public TenantPrepaymentAccount selectByIdForUpdate(Long id) {
        return tenantPrepaymentAccountDao.selectByIdForUpdate(id);
    }

    @Override
    public int decreaseFreezeBalance(Long accountId, BigDecimal changeAmount) {
        return tenantPrepaymentAccountDao.decreaseFreezeBalance(accountId, changeAmount);
    }
}
