package com.cosfo.mall.tenant.service;

import com.cosfo.mall.client.openapi.req.PlaceOrderReq;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.SpecialTenantQueryFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 特殊定制化租户 查询服务
 * <AUTHOR>
 * @Date 2025/4/15 15:21
 * @Version 1.0
 */
@Slf4j
@Component
public class SpecialTenantService {

    @Autowired
    private SpecialTenantQueryFacade specialTenantQueryFacade;

    /**
     * 订单下单是否需要指定expectedDeliveryDate
     * @param tenantId 租户id
     * @return true 需要指定expectedDeliveryDate, false 不需要指定expectedDeliveryDate
     */
    public boolean placeOrderWithExpectedDeliveryDate(Long tenantId) {
        return specialTenantQueryFacade.isChageeTenant(tenantId);
    }

    /**
     * 订单下单是否不需要占用库存
     * @param tenantId 租户id
     * @return true 不需要占库存 false 需要占用库存
     */
    public boolean placeOrderNotNeedOccupyInventory(Long tenantId) {
        return specialTenantQueryFacade.isChageeTenant(tenantId);
    }

    /**
     * 订单退单是否不需要释放库存
     * @param tenantId 租户id
     * @return true 不需要释放库存 false 需要释放库存
     */
    public boolean cancelOrderNotNeedReleaseInventory(Long tenantId) {
        return specialTenantQueryFacade.isChageeTenant(tenantId);
    }

    /**
     * 【特殊租户】校验下单参数
     * @param tenantId 租户id
     * @param placeOrderReq 下单参数
     */
    public void validatePlaceOrderParam(Long tenantId, PlaceOrderReq placeOrderReq){
        if (specialTenantQueryFacade.isChageeTenant(tenantId)){
            if (null == placeOrderReq.getExpectedDeliveryDate()){
                throw new BizException("下单参数校验失败，期望配送日期不能为空");
            }
            if (StringUtils.isEmpty(placeOrderReq.getCustomerOrderExtraNo())){
                throw new BizException("下单参数校验失败，外部订单额外编号不能为空");
            }
            if (null == placeOrderReq.getOrderPaymentTime()){
                throw new BizException("下单参数校验失败，外部支付时间不能为空");
            }
        }
    }

}
