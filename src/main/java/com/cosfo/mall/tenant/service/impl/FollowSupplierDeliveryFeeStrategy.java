package com.cosfo.mall.tenant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.common.constants.TenantDeliveryFeeRuleEnums;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.tenant.model.dto.DeliveryFeeResultDTO;
import com.cosfo.mall.tenant.model.po.TenantDeliveryFeeRule;
import com.cosfo.mall.tenant.service.DeliveryFeeStrategy;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.provider.DeliveryInfoQueryProvider;
import net.summerfarm.ofc.client.req.DeliveryDateQueryReq;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.marketing.center.client.freight.provider.DeliveryFeeRuleQueryProvider;
import net.xianmu.marketing.center.client.freight.req.DeliveryFeeQueryReq;
import net.xianmu.marketing.center.client.freight.req.DeliveryFeeSkuInfoQueryReq;
import net.xianmu.marketing.center.client.freight.resp.DeliveryFeeResp;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Slf4j
@Service
public class FollowSupplierDeliveryFeeStrategy implements DeliveryFeeStrategy {

    @DubboReference
    private DeliveryFeeRuleQueryProvider deliveryFeeRuleQueryProvider;
    @DubboReference
    private DeliveryInfoQueryProvider deliveryInfoQueryProvider;
    @DubboReference
    private TenantQueryProvider tenantQueryProvider;

    // 日配0 非日配1
    private static final Integer isEveryDayFlag = 0;

    private static final Long XIANMU_SKU_TENANT_ID = 1L;

    @Override
    public Boolean support(Integer type) {
        return TenantDeliveryFeeRuleEnums.Type.FOLLOW_SUPPLIER.getType().equals(type);
    }


    @Override
    public DeliveryFeeResultDTO calculateDeliveryFee(OrderDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule) {
        List<OrderItemDTO> orderItemDTOList = orderDTO.getOrderItemDTOList();

        // 如果有自营代仓货品，运费为0
        boolean hasSelfGoodFlag = orderItemDTOList.stream().anyMatch(e -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(e.getGoodsType()));
        if(hasSelfGoodFlag){
            log.info("商品中含有自营代仓货品，运费为0。OrderDTO={}", JSON.toJSONString(orderDTO));
            DeliveryFeeResultDTO deliveryFeeResultDTO = new DeliveryFeeResultDTO();
            deliveryFeeResultDTO.setDeliveryFee(BigDecimal.ZERO);
            return deliveryFeeResultDTO;
        }

        // 供应商运费 免运费供应商运费为0
        BigDecimal supplierDeliveryFee = BigDecimal.ZERO;
        String rule = null;
        // 调取鲜沐服务获取运费
        try {
            DeliveryDateQueryReq req = new DeliveryDateQueryReq();
            req.setSource(OfcOrderSourceEnum.SAAS_MALL);
            req.setTenantId(orderDTO.getTenantId());
            req.setCity(orderDTO.getCity());
            req.setArea(orderDTO.getArea());
            req.setPayTime(LocalDateTime.now());
            DeliveryDateQueryResp deliveryDateQueryResp = RpcResultUtil.handle(deliveryInfoQueryProvider.queryDeliveryDate(req));
            if (deliveryDateQueryResp == null) {
                throw new BizException("获取配送时间异常");
            }

            // 获取租户信息，取adminId 鲜沐大客户id作为传参
            TenantResultResp tenantResultResp = RpcResultUtil.handle(tenantQueryProvider.getTenantById(orderDTO.getTenantId()));
            if(tenantResultResp == null){
                throw new BizException("获取租户信息异常，tenantId=" + orderDTO.getTenantId());
            }


            DeliveryFeeQueryReq deliveryFeeQueryReq = new DeliveryFeeQueryReq();
            deliveryFeeQueryReq.setTenantId(orderDTO.getTenantId());
            deliveryFeeQueryReq.setXmAdminId(NumberUtils.toInt(String.valueOf(tenantResultResp.getAdminId()), -1));
            deliveryFeeQueryReq.setIsEveryDayFlag(isEveryDayFlag.equals(deliveryDateQueryResp.getIsEveryDayFlag()));
            deliveryFeeQueryReq.setDeliveryDate(deliveryDateQueryResp.getDeliveryDate());
            deliveryFeeQueryReq.setProvince(orderDTO.getOrderAddress().getProvince());
            deliveryFeeQueryReq.setCity(orderDTO.getOrderAddress().getCity());
            deliveryFeeQueryReq.setArea(orderDTO.getOrderAddress().getArea());
            deliveryFeeQueryReq.setAddress(orderDTO.getOrderAddress().getAddress());
            List<DeliveryFeeSkuInfoQueryReq> freightSkuInfoQueryReqs = orderItemDTOList.stream().map(orderItemDTO -> {
                DeliveryFeeSkuInfoQueryReq deliveryFeeSkuInfoQueryReq = new DeliveryFeeSkuInfoQueryReq();
                deliveryFeeSkuInfoQueryReq.setSku(orderItemDTO.getSku());
                deliveryFeeSkuInfoQueryReq.setSkuTenantId(XIANMU_SKU_TENANT_ID);
                deliveryFeeSkuInfoQueryReq.setCategoryType(orderItemDTO.getCategoryType());
                deliveryFeeSkuInfoQueryReq.setSkuSubType(orderItemDTO.getSubAgentType());
                deliveryFeeSkuInfoQueryReq.setSkuPrice(NumberUtil.div(orderItemDTO.getCalcPartDeliveryFee(), orderItemDTO.getAmount(), 2));
                deliveryFeeSkuInfoQueryReq.setSkuAmount(orderItemDTO.getAmount());
                deliveryFeeSkuInfoQueryReq.setTotalPrice(orderItemDTO.getCalcPartDeliveryFee());
                return deliveryFeeSkuInfoQueryReq;
            }).collect(Collectors.toList());
            deliveryFeeQueryReq.setFreightSkuInfoQueryReqs(freightSkuInfoQueryReqs);

            DeliveryFeeResp deliveryFeeResp = RpcResultUtil.handle(deliveryFeeRuleQueryProvider.queryDeliveryFee(deliveryFeeQueryReq));

            if (deliveryFeeResp != null && deliveryFeeResp.getDeliveryFee() != null) {
                supplierDeliveryFee = deliveryFeeResp.getDeliveryFee();
                rule = JSON.toJSONString(deliveryFeeResp.getHitRuleDetailSnapshot());
            }
        } catch (Exception e) {
            log.error("获取鲜沐运费失败", e);
        }

        DeliveryFeeResultDTO deliveryFeeResultDTO = new DeliveryFeeResultDTO();
        deliveryFeeResultDTO.setDeliveryFee(supplierDeliveryFee);
        deliveryFeeResultDTO.setRule(rule);
        return deliveryFeeResultDTO;
    }
}
