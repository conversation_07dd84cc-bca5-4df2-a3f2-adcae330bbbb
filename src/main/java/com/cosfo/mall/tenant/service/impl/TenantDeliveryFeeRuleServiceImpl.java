package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.tenant.dao.TenantDeliveryFeeAreaDao;
import com.cosfo.mall.tenant.mapper.TenantDeliveryFeeRuleMapper;
import com.cosfo.mall.tenant.model.po.TenantDeliveryFeeRule;
import com.cosfo.mall.tenant.service.TenantDeliveryFeeRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 23:22
 */
@Service
public class TenantDeliveryFeeRuleServiceImpl implements TenantDeliveryFeeRuleService {

    @Resource
    private TenantDeliveryFeeRuleMapper tenantDeliveryFeeRuleMapper;
    @Resource
    private TenantDeliveryFeeAreaDao tenantDeliveryFeeAreaDao;

    @Override
    public TenantDeliveryFeeRule selectByTenantId(Long tenantId) {
        return tenantDeliveryFeeRuleMapper.selectByTenantId(tenantId);
    }
}
