package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.tenant.dao.TenantPayConfigLogDao;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.tenant.model.po.TenantPayConfigLog;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/29  14:13
 */
@Service
public class TenantAuthConnectionServiceImpl implements TenantAuthConnectionService {
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private TenantPayConfigLogDao tenantPayConfigLogDao;

    @Override
    public String selectPublicKey(String huifuId) {
        List<TenantPayConfigLog> tenantPayConfigLogs = tenantPayConfigLogDao.queryByHuiFuId(null ,huifuId, null);
        if (CollectionUtils.isEmpty(tenantPayConfigLogs)) {
            throw new BizException("汇付商户信息配置错误");
        }

        return tenantPayConfigLogs.get(0).getHuifuPublicKey();
    }

    @Override
    public TenantAuthConnectionDTO selectByTenantId(Long tenantId) {
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        TenantAuthConnectionDTO tenantAuthConnectionDTO = new TenantAuthConnectionDTO();
        BeanUtils.copyProperties(tenantAuthConnection, tenantAuthConnectionDTO);
        return tenantAuthConnectionDTO;
    }

    @Override
    public TenantAuthConnectionDTO queryHistoryPayConfig(Long tenantId, String huifuId, String payMchId) {
        List<TenantPayConfigLog> tenantPayConfigLogs = tenantPayConfigLogDao.queryByHuiFuId(tenantId, huifuId, payMchId);
        if (CollectionUtils.isEmpty(tenantPayConfigLogs)) {
            throw new BizException("汇付商户信息配置错误");
        }

        TenantPayConfigLog tenantPayConfigLog = tenantPayConfigLogs.get(NumberConstant.ZERO);
        TenantAuthConnectionDTO tenantAuthConnectionDTO = new TenantAuthConnectionDTO();
        tenantAuthConnectionDTO.setTenantId(tenantPayConfigLog.getTenantId());
        tenantAuthConnectionDTO.setPayMchid(tenantPayConfigLog.getPayMchId());
        tenantAuthConnectionDTO.setPaySecret(tenantPayConfigLog.getPaySecret());
        tenantAuthConnectionDTO.setPayCertPath(tenantPayConfigLog.getPayCertPath());
        tenantAuthConnectionDTO.setHuifuId(tenantPayConfigLog.getHuifuId());
        tenantAuthConnectionDTO.setSecretKey(tenantPayConfigLog.getSecretKey());
        tenantAuthConnectionDTO.setPublicKey(tenantPayConfigLog.getPublicKey());
        tenantAuthConnectionDTO.setHuifuPublicKey(tenantPayConfigLog.getHuifuPublicKey());
        return tenantAuthConnectionDTO;
    }
}
