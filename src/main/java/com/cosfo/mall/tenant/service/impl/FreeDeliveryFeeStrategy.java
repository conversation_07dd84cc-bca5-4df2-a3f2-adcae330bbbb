package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.common.constants.TenantDeliveryFeeRuleEnums;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.tenant.model.dto.DeliveryFeeResultDTO;
import com.cosfo.mall.tenant.model.po.TenantDeliveryFeeRule;
import com.cosfo.mall.tenant.service.DeliveryFeeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Slf4j
@Service
public class FreeDeliveryFeeStrategy implements DeliveryFeeStrategy {

    @Override
    public Boolean support(Integer type) {
        return TenantDeliveryFeeRuleEnums.Type.FREE.getType().equals(type);
    }

    @Override
    public DeliveryFeeResultDTO calculateDeliveryFee(OrderDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule) {
        DeliveryFeeResultDTO deliveryFeeResultDTO = new DeliveryFeeResultDTO();
        deliveryFeeResultDTO.setDeliveryFee(BigDecimal.ZERO);
        return deliveryFeeResultDTO;
    }
}
