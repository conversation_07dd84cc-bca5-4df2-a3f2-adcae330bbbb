package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.tenant.mapper.TenantFundAccountConfigMapper;
import com.cosfo.mall.tenant.model.po.TenantFundAccountConfig;
import com.cosfo.mall.tenant.service.TenantFundAccountConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * @description:
 * @author: George
 * @date: 2025-04-22
 **/
@Service
public class TenantFundAccountConfigServiceImpl implements TenantFundAccountConfigService {

    @Resource
    private TenantFundAccountConfigMapper tenantFundAccountConfigMapper;

    @Override
    public TenantFundAccountConfig getByTenantId(Long tenantId) {
        return tenantFundAccountConfigMapper.selectByTenantId(tenantId);
    }
}
