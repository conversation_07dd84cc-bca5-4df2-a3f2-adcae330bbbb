package com.cosfo.mall.tenant.service;

import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;

import java.util.List;

/**
 * @desc 预付交易明细服务层
 * <AUTHOR>
 * @date 2023/3/22 17:03
 */
public interface TenantPrepaymentTransactionItemService {


    /**
     * 批量插入交易明细记录
     * @param tenantPrepaymentTransactionItems
     */
    void batchInsertTransactionItem(List<TenantPrepaymentTransactionItem> tenantPrepaymentTransactionItems);

    /**
     * 根据订单id查询
     * @param tenantId
     * @param orderId
     * @return
     */
    List<TenantPrepaymentTransactionItem> queryByOrderId(Long tenantId, Long orderId);

    /**
     * 根据订单明细查询
     * @param tenantId
     * @param orderItemId
     * @param transactionType
     * @return
     */
    List<TenantPrepaymentTransactionItem> queryByOrderItemId(Long tenantId, Long orderItemId, Integer transactionType);

    /**
     * 批量更新交易明细记录
     *
     * @param prepayItems
     */
    void batchUpdateTransactionItem(List<TenantPrepaymentTransactionItem> prepayItems);

    /**
     * 根据订单ids查询
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<TenantPrepaymentTransactionItem> queryByOrderIds(Long tenantId, List<Long> orderIds);
}
