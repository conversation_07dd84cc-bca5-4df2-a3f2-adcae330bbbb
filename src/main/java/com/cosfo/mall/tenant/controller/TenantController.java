package com.cosfo.mall.tenant.controller;

import com.cosfo.mall.common.config.SystemTenantProperties;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.model.dto.TenantIdDTO;
import com.cosfo.mall.tenant.model.vo.TenantCustomerPhoneVO;
import com.cosfo.mall.tenant.service.TenantService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tenant")
public class TenantController extends BaseController {

    @Resource
    private TenantService tenantService;

    @Resource
    private SystemTenantProperties systemTenantProperties;

    /**
     * 查询租户基本信息
     *
     * @return
     */
    @RequestMapping(value = "/tenantInfo", method = RequestMethod.GET)
    public ResultDTO<TenantDTO> selectTenantInfo() {
        return tenantService.selectTenantInfo(systemTenantProperties.getWurthTenantId());
    }

    /**
     * 查询品牌方客服电话
     *
     * @return
     */
    @PostMapping("/query/customer-phone")
    public CommonResult<List<TenantCustomerPhoneVO>> queryCustomerPhone() {
        Long tenantId = systemTenantProperties.getWurthTenantId();
        return CommonResult.ok(tenantService.queryTenantCustomerPhone(tenantId));
    }

    /**
     * 查询快速验证手机号标识
     *
     * @param tenantDTO
     * @return
     */
    @PostMapping("/query/use-phone-verification-flag")
    public CommonResult<Boolean> queryUsePhoneVerificationFlag(@RequestBody TenantIdDTO tenantDTO) {
        if (tenantDTO == null || tenantDTO.getTenantId() == null) {
            return CommonResult.ok(false);
        }
        return CommonResult.ok(tenantService.queryUsePhoneVerificationFlag(tenantDTO.getTenantId()));
    }
}
