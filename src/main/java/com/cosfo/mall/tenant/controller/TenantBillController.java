package com.cosfo.mall.tenant.controller;

import com.cosfo.mall.tenant.model.dto.TenantBillInitDTO;
import com.cosfo.mall.tenant.service.TenantBillService;
import com.google.common.collect.Sets;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-03
 **/
@RestController
@RequestMapping("/tenant/bill")
public class TenantBillController {

    @Resource
    private TenantBillService tenantBillService;


    /**
     * 兜底接口，用于补偿账单
     *
     * @param paymentId 支付单id
     * @return CommonResult
     */
    @PostMapping(value = "/upsert/payment")
    public CommonResult<?> createBillForPayment(Long paymentId) {
        tenantBillService.createTenantBillForPayment(paymentId);
        return CommonResult.ok();
    }


    /**
     * 兜底接口，用于补偿账单
     *
     * @param refundId 退款单id
     * @return CommonResult
     */
    @PostMapping(value = "/upsert/refund")
    public CommonResult<?> createBillForRefund(Long refundId) {
        tenantBillService.createTenantBillForRefund(refundId);
        return CommonResult.ok();
    }

//    @Resource
//    private TenantBillOfflineBuilder tenantBillOfflineBuilder;
//    @PostMapping(value = "/init")
//    public CommonResult init(@RequestBody TenantBillInitDTO tenantBillInitDTO) {
//        tenantBillOfflineBuilder.initTenantBill(Sets.newHashSet(tenantBillInitDTO.getTenantId()),
//            tenantBillInitDTO.getStartTime(), tenantBillInitDTO.getEndTime());
//        return CommonResult.ok();
//    }
}
