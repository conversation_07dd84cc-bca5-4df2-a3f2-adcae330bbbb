package com.cosfo.mall.tenant.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 预付账户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface TenantPrepaymentAccountDao extends IService<TenantPrepaymentAccount> {

    /**
     * 查询品牌给供应商预付的信息
     * @param tenantId
     * @param supplierTenantId
     * @return
     */
    List<TenantPrepaymentAccount> queryTenant4SupplierPrepay(Long tenantId, Long supplierTenantId);

    /**
     * 扣减可用金额
     * @param id
     * @param changeAmount
     * @return
     */
    int decreaseAvailableAmount(Long id, BigDecimal changeAmount);

    /**
     * 增加可用金额
     * @param id
     * @param changeAmount
     * @return
     */
    int increaseAvailableAmount(Long id, BigDecimal changeAmount);

    /**
     * 根据主键查询(包含排他锁)
     * @param id
     * @return
     */
    TenantPrepaymentAccount selectByIdForUpdate(Long id);
}
