package com.cosfo.mall.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.tenant.dao.TenantPrepaymentTransactionDao;
import com.cosfo.mall.tenant.mapper.TenantPrepaymentTransactionMapper;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 预付交易明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class TenantPrepaymentTransactionDaoImpl extends ServiceImpl<TenantPrepaymentTransactionMapper, TenantPrepaymentTransaction> implements TenantPrepaymentTransactionDao {

    @Override
    public List<TenantPrepaymentTransaction> queryByAssociatedOrderNos(Long tenantId, List<String> associatedOrderNos) {
        LambdaQueryWrapper<TenantPrepaymentTransaction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrepaymentTransaction::getTenantId, tenantId);
        queryWrapper.in(TenantPrepaymentTransaction::getAssociatedOrderNo, associatedOrderNos);
        return list(queryWrapper);
    }
}
