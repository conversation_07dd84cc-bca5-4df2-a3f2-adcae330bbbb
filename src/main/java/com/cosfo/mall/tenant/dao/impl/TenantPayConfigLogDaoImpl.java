package com.cosfo.mall.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.tenant.dao.TenantPayConfigLogDao;
import com.cosfo.mall.tenant.mapper.TenantPayConfigLogMapper;
import com.cosfo.mall.tenant.model.po.TenantPayConfigLog;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/22
 */
@Service
public class TenantPayConfigLogDaoImpl extends ServiceImpl<TenantPayConfigLogMapper, TenantPayConfigLog> implements TenantPayConfigLogDao {

    @Override
    public List<TenantPayConfigLog> queryByHuiFuId(Long tenantId,String huiFuId, String payMchId) {
        LambdaQueryWrapper<TenantPayConfigLog> lambdaQueryWrapper =  new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(Objects.nonNull(huiFuId) , TenantPayConfigLog::getHuifuId, huiFuId);
        lambdaQueryWrapper.eq(Objects.nonNull(tenantId), TenantPayConfigLog::getTenantId, tenantId);
        lambdaQueryWrapper.eq(Objects.nonNull(payMchId), TenantPayConfigLog::getPayMchId, payMchId);
        lambdaQueryWrapper.orderByDesc(TenantPayConfigLog::getId);
        return list(lambdaQueryWrapper);
    }
}
