package com.cosfo.mall.tenant.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;

import java.util.List;

/**
 * <p>
 * 预付交易明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface TenantPrepaymentTransactionDao extends IService<TenantPrepaymentTransaction> {

    /**
     * 根据相关单号查询
     * @param tenantId
     * @param associatedOrderNos
     * @return
     */
    List<TenantPrepaymentTransaction> queryByAssociatedOrderNos(Long tenantId, List<String> associatedOrderNos);
}
