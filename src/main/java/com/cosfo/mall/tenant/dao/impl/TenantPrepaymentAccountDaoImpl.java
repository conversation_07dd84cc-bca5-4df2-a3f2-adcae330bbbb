package com.cosfo.mall.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.tenant.dao.TenantPrepaymentAccountDao;
import com.cosfo.mall.tenant.mapper.TenantPrepaymentAccountMapper;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 预付账户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class TenantPrepaymentAccountDaoImpl extends ServiceImpl<TenantPrepaymentAccountMapper, TenantPrepaymentAccount> implements TenantPrepaymentAccountDao {

    @Resource
    private TenantPrepaymentAccountMapper tenantPrepaymentAccountMapper;

    @Override
    public List<TenantPrepaymentAccount> queryTenant4SupplierPrepay(Long tenantId, Long supplierTenantId) {
        return tenantPrepaymentAccountMapper.queryTenant4SupplierPrepay(tenantId, supplierTenantId);
    }

    @Override
    public int decreaseAvailableAmount(Long id, BigDecimal changeAmount) {
        return tenantPrepaymentAccountMapper.decreaseAvailableAmount(id, changeAmount);
    }

    @Override
    public int increaseAvailableAmount(Long id, BigDecimal changeAmount) {
        return tenantPrepaymentAccountMapper.increaseAvailableAmount(id, changeAmount);
    }

    @Override
    public TenantPrepaymentAccount selectByIdForUpdate(Long id) {
        return tenantPrepaymentAccountMapper.selectByIdForUpdate(id);
    }
}
