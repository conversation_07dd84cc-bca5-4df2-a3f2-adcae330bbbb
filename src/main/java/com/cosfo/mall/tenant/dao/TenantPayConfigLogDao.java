package com.cosfo.mall.tenant.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.tenant.model.po.TenantPayConfigLog;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/22
 */
public interface TenantPayConfigLogDao extends IService<TenantPayConfigLog> {

    /**
     * 查询历史支付配置信息
     *
     * @param tenantId
     * @param huiFuId
     * @Param payMchId
     * @return
     */
    List<TenantPayConfigLog> queryByHuiFuId(Long tenantId,String huiFuId, String payMchId);
}
