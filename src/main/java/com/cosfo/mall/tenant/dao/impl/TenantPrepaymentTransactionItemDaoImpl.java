package com.cosfo.mall.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.tenant.dao.TenantPrepaymentTransactionItemDao;
import com.cosfo.mall.tenant.mapper.TenantPrepaymentTransactionItemMapper;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 预付交易明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Service
public class TenantPrepaymentTransactionItemDaoImpl extends ServiceImpl<TenantPrepaymentTransactionItemMapper, TenantPrepaymentTransactionItem> implements TenantPrepaymentTransactionItemDao {

    @Override
    public List<TenantPrepaymentTransactionItem> queryByOrderId(Long tenantId, Long orderId) {
        LambdaQueryWrapper<TenantPrepaymentTransactionItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrepaymentTransactionItem::getTenantId, tenantId);
        queryWrapper.eq(TenantPrepaymentTransactionItem::getOrderId, orderId);
        return list(queryWrapper);
    }

    @Override
    public List<TenantPrepaymentTransactionItem> queryByOrderItemId(Long tenantId, Long orderItemId, Integer transactionType) {
        LambdaQueryWrapper<TenantPrepaymentTransactionItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrepaymentTransactionItem::getTenantId, tenantId);
        queryWrapper.eq(TenantPrepaymentTransactionItem::getOrderItemId, orderItemId);
        queryWrapper.eq(Objects.nonNull(transactionType), TenantPrepaymentTransactionItem::getOrderItemId, orderItemId);
        return list(queryWrapper);
    }

    @Override
    public List<TenantPrepaymentTransactionItem> queryByOrderIds(Long tenantId, Collection<Long> orderIds) {
        if (Objects.isNull(orderIds) || orderIds.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TenantPrepaymentTransactionItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrepaymentTransactionItem::getTenantId, tenantId);
        queryWrapper.in(TenantPrepaymentTransactionItem::getOrderId, orderIds);
        return list(queryWrapper);
    }
}
