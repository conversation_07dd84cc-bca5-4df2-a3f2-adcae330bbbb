package com.cosfo.mall.tenant.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 预付交易明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
public interface TenantPrepaymentTransactionItemDao extends IService<TenantPrepaymentTransactionItem> {

    /**
     * 根据订单id查询
     * @param tenantId
     * @param orderId
     * @return
     */
    List<TenantPrepaymentTransactionItem> queryByOrderId(Long tenantId, Long orderId);

    /**
     * 根据订单明细查询
     * @param tenantId
     * @param orderItemId
     * @param transactionType
     * @return
     */
    List<TenantPrepaymentTransactionItem> queryByOrderItemId(Long tenantId, Long orderItemId, Integer transactionType);

    /**
     * 查询交易记录
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<TenantPrepaymentTransactionItem> queryByOrderIds(Long tenantId, Collection<Long> orderIds);
}
