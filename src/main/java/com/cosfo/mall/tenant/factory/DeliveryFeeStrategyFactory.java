package com.cosfo.mall.tenant.factory;

import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.tenant.service.DeliveryFeeStrategy;
import com.cosfo.mall.tenant.service.impl.CustomizeDeliveryFeeStrategy;
import com.cosfo.mall.tenant.service.impl.FollowSupplierDeliveryFeeStrategy;
import com.cosfo.mall.tenant.service.impl.FreeDeliveryFeeStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/17
 */
@Component
public class DeliveryFeeStrategyFactory {
    private static List<DeliveryFeeStrategy> deliveryFeeStrategyList = new LinkedList<>();
    /**
     * 免运费
     */
    @Resource
    private FreeDeliveryFeeStrategy freeDeliveryFeeStrategy;
    /**
     * 跟随供应商
     */
    @Resource
    private FollowSupplierDeliveryFeeStrategy followSupplierDeliveryFeeStrategy;
    /**
     * 自定义
     */
    @Resource
    private CustomizeDeliveryFeeStrategy customizeDeliveryFeeStrategy;

    @PostConstruct
    public void init() {
        deliveryFeeStrategyList.add(freeDeliveryFeeStrategy);
        deliveryFeeStrategyList.add(followSupplierDeliveryFeeStrategy);
        deliveryFeeStrategyList.add(customizeDeliveryFeeStrategy);
    }

    public static DeliveryFeeStrategy getByType(Integer type){
        for (DeliveryFeeStrategy deliveryFeeStrategy:deliveryFeeStrategyList){
            if(deliveryFeeStrategy.support(type)){
                return deliveryFeeStrategy;
            }
        }

        throw new DefaultServiceException(ResultDTOEnum.PROFIT_SHARING_HANDLE_NOT_EXIST.getCode(), ResultDTOEnum.PROFIT_SHARING_HANDLE_NOT_EXIST.getMessage());
    }
}
