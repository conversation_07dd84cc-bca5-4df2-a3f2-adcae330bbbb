package com.cosfo.mall.tenant.mapper;

import com.cosfo.mall.tenant.model.po.TenantCommonConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TenantCommonConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TenantCommonConfig record);

    int insertSelective(TenantCommonConfig record);

    TenantCommonConfig selectByPrimaryKey(Long id);

    TenantCommonConfig selectByTenantIdAndConfigKey(@Param("tenantId") Long tenantId, @Param("configKey") String configKey);

    int updateConfigValueById(TenantCommonConfig record);

    int updateByPrimaryKeySelective(TenantCommonConfig record);

    int updateByPrimaryKey(TenantCommonConfig record);
}