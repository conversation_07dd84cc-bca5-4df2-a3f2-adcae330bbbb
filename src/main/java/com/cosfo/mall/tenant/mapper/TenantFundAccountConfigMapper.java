package com.cosfo.mall.tenant.mapper;

import com.cosfo.mall.tenant.model.po.TenantFundAccountConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: 租户资金账户配置表
 * @author: <PERSON>
 * @date: 2025-04-21
 **/
@Mapper
public interface TenantFundAccountConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantFundAccountConfig record);

    int insertSelective(TenantFundAccountConfig record);

    TenantFundAccountConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantFundAccountConfig record);

    int updateByPrimaryKey(TenantFundAccountConfig record);

    /**
     * 根据租户Id查询资金账户配置
     *
     * @param tenantId
     * @return
     */
    TenantFundAccountConfig selectByTenantId(Long tenantId);
}