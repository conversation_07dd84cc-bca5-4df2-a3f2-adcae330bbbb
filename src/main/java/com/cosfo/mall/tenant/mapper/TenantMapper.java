//package com.cosfo.mall.tenant.mapper;
//
//import com.cosfo.mall.tenant.model.dto.TenantDTO;
//import com.cosfo.mall.tenant.model.po.Tenant;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * @desc 租户持久层
// * <AUTHOR>
// * @date 2022/5/22
// */
//@Deprecated
//@Mapper
//public interface TenantMapper {
//
////    /**
////     * 删除
////     * @param id
////     * @return
////     */
////    int deleteByPrimaryKey(Long id);
////
////    /**
////     * 新增
////     * @param record
////     * @return
////     */
////    int insert(Tenant record);
////
////    /**
////     * 新增
////     * @param record
////     * @return
////     */
////    int insertSelective(Tenant record);
////
////    /**
////     * 查询
////     * @param id
////     * @return
////     */
////    Tenant selectByPrimaryKey(Long id);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKeySelective(Tenant record);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKey(Tenant record);
////
////    /**
////     * 根据手机号查询
////     *
////     * @param phone
////     * @return
////     */
////    Tenant selectByPhone(@Param("phone") String phone);
////
////    /**
////     * 查询供应商信息
////     *
////     * @param supplierTenantIds
////     * @return
////     */
////    List<Tenant> querySupplierInfoBySupplierTenantIds(@Param("supplierTenantIds") List<Long> supplierTenantIds);
////
////    /**
////     * 通过鲜沐大客户adminId查询品牌方租户信息
////     *
////     * @param adminId
////     * @return
////     */
////    TenantDTO selectByAdminId(@Param("adminId") Long adminId);
////
////    /**
////     * 根据租户类型查询品牌方
////     *
////     * @param type 1供应商 2帆台
////     * @return
////     */
////    TenantDTO selectByType(Integer type);
//}
