package com.cosfo.mall.tenant.mapper;

import com.cosfo.mall.tenant.model.po.TenantBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TenantBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantBill record);

    int insertSelective(TenantBill record);

    TenantBill selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantBill record);

    int updateByPrimaryKey(TenantBill record);

    int batchInsert(@Param("list") List<TenantBill> tenantBillList);

    /**
     * 根据租户Id和recordNo查询
     *
     * @param tenantId
     * @param recordNo
     * @return
     */
    TenantBill queryByTenantIdAndRecordNo(@Param("tenantId") Long tenantId,
                                          @Param("recordNo") String recordNo);
}