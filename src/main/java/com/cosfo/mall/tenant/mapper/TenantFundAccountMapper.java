package com.cosfo.mall.tenant.mapper;

import com.cosfo.mall.tenant.model.po.TenantFundAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: ${description}
 * @author: <PERSON>
 * @date: 2025-05-29
 **/
@Mapper
public interface TenantFundAccountMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantFundAccount record);

    int insertSelective(TenantFundAccount record);

    TenantFundAccount selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantFundAccount record);

    int updateByPrimaryKey(TenantFundAccount record);

    /**
     * 根据ids查询
     *
     * @param ids
     * @return
     */
    List<TenantFundAccount> queryByIds(@Param("ids") List<Long> ids);
}