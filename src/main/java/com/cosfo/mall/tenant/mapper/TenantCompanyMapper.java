//package com.cosfo.mall.tenant.mapper;
//
//import com.cosfo.mall.tenant.model.po.TenantCompany;
//import org.apache.ibatis.annotations.Mapper;
//
///**
// * <AUTHOR>
// */
//@Deprecated
//@Mapper
//public interface TenantCompanyMapper {
//
////    /**
////     * 删除
////     * @param id
////     * @return
////     */
////    int deleteByPrimaryKey(Long id);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insert(TenantCompany record);
////
////    /**
////     * 插入
////     * @param record
////     * @return
////     */
////    int insertSelective(TenantCompany record);
////
////    /**
////     * 查询
////     * @param id
////     * @return
////     */
////    TenantCompany selectByPrimaryKey(Long id);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKeySelective(TenantCompany record);
////
////    /**
////     * 更新
////     * @param record
////     * @return
////     */
////    int updateByPrimaryKey(TenantCompany record);
////
////    /**
////     * 根据tenantId查询
////     * @param tenantId
////     * @return
////     */
////    TenantCompany selectByTenantId(Long tenantId);
//}
