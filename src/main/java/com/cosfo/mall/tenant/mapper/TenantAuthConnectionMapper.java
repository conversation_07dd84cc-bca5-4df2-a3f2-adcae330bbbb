package com.cosfo.mall.tenant.mapper;

import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
public interface TenantAuthConnectionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TenantAuthConnection record);

    int insertSelective(TenantAuthConnection record);

    TenantAuthConnection selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantAuthConnection record);

    int updateByPrimaryKey(TenantAuthConnection record);

    /**
     * 使用租户id查询认证小程序信息
     * @param tenantId 租户id
     * @return 认证信息
     */
    TenantAuthConnection selectByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 查询小程序认证信息
     * @param appId appId
     * @return 认证信息
     */
    TenantAuthConnection selectByAppId(@Param("appId") String appId);
    /**
     * 查询
     * @param query
     * @return
     */
    TenantAuthConnection selectOne(TenantAuthConnection query);

    /**
     * 根据汇付id查询支付信息
     * @param huifuId 商户信息
     * @return 支付信息
     */
    List<TenantAuthConnection> selectByHuifuId(String huifuId);
}
