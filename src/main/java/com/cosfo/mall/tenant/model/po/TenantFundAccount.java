package com.cosfo.mall.tenant.model.po;


/**
 * @description: ${description}
 * @author: <PERSON>
 * @date: 2025-05-29
 **/

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户资金账户表
 * <AUTHOR>
 */
@Data
public class TenantFundAccount {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 配置ID（tenant_fund_account_config.id）
     */
    private Long configId;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}