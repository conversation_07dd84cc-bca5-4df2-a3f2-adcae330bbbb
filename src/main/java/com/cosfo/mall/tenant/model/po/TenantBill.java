package com.cosfo.mall.tenant.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * tenant_bill
 * <AUTHOR>
@Data
public class TenantBill implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 账单金额
     */
    private BigDecimal billPrice;

    /**
     * 类型：0、收入 1、支出
     */
    private Integer type;

    /**
     * 订单编号、售后单编号
     */
    private String recordNo;

    /**
     * 支付类型：1微信 2账期 3余额 4支付宝
     */
    private Integer paymentType;

    /**
     * 支付渠道:0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 手续费
     */
    private BigDecimal feeAmount;

    private static final long serialVersionUID = 1L;
}
