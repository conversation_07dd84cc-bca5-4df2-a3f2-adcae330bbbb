package com.cosfo.mall.tenant.model.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tenant_common_config 租户公共配置表
 * <AUTHOR>
 */
@Data
public class TenantCommonConfig implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 配置名称
     */
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}