package com.cosfo.mall.tenant.model.bo;

import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/3/29 11:50
 */
@Data
public class TenantPrepaymentCalculateBO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单运费
     */
    private BigDecimal orderDeliveryFee;

    /**
     * 订单明细id
     */
    private Long orderItemId;
    /**
     * 订单项数量
     */
    private Integer orderItemAmount;

    /**
     * 订单项金额
     */
    private BigDecimal orderItemPrice;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 售后运费
     */
    private BigDecimal refundDeliveryFee;

    /**
     * 售后金额
     */
    private BigDecimal refundPrice;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 最后一次售后标识
     */
    private Boolean lastOrderAfterSaleFlag;

    /**
     * 售后单号集合
     */
    private List<String> orderAfterSaleNos;

    /**
     * 交易
     */
    private List<TenantPrepaymentTransaction> transactionList;
}
