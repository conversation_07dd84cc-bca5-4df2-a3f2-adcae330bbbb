package com.cosfo.mall.tenant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * tenant_auth_connection
 * <AUTHOR>
@Data
public class TenantAuthConnection implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 授权人APPID
     */
    private String appId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 1-正常
     */
    private Integer status;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 直连支付商户号
     */
    private String payMchid;

    /**
     * 支付秘钥
     */
    private String paySecret;

    /**
     * 证书路径
     */
    private String payCertPath;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 汇付商号
     */
    private String huifuId;

    /**
     * 租户汇付私钥
     */
    private String secretKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 汇付公钥
     */
    private String huifuPublicKey;

    /**
     * 公众号appId
     */
    private String oaAppId;

    /**
     * 公众号app secret
     */
    private String oaAppSecret;

    /**
     * 小程序微信直连开关 0、关闭 1、开启
     */
    private Integer wechatDirectSwitch;
    /**
     * 小程序微信间连开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSwitch;
    /**
     * 小程序微信间连分账开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSharingSwitch;
    /**
     * H5支付宝间连开关 0、关闭 1、开启
     */
    private Integer aliIndirectSwitch;
    /**
     * H5支付宝间连分账开关 0、关闭 1、开启
     */
    private Integer aliIndirectSharingSwitch;

    /**
     * H5微信间连开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSwitch;

    /**
     * H5微信间连分账开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSharingSwitch;

    /**
     * 小程序微信间联-汇付插件开关 0、关闭 1、开启
     */
    private Integer wechatIndirectPluginSwitch;

    /**
     * 小程序微信支付开关 0、关闭 1开启
     */
    private Integer appletWechatPaySwitch;

    /**
     * 间连渠道 1、汇付 2、智付
     */
    private Integer indirectOnlineChannel;

    private static final long serialVersionUID = 1L;
}
