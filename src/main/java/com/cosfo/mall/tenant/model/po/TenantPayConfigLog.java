package com.cosfo.mall.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/22
 */
@Data
@TableName("tenant_pay_config_log")
public class TenantPayConfigLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 直连支付商户号
     */
    @TableField("pay_mchid")
    private String payMchId;

    /**
     * 支付密钥
     */
    @TableField("pay_secret")
    private String paySecret;

    /**
     * 证书路径
     */
    @TableField("pay_cert_path")
    private String payCertPath;

    /**
     * 汇付商户号
     */
    @TableField("huifu_id")
    private String huifuId;

    /**
     * 汇付支付私钥
     */
    @TableField("secret_key")
    private String secretKey;

    /**
     * 支付公钥
     */
    @TableField("public_key")
    private String publicKey;

    /**
     * 汇付公钥
     */
    @TableField("huifu_public_key")
    private String huifuPublicKey;

    /**
     * 操作人Id
     */
    @TableField("tenant_account_id")
    private Long tenantAccountId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}
