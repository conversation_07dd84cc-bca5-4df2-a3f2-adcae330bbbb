package com.cosfo.mall.tenant.model.po;

import java.util.Date;
import lombok.Data;

/**
 * 描述:ten_tenant表的实体类
 * @version
 * @author:  Song
 * @创建时间: 2022-05-06
 */
@Data
public class Tenant {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型：0、入驻商户
     */
    private Integer type;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 归属DB
     */
    private String belongDB;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 大客户adminId
     */
    private Long adminId;

    /**
     * 分账开关
     */
    @Deprecated
    private Integer profitSharingSwitch;

    /**
     * 支付渠道 0 微信 1 汇付
     */
    @Deprecated
    private Integer onlinePayChannel;
}