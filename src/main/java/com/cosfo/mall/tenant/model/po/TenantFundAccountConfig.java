package com.cosfo.mall.tenant.model.po;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @description: 租户资金账户配置表
 * @author: <PERSON>
 * @date: 2025-04-21
 **/
@Data
public class TenantFundAccountConfig {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 限制商品分组（逗号分隔的分组编码）
     */
    private String limitGoodsGroup;

    /**
     * 是否可支付运费，1-是，0-否
     */
    private Integer allowShippingFee;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}