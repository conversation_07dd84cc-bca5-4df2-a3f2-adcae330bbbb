package com.cosfo.mall.tenant.model.bo;

import com.cosfo.mall.order.model.bo.OrderItemNeedPrepayBO;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransaction;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentTransactionItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc 预付交易业务对象
 * <AUTHOR>
 * @date 2023/3/18 17:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantPrepaymentTransactionBO {

    /**
     * 订单金额
     */
    private String orderPrice;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 供应价费用
     */
    private BigDecimal needPrepaySupplyFee;

    /**
     * 代仓费用
     */
    private BigDecimal needPrepayAgentFee;


    /**
     * 供应价预付
     */
    private BigDecimal supplyPayableAmount;

    /**
     * 代仓费用预付
     */
    private BigDecimal agentPayableAmount;

    /**
     * 供应价和代仓费用预付
     */
    private BigDecimal supplyAndAgentPayableAmount;


    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 供应价需要预付的
     */
    private List<OrderItemNeedPrepayBO> needPrepaySupplyList;

    /**
     * 代仓需要预付的
     */
    private List<OrderItemNeedPrepayBO> needPrepayAgentList;

    /**
     * 供应价运费
     */
    private BigDecimal needPrepaySupplyDeliveryFee;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 供应价账户
     */
    private TenantPrepaymentAccount supplyPayableAccount;

    /**
     * 代仓费用账户
     */
    private TenantPrepaymentAccount agentPayableAccount;

    /**
     * 供应价、代仓费用账户
     */
    private TenantPrepaymentAccount supplyAndAgentPayableAccount;

    /**
     * 预付交易item明细
     */
    private List<TenantPrepaymentTransactionItem> tenantPrepaymentTransactionItems;

    /**
     * 预付交易明细
     */
    List<TenantPrepaymentTransaction> tenantPrepaymentTransactions;

    /**
     * 总退款金额
     */
    private BigDecimal totalRefundAmount;

}
