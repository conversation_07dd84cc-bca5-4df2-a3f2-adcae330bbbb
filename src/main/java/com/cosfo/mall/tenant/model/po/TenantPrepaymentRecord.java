package com.cosfo.mall.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 预付记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantPrepaymentRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应商id
     */
    private Long supplierTenantId;

    /**
     * 预收/预付金额
     */
    private BigDecimal transactionAmount;

    /**
     * 0、供应商直供 1、代仓费用 2、供应商直供和代仓费用
     */
    private Integer payableTarget;

    /**
     * 0、待审核 1、审核通过 2、 审核失败
     */
    private Integer auditStatus;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 凭证
     */
    private String proof;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人id
     */
    private Long auditorId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 交易类型 0、预付 1、预收
     */
    private Integer transactionType;


}
