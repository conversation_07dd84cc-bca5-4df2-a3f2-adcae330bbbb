package com.cosfo.mall.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 预付交易明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantPrepaymentTransactionItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单明细id
     */
    private Long orderItemId;

    /**
     * 预付账户id
     */
    private Long prepaymentAccountId;

    /**
     * 交易类型 0、预付 1、预付退款 2、直供货品消费 3、直供货品退款 4、运费 5、运费退款 6、代仓费用 7、代仓费用退款
     */
    private Integer transactionType;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 扣减类型
     * 1、实际扣减
     *
     * @see com.cosfo.mall.common.context.DecreaseTypeEnum
     */
    private Integer decreaseType;


}
