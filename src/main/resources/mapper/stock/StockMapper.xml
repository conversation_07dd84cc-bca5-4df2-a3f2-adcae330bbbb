<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.stock.mapper.StockMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.stock.model.po.Stock">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, item_id, amount, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.stock.model.po.Stock" useGeneratedKeys="true">
    insert into stock (tenant_id, item_id, amount,
      update_time, create_time)
    values (#{tenantId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, #{amount,jdbcType=INTEGER},
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.stock.model.po.Stock" useGeneratedKeys="true">
    insert into stock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.stock.model.po.Stock">
    update stock
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.stock.model.po.Stock">
    update stock
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock
    where tenant_id = #{tenantId} and item_id = #{itemId,jdbcType=BIGINT}
  </select>
  <update id="increaseStock">
    update stock set amount = amount + #{addAmount} where id = #{id}
  </update>

  <select id="batchQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock
    where tenant_id = #{tenantId}
      and item_id in
    <foreach collection="itemIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="preUpdateQuery" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock
    where tenant_id = #{tenantId} and item_id = #{itemId,jdbcType=BIGINT} for update
  </select>
</mapper>
