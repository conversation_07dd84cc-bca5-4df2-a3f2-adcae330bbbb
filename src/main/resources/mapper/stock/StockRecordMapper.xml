<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.stock.mapper.StockRecordMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.stock.model.po.StockRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="stock_sku_id" jdbcType="BIGINT" property="stockSkuId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="before_amount" jdbcType="INTEGER" property="beforeAmount" />
    <result column="change_amount" jdbcType="INTEGER" property="changeAmount" />
    <result column="after_amount" jdbcType="INTEGER" property="afterAmount" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="record_no" jdbcType="VARCHAR" property="recordNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, stock_sku_id, `type`, before_amount, change_amount, after_amount, 
    update_time, create_time, record_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.stock.model.po.StockRecord" useGeneratedKeys="true">
    insert into stock_record (tenant_id, stock_sku_id, `type`, 
      before_amount, change_amount, after_amount, 
      update_time, create_time, record_no
      )
    values (#{tenantId,jdbcType=BIGINT}, #{stockSkuId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, 
      #{beforeAmount,jdbcType=INTEGER}, #{changeAmount,jdbcType=INTEGER}, #{afterAmount,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{recordNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.stock.model.po.StockRecord" useGeneratedKeys="true">
    insert into stock_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="stockSkuId != null">
        stock_sku_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="beforeAmount != null">
        before_amount,
      </if>
      <if test="changeAmount != null">
        change_amount,
      </if>
      <if test="afterAmount != null">
        after_amount,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="recordNo != null">
        record_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="stockSkuId != null">
        #{stockSkuId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="beforeAmount != null">
        #{beforeAmount,jdbcType=INTEGER},
      </if>
      <if test="changeAmount != null">
        #{changeAmount,jdbcType=INTEGER},
      </if>
      <if test="afterAmount != null">
        #{afterAmount,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.stock.model.po.StockRecord">
    update stock_record
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="stockSkuId != null">
        stock_sku_id = #{stockSkuId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="beforeAmount != null">
        before_amount = #{beforeAmount,jdbcType=INTEGER},
      </if>
      <if test="changeAmount != null">
        change_amount = #{changeAmount,jdbcType=INTEGER},
      </if>
      <if test="afterAmount != null">
        after_amount = #{afterAmount,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordNo != null">
        record_no = #{recordNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.stock.model.po.StockRecord">
    update stock_record
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      stock_sku_id = #{stockSkuId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=INTEGER},
      before_amount = #{beforeAmount,jdbcType=INTEGER},
      change_amount = #{changeAmount,jdbcType=INTEGER},
      after_amount = #{afterAmount,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      record_no = #{recordNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>