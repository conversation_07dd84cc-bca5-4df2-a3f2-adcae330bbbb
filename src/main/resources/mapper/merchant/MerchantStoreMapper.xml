<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantStoreMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.merchant.model.po.MerchantStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bill_switch" property="billSwitch"/>
    <result column="online_payment" property="onlinePayment"/>
    <result column="balance_authority" property="balanceAuthority"/>
    <result column="store_no" property="storeNo"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_name, `type`, register_time, `status`, audit_remark, audit_time,
    create_time, update_time, bill_switch, online_payment, store_no, balance_authority
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_store
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="countStoreNum" resultType="java.lang.Long">
      select ifnull(count(*), 0)
      from merchant_store
      where tenant_id = #{tenantId}
    </select>
  <select id="selectByName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store
    where store_name = #{storeName} and tenant_id = #{tenantId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_store
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantStore" useGeneratedKeys="true">
    insert into merchant_store (tenant_id, store_name, `type`,
      register_time, `status`, audit_remark,
      audit_time, bill_switch, online_payment, store_no
      )
    values (#{tenantId,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
      #{registerTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{auditRemark,jdbcType=VARCHAR},
      #{auditTime,jdbcType=TIMESTAMP},  #{billSwitch}, #{onlinePayment}, #{storeNo,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantStore" useGeneratedKeys="true">
    insert into merchant_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="billSwitch != null">
        bill_switch,
      </if>
      <if test="onlinePayment != null">
        online_payment,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=BIGINT},
      </if>
      <if test="billSwitch != null">
        #{billSwitch},
      </if>
      <if test="onlinePayment != null">
        #{onlinePayment},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.merchant.model.po.MerchantStore">
    update merchant_store
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=BIGINT},
      </if>
      <if test="billSwitch != null">
        bill_switch = #{billSwitch},
      </if>
      <if test="onlinePayment != null">
        online_payment = #{onlinePayment},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.merchant.model.po.MerchantStore">
    update merchant_store
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      register_time = #{registerTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      store_no = #{storeNo,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
