<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantDeliveryFeeRuleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="rule_type" property="ruleType"/>
    <result column="price_type" property="priceType"/>
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="free_delivery_price" jdbcType="DECIMAL" property="freeDeliveryPrice" />
    <result column="relate_number" property="relateNumber"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="free_delivery_type" jdbcType="INTEGER" property="freeDeliveryType" />
    <result column="free_delivery_quantity" jdbcType="INTEGER" property="freeDeliveryQuantity" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `type`, rule_type, price_type, delivery_fee, free_delivery_price, relate_number, create_time, update_time, free_delivery_type, free_delivery_quantity
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_delivery_fee_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectOne" resultMap="BaseResultMap">
    select
    rule.id, rule.tenant_id, rule.`type`, rule.rule_type, rule.price_type, rule.delivery_fee, rule.free_delivery_price, rule.relate_number, rule.create_time, rule.update_time, rule.free_delivery_type, rule.free_delivery_quantity
    from merchant_delivery_fee_rule rule
    left join merchant_delivery_rule_warehouse_relation r on rule.tenant_id = r.tenant_id and rule.id= r.rule_id
    <where>
      <if test="type != null">
        and rule.type = #{type}
      </if>
      <if test="tenantId != null">
        and rule.tenant_id = #{tenantId}
      </if>
      <if test="warehouseNo != null">
        and r.warehouse_no = #{warehouseNo} and rule.default_type = 0
      </if>
      <if test="warehouseNo == null">
       and rule.default_type = 1
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_delivery_fee_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule" useGeneratedKeys="true">
    insert into merchant_delivery_fee_rule (tenant_id, `type`, rule_type, price_type, delivery_fee,
      free_delivery_price, relate_number, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{ruleType}, #{priceType}, #{deliveryFee,jdbcType=DECIMAL},
      #{freeDeliveryPrice,jdbcType=DECIMAL}, #{relateNumber}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule" useGeneratedKeys="true">
    insert into merchant_delivery_fee_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="freeDeliveryPrice != null">
        free_delivery_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="freeDeliveryPrice != null">
        #{freeDeliveryPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule">
    update merchant_delivery_fee_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="freeDeliveryPrice != null">
        free_delivery_price = #{freeDeliveryPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.merchant.model.po.MerchantDeliveryFeeRule">
    update merchant_delivery_fee_rule
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      free_delivery_price = #{freeDeliveryPrice,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
