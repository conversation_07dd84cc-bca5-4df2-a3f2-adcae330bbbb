<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantStoreAccountMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.merchant.model.po.MerchantStoreAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, account_name, phone, `type`, register_time, audit_time,
    open_id, union_id, create_time, update_time, status, last_login_time, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_store_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_store_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantStoreAccount" useGeneratedKeys="true">
    insert into merchant_store_account (tenant_id, store_id, account_name,
      phone, `type`, register_time,
      audit_time, open_id, union_id, status)
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{accountName,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{registerTime,jdbcType=TIMESTAMP},
      #{auditTime,jdbcType=TIMESTAMP}, #{openId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{status})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantStoreAccount" useGeneratedKeys="true">
    insert into merchant_store_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.merchant.model.po.MerchantStoreAccount">
    update merchant_store_account
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.merchant.model.po.MerchantStoreAccount">
    update merchant_store_account
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      account_name = #{accountName,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      register_time = #{registerTime,jdbcType=TIMESTAMP},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      open_id = #{openId,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_store_account
    where
       id = #{accountId}
       and tenant_id = #{tenantId}
  </select>
  <select id="selectOne" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_store_account
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="openId != null">
        and open_id = #{openId}
      </if>
      <if test="storeId != null">
        and store_id = #{storeId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="phone != null">
        and phone = #{phone}
      </if>
      <if test="deleteFlag != null">
        and delete_flag = #{deleteFlag}
      </if>
    </where>
  </select>

  <select id="selectByPhoneAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_store_account
    where
        phone = #{phone}
        and tenant_id = #{tenantId} and delete_flag = 1
  </select>

  <select id="queryManagerInfo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store_account
    where type = 0 and status = 1 and store_id = #{storeId}
  </select>
  <select id="selectByStoreId" resultType="com.cosfo.mall.merchant.model.dto.MerchantStoreAccountDTO">
    select id, tenant_id tenantId, store_id storeId, account_name accountName, phone, `type`, register_time registerTime, audit_time auditTime,
           open_id openId, union_id unionId, status, last_login_time lastLoginTime, delete_flag deleteFlag
    from merchant_store_account
    where tenant_id = #{tenantId} and store_id = #{storeId} and delete_flag = 1
  </select>
</mapper>
