<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantContactMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.merchant.model.po.MerchantContact">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="address_id" jdbcType="BIGINT" property="addressId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="default_flag" jdbcType="INTEGER" property="defaultFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, address_id, `name`, phone, default_flag,
      create_time, update_time
  </sql>
  <insert id="insert" parameterType="com.cosfo.mall.merchant.model.po.MerchantContact">
    insert into merchant_contact (id, tenant_id, address_id,
      `name`, phone, default_flag)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{addressId,jdbcType=BIGINT},
      #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{defaultFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cosfo.mall.merchant.model.po.MerchantContact">
    insert into merchant_contact
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="addressId != null">
        address_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="defaultFlag != null">
        default_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="addressId != null">
        #{addressId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="defaultFlag != null">
        #{defaultFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective">
    update merchant_contact
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="addressId != null">
        address_id = #{addressId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="defaultFlag != null">
        default_flag = #{defaultFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_contact
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="queryDefaultContact" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_contact
    where tenant_id = #{tenantId}
    and address_id = #{addressId}
    and default_flag = 1
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_contact
    where tenant_id = #{tenantId}
    and id = #{merchantContactId}
  </select>

  <select id="queryMerchantContact" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_contact
    where tenant_id = #{tenantId}
    and address_id = #{addressId}
  </select>
  <select id="selectByAddressId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_contact
    where tenant_id = #{tenantId}
    and address_id = #{addressId}
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_contact
    where id = #{id}
  </select>

</mapper>
