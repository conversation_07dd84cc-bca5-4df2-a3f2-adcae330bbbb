<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantAddressMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.merchant.model.po.MerchantAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="house_number" jdbcType="VARCHAR" property="houseNumber" />
    <result column="poi_note" jdbcType="VARCHAR" property="poiNote" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, province, city, area, address, house_number, poi_note, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByStoreId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_address
    where tenant_id = #{tenantId} and store_id = #{storeId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantAddress" useGeneratedKeys="true">
    insert into merchant_address (tenant_id, store_id, province,
                                  city, area, address,
                                  house_number, poi_note, create_time,
                                  update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{province,jdbcType=VARCHAR},
            #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
            #{houseNumber,jdbcType=VARCHAR}, #{poiNote,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.merchant.model.po.MerchantAddress" useGeneratedKeys="true">
    insert into merchant_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="houseNumber != null">
        house_number,
      </if>
      <if test="poiNote != null">
        poi_note,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null">
        #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null">
        #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.merchant.model.po.MerchantAddress">
    update merchant_address
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null">
        house_number = #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null">
        poi_note = #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.merchant.model.po.MerchantAddress">
    update merchant_address
    set tenant_id = #{tenantId,jdbcType=BIGINT},
        store_id = #{storeId,jdbcType=BIGINT},
        province = #{province,jdbcType=VARCHAR},
        city = #{city,jdbcType=VARCHAR},
        area = #{area,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        house_number = #{houseNumber,jdbcType=VARCHAR},
        poi_note = #{poiNote,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
