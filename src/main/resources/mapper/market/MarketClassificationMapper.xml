<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.market.mapper.MarketClassificationMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.market.model.po.MarketClassification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `name`, icon, parent_id, sort, create_time, update_time
  </sql>
  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from market_classification
    <where>
      tenant_id = #{tenantId}
      <if test="name != null">
        and name = #{name}
      </if>
      <if test="parentId != null">
        and parent_id = #{parentId}
      </if>
    </where>
    order by sort
  </select>
</mapper>
