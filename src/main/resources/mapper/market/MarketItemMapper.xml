<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.market.mapper.MarketItemMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.mall.market.model.po.MarketItem">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="brand_name" property="brandName"/>
        <result column="brand_id" property="brandId"/>
        <result column="market_id" property="marketId"/>
        <result column="goods_type" property="goodsType"/>
    </resultMap>
    <resultMap id="DTOMap" type="com.cosfo.mall.market.model.dto.MarketItemDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="sub_title" jdbcType="VARCHAR" property="subTitle"/>
        <result column="origin" jdbcType="VARCHAR" property="origin"/>
        <result column="main_picture" jdbcType="VARCHAR" property="mainPicture"/>
        <result column="detail_picture" jdbcType="VARCHAR" property="detailPicture"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit"/>
        <result column="guarantee_period" jdbcType="VARCHAR" property="guaranteePeriod"/>
        <result column="guarantee_unit" jdbcType="TINYINT" property="guaranteeUnit"/>
        <result column="storage_location" jdbcType="TINYINT" property="storageLocation"/>
        <result column="storage_temperature" jdbcType="VARCHAR" property="storageTemperature"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="brandName" property="brandName"/>
        <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType"/>
        <result column="delivery_type" property="warehouseType"/>
        <result column="mini_order_quantity" jdbcType="INTEGER" property="miniOrderQuantity"/>
        <result column="goods_type" jdbcType="INTEGER" property="goodsType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , tenant_id, sku_id, specification,
    specification_unit, create_time, update_time , brand_name , brand_id, market_id,goods_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from market_item
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="listAll" resultMap="DTOMap">
        select mi.id, m.main_picture, m.title, mi.specification, mi.specification_unit,
        mi.sku_id,mai.mini_order_quantity, mi.goods_type
        from market_item mi
        left join market m on mi.market_id = m.id
        inner join market_area_item mai on mai.item_id = mi.id and mai.tenant_id = #{tenantId}
        <where>
            mi.goods_type in (0,2) and mai.on_sale = 1
            <if test="tenantId != null">
                and mi.tenant_id = #{tenantId}
            </if>
            <if test="title != null">
                and m.title like concat ('%', #{title}, '%')
            </if>
            <if test="marketAreaItemIds != null and marketAreaItemIds.size() > 0">
                and mai.id in
                <foreach collection="marketAreaItemIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="marketIds != null and marketIds.size() > 0">
                and mi.market_id in
                <foreach collection="marketIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="skuIds != null and skuIds.size() > 0">
            union all
            select mi.id, m.main_picture, m.title, mi.specification, mi.specification_unit,
            mi.sku_id,mai.mini_order_quantity, mi.goods_type
            from market_item mi
            left join market m on mi.market_id = m.id
            inner join market_area_item mai on mai.item_id = mi.id and mai.tenant_id = #{tenantId} and mai.on_sale = 1
            <where>
                mi.goods_type = 1 and mai.on_sale = 1
                <if test="tenantId != null">
                    and mi.tenant_id = #{tenantId}
                </if>
                <if test="title != null">
                    and m.title like concat ('%', #{title}, '%')
                </if>
                and mi.sku_id in
                <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                <if test="marketAreaItemIds != null">
                    and mai.id in
                    <foreach collection="marketAreaItemIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="marketIds != null and marketIds.size() > 0">
                    and mi.market_id in
                    <foreach collection="marketIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </where>
        </if>
    </select>

    <select id="selectSupplySkuId" resultType="java.lang.Long">
        select s.supply_sku_id
        from market_item mi
                 left join product_sku sk on mi.tenant_id = sk.tenant_id and mi.sku_id = sk.id
                 left join product_pricing_supply s on sk.tenant_id = s.tenant_id and sk.id = s.sku_id
        where mi.id = #{itemId}
    </select>


    <!-- 批量查询商品信息-->
    <select id="batchByItemIds" resultType="com.cosfo.mall.market.model.vo.MarketItemVO">
        select
        i.market_id marketId, i.id itemId, i.tenant_id tenantId, i.sku_id sku_id, m.title, m.main_picture mainPicture,
        i.specification, i.specification_unit specificationUnit,
        a.on_sale onSale, a.warehouse_type warehouseType, a.delivery_type deliveryType, a.id
        areaItemId,a.mini_order_quantity miniOrderQuantity, i.supplier_name supplierName,
        i.after_sale_unit afterSaleUnit, i.max_after_sale_amount maxAfterSaleAmount,i.delete_flag deleteFlag,
        i.goods_type goodsType, i.supplier_id supplierId, i.item_sale_mode itemSaleMode
        from
        market_item i
        left join market_area_item a on a.item_id = i.id
        left join market m on i.market_id = m.id
        where
        i.tenant_id = #{tenantId}
        <if test="itemIds != null">
            and i.id in
            <foreach collection="itemIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="batchQueryOnSaleByMarketIds" resultMap="DTOMap">
    select mi.id, m.main_picture, m.title, mi.specification, mi.specification_unit, mi.sku_id
    from market_item mi
    left join market m on m.id = mi.market_id
    left join market_area_item a on a.item_id = mi.id
    where mi.tenant_id = #{tenantId} and a.on_sale = 1
    <if test="marketIds != null">
      and mi.market_id in
      <foreach collection="marketIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>
