<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.market.mapper.MarketItemClassificationMapper">

    <resultMap type="com.cosfo.mall.market.model.po.MarketItemClassification" id="MarketItemClassificationMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="classificationId" column="classification_id" jdbcType="INTEGER"/>
        <result property="marketId" column="market_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

