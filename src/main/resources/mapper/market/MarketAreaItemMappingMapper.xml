<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.market.mapper.MarketAreaItemMappingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.market.model.po.MarketAreaItemMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="area_item_id" jdbcType="BIGINT" property="areaItemId" />
    <result column="store_price_type" property="storePriceType"/>
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="mapping_number" jdbcType="DECIMAL" property="mappingNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, area_item_id, store_price_type, `type`, mapping_number, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from market_area_item_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_area_item_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.market.model.po.MarketAreaItemMapping" useGeneratedKeys="true">
    insert into market_area_item_mapping (tenant_id, area_item_id, store_price_type, `type`,
      mapping_number, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{areaItemId,jdbcType=BIGINT}, #{storePriceType}, #{type,jdbcType=TINYINT},
      #{mappingNumber,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.market.model.po.MarketAreaItemMapping" useGeneratedKeys="true">
    insert into market_area_item_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="areaItemId != null">
        area_item_id,
      </if>
      <if test="storePriceType != null">
        store_price_type,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="mappingNumber != null">
        mapping_number,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="areaItemId != null">
        #{areaItemId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="storePriceType != null">
        #{storePriceType},
      </if>
      <if test="mappingNumber != null">
        #{mappingNumber,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.market.model.po.MarketAreaItemMapping">
    update market_area_item_mapping
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="areaItemId != null">
        area_item_id = #{areaItemId,jdbcType=BIGINT},
      </if>
      <if test="storePriceType != null">
        `store_price_type` = #{storePriceType,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="mappingNumber != null">
        mapping_number = #{mappingNumber,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.market.model.po.MarketAreaItemMapping">
    update market_area_item_mapping
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      area_item_id = #{areaItemId,jdbcType=BIGINT},
      `store_price_type` = #{storePriceType,jdbcType=TINYINT},
      `type` = #{type,jdbcType=TINYINT},
      mapping_number = #{mappingNumber,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryUnificationPriceByTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    market_area_item_mapping
    where
    tenant_id = #{tenantId}
    and store_price_type = 0
  </select>

  <select id="queryOtherPriceByTenantIdAndStoreId" resultMap="BaseResultMap">
    select
      m.id, m.tenant_id, m.area_item_id, m.store_price_type, m.`type`, m.mapping_number, m.create_time, m.update_time
    from
    market_area_item_mapping m
    left join market_area_item_store_price_mapping s on m.id = s.area_item_mapping_id
    where
    m.tenant_id = #{tenantId}
    and  s.store_id = #{storeId}
    and m.store_price_type = 1
  </select>

  <select id="queryUnificationPriceByTenantIdAndStoreIdAndAreaItemId" resultMap="BaseResultMap">
    select
      m.id, m.tenant_id, m.area_item_id, m.store_price_type, m.`type`, m.mapping_number, m.create_time, m.update_time
    from
      market_area_item_mapping m
        left join market_area_item_store_price_mapping s on m.id = s.area_item_mapping_id
    where
      m.tenant_id = #{tenantId}
      and m.store_price_type = 0
      and m.area_item_id = #{areaItemId}
  </select>

  <select id="queryOtherPriceByTenantIdAndStoreIdAndAreaItemId" resultMap="BaseResultMap">
    select
      m.id, m.tenant_id, m.area_item_id, m.store_price_type, m.`type`, m.mapping_number, m.create_time, m.update_time
    from
      market_area_item_mapping m
        left join market_area_item_store_price_mapping s on m.id = s.area_item_mapping_id
    where
      m.tenant_id = #{tenantId}
      and  s.store_id = #{storeId}
      and m.store_price_type = 1
      and m.area_item_id = #{areaItemId}
  </select>
</mapper>