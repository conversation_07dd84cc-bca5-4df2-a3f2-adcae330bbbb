<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.market.mapper.MarketMapper">

    <select id="selectByTitleAndClassificationId" resultType="com.cosfo.mall.market.model.po.Market">
        select
            m.id,m.tenant_id,m.title,m.sub_title,m.category_id,m.main_picture,m.detail_picture,m.create_time,m.update_time
        from
        market m
        left join market_item_classification c on m.id = c.market_id
        <where>
            <if test="title != null and title != ''">
                m.title like concat('%',#{title},'%')
            </if>
            <if test="classificationId != null">
                c.classification_id = #{classificationId}
            </if>
        </where>
    </select>
</mapper>
