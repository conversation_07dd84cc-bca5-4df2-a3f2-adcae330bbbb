<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.market.mapper.MarketAreaItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.market.model.po.MarketAreaItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="on_sale" jdbcType="TINYINT" property="onSale" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="delivery_type" property="deliveryType"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mini_order_quantity" jdbcType="INTEGER" property="miniOrderQuantity" />
  </resultMap>

  <sql id="Base_Column_List">
    id, tenant_id, item_id, on_sale, warehouse_type, delivery_type, create_time, update_time,mini_order_quantity
  </sql>
  <select id="selectOne" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_area_item
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
    </where>
  </select>
</mapper>
