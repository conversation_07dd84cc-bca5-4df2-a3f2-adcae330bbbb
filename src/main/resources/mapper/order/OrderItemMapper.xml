<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.order.mapper.OrderItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.order.model.po.OrderItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="payable_price" jdbcType="DECIMAL" property="payablePrice" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="store_no" jdbcType="TINYINT" property="storeNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="after_sale_expiry_time" property="afterSaleExpiryTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, order_id, item_id, amount, payable_price, total_price, `status`,`store_no`, create_time,
    update_time, after_sale_expiry_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.OrderItem" useGeneratedKeys="true">
    insert into order_item (tenant_id, order_id, item_id,
      amount, payable_price, total_price,
      `status`,`store_no`, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT},
      #{amount,jdbcType=INTEGER}, #{payablePrice,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL},
      #{status,jdbcType=TINYINT},#{storeNo}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.OrderItem" useGeneratedKeys="true">
    insert into order_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="payablePrice != null">
        payable_price,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="storeNo != null">
        `store_no`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="payablePrice != null">
        #{payablePrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.order.model.po.OrderItem">
    update order_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="payablePrice != null">
        payable_price = #{payablePrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="storeNo != null">
        `store_no` = #{storeNo,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.order.model.po.OrderItem">
    update order_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=INTEGER},
      payable_price = #{payablePrice,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      `store_no` = #{storeNo,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateOrderItemStatus">
    update order_item
        <set>
          <if test="status != null">
            status = #{status},
            update_time = now(),
          </if>
          <if test="afterSaleExpiryTime != null">
            after_sale_expiry_time = #{afterSaleExpiryTime}
          </if>
        </set>
        where tenant_id = #{tenantId}
        and order_id = #{orderId}
        and id = #{orderItemId}
  </update>

  <update id="batchUpdateOrderItemStatus">
    update order_item
    set status = #{status},
        update_time = now()
    where tenant_id = #{tenantId}
      and order_id = #{orderId}
  </update>
  <update id="batchUpdateOrderItemStatusAndStoreNo">
    update order_item
    set status = #{status},
        update_time = now(),
        store_no= #{storeNo}
    where tenant_id = #{tenantId}
      and order_id = #{orderId}
  </update>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from order_item
    where
    tenant_id = #{tenantId}
    and order_id = #{orderId}
  </select>

  <select id="queryOrderItemVOByOrderId" resultType="com.cosfo.mall.order.model.vo.OrderItemVO">
    select
        i.id, i.item_id itemId, i.amount, i.payable_price price, i.total_price totalPrice,
        s.supplier_tenant_id supplierTenantId, s.title, s.main_picture mainPicture, s.specification, s.sku_id skuId, s.supplier_sku_id supplierSkuId, i.order_id orderId,
        s.supply_price supplyPrice, s.delivery_type deliveryType, s.warehouse_type warehouseType,s.specification_unit specificationUnit, i.after_sale_expiry_time afterSaleExpiryTime,
        s.goods_type goodsType
    from
      order_item i
    left join order_item_snapshot s on i.id = s.order_item_id
    where i.tenant_id = #{tenantId}
    and i.order_id = #{orderId}
  </select>
  <select id="queryOrderItemById" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from order_item
    where tenant_id = #{tenantId} and id = #{orderItemId}
    <if test="orderId != null">
      and order_id = #{orderId}
    </if>
  </select>

  <select id="queryOrderItemVOById" resultType="com.cosfo.mall.order.model.vo.OrderItemVO">
    select
      i.id, i.item_id itemId, i.amount, i.payable_price price, i.total_price totalPrice,
      s.supplier_tenant_id supplierTenantId, s.title, s.main_picture mainPicture, s.specification, i.order_id orderId,s.specification_unit specificationUnit
    from
      order_item i
        left join order_item_snapshot s on i.id = s.order_item_id
    where i.tenant_id = #{tenantId}
      and i.id = #{orderItemId}
  </select>

  <update id="updateByOrderId">
    update order_item set status = #{status} where order_id = #{orderId} and tenant_id = #{tenantId}
  </update>

  <select id="queryByOrderId" resultType="com.cosfo.mall.order.model.dto.OrderItemDTO">
    select
      oi.id, oi.tenant_id, oi.order_id, oi.item_id, oi.amount, oi.payable_price, oi.total_price, oi.`status`, oi.`store_no`, s.max_after_sale_amount maxAfterSaleAmount, s.sku_id skuId, s.supplier_sku_id supplierSkuId
    from order_item oi inner join order_item_snapshot s on oi.tenant_id = s.tenant_id and oi.id = s.order_item_id
    where
    oi.tenant_id = #{tenantId}
    and oi.order_id = #{orderId}
  </select>

  <select id="queryByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_item
    where id in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <update id="updateSharingTime">
    update order_item
    set after_sale_expiry_time = #{afterSaleExpiryTime}
    where id = #{id}
  </update>

  <select id="selectByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    order_item
    where
    order_id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <update id="batchUpdateOrderItemStatusByOrderIds">
    update order_item
    set status = #{status},
        update_time = now()
    where tenant_id = #{tenantId}
      and order_id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </update>

  <select id="queryOrderItemVOByOrderIds" resultType="com.cosfo.mall.order.model.dto.OrderItemDTO">
    select
      i.id, i.item_id itemId, i.amount, i.payable_price price, i.total_price totalPrice,
      s.supplier_tenant_id supplierTenantId, s.title, s.main_picture mainPicture, s.specification, s.sku_id skuId, s.supplier_sku_id supplySkuId, i.order_id orderId,
      s.supply_price supplyPrice, s.delivery_type deliveryType, s.warehouse_type warehouseType,s.specification_unit specificationUnit, i.after_sale_expiry_time afterSaleExpiryTime,
      s.goods_type goodsType
    from
      order_item i
        left join order_item_snapshot s on i.id = s.order_item_id
    where i.tenant_id = #{tenantId}
      and i.order_id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>
