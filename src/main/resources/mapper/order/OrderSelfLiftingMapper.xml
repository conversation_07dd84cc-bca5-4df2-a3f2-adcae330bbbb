<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.order.mapper.OrderSelfLiftingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.order.model.po.OrderSelfLifting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="expect_time" jdbcType="TIMESTAMP" property="expectTime" />
    <result column="actual_time" jdbcType="TIMESTAMP" property="actualTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="address" jdbcType="VARCHAR" property="address" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, warehouse_no, expect_time, actual_time, create_time, update_time, address
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_self_lifting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByOrderNo" resultType="com.cosfo.mall.order.model.dto.OrderSelfLiftingDTO">
    select  id, order_no orderNo, warehouse_no warehouseNo, expect_time expectTime, actual_time actualTime, create_time createTime, update_time updateTime, address
    from order_self_lifting
    where order_no = #{orderNo}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_self_lifting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.OrderSelfLifting" useGeneratedKeys="true">
    insert into order_self_lifting (order_no, warehouse_no, expect_time,
      actual_time, address)
    values (#{orderNo,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER}, #{expectTime,jdbcType=TIMESTAMP},
      #{actualTime,jdbcType=TIMESTAMP}, #{address,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.OrderSelfLifting" useGeneratedKeys="true">
    insert into order_self_lifting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="expectTime != null">
        expect_time,
      </if>
      <if test="actualTime != null">
        actual_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="address != null">
        address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="expectTime != null">
        #{expectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualTime != null">
        #{actualTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.order.model.po.OrderSelfLifting">
    update order_self_lifting
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="expectTime != null">
        expect_time = #{expectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualTime != null">
        actual_time = #{actualTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.order.model.po.OrderSelfLifting">
    update order_self_lifting
    set order_no = #{orderNo,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      expect_time = #{expectTime,jdbcType=TIMESTAMP},
      actual_time = #{actualTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      address = #{address,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateActualTime">
    update order_self_lifting
    set actual_time = #{actualTime}
    where order_no = #{orderNo} and warehouse_no = #{warehouseNo}
  </update>
</mapper>
