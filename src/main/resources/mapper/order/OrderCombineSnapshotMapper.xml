<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.order.mapper.OrderCombineSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.order.model.po.OrderCombineSnapshot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="combine_order_id" jdbcType="BIGINT" property="combineOrderId" />
    <result column="combine_item_id" jdbcType="BIGINT" property="combineItemId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, combine_order_id, combine_item_id, item_id, quantity, original_price, 
    order_item_id, create_time, update_time
  </sql>
</mapper>