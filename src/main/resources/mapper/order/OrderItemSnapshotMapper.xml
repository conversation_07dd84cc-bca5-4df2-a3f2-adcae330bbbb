<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.order.mapper.OrderItemSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.order.model.po.OrderItemSnapshot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="supplier_tenant_id" jdbcType="BIGINT" property="supplierTenantId" />
    <result column="supplier_sku_id" jdbcType="BIGINT" property="supplierSkuId" />
    <result column="area_item_id" jdbcType="BIGINT" property="areaItemId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="main_picture" jdbcType="VARCHAR" property="mainPicture" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="supply_price" property="supplyPrice"/>
    <result column="warehouse_type" property="warehouseType"/>
    <result column="delivery_type" property="deliveryType"/>
    <result column="max_after_sale_amount" property="maxAfterSaleAmount"/>
    <result column="after_sale_unit" property="afterSaleUnit"/>
    <result column="pricing_type" property="pricingType"/>
    <result column="pricing_number" property="pricingNumber"/>
    <result column="after_sale_rule" property="afterSaleRule"/>
    <result column="goods_type" property="goodsType"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, order_item_id, sku_id, supplier_tenant_id, supplier_sku_id, area_item_id,
    title, main_picture, specification, create_time, update_time, supply_price, warehouse_type, delivery_type, max_after_sale_amount, after_sale_unit, pricing_type, pricing_number, after_sale_rule, goods_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_item_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_item_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.OrderItemSnapshot" useGeneratedKeys="true">
    insert into order_item_snapshot (tenant_id, order_item_id, sku_id,
      supplier_tenant_id, supplier_sku_id, area_item_id,
      title, main_picture, specification,
      create_time, update_time,supply_price, warehouse_type, delivery_type, max_after_sale_amount, after_sale_unit, pricing_type, pricing_number, after_sale_rule)
    values (#{tenantId,jdbcType=BIGINT}, #{orderItemId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT},
      #{supplierTenantId,jdbcType=BIGINT}, #{supplierSkuId,jdbcType=BIGINT}, #{areaItemId,jdbcType=BIGINT},
      #{title,jdbcType=VARCHAR}, #{mainPicture,jdbcType=VARCHAR}, #{specification,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{supplyPrice}, #{warehouseType}, #{deliveryType},
      #{maxAfterSaleAmount}, #{afterSaleUnit}, #{pricingType}, #{pricingNumber}, #{afterSaleRule}), #{goodsType}
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.OrderItemSnapshot" useGeneratedKeys="true">
    insert into order_item_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="supplierTenantId != null">
        supplier_tenant_id,
      </if>
      <if test="supplierSkuId != null">
        supplier_sku_id,
      </if>
      <if test="areaItemId != null">
        area_item_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="mainPicture != null">
        main_picture,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="supplyPrice != null">
        supply_price,
      </if>
      <if test="warehouseType != null">
        warehouse_type,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="specificationUnit != null">
        specification_unit,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="afterSaleUnit != null">
        after_sale_unit,
      </if>
      <if test="maxAfterSaleAmount != null">
        max_after_sale_amount,
      </if>
      <if test="pricingType != null">
        pricing_type,
      </if>
      <if test="pricingNumber != null">
        pricing_number,
      </if>
      <if test="afterSaleRule != null">
        after_sale_rule,
      </if>
      <if test="goodsType != null">
        goods_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="supplierTenantId != null">
        #{supplierTenantId,jdbcType=BIGINT},
      </if>
      <if test="supplierSkuId != null">
        #{supplierSkuId,jdbcType=BIGINT},
      </if>
      <if test="areaItemId != null">
        #{areaItemId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="mainPicture != null">
        #{mainPicture,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplyPrice != null">
        #{supplyPrice},
      </if>
      <if test="warehouseType != null">
        #{warehouseType},
      </if>
      <if test="deliveryType != null">
        #{deliveryType},
      </if>
      <if test="specificationUnit != null">
        #{specificationUnit},
      </if>
      <if test="supplierName != null">
        #{supplierName},
      </if>
      <if test="afterSaleUnit != null">
        #{afterSaleUnit},
      </if>
      <if test="maxAfterSaleAmount != null">
        #{maxAfterSaleAmount},
      </if>
      <if test="pricingType != null">
        #{pricingType},
      </if>
      <if test="pricingNumber != null">
        #{pricingNumber},
      </if>
      <if test="afterSaleRule != null">
        #{afterSaleRule},
      </if>
      <if test="goodsType != null">
        #{goodsType},
      </if>
      <if test="orderId != null">
        #{orderId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.order.model.po.OrderItemSnapshot">
    update order_item_snapshot
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="supplierTenantId != null">
        supplier_tenant_id = #{supplierTenantId,jdbcType=BIGINT},
      </if>
      <if test="supplierSkuId != null">
        supplier_sku_id = #{supplierSkuId,jdbcType=BIGINT},
      </if>
      <if test="areaItemId != null">
        area_item_id = #{areaItemId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="mainPicture != null">
        main_picture = #{mainPicture,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplyPrice != null">
        supply_price = #{supplyPrice}
      </if>
      <if test="warehouseType != null">
        warehouse_type = #{warehouseType},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType},
      </if>
      <if test="afterSaleUnit != null">
        after_sale_unit = #{afterSaleUnit},
      </if>
      <if test="maxAfterSaleAmount != null">
        max_after_sale_amount = #{maxAfterSaleAmount},
      </if>
      <if test="pricingType != null">
        pricing_type = #{pricingType},
      </if>
      <if test="pricingNumber != null">
        pricing_number = #{pricingNumber},
      </if>
      <if test="afterSaleRule != null">
        after_sale_rule = #{afterSaleRule},
      </if>
      <if test="goodsType != null">
        goods_type = #{goodsType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.order.model.po.OrderItemSnapshot">
    update order_item_snapshot
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      supplier_tenant_id = #{supplierTenantId,jdbcType=BIGINT},
      supplier_sku_id = #{supplierSkuId,jdbcType=BIGINT},
      area_item_id = #{areaItemId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      main_picture = #{mainPicture,jdbcType=VARCHAR},
      specification = #{specification,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      supply_price = #{supplyPrice},
      warehouse_type = #{warehouseType},
      delivery_type = #{deliveryType},
      after_sale_unit = #{afterSaleUnit},
      max_after_sale_amount = #{maxAfterSaleAmount},
      pricing_type = #{pricingType},
      pricing_number = #{pricingNumber},
      after_sale_rule = #{afterSaleRule},
      goods_type = #{goodsType},
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByItemId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from order_item_snapshot
    where tenant_id = #{tenantId} and order_item_id = #{orderItemId}
  </select>

  <select id="batchQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_item_snapshot
    <where>
      <if test="tenantId != null">
        tenant_id = #{tenantId}
      </if>
      <if test="orderItemIds != null and orderItemIds.size() > 0">
      and order_item_id in
        <foreach collection="orderItemIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="batchQuerySkuByOrderItemIds" resultType="com.cosfo.mall.order.model.dto.OrderItemSkuDTO">
    SELECT
        ps.sku,
        ois.`order_item_id` id
    FROM
        `order_item_snapshot` ois
    INNER JOIN `product_sku` ps on ois.`sku_id` = ps.id
    where
        ois.`order_item_id` IN
        <foreach collection="orderItemIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
  </select>
</mapper>
