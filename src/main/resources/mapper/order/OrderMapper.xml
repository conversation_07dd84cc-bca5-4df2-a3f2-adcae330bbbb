<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.order.mapper.OrderMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.order.model.po.Order">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="supplier_tenant_id" property="supplierTenantId"/>
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="payable_price" jdbcType="DECIMAL" property="payablePrice" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="pay_type" jdbcType="TINYINT" property="payType"/>
    <result column="online_pay_channel" jdbcType="INTEGER" property="onlinePayChannel"/>
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="finished_time" property="finishedTime"/>
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="apply_end_time" property="applyEndTime"/>
    <result column="auto_finished_time" property="autoFinishedTime"/>
    <result column="warehouse_no" property="warehouseNo"/>
    <result column="combine_order_id" property="combineOrderId"/>
    <result column="order_type" property="orderType"/>
    <result column="order_version" property="orderVersion"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, account_id, supplier_tenant_id, order_no, warehouse_type, payable_price, total_price,
    `status`, delivery_fee, pay_type, online_pay_channel, pay_time, delivery_time, create_time, update_time, finished_time, remark, apply_end_time, auto_finished_time, warehouse_no,
    combine_order_id, order_type, order_version
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `order`
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `order`
    where id = #{id,jdbcType=BIGINT} for update
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from `order`
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.Order" useGeneratedKeys="true">
    insert into `order` (tenant_id, store_id, account_id, supplier_tenant_id,
      order_no, warehouse_type, payable_price,
      total_price, `status`, delivery_fee, pay_type, online_pay_channel,
      pay_time, delivery_time, create_time,
      update_time,remark, apply_end_time, auto_finished_time, warehouse_no, combine_order_id, order_type, order_version)
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{supplierTenantId},
      #{orderNo,jdbcType=VARCHAR}, #{warehouseType,jdbcType=TINYINT}, #{payablePrice,jdbcType=DECIMAL},
      #{totalPrice,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{deliveryFee,jdbcType=DECIMAL}, #{payType}, #{onlinePayChannel,jdbcType=INTEGER},
      #{payTime,jdbcType=TIMESTAMP}, #{deliveryTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},#{remark,jdbcType=VARCHAR}, #{applyEndTime}, #{autoFinishedTime}, #{warehouse_no}, #{combineOrderId}, #{orderType}, #{orderVersion})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.Order" useGeneratedKeys="true">
    insert into `order`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="supplierTenantId != null">
        supplier_tenant_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="warehouseType != null">
        warehouse_type,
      </if>
      <if test="payablePrice != null">
        payable_price,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="onlinePayChannel != null">
        online_pay_channel,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="applyEndTime != null">
        apply_end_time,
      </if>
      <if test="autoFinishedTime != null">
        auto_finished_time,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="combineOrderId != null">
        combine_order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderVersion != null">
        order_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="supplierTenantId != null">
        #{supplierTenantId},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseType != null">
        #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="payablePrice != null">
        #{payablePrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        #{payType},
      </if>
      <if test="onlinePayChannel != null">
        #{onlinePayChannel,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyEndTime != null">
        #{applyEndTime},
      </if>
      <if test="autoFinishedTime != null">
        #{autoFinishedTime},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo},
      </if>
      <if test="combineOrderId != null">
        #{combineOrderId},
      </if>
      <if test="orderType != null">
        #{orderType},
      </if>
      <if test="orderVersion != null">
        #{orderVersion},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.order.model.po.Order">
    update `order`
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="supplierTenantId != null">
        supplier_tenant_id = #{supplierTenantId},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseType != null">
        warehouse_type = #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="payablePrice != null">
        payable_price = #{payablePrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        pay_type = #{payType},
      </if>
      <if test="onlinePayChannel != null">
        online_pay_channel = #{onlinePayChannel,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyEndTime != null">
        apply_end_time = #{applyEndTime},
      </if>
      <if test="autoFinishedTime">
        auto_finished_time = #{autoFinishedTime},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo},
      </if>
      <if test="combineOrderId != null">
        combine_order_id = #{combineOrderId},
      </if>
      <if test="orderType != null">
        order_type = #{orderType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.order.model.po.Order">
    update `order`
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      supplier_tenant_id = #{supplierTenantId},
      order_no = #{orderNo,jdbcType=VARCHAR},
      warehouse_type = #{warehouseType,jdbcType=TINYINT},
      payable_price = #{payablePrice,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      pay_type = #{payType},
      online_pay_channel = #{onlinePayChannel,jdbcType=INTEGER},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      apply_end_time = #{applyEndTime},
      auto_finished_time = #{autoFinishedTime},
      warehouse_no = #{warehouseNo},
      combine_order_id = #{combineOrderId},
      order_type = #{orderType}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="count" resultType="integer">
    select
      count(o.id)
    from `order` o
    where
    o.tenant_id = #{tenantId}
    and o.store_id = #{storeId}
    <if test="status != null &amp; status != 3">
      and o.status = #{status}
    </if>
    <!-- 小程序待收货-->
    <if test="status == 3">
      and o.status in (3,4,10,11,12)
    </if>
  </select>

  <select id="list" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from `order` o
    where
      o.tenant_id = #{tenantId}
      and o.store_id = #{storeId}
      <if test="status != null &amp; status != 3">
       and o.status = #{status}
      </if>
    <if test="status == 3">
      and o.status in (3,4,10,11,12)
    </if>
    order by o.create_time desc
    limit #{offset},#{limit}
  </select>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    `order` o
    where
        o.id = #{orderId}
    <if test="tenantId != null">
      and tenant_id = #{tenantId}
    </if>
    <if test="storeId != null">
      and store_id = #{storeId}
    </if>
  </select>

  <update id="updateOrderStatus">
    update `order`
        <set>
            <if test="status != null">
              status = #{status},
            </if>
            <if test="deliveryTime != null">
              delivery_time = #{deliveryTime},
            </if>
            <if test="status == 3 or status == 10">
              pay_time = now(),
              total_price = payable_price,
            </if>
            <if test="status == 5">
              finished_time = IFNULL(finished_time,now()),
            </if>
            <if test="status == 7">
              finished_time = IFNULL(finished_time,now()),
            </if>
          <if test="status == 9">
            finished_time = IFNULL(finished_time,now()),
          </if>
          update_time = now()
        </set>
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="storeId != null">
        and store_id = #{storeId}
      </if>
      <if test="orderId != null">
        and id = #{orderId}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo}
      </if>
    </where>
  </update>

  <update id="finishOrderWithDeliveryTime">
    update `order`
    <set>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime},
      </if>
      finished_time = IFNULL(finished_time,now()),
      update_time = now()
    </set>
    where id = #{orderId}
  </update>

  <select id="queryOrderVOByOrderId" resultType="com.cosfo.mall.order.model.vo.OrderVO">
    select
        o.supplier_tenant_id supplierTenantId, o.status, o.payable_price payablePrice, o.delivery_fee deliveryFee,
        o.total_price totalPrice, o.create_time orderTime, o.order_no orderNo, o.account_id accountId, o.delivery_time deliveryTime,
        o.pay_type payType,o.online_pay_channel onlinePayChannel, o.pay_time payTime, o.finished_time finishedTime ,o.warehouse_type warehouseType,o.remark remark,
        o.apply_end_time applyEndTime, o.auto_finished_time autoFinishedTime, o.combine_order_id combineOrderId, o.order_type orderType
    from
      `order` o
    where
      o.tenant_id = #{tenantId}
      and o.store_id = #{storeId}
      and o.id = #{orderId}
  </select>

  <select id="batchQueryByOrderNoList" resultType="com.cosfo.mall.order.model.vo.OrderVO">
    select
      o.supplier_tenant_id supplierTenantId, o.status, o.payable_price payablePrice, o.delivery_fee deliveryFee,
      o.total_price totalPrice, o.create_time orderTime, o.order_no orderNo, o.account_id accountId, o.delivery_time deliveryTime,
      o.pay_type payType,o.online_pay_channel onlinePayChannel, o.pay_time payTime,o.id orderId,o.tenant_id tenantId, o.store_id storeId
    from
      `order` o
    where
    o.order_no in
        <foreach collection="orderNos" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
  </select>

  <select id="selectByOrderIds" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"></include>
    from
      `order` o
    where
      o.tenant_id = #{tenantId}
      and o.store_id = #{storeId}
      and o.id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectOnlyByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    `order` o
    where o.id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryNeedDeliveryOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    `order` o
    where
    (o.delivery_time between #{startTime} and #{endTime} )
    and status in (4,10)
    and warehouse_type = 1
    and order_version is null
  </select>

  <select id="queryOrderNum" resultType="integer">
    select
        count(o.id)
    from
    `order` o
    where
      o.tenant_id = #{tenantId}
      and o.store_id = #{storeId}
    <if test="status != null &amp; status != 3">
      and o.status = #{status}
    </if>
    <if test="status == 3">
      and o.status in (3,4,10)
    </if>
  </select>

  <select id="selectByOrderNO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from `order`
    <where>
      <if test="orderNo != null">
        and order_no = #{orderNo}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
    </where>
  </select>

  <update id="orderFinished">
    update `order`
        set status = 5,
          update_time = now(),
          finished_time = now()
        where
          status in (4) and id in
          <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
  </update>

  <select id="selectNeedAutoFinishedOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    `order`
    where
    delivery_time &lt; #{endTime}
    and status in (4)
  </select>

  <select id="querySynchronizedOrderInfo" resultType="com.cosfo.mall.order.model.dto.OrderSynchronizedDTO">
    select order_no orderNo,
           o.tenant_id tenantId,
           o.store_id storeId,
           o.create_time orderTime,
           o.total_price totalPrice,
           case o.status
           when 3 THen 2
           when 10 THEN 2
           when 4 THEN 3
           when 5 THEN 6
           when 7 THEN 8 end as status
    from `order` o
    where o.warehouse_type = 1 and DATE_FORMAT(o.create_time,'%Y-%m-%d') = #{time}
    and o.status not in (1,2,6)
    and o.order_version is null
  </select>
  <select id="querySameDeliveryOrders" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from `order` o
    <where>
      o.status in (3, 4, 5, 10)
      <if test="id != null">
        and o.id <![CDATA[ < ]]> #{id}
      </if>
      <if test="tenantId != null">
        and o.tenant_id = #{tenantId}
      </if>
      <if test="storeId != null">
        and o.store_id = #{storeId}
      </if>
      <if test="deliveryTime != null">
        and o.delivery_time = #{deliveryTime}
      </if>
      <if test="supplierTenantId != null">
        and o.supplier_tenant_id = #{supplierTenantId}
      </if>
    </where>
  </select>
  <select id="queryOne" resultMap="BaseResultMap">
    select o.id, o.tenant_id, o.store_id, o.account_id, o.supplier_tenant_id, o.order_no, o.warehouse_type, o.payable_price, o.total_price,
    o.`status`, o.delivery_fee, o.pay_type, o.online_pay_channel, o.pay_time, o.delivery_time,o.remark
    from `order` o
    <if test="afterSaleOrderNo != null">
      inner join order_after_sale oas on o.id = oas.order_id
    </if>
    <where>
      <if test="afterSaleOrderNo != null">
        oas.after_sale_order_no = #{afterSaleOrderNo}
      </if>
    </where>

  </select>
  <select id="queryByOrderNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from `order` o
    where
    o.order_no in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <update id="updateOrderStatusById">
    update `order`
    <set>
      status = #{afterStatus},
      <if test="afterStatus == 3 or afterStatus == 10">
        pay_time = now(),
        total_price = payable_price,
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime}
      </if>
    </set>
    where id = #{orderId} and status = #{beforeStatus}
  </update>

  <update id="batchUpdateOrderStatusByIds">
    update `order`
    <set>
      status = #{afterStatus},
      update_time = now()
    </set>
    where id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    and status = #{beforeStatus}
  </update>

  <update id="updateOrderDeliveryTime">
    update `order`
    <set>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime},
      </if>
      update_time = now()
    </set>
    where id = #{orderId} and status = #{beforeStatus}
  </update>

  <update id="billPayOrder">
    update `order`
        set pay_type = 2,
            total_price = payable_price,
            status = 3,
            pay_time = now(),
            update_time = now()
        where order_no = #{orderNo}
        and tenant_id = #{tenantId}
  </update>

  <update id="updatePayType">
    update `order`
    set pay_type = #{payType},
        online_pay_channel = #{onlinePayChannel},
        update_time = now()
    where id = #{orderId}
  </update>

  <select id="queryOrderByStatusAndWarehouseType2TmsPlan" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from `order`
    <where>
      <if test="status != null">
        status = #{status}
      </if>
      <if test="warehouseType != null">
        and warehouse_type = #{warehouseType}
        and order_version is null
      </if>
    </where>
  </select>

  <select id="queryByCombineOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from `order`
    where
    combine_order_id in
    <foreach collection="combineOrderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <update id="batchCancelOrder">
    update `order`
    set status = #{status},
        update_time = now()
    where tenant_id = #{tenantId} and status in (1,2)
    and id in
    <foreach collection="orderIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </update>

  <select id="queryEffectiveDeliveryOrders" resultType="com.cosfo.mall.order.model.dto.OrderDTO">
    select <include refid="Base_Column_List"/>
    from `order` o
    <where>
      o.status in (3, 4, 5, 7, 9, 10)
      <if test="id != null">
        and o.id <![CDATA[ < ]]> #{id}
      </if>
      <if test="tenantId != null">
        and o.tenant_id = #{tenantId}
      </if>
      <if test="storeId != null">
        and o.store_id = #{storeId}
      </if>
      <if test="deliveryTime != null">
        and date_format(o.delivery_time,'%Y-%m-%d') = #{deliveryTime}
      </if>
      <if test="supplierTenantId != null">
        and o.supplier_tenant_id = #{supplierTenantId}
      </if>
    </where>
  </select>

</mapper>
