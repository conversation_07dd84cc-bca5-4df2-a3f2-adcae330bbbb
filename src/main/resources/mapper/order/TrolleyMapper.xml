<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.order.mapper.TrolleyMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.order.model.po.Trolley">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, account_id, store_id, item_id, amount, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trolley
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from trolley
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.Trolley" useGeneratedKeys="true">
    insert into trolley (tenant_id, account_id, store_id, 
      item_id, amount, create_time, 
      update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{itemId,jdbcType=BIGINT}, #{amount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.order.model.po.Trolley" useGeneratedKeys="true">
    insert into trolley
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VBIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.order.model.po.Trolley">
    update trolley
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.order.model.po.Trolley">
    update trolley
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 添加购物车商品，没有则创建，有则更新-->
  <insert id="merge" parameterType="com.cosfo.mall.order.model.po.Trolley" >
    INSERT INTO `trolley` (tenant_id, account_id, store_id, item_id, amount, update_time)
    VALUES (#{tenantId}, #{accountId} , #{storeId}, #{itemId}, #{amount}, NOW())
    ON DUPLICATE KEY UPDATE amount = ifNull(amount, 0) + VALUES(amount), update_time = NOW()
  </insert>

  <!-- 删除商品-->
  <delete id="deleteByItemId" parameterType="com.cosfo.mall.order.model.po.Trolley">
    delete from trolley
    where tenant_id = #{tenantId}
    and account_id = #{accountId}
    and store_id = #{storeId}
    and item_id = #{itemId}
  </delete>

  <delete id="delete" parameterType="com.cosfo.mall.order.model.po.Trolley">
    delete from trolley
    where tenant_id = #{tenantId}
      and account_id = #{accountId}
      and store_id = #{storeId}
  </delete>

  <select id="selectByAccountId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from trolley
    where tenant_id = #{tenantId}
    and store_id = #{storeId}
    and account_id = #{accountId}
  </select>

  <select id="selectByItemId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from trolley
    where tenant_id = #{tenantId}
    and store_id = #{storeId}
    and account_id = #{accountId}
    and item_id = #{itemId}
  </select>

  <select id="selectByItemIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from trolley
    where tenant_id = #{tenantId}
    and store_id = #{storeId}
    and account_id = #{accountId}
    and item_id in
    <foreach collection="itemIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <delete id="batchDelete">
    delete from trolley
    where tenant_id = #{tenantId}
      and account_id = #{accountId}
      and store_id = #{storeId}
      and item_id in
    <foreach collection="itemIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </delete>

  <insert id="batchInsert">
    INSERT INTO `trolley` (tenant_id, account_id, store_id, item_id, amount)
    VALUES
    <foreach collection="trolleys" item="item" separator=",">
      (#{item.tenantId}, #{item.accountId}, #{item.storeId}, #{item.itemId}, #{item.amount}
      )
    </foreach>
  </insert>
</mapper>