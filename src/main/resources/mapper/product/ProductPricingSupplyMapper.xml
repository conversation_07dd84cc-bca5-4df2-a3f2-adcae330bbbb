<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.product.mapper.ProductPricingSupplyMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.product.model.po.ProductPricingSupply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="supply_sku_id" jdbcType="BIGINT" property="supplySkuId" />
    <result column="supply_tenant_id" jdbcType="BIGINT" property="supplyTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, supply_sku_id, supply_tenant_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_pricing_supply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_pricing_supply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupply" useGeneratedKeys="true">
    insert into product_pricing_supply (tenant_id, supply_sku_id,  supply_tenant_id,
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{supplySkuId,jdbcType=BIGINT},  #{supplyTenantId,jdbcType=BIGINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupply" useGeneratedKeys="true">
    insert into product_pricing_supply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="supplySkuId != null">
        supply_sku_id,
      </if>
      <if test="supplyTenantId != null">
        supply_tenant_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="supplySkuId != null">
        #{supplySkuId,jdbcType=BIGINT},
      </if>
      <if test="supplyTenantId != null">
        #{supplyTenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupply">
    update product_pricing_supply
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="supplySkuId != null">
        supply_sku_id = #{supplySkuId,jdbcType=BIGINT},
      </if>
      <if test="supplyTenantId != null">
        supply_tenant_id = #{supplyTenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupply">
    update product_pricing_supply
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      supply_sku_id = #{supplySkuId,jdbcType=BIGINT},
      supply_tenant_id = #{supplyTenantId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getSupplierSkuIdBySupplySkuIds" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"></include>
    from product_pricing_supply
    where
        tenant_id = #{tenantId}
    <if test="supplySkuIds != null">
      and supply_sku_id in
      <foreach collection="supplySkuIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="selectBySupplySkuId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from product_pricing_supply
    where tenant_id = #{tenant} and supply_sku_id = #{supplySkuId}
  </select>

  <select id="queryByCityName" resultType="com.cosfo.mall.product.model.dto.ProductPricingSupplyDTO">
    select
      s.id, s.tenant_id tenantId, s.supply_sku_id supplySkuId, s.supply_tenant_id supplyTenantId,
      m.id productPricingSupplyCityMappingId, m.city_id cityId , m.`type` type, m.supply_type supplyType, m.price price, m.start_time startTime, m.end_time endTime
    from
      product_pricing_supply s
    left join product_pricing_supply_city_mapping m on s.id = m.product_pricing_supply_id
    where s.tenant_id = #{tenantId} and m.city_id = #{cityId} and m.start_time &lt;= now() and m.end_time &gt;= now()
  </select>

  <select id="selectBySkuIdAndCityId" resultType="com.cosfo.mall.product.model.dto.ProductPricingSupplyDTO">
    select
    s.id, s.tenant_id tenantId, s.supply_sku_id supplySkuId, s.supply_tenant_id supplyTenantId,
    m.id productPricingSupplyCityMappingId, m.city_id cityId , m.`type` type, m.supply_type supplyType, m.price price, m.start_time startTime, m.end_time endTime
    from
    product_pricing_supply s
    left join product_pricing_supply_city_mapping m on s.id = m.product_pricing_supply_id
    where s.tenant_id = #{tenantId} and m.city_id = #{cityId} AND s.supply_sku_id = #{skuId}
      and m.start_time &lt;= now() and m.end_time &gt;= now()
  </select>
</mapper>