<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.product.mapper.ProductSpuMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.product.model.po.ProductSpu">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="brand_name" property="brandName"/>
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="main_picture" jdbcType="VARCHAR" property="mainPicture" />
    <result column="detail_picture" jdbcType="VARCHAR" property="detailPicture" />
    <result column="storage_location" jdbcType="TINYINT" property="storageLocation" />
    <result column="storage_temperature" jdbcType="VARCHAR" property="storageTemperature" />
    <result column="guarantee_period" jdbcType="INTEGER" property="guaranteePeriod" />
    <result column="guarantee_unit" jdbcType="TINYINT" property="guaranteeUnit" />
    <result column="origin" jdbcType="VARCHAR" property="origin" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, category_id, brand_id, title, sub_title, main_picture, detail_picture, 
    storage_location, storage_temperature, guarantee_period, guarantee_unit, origin, 
    create_time, update_time, brand_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_spu
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_spu
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.cosfo.mall.product.model.po.ProductSpu" >
    insert into product_spu (id, tenant_id, category_id, brand_id,
      title, sub_title, main_picture, 
      detail_picture, storage_location, storage_temperature, 
      guarantee_period, guarantee_unit, origin, brand_name)
    values (#{id}, #{tenantId,jdbcType=BIGINT}, #{categoryId,jdbcType=BIGINT}, #{brandId,jdbcType=BIGINT},
      #{title,jdbcType=VARCHAR}, #{subTitle,jdbcType=VARCHAR}, #{mainPicture,jdbcType=VARCHAR}, 
      #{detailPicture,jdbcType=VARCHAR}, #{storageLocation,jdbcType=TINYINT}, #{storageTemperature,jdbcType=VARCHAR}, 
      #{guaranteePeriod,jdbcType=INTEGER}, #{guaranteeUnit,jdbcType=TINYINT}, #{origin,jdbcType=VARCHAR}, #{brandName})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductSpu" useGeneratedKeys="true">
    insert into product_spu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="subTitle != null">
        sub_title,
      </if>
      <if test="mainPicture != null">
        main_picture,
      </if>
      <if test="detailPicture != null">
        detail_picture,
      </if>
      <if test="storageLocation != null">
        storage_location,
      </if>
      <if test="storageTemperature != null">
        storage_temperature,
      </if>
      <if test="guaranteePeriod != null">
        guarantee_period,
      </if>
      <if test="guaranteeUnit != null">
        guarantee_unit,
      </if>
      <if test="origin != null">
        origin,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="mainPicture != null">
        #{mainPicture,jdbcType=VARCHAR},
      </if>
      <if test="detailPicture != null">
        #{detailPicture,jdbcType=VARCHAR},
      </if>
      <if test="storageLocation != null">
        #{storageLocation,jdbcType=TINYINT},
      </if>
      <if test="storageTemperature != null">
        #{storageTemperature,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePeriod != null">
        #{guaranteePeriod,jdbcType=INTEGER},
      </if>
      <if test="guaranteeUnit != null">
        #{guaranteeUnit,jdbcType=TINYINT},
      </if>
      <if test="origin != null">
        #{origin,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="brandName != null">
        #{brandName},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.product.model.po.ProductSpu">
    update product_spu
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        sub_title = #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="mainPicture != null">
        main_picture = #{mainPicture,jdbcType=VARCHAR},
      </if>
      <if test="detailPicture != null">
        detail_picture = #{detailPicture,jdbcType=VARCHAR},
      </if>
      <if test="storageLocation != null">
        storage_location = #{storageLocation,jdbcType=TINYINT},
      </if>
      <if test="storageTemperature != null">
        storage_temperature = #{storageTemperature,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePeriod != null">
        guarantee_period = #{guaranteePeriod,jdbcType=INTEGER},
      </if>
      <if test="guaranteeUnit != null">
        guarantee_unit = #{guaranteeUnit,jdbcType=TINYINT},
      </if>
      <if test="origin != null">
        origin = #{origin,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.product.model.po.ProductSpu">
    update product_spu
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      category_id = #{categoryId,jdbcType=BIGINT},
      brand_id = #{brandId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      main_picture = #{mainPicture,jdbcType=VARCHAR},
      detail_picture = #{detailPicture,jdbcType=VARCHAR},
      storage_location = #{storageLocation,jdbcType=TINYINT},
      storage_temperature = #{storageTemperature,jdbcType=VARCHAR},
      guarantee_period = #{guaranteePeriod,jdbcType=INTEGER},
      guarantee_unit = #{guaranteeUnit,jdbcType=TINYINT},
      origin = #{origin,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      brand_name = #{brandName}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>