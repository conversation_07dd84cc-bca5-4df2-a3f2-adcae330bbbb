<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.product.mapper.ProductSkuMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.product.model.po.ProductSku">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sku" property="sku"></result>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, spu_id, specification, specification_unit, create_time, update_time, sku
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_sku
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_sku
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.cosfo.mall.product.model.po.ProductSku">
    insert into product_sku (id, tenant_id, spu_id, specification,
      specification_unit, sku
      )
    values (#{id}, #{tenantId,jdbcType=BIGINT}, #{spuId,jdbcType=BIGINT}, #{specification,jdbcType=VARCHAR},
      #{specificationUnit,jdbcType=VARCHAR}, #{sku}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductSku" useGeneratedKeys="true">
    insert into product_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="specificationUnit != null">
        specification_unit,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="agentType != null">
        agent_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="specificationUnit != null">
        #{specificationUnit,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sku != null">
        #{sku},
      </if>
      <if test="volume != null">
        #{volume},
      </if>
      <if test="weight != null">
        #{weight},
      </if>
      <if test="agentType != null">
        #{agentType},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.product.model.po.ProductSku">
    update product_sku
    <set>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="specificationUnit != null">
        specification_unit = #{specificationUnit,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sku != null">
        sku = #{sku},
      </if>
      <if test="volume != null">
        volume = #{volume},
      </if>
      <if test="weight != null">
        weight = #{weight},
      </if>
      <if test="agentType != null">
        agent_type = #{agentType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.product.model.po.ProductSku">
    update product_sku
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      spu_id = #{spuId,jdbcType=BIGINT},
      specification = #{specification,jdbcType=VARCHAR},
      specification_unit = #{specificationUnit,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>