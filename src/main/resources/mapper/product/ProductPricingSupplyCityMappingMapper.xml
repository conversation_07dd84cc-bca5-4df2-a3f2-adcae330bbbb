<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.product.mapper.ProductPricingSupplyCityMappingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.product.model.po.ProductPricingSupplyCityMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_pricing_supply_id" jdbcType="BIGINT" property="productPricingSupplyId" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="supply_type" jdbcType="TINYINT" property="supplyType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, product_pricing_supply_id, city_id, `type`, supply_type, price, start_time, end_time, 
    create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_pricing_supply_city_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_pricing_supply_city_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupplyCityMapping" useGeneratedKeys="true">
    insert into product_pricing_supply_city_mapping (product_pricing_supply_id, city_id, `type`, 
      supply_type, price, start_time, 
      end_time, create_time, update_time, 
      deleted)
    values (#{productPricingSupplyId,jdbcType=BIGINT}, #{cityId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, 
      #{supplyType,jdbcType=TINYINT}, #{price,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupplyCityMapping" useGeneratedKeys="true">
    insert into product_pricing_supply_city_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productPricingSupplyId != null">
        product_pricing_supply_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="supplyType != null">
        supply_type,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productPricingSupplyId != null">
        #{productPricingSupplyId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="supplyType != null">
        #{supplyType,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupplyCityMapping">
    update product_pricing_supply_city_mapping
    <set>
      <if test="productPricingSupplyId != null">
        product_pricing_supply_id = #{productPricingSupplyId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="supplyType != null">
        supply_type = #{supplyType,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.product.model.po.ProductPricingSupplyCityMapping">
    update product_pricing_supply_city_mapping
    set product_pricing_supply_id = #{productPricingSupplyId,jdbcType=BIGINT},
      city_id = #{cityId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      supply_type = #{supplyType,jdbcType=TINYINT},
      price = #{price,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>