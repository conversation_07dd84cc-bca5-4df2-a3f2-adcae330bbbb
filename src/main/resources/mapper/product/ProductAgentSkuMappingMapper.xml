<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.product.mapper.ProductAgentSkuMappingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.product.model.po.ProductAgentSkuMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="agent_tenant_id" jdbcType="BIGINT" property="agentTenantId" />
    <result column="agent_sku_id" jdbcType="BIGINT" property="agentSkuId" />
    <result column="agent_sku_code" jdbcType="VARCHAR" property="agentSkuCode"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, sku_id, agent_tenant_id, agent_sku_id, agent_sku_code, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_agent_sku_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_agent_sku_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductAgentSkuMapping" useGeneratedKeys="true">
    insert into product_agent_sku_mapping (tenant_id, sku_id, agent_tenant_id, 
      agent_sku_id,agent_sku_code, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}, #{agentTenantId,jdbcType=BIGINT}, 
      #{agentSkuId,jdbcType=BIGINT}, #{agentSkuCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductAgentSkuMapping" useGeneratedKeys="true">
    insert into product_agent_sku_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="agentTenantId != null">
        agent_tenant_id,
      </if>
      <if test="agentSkuId != null">
        agent_sku_id,
      </if>
      <if test="agentSkuCode != null">
        agent_sku_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="agentTenantId != null">
        #{agentTenantId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuId != null">
        #{agentSkuId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuCode != null">
        #{agentSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.product.model.po.ProductAgentSkuMapping">
    update product_agent_sku_mapping
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="agentTenantId != null">
        agent_tenant_id = #{agentTenantId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuId != null">
        agent_sku_id = #{agentSkuId,jdbcType=BIGINT},
      </if>
      <if test="agentSkuCode != null">
        agent_sku_code = #{agentSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.product.model.po.ProductAgentSkuMapping">
    update product_agent_sku_mapping
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      agent_tenant_id = #{agentTenantId,jdbcType=BIGINT},
      agent_sku_id = #{agentSkuId,jdbcType=BIGINT},
      agent_sku_code = #{agentSkuCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryAgentSkuInfo" resultType="com.cosfo.mall.product.model.dto.ProductAgentSkuDTO">
    select
        id agentId, tenant_id tenantId, sku_id skuId, agent_tenant_id agentTenantId, agent_sku_id agentSkuId, agent_sku_code agentSkuCode
    from product_agent_sku_mapping
    where tenant_id = #{tenantId}
    and sku_id in
    <foreach collection="skuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryAgentSkuInfoByAgentSkuIds" resultType="com.cosfo.mall.product.model.dto.ProductAgentSkuDTO">
    select
    id agentId, tenant_id tenantId, sku_id skuId, agent_tenant_id agentTenantId, agent_sku_id agentSkuId
    from product_agent_sku_mapping
    where agent_tenant_id = #{agentTenantId}
    and tenant_id = #{tenantId}
    and agent_sku_id in
    <foreach collection="agentSkuIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectBySkuIdAndTenantId" resultType="com.cosfo.mall.product.model.dto.ProductAgentSkuDTO">
    select
      id agentId, tenant_id tenantId, sku_id skuId, agent_tenant_id agentTenantId, agent_sku_id agentSkuId, agent_sku_code agentSkuCode
    from product_agent_sku_mapping
    where tenant_id = #{tenantId}
    and sku_id = #{skuId}
  </select>

  <select id="selectByAgentSkuIdAndAgentTenantIdAndTenantId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from product_agent_sku_mapping
    where agent_sku_id = #{agentSkuId}
    and agent_tenant_id = #{agentTenantId}
    and tenant_id = #{tenantId}
  </select>

  <select id="listBySkuCodes" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from product_agent_sku_mapping
    where tenant_id = #{tenantId}
    and agent_sku_code in
    <foreach collection="agentSkuCodes" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selfAgentListBySkuCodes" resultMap="BaseResultMap">
    select agent.id, agent.tenant_id, agent.sku_id, agent.agent_tenant_id, agent.agent_sku_id, agent.agent_sku_code,
    agent.create_time, agent.update_time
    from product_agent_sku_mapping agent
    LEFT JOIN product_sku sku on sku.id = agent.sku_id
    where agent.tenant_id = #{tenantId}
    and sku.agent_type = 1
    and agent.agent_sku_code in
    <foreach collection="agentSkuCodes" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>