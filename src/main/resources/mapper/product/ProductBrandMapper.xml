<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.product.mapper.ProductBrandMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.mall.product.model.po.ProductBrand">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from brand
        where id = #{id}
    </select>
    <select id="selectByName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from brand
        where name = #{brandName}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from brand
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductBrand" useGeneratedKeys="true">
        insert into brand (`name`
        )
        values (#{name,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.product.model.po.ProductBrand" useGeneratedKeys="true">
        insert into brand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.product.model.po.ProductBrand">
        update brand
        <set>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.product.model.po.ProductBrand">
        update brand
        set `name` = #{name,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
