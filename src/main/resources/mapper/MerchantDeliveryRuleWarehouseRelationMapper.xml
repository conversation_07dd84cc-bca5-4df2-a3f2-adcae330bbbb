<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.merchant.mapper.MerchantDeliveryRuleWarehouseRelationMapper">

    <resultMap type="com.cosfo.mall.merchant.model.po.MerchantDeliveryRuleWarehouseRelation"
               id="MerchantDeliveryRuleWarehouseRelationMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="MerchantDeliveryRuleWarehouseRelationMap">
        select id,
               tenant_id,
               rule_id,
               warehouse_no,
               create_time,
               update_time
        from merchant_delivery_rule_warehouse_relation
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="MerchantDeliveryRuleWarehouseRelationMap">
        select
        id, tenant_id, rule_id, warehouse_no, create_time, update_time
        from merchant_delivery_rule_warehouse_relation
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="ruleId != null">
                and rule_id = #{ruleId}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from merchant_delivery_rule_warehouse_relation
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="ruleId != null">
                and rule_id = #{ruleId}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into merchant_delivery_rule_warehouse_relation(tenant_id, rule_id, warehouse_no, create_time, update_time)
        values (#{tenantId}, #{ruleId}, #{warehouseNo}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into merchant_delivery_rule_warehouse_relation(tenant_id, rule_id, warehouse_no, create_time,
        update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tenantId}, #{entity.ruleId}, #{entity.warehouseNo}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into merchant_delivery_rule_warehouse_relation(tenant_id, rule_id, warehouse_no, create_time,
        update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tenantId}, #{entity.ruleId}, #{entity.warehouseNo}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        tenant_id = values(tenant_id),
        rule_id = values(rule_id),
        warehouse_no = values(warehouse_no),
        create_time = values(create_time),
        update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update merchant_delivery_rule_warehouse_relation
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from merchant_delivery_rule_warehouse_relation
        where id = #{id}
    </delete>

</mapper>

