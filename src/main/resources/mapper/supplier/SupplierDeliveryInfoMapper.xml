<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.supplier.mapper.SupplierDeliveryInfoMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="supplier_delivery_fee" jdbcType="DECIMAL" property="supplierDeliveryFee" />
    <result column="supplier_delivery_fee_rule" jdbcType="VARCHAR" property="supplierDeliveryFeeRule" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, supplier_delivery_fee, supplier_delivery_fee_rule, type, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from supplier_delivery_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_delivery_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo" useGeneratedKeys="true">
    insert into supplier_delivery_info (order_no, supplier_delivery_fee, supplier_delivery_fee_rule, type)
    values (#{orderNo,jdbcType=VARCHAR}, #{supplierDeliveryFee,jdbcType=DECIMAL}, #{supplierDeliveryFeeRule,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo" useGeneratedKeys="true">
    insert into supplier_delivery_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="supplierDeliveryFee != null">
        supplier_delivery_fee,
      </if>
      <if test="supplierDeliveryFeeRule != null">
        supplier_delivery_fee_rule,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierDeliveryFee != null">
        #{supplierDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="supplierDeliveryFeeRule != null">
        #{supplierDeliveryFeeRule,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo">
    update supplier_delivery_info
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierDeliveryFee != null">
        supplier_delivery_fee = #{supplierDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="supplierDeliveryFeeRule != null">
        supplier_delivery_fee_rule = #{supplierDeliveryFeeRule,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo">
    update supplier_delivery_info
    set order_no = #{orderNo,jdbcType=VARCHAR},
      supplier_delivery_fee = #{supplierDeliveryFee,jdbcType=DECIMAL},
      supplier_delivery_fee_rule = #{supplierDeliveryFeeRule,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    supplier_delivery_info
    where order_no = #{orderNo}
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    supplier_delivery_info
    where create_time > '2022-12-06 00:00:00'
  </select>
</mapper>
