<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.bill.mapper.BillProfitSharingRefundSnapshotMapper">
    <insert id="saveBatch" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot" useGeneratedKeys="true">
        insert into bill_profit_sharing_refund_snapshot(tenant_id, order_id, order_after_sale_id, account_id,
        profit_sharing_type, origin_price, refund_price, should_refund_price,
        actual_refund_price, final_refund_price, order_item_id, org_profit_sharing_no)
        values
        <foreach collection="billProfitSharingRefundSnapshots" item="item" separator=",">
            (#{item.tenantId},#{item.orderId},#{item.orderAfterSaleId},#{item.accountId},#{item.profitSharingType},#{item.originPrice},
            #{item.refundPrice},#{item.shouldRefundPrice}, #{item.actualRefundPrice}, #{item.finalRefundPrice},
            #{item.orderItemId}, #{item.orgProfitSharingNo})
        </foreach>
    </insert>
</mapper>
