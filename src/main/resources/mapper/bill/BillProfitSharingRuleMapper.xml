<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.bill.mapper.BillProfitSharingRuleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.bill.model.po.BillProfitSharingRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="account_type" jdbcType="TINYINT" property="accountType"/>
    <result column="delivery_type" jdbcType="BIGINT" property="deliveryType" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="mapping_type" jdbcType="TINYINT" property="mappingType" />
    <result column="number" jdbcType="DECIMAL" property="number" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    tenant_id,
    account_id,
    account_type,
    delivery_type,
    `type`,
    mapping_type,
    `number`,
    create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_profit_sharing_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_profit_sharing_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingRule" useGeneratedKeys="true">
    insert into bill_profit_sharing_rule (tenant_id, account_id, delivery_type, 
      `type`, mapping_type, `number`, 
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{deliveryType,jdbcType=BIGINT}, 
      #{type,jdbcType=TINYINT}, #{mappingType,jdbcType=TINYINT}, #{number,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingRule" useGeneratedKeys="true">
    insert into bill_profit_sharing_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="mappingType != null">
        mapping_type,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="mappingType != null">
        #{mappingType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingRule">
    update bill_profit_sharing_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="mappingType != null">
        mapping_type = #{mappingType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from
    bill_profit_sharing_rule
    <where>
      tenant_id = #{tenantId}
    </where>
  </select>

  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingRule">
    update bill_profit_sharing_rule
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      delivery_type = #{deliveryType,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      mapping_type = #{mappingType,jdbcType=TINYINT},
      `number` = #{number,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryProfitSharingTenant" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_rule
    where
    tenant_id = #{tenantId}
    and role_code = #{roleCode}
  </select>

  <select id="selectByTenantIdAndDeliveryType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from
    bill_profit_sharing_rule
    <where>
      tenant_id = #{tenantId} and delivery_type = #{deliveryType}
    </where>
  </select>
</mapper>