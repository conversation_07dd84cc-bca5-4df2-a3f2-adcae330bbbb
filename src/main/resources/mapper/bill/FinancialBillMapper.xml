<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.bill.mapper.FinancialBillMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.bill.model.po.FinancialBill">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="payee_id" jdbcType="BIGINT" property="payeeId" />
    <result column="payer_id" jdbcType="BIGINT" property="payerId" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="order_receivable_price" jdbcType="DECIMAL" property="orderReceivablePrice" />
    <result column="order_after_sale_total_price" jdbcType="DECIMAL" property="orderAfterSaleTotalPrice" />
    <result column="receivable_price" jdbcType="DECIMAL" property="receivablePrice" />
    <result column="received_price" jdbcType="DECIMAL" property="receivedPrice" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, bill_no, payee_id, payer_id, bill_type, order_receivable_price, order_after_sale_total_price, 
    receivable_price, received_price, start_time, end_time, `type`, `status`, audit_time, 
    file_path, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from financial_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.FinancialBill" useGeneratedKeys="true">
    insert into financial_bill (tenant_id, bill_no, payee_id, 
      payer_id, bill_type, order_receivable_price, 
      order_after_sale_total_price, receivable_price, 
      received_price, start_time, end_time, 
      `type`, `status`, audit_time, 
      file_path, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{billNo,jdbcType=VARCHAR}, #{payeeId,jdbcType=BIGINT}, 
      #{payerId,jdbcType=BIGINT}, #{billType,jdbcType=TINYINT}, #{orderReceivablePrice,jdbcType=DECIMAL}, 
      #{orderAfterSaleTotalPrice,jdbcType=DECIMAL}, #{receivablePrice,jdbcType=DECIMAL}, 
      #{receivedPrice,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{filePath,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.FinancialBill" useGeneratedKeys="true">
    insert into financial_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="payeeId != null">
        payee_id,
      </if>
      <if test="payerId != null">
        payer_id,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="orderReceivablePrice != null">
        order_receivable_price,
      </if>
      <if test="orderAfterSaleTotalPrice != null">
        order_after_sale_total_price,
      </if>
      <if test="receivablePrice != null">
        receivable_price,
      </if>
      <if test="receivedPrice != null">
        received_price,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null">
        #{payeeId,jdbcType=BIGINT},
      </if>
      <if test="payerId != null">
        #{payerId,jdbcType=BIGINT},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="orderReceivablePrice != null">
        #{orderReceivablePrice,jdbcType=DECIMAL},
      </if>
      <if test="orderAfterSaleTotalPrice != null">
        #{orderAfterSaleTotalPrice,jdbcType=DECIMAL},
      </if>
      <if test="receivablePrice != null">
        #{receivablePrice,jdbcType=DECIMAL},
      </if>
      <if test="receivedPrice != null">
        #{receivedPrice,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.bill.model.po.FinancialBill">
    update financial_bill
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null">
        payee_id = #{payeeId,jdbcType=BIGINT},
      </if>
      <if test="payerId != null">
        payer_id = #{payerId,jdbcType=BIGINT},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="orderReceivablePrice != null">
        order_receivable_price = #{orderReceivablePrice,jdbcType=DECIMAL},
      </if>
      <if test="orderAfterSaleTotalPrice != null">
        order_after_sale_total_price = #{orderAfterSaleTotalPrice,jdbcType=DECIMAL},
      </if>
      <if test="receivablePrice != null">
        receivable_price = #{receivablePrice,jdbcType=DECIMAL},
      </if>
      <if test="receivedPrice != null">
        received_price = #{receivedPrice,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.bill.model.po.FinancialBill">
    update financial_bill
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      bill_no = #{billNo,jdbcType=VARCHAR},
      payee_id = #{payeeId,jdbcType=BIGINT},
      payer_id = #{payerId,jdbcType=BIGINT},
      bill_type = #{billType,jdbcType=TINYINT},
      order_receivable_price = #{orderReceivablePrice,jdbcType=DECIMAL},
      order_after_sale_total_price = #{orderAfterSaleTotalPrice,jdbcType=DECIMAL},
      receivable_price = #{receivablePrice,jdbcType=DECIMAL},
      received_price = #{receivedPrice,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      file_path = #{filePath,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByStoreIdAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    financial_bill
    where tenant_id = #{tenantId}
    and payer_id = #{storeId}
  </select>

  <select id="list" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    financial_bill
    where tenant_id = #{tenantId}
    and payer_id = #{storeId}
    and `type` = #{type}
    <if test='status == "1"'>
      and `status` = 1
    </if>
    <if test='status == "2"'>
      and `status` = 2 and create_time between #{startTime} and #{endTime}
    </if>
    order by create_time desc
  </select>

  <select id="getYearReceivablePrice" resultType="decimal">
    select
        sum(receivable_price)
    from
    financial_bill
    where tenant_id = #{tenantId}
    and payer_id = #{storeId}
    and `type` = #{type}
    <choose>
      <when test='status == "1"'>
        and `status` = 1
      </when>
      <when test='status == "2"'>
        and `status` = 2 and create_time between #{startTime} and #{endTime}
      </when>
    </choose>
  </select>

  <update id="uploadStatus">
    update financial_bill
    set status = #{status},
        update_time = now()
    where id = #{billId}
  </update>

  <select id="queryNeedPayBill" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    financial_bill
    where payer_id = #{storeId} and tenant_id = #{tenantId} and status = 1
  </select>
</mapper>