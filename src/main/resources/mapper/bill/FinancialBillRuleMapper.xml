<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.bill.mapper.FinancialBillRuleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.bill.model.po.FinancialBillRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `type`, `day`, start_time, end_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_bill_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from financial_bill_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.FinancialBillRule" useGeneratedKeys="true">
    insert into financial_bill_rule (tenant_id, `type`, `day`, 
      start_time, end_time, create_time, 
      update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{day,jdbcType=INTEGER}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.FinancialBillRule" useGeneratedKeys="true">
    insert into financial_bill_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="day != null">
        `day`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="day != null">
        #{day,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.bill.model.po.FinancialBillRule">
    update financial_bill_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="day != null">
        `day` = #{day,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.bill.model.po.FinancialBillRule">
    update financial_bill_rule
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      `day` = #{day,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryTenantBillRule" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    financial_bill_rule
    where tenant_id = #{tenantId}
</select>
</mapper>