<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.payment.mapper.PaymentItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.payment.model.po.PaymentItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="payment_id" jdbcType="BIGINT" property="paymentId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_price" jdbcType="DECIMAL" property="orderPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="payment_receipt" jdbcType="VARCHAR" property="paymentReceipt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, payment_id, order_id, order_price, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from payment_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from payment_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.PaymentItem" useGeneratedKeys="true">
    insert into payment_item (tenant_id, payment_id, order_id,
      order_price, create_time)
    values (#{tenantId,jdbcType=BIGINT}, #{paymentId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT},
      #{orderPrice,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.PaymentItem" useGeneratedKeys="true">
    insert into payment_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.payment.model.po.PaymentItem">
    update payment_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.payment.model.po.PaymentItem">
    update payment_item
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      payment_id = #{paymentId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_price = #{orderPrice,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByOrderId" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, payment_id, order_id, order_price, pi.create_time,pi.payment_receipt
    from payment_item pi
           left join payment p on pi.payment_id = p.id
    where pi.tenant_id = #{tenantId} and pi.order_id = #{orderId}
    order by pi.id desc
    limit 1
  </select>
  <select id="selectByPaymentId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from payment_item
    where payment_id = #{paymentId}
  </select>

  <select id="selectPaySuccessByOrderId" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, payment_id, order_id, order_price, pi.create_time,pi.payment_receipt
    from payment_item pi
           left join payment p on pi.payment_id = p.id
    where p.status = 1 and pi.tenant_id = #{tenantId} and pi.order_id = #{orderId}
  </select>

  <select id="selectByOrderIds" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, payment_id, order_id, order_price, pi.create_time,pi.payment_receipt
    from payment_item pi
    left join payment p on pi.payment_id = p.id
    where pi.tenant_id = #{tenantId} and pi.order_id in
    <foreach collection="orderIds" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
  </select>

  <!-- 在这里配置命名空间等 -->

  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO payment_item (tenant_id, payment_id, order_id, order_price,payment_receipt)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId}, #{item.paymentId}, #{item.orderId}, #{item.orderPrice}, #{item.paymentReceipt})
    </foreach>
  </insert>

  <select id="selectByPaymentNo" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, pi.payment_id, pi.order_id, pi.order_price, pi.create_time,pi.payment_receipt
    from payment_item pi
           inner join payment p on pi.tenant_id = p.tenant_id and pi.payment_id = p.id
    where p.payment_no = #{paymentNo}
  </select>

  <select id="selectAllByOrderId" resultMap="BaseResultMap">
    select pi.id, pi.tenant_id, payment_id, order_id, order_price, pi.create_time,pi.payment_receipt
    from payment_item pi
           left join payment p on pi.payment_id = p.id
    where pi.tenant_id = #{tenantId} and pi.order_id = #{orderId}
    order by pi.id desc
  </select>
</mapper>