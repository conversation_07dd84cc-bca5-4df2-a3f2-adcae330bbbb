<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.payment.mapper.RefundAcctSplitDetailMapper">

    <insert id="saveBatch" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.RefundAcctSplitDetail"
            useGeneratedKeys="true">
        insert into refund_acct_split_detail (tenant_id, acct_split_tenant_id, huifu_id, div_amt, refund_id)
        values
        <foreach collection="refundAcctSplitDetailList" item="item" separator="," >
            (#{item.tenantId}, #{item.acctSplitTenantId}, #{item.huifuId}, #{item.divAmt}, #{item.refundId})
        </foreach>
    </insert>

</mapper>