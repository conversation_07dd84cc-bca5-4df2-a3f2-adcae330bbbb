<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.payment.mapper.HuiFuPaymentRateRetryMapper">

    <update id="increaseRetryNum">
        update huifu_payment_rate_retry set retry_num = retry_num + 1 where id = #{id}
    </update>

    <select id="selectListByTime" resultType="com.cosfo.mall.payment.model.po.HuiFuPaymentRateRetry">
        select id, payment_id paymentId, tenant_id tenantId
        from huifu_payment_rate_retry
        where create_time between #{startTime} and #{endTime} and retry_num <![CDATA[ < ]]> 3
        limit 50
    </select>
</mapper>