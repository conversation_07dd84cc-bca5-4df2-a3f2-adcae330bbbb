<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.system.mapper.SystemParametersMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.system.model.po.SystemParameters">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_key, param_value, description, create_time, update_time
  </sql>
  <select id="selectOne" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from system_parameters
    <where>
      <if test="paramKey != null">
        and param_key = #{paramKey}
      </if>
    </where>
  </select>
  <select id="selectByKey"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from system_parameters
    where param_key = #{paramKey}
  </select>
</mapper>
