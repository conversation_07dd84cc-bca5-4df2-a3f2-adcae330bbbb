<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.tenant.model.po.Tenant">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="belong_DB" property="belongDB"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="admin_id" property="adminId"/>
    <result column="profit_sharing_switch" property="profitSharingSwitch"/>
    <result column="online_pay_channel" property="onlinePayChannel"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, phone, password, tenant_name, type, status, belong_DB, create_time, update_time,
        admin_id, profit_sharing_switch,online_pay_channel
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.cosfo.mall.tenant.model.po.Tenant">
    insert into tenant (id, phone, password,
      tenant_name, type, status, belong_DB,
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
      #{tenantName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{belong_DB},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cosfo.mall.tenant.model.po.Tenant">
    insert into tenant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="tenantName != null">
        tenant_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="belongDB != null">
        belong_DB,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="tenantName != null">
        #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="belongDB != null">
        #{belongDB},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.tenant.model.po.Tenant">
    update tenant
    <set>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="belongDB != null">
        belong_DB = #{belongDB},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.tenant.model.po.Tenant">
    update tenant
    set phone = #{phone,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      belong_DB = #{belongDB},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByPhone" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from tenant
    where
       phone = #{phone}
  </select>

  <select id="querySupplierInfoBySupplierTenantIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from tenant
    <where>
        <if test="supplierTenantIds != null">
         id in
        <foreach collection="supplierTenantIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        </if>
    </where>
  </select>

  <select id="selectByAdminId" resultType="com.cosfo.mall.tenant.model.dto.TenantDTO">
    select
      id, tenant_name TenantName, admin_id adminId
    from
    tenant
    where admin_id = #{adminId}
  </select>
  
  <select id="selectByType" resultType="com.cosfo.mall.tenant.model.dto.TenantDTO">
    select
      id, tenant_name TenantName, phone, type, status
    from
      tenant
    where type = #{type}
  </select>
</mapper>