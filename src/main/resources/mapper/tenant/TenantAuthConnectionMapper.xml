<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.tenant.model.po.TenantAuthConnection">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="account_type" property="accountType"/>
    <result column="pay_mchid" jdbcType="VARCHAR" property="payMchid" />
    <result column="pay_secret" jdbcType="VARCHAR" property="paySecret"/>
    <result column="pay_cert_path" jdbcType="VARCHAR" property="payCertPath"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="huifu_id" jdbcType="VARCHAR" property="huifuId"/>
    <result column="secret_key" jdbcType="VARCHAR" property="secretKey"/>
    <result column="public_key" jdbcType="VARCHAR" property="publicKey"/>
    <result column="huifu_public_key" jdbcType="VARCHAR" property="huifuPublicKey"/>
    <result column="oa_app_id" property="oaAppId"/>
    <result column="oa_app_secret" property="oaAppSecret"/>
    <result column="wechat_direct_switch" property="wechatDirectSwitch"/>
    <result column="wechat_indirect_switch" property="wechatIndirectSwitch"/>
    <result column="wechat_indirect_sharing_switch" property="wechatIndirectSharingSwitch"/>
    <result column="ali_indirect_switch" property="aliIndirectSwitch"/>
    <result column="ali_indirect_sharing_switch" property="aliIndirectSharingSwitch"/>
    <result column="h5_wechat_indirect_switch" property="h5WechatIndirectSwitch"/>
    <result column="h5_wechat_indirect_sharing_switch" property="h5WechatIndirectSharingSwitch"/>
    <result column="wechat_indirect_plugin_switch" property="wechatIndirectPluginSwitch"/>
    <result column="applet_wechat_pay_switch" property="appletWechatPaySwitch"/>
    <result column="indirect_online_channel" property="indirectOnlineChannel"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, app_id, tenant_id, `status`, account_type, pay_mchid, pay_secret, pay_cert_path, create_time,
    update_time, huifu_id, secret_key,public_key,huifu_public_key, oa_app_id, oa_app_secret,
     wechat_direct_switch, wechat_indirect_switch, wechat_indirect_sharing_switch, ali_indirect_switch, ali_indirect_sharing_switch,
     h5_wechat_indirect_switch,h5_wechat_indirect_sharing_switch,wechat_indirect_plugin_switch,applet_wechat_pay_switch, indirect_online_channel
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tenant_auth_connection
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant_auth_connection
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.tenant.model.po.TenantAuthConnection" useGeneratedKeys="true">
    insert into tenant_auth_connection (app_id, tenant_id, `status`, account_type,
      pay_mchid, pay_secret, pay_cert_path, 
      create_time, update_time, huifu_id, secret_key, oa_app_id, oa_app_secret)
    values (#{appId,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{accountType},
      #{payMchid,jdbcType=VARCHAR}, #{paySecret,jdbcType=VARCHAR}, #{payCertPath,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{huifuId,jdbcType=VARCHAR}, #{secretKey,jdbcType=VARCHAR}, #{oaAppId}, #{oaAppSecret})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.tenant.model.po.TenantAuthConnection" useGeneratedKeys="true">
    insert into tenant_auth_connection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="payMchid != null">
        pay_mchid,
      </if>
      <if test="paySecret != null">
        pay_secret,
      </if>
      <if test="payCertPath != null">
        pay_cert_path,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="huifuId != null">
        huifu_id,
      </if>
      <if test="secretKey != null">
        secret_key,
      </if>
      <if test="publicKey != null">
        public_key,
      </if>
      <if test="huifuPublicKey != null">
        huifu_public_key,
      </if>
      <if test="oaAppId != null">
        oa_app_id,
      </if>
      <if test="oaAppSecret != null">
        oa_app_secret,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        #{accountType},
      </if>
      <if test="payMchid != null">
        #{payMchid,jdbcType=VARCHAR},
      </if>
      <if test="paySecret != null">
        #{paySecret,jdbcType=VARCHAR},
      </if>
      <if test="payCertPath != null">
        #{payCertPath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="huifuId != null">
        #{huifuId,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        #{publicKey},
      </if>
      <if test="huifuPublicKey != null">
        #{huifuPublicKey},
      </if>
      <if test="oaAppId != null">
        #{oaAppId},
      </if>
      <if test="oaAppSecret != null">
        #{oaAppSecret},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.tenant.model.po.TenantAuthConnection">
    update tenant_auth_connection
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        account_type = #{accountType},
      </if>
      <if test="payMchid != null">
        pay_mchid = #{payMchid,jdbcType=VARCHAR},
      </if>
      <if test="paySecret != null">
        pay_secret = #{paySecret,jdbcType=VARCHAR},
      </if>
      <if test="payCertPath != null">
        pay_cert_path = #{payCertPath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="huifuId != null">
        huifu_id = #{huifuId,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        secret_key = #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        public_key = #{publicKey},
      </if>
      <if test="huifuPublicKey != null">
        huifu_public_key = #{huifuPublicKey},
      </if>
      <if test="oaAppId != null">
        oa_app_id = #{oaAppId},
      </if>
      <if test="oaAppSecret != null">
        oa_app_secret = #{oaAppSecret},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.tenant.model.po.TenantAuthConnection">
    update tenant_auth_connection
    set app_id = #{appId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=INTEGER},
      account_type = #{accountType},
      pay_mchid = #{payMchid,jdbcType=VARCHAR},
      pay_secret = #{paySecret,jdbcType=VARCHAR},
      pay_cert_path = #{payCertPath,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      huifu_id = #{huifuId,jdbcType=VARCHAR},
      secret_key = #{secretKey,jdbcType=VARCHAR},
      oa_app_id = #{oaAppId},
      oa_app_secret = #{oaAppSecret}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByTenantId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from tenant_auth_connection
    where tenant_id = #{tenantId}
  </select>
  <select id="selectByAppId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tenant_auth_connection
    where app_id = #{appId}
  </select>
  <select id="selectOne" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tenant_auth_connection
    <where>
      <if test="appId != null">
        and app_id = #{appId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
  </select>
  <select id="selectByHuifuId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from tenant_auth_connection
    <where>
      huifu_id = #{huifuId}
    </where>
  </select>
</mapper>
