<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantDeliveryFeeRuleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.tenant.model.po.TenantDeliveryFeeRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="default_price" property="defaultPrice"/>
    <result column="free_number" property="freeNumber"/>
    <result column="free_type" property="freeType"/>
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `type`,default_price, free_number, free_type, `operator`, create_time, update_time
  </sql>

  <select id="selectByTenantId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tenant_delivery_fee_rule
    where tenant_id = #{tenantId}
  </select>
</mapper>
