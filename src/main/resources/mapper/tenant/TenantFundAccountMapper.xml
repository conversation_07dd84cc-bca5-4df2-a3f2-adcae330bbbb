<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantFundAccountMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.tenant.model.po.TenantFundAccount">
    <!--@mbg.generated-->
    <!--@Table tenant_fund_account-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, account_name, config_id, update_user_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tenant_fund_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tenant_fund_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.tenant.model.po.TenantFundAccount" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tenant_fund_account (tenant_id, account_name, config_id, 
      update_user_id, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{accountName,jdbcType=VARCHAR}, #{configId,jdbcType=BIGINT}, 
      #{updateUserId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.tenant.model.po.TenantFundAccount" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tenant_fund_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.tenant.model.po.TenantFundAccount">
    <!--@mbg.generated-->
    update tenant_fund_account
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.tenant.model.po.TenantFundAccount">
    <!--@mbg.generated-->
    update tenant_fund_account
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      account_name = #{accountName,jdbcType=VARCHAR},
      config_id = #{configId,jdbcType=BIGINT},
      update_user_id = #{updateUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tenant_fund_account
    where id in
    <foreach collection="ids" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>