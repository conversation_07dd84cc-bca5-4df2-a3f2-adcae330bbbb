<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantBillMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.mall.tenant.model.po.TenantBill">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="bill_price" jdbcType="DECIMAL" property="billPrice"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="record_no" jdbcType="VARCHAR" property="recordNo"/>
        <result column="payment_type" jdbcType="INTEGER" property="paymentType"/>
        <result column="online_pay_channel" jdbcType="INTEGER" property="onlinePayChannel"/>
        <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , tenant_id, store_id, create_time, update_time, bill_no, bill_price, `type`, record_no , payment_type, online_pay_channel, fee_amount
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tenant_bill
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tenant_bill
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.tenant.model.po.TenantBill"
            useGeneratedKeys="true">
        insert into tenant_bill (tenant_id, store_id, create_time,
                                 update_time, bill_no, bill_price,
                                 `type`, record_no, payment_type, online_pay_channel, fee_amount)
        values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{billNo,jdbcType=VARCHAR}, #{billPrice,jdbcType=DECIMAL},
                #{type,jdbcType=INTEGER}, #{recordNo,jdbcType=VARCHAR}, #{paymentType,jdbcType=INTEGER},
                #{onlinePayChannel,jdbcType=INTEGER}, #{feeAmount,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.mall.tenant.model.po.TenantBill" useGeneratedKeys="true">
        insert into tenant_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="billNo != null">
                bill_no,
            </if>
            <if test="billPrice != null">
                bill_price,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="recordNo != null">
                record_no,
            </if>
            <if test="paymentType != null">
                payment_type,
            </if>
            <if test="onlinePayChannel != null">
                online_pay_channel,
            </if>
            <if test="feeAmount != null">
                fee_amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="billNo != null">
                #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="billPrice != null">
                #{billPrice,jdbcType=DECIMAL},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="recordNo != null">
                #{recordNo,jdbcType=VARCHAR},
            </if>
            <if test="paymentType != null">
                #{paymentType,jdbcType=INTEGER},
            </if>
            <if test="onlinePayChannel != null">
                #{onlinePayChannel,jdbcType=INTEGER},
            </if>
            <if test="feeAmount != null">
                #{feeAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.tenant.model.po.TenantBill">
        update tenant_bill
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="billNo != null">
                bill_no = #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="billPrice != null">
                bill_price = #{billPrice,jdbcType=DECIMAL},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="recordNo != null">
                record_no = #{recordNo,jdbcType=VARCHAR},
            </if>
            <if test="paymentType != null">
                payment_type = #{paymentType,jdbcType=INTEGER},
            </if>
            <if test="onlinePayChannel != null">
                online_pay_channel = #{onlinePayChannel,jdbcType=INTEGER},
            </if>
            <if test="feeAmount != null">
                fee_amount = #{feeAmount,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.tenant.model.po.TenantBill">
        update tenant_bill
        set tenant_id          = #{tenantId,jdbcType=BIGINT},
            store_id           = #{storeId,jdbcType=BIGINT},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            bill_no            = #{billNo,jdbcType=VARCHAR},
            bill_price         = #{billPrice,jdbcType=DECIMAL},
            `type`             = #{type,jdbcType=INTEGER},
            record_no          = #{recordNo,jdbcType=VARCHAR},
            payment_type       = #{paymentType,jdbcType=INTEGER},
            online_pay_channel = #{onlinePayChannel,jdbcType=INTEGER},
            fee_amount         = #{feeAmount,jdbcType=INTEGER},
            where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryByTenantIdAndRecordNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        tenant_bill
        where
        tenant_id = #{tenantId}
        and record_no = #{recordNo}
    </select>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tenant_bill
        (tenant_id, store_id, bill_no, bill_price, type, record_no, payment_type,
        online_pay_channel, fee_amount)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantId}, #{item.storeId}, #{item.billNo},
            #{item.billPrice}, #{item.type}, #{item.recordNo}, #{item.paymentType}, #{item.onlinePayChannel},
            #{item.feeAmount})
        </foreach>
    </insert>
</mapper>