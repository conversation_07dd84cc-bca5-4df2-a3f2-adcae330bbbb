<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantDeliveryFeeAreaMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.tenant.model.po.TenantDeliveryFeeArea">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="default_price" jdbcType="DECIMAL" property="defaultPrice" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, rule_id, province, city, area, default_price, `rule`, create_time, 
    update_time, group_id
  </sql>
</mapper>