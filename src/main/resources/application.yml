spring:
  application:
    name: wurth-mall
  profiles:
    active: wurth
logging:
  level:
    root: info
    org.springframework: DEBUG  # Change this to DEBUG
    org.mybatis: INFO
    com.cosfo.mall: INFO
  pattern:
    console: "%d - %msg%n"
server:
  port: 8081
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: false  # 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
  supportMethodsArguments: true
  params: count=countSql

huifu:
  sys_id: 6666000125128386
  product_id: MCS

xm:
  log:
    enable: true
    resp: true

mybatis-plus:
  global-config:
    db-config:
      id-type: AUTO

qiniu:
  XM_BUCKETNAME: suyuan

springdoc:
  api-docs:
    enabled: true