logging:
  level:
    root: info
    org.springframework: INFO
    org.mybatis: INFO
    com.cosfo.mall: INFO
  pattern:
    console: "%d - %msg%n"
server:
  port: 80
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: false  # 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
  supportMethodsArguments: true
  params: count=countSql

# 数据库配置
spring:
  application:
    name: wurth-mall
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ************************************************************************************************************
          username: dev
          password: xianmu619
          driver-class-name: com.mysql.cj.jdbc.Driver
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000

  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 10
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 11bd83ed-c356-47d5-9d67-754a7da15a94
    groupId: wurth-mall
    appKey: jTODo7thlrroJRQoCJSkdg
  # auth服务依赖
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 10
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）

redisson:
  address: test-redis.summerfarm.net:6379
  password: xianmu619
  type: STANDALONE
  enabled: true
  database: 10

tenant:
  # 是否开启租户模式
  enable: false

executor:
  corePoolSize: 10
  maxPoolSize: 200
  queueCapacity: 1024
  keepAliveTime: 0


dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: 6fe25753-27a3-43fc-8a53-43ee406f1c44
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

xm:
  log:
    enable: true
    resp: true

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: afa07906-0987-4392-b1de-74cb4218ae9f

wurth:
  system:
    tenant_id: 10000

permission:
  allowedAnonymousUris:
    - /marketClassification/**
    - /home/<USER>
    - /marketItem/**