package com.cosfo.mall.wechat.service.Impl;

import com.cosfo.mall.wechat.service.WeixinService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class WeixinServiceImplTest {
    @Resource
    private WeixinService weixinService;

    @Test
    void getOaOpenId() {
        String oaOpenId = weixinService.getOaOpenId("041XmI000BSPgQ1QE4300W4Ym42XmI04", 2L);

    }
}