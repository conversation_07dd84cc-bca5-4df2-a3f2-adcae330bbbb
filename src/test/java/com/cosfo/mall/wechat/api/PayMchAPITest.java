package com.cosfo.mall.wechat.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.order.model.dto.HuiFuConfirmResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuResultDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.bean.profitsharing.*;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class PayMchAPITest {

    @Resource
    private HuiFuConfig huiFuConfig;


    @Test
    void queryProfitsharingOrder() {
        QueryOrderParams queryOrderParams = new QueryOrderParams();
        queryOrderParams.setOutOrderNo("420000155520221019021fassdfafsdfd");
        queryOrderParams.setTransactionId("4200001555202210190216934961");
        PayMchAPI payMchAPI = new PayMchAPI();
        ProfitSharingOrderResult profitSharingOrderResult = payMchAPI.queryProfitsharingOrder(queryOrderParams, "C:\\Users\\<USER>\\Downloads\\WXCertUtil\\cert", "1632558530");
        System.out.println(JSON.toJSON(profitSharingOrderResult));
    }

    @Test
    void queryAmounts() {
        PayMchAPI payMchAPI = new PayMchAPI();
        ProfitSharingOrderAmount profitSharingOrderAmount = payMchAPI.queryAmounts("4200001583202210199473252514", "C:\\Users\\<USER>\\Downloads\\WXCertUtil\\cert", "1632558530");
        System.out.println(JSON.toJSON(profitSharingOrderAmount));
    }

    @Test
    void queryPayResult() {
        PayMchAPI payMchAPI = new PayMchAPI();
        payMchAPI.queryDirectPayResult("1363921802", "P1582985413869158400", "");
        ProfitSharingOrderAmount profitSharingOrderAmount = payMchAPI.queryAmounts("4200001583202210199473252514", "C:\\Users\\<USER>\\Downloads\\WXCertUtil\\cert", "1632558530");
        System.out.println(JSON.toJSON(profitSharingOrderAmount));
    }

    @Test
    public void test() {
        String body = "{\"data\":{\"acct_resp_code\":\"000\",\"acct_resp_desc\":\"成功\",\"confirmed_amt\":\"0.10\",\"hf_seq_id\":\"003500TOP2A230106140754P820ac139c4900000\",\"huifu_id\":\"6666000124879551\",\"org_hf_seq_id\":\"002900TOP2A230106103839P837ac139c2c00000\",\"org_mer_ord_id\":\"P1611190476735614976\",\"org_req_date\":\"20230106\",\"org_req_seq_id\":\"P1611190476735614976\",\"req_date\":\"20230106\",\"req_seq_id\":\"HP2023010614075305\",\"resp_code\":\"00000000\",\"resp_desc\":\"交易成功\",\"trans_stat\":\"S\",\"unconfirm_amt\":\"0.00\"},\"sign\":\"CuVOgrDk/ppMBGvYBmT9Qr+Yd2Bb292ocRCzE5MsWLWNUbLcv3wXyNgZi8WUF4v1/7Y5UUceG85iVXbLhuCVI3pKFqAlwpC5P9jBw/LnP2nfED+z8N+AHRmZjZB8JzFkTCW+QJw3Pc+9VnvnRDs9lPuppwR1gIth9iPPKff6Bgpllj4Ed09rf7I0DnDNLqnmZRDUg8PcMRYrtkw8NA88dIzTM+0I+He2+GoeNA8wq5xmWNRyFgN81+i4BuxfWEk5Pj+xhl5Z9Z2+3OKrj29jWvH0vtXeV+M8H+z6qPQ6xAn4/+N8iTEEHJlUPTPQZqiiJlNmtt18EfZpPc8FiBa5ZQ==\"}";

        HuiFuResultDTO huiFuResultDTO = JSONObject.parseObject(body, HuiFuResultDTO.class);
        HuiFuConfirmResponseDTO huiFuConfirmResponseDTO = JSONObject.parseObject(JSON.toJSONString(huiFuResultDTO.getData()), HuiFuConfirmResponseDTO.class);
    }

    @Test
    public void queryHuiFuResult() {
        HuiFuPayment huiFuPayment = new HuiFuPayment();
        huiFuPayment.setReqDate("20230331");
        huiFuPayment.setHuifuId("6666000124879551");
        huiFuPayment.setHfSeqId("HP2023033100521695");
        TenantAuthConnection authConnection = new TenantAuthConnection();
        authConnection.setSecretKey("MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJwjby9EM/o1sFx7MfsyQENTwHpqAWyN+GY/f/MxfwqYq1s9n5rr7rttarQ2+jz3Pqffj3rxI0cwp9qcyc5+vtdmQlkDEbdDlKIrxn0IdMOtbiWxbCTS5A62mwmgIMIctMkO/WMLyLGbVKwO03hmme2MN5JcfZsacEclw7VVevcBAgMBAAECgYA9v42zBagApcbvbBEiJIdhmPef8dhKVAMOfMJfLQ3u5N2voblDDVdGbEksqClifu0aesFFf6PR/Z4ESMlOjrVt3+jy/dEXshYaGefnTFDejda4CqDlxyC0Urtx+nJR/EEWkLdE0J31hCas2qd4uriQWb9LYwyT+Vmzfil7hakm+QJBAMxa1R2dUWhytu7I+Lv4wd1IsgGiN8XpLkZrYu/ELef+/5Hin8ulh5wXplgHAXmOBtI1F/BubmUFrr/J0XXkzM8CQQDDmSPIVebNbL9c+RRyLK8IxStK33loLqiyP3Ki9w5DkyEfQIS1ZKEn3yiCMF6lSPPC9kdGJojEDsmMljYj2hMvAkEAlysaO64Eap9xc3J3jZWW03rTMyIs39p+wNjvdy5IRzX7GI/sZVkt1+omwzdrf9/wa8+axeDrCRALFRTWPb/jQQJADpRao3MrugIyHWb/jCcigN/Zg0I3FXns3yR1kgoiSQ0tedvgSHkQvh0XMuUSGPXgHZcn5uW2ag7EHTORI4j8xwJBAL+ypYYl5YOXSfiv1vXSmExIbUVUpuGmw7I0PNEL0o5FIi4zR6cluD87T8rVEIiGt0Oog6gYk87fQhnUefpWep4=");

//        HuiFuiPaymentReceive huiFuiPaymentReceive = PayMchAPI.queryHuiFuProfitsharingOrder(huiFuPayment, authConnection, huiFuConfig);
    }

    @Test
    public void closePayOrderTest(){
        String paymentNo = "P1678678406082662400";
        String mchId = "1632558530";
        String certPath = "E:\\cert";
        PayMchAPI.wechatCloseJsapiPayOrder(paymentNo, mchId, certPath);
    }
}