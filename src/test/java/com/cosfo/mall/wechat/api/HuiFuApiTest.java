package com.cosfo.mall.wechat.api;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.HuiFuBalanceQueryDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentCloseResponseDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.bean.huifu.QryWxConfDTO;
import com.cosfo.mall.wechat.bean.huifu.SettleMentInfoDTO;
import net.xianmu.common.exception.BizException;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/11
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class HuiFuApiTest {

    @Resource
    private HuiFuConfig huiFuConfig;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;

    @Test
    public void test(){
        String wx = "{\"pay_scene\":\"1\",\"wx_mer_infos\":[{\"pay_channel_id\":\"********\",\"sub_mer_id\":\"W1035134718842880697\",\"bank_mer_code\":\"*********\"}],\"pay_channel_id_list\":[\"********\"],\"fee_charge_type\":\"1\",\"fee_rate\":\"0.23\"}";
        JSONObject.parseObject(wx, QryWxConfDTO.class);

        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(700L);
        SettleMentInfoDTO settleMentInfoDTO = HuiFuApi.queryMerchantBasicData(tenantAuthConnection, huiFuConfig);
        System.out.println(settleMentInfoDTO);
    }

    @Test
    public void closeTest(){
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(1003L);
        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectById(1872L);
        HuiFuApi.huiFuPaymentClose(huiFuPayment,tenantAuthConnection, huiFuConfig);

        HuiFuPaymentCloseResponseDTO huiFuPaymentCloseResponseDTO = HuiFuApi.huiFuPaymentCloseQuery(huiFuPayment, tenantAuthConnection, huiFuConfig);
    }

    @Test
    public void huiFuBalanceQueryTest(){
        String huiFuId = "**************51";
        List<TenantAuthConnection> tenantAuthConnections = tenantAuthConnectionMapper.selectByHuifuId(huiFuId);
        HuiFuBalanceQueryDTO huiFuBalanceQueryDTO = new HuiFuBalanceQueryDTO();
        huiFuBalanceQueryDTO.setReqDate(TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_STRING));
        huiFuBalanceQueryDTO.setReqSeqId(Global.createHuiFuNo(Global.HUIFU_BALANCE_INFO_CODE));
        huiFuBalanceQueryDTO.setHuiFuId(huiFuId);
        HuiFuApi.huiFuBalanceQuery(huiFuBalanceQueryDTO, tenantAuthConnections.get(NumberConstant.ZERO), huiFuConfig);
    }
}