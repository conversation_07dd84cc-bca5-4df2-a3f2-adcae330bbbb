package com.cosfo.mall.wechat.api;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.wechat.client.LocalHttpClient;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/28
 */
class WeChatOAApiTest {

    @Test
    void getWeChatCode() {
        String url = WeChatOAApi.getWeChatCode("", "snsapi_userinfo", "wx86d6db2ec4b9e9cc");
        HttpUriRequest httpUriRequest = RequestBuilder.get()
                .setUri(url)
                .build();
        String code = LocalHttpClient.executeJsonResult(httpUriRequest, String.class);
        System.out.println(code);
    }

    @Test
    void getOpenId() {
    }
}