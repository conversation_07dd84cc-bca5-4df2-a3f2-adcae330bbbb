package com.cosfo.mall.tenant.service;

import com.cosfo.mall.common.config.TenantGrayConfig;
import com.cosfo.mall.tenant.model.vo.TenantCustomerPhoneVO;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantServiceTest {

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantGrayConfig tenantGrayConfig;

    @Test
    void queryTenantCustomerPhone() {
        List<TenantCustomerPhoneVO> tenantCustomerPhoneVOS = tenantService.queryTenantCustomerPhone(2L);
        System.out.println(tenantCustomerPhoneVOS);
    }

    @Test
    void enableHuiFuRefundRetry() {
        boolean enableNew = tenantGrayConfig.enableHuiFuRefundRetry(2L);
        System.out.println(enableNew);
    }
}