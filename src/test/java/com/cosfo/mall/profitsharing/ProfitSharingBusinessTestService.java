package com.cosfo.mall.profitsharing;

import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @description: 分账业务测试类
 * @author: George
 * @date: 2024-01-10
 **/
@SpringBootTest
public class ProfitSharingBusinessTestService {

    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;

    /**
     * 保存分账规则单测
     */
    @Test
    public void testSaveOrderProfitSharingRule() {
        Long orderId = 99870L;
        profitSharingBusinessService.saveOrderProfitSharingRule(orderId);
    }

    @Test
    public void testCalculateProfitSharing() {
        BillProfitSharingOrderDTO billProfitSharingOrderDTO = new BillProfitSharingOrderDTO();
        billProfitSharingOrderDTO.setId(1L);
        billProfitSharingOrderDTO.setTenantId(2L);
        billProfitSharingOrderDTO.setOrderId(99208L);
        profitSharingBusinessService.calculateProfitSharing(billProfitSharingOrderDTO);
    }
}
