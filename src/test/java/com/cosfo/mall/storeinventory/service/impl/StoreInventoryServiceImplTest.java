package com.cosfo.mall.storeinventory.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.storeinventory.model.dto.MarketItemStoreInventoryCheckDTO;
import com.cosfo.mall.storeinventory.model.input.StoreInventoryCheckInput;
import com.cosfo.mall.storeinventory.model.vo.StoreInventoryCheckOrderVO;
import com.cosfo.mall.storeinventory.service.StoreInventoryService;
import com.cosfo.storeinventory.application.checkorder.CheckOrderCommandService;
import com.cosfo.storeinventory.application.inventory.StoreInventoryQueryService;
import com.cosfo.storeinventory.model.dto.StoreInventoryCheckRecordPageQueryDTO;
import com.cosfo.storeinventory.model.dto.req.CheckOrderOptReqDTO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
class StoreInventoryServiceImplTest {


    @Resource
    private CheckOrderCommandService checkOrderCommandService;

    @Resource
    private StoreInventoryService storeInventoryService;

    @Resource
    private StoreInventoryQueryService storeInventoryQueryService;

    @Test
    void checkDelete() {
        checkOrderCommandService.checkDelete("aaaaa123", 222L, "bbb");
    }

    @Test
    void checkSubmit() {
        CheckOrderOptReqDTO reqDTO = new CheckOrderOptReqDTO();
        reqDTO.setInventoryCheckNo("");
//        reqDTO.setTenantId(2L);
//        reqDTO.setStoreId(0L);
        reqDTO.setCheckName("");
        reqDTO.setUpdatorId(0L);
        reqDTO.setUpdatorName("");
        reqDTO.setItemDTOList(Lists.newArrayList());

        checkOrderCommandService.checkSubmit(reqDTO);
    }


//    @Test
//    void orderFinishStoreInventoryInbound() {
//        Long orderId = 117618L;
//        storeInventoryService.orderFinishStoreInventoryInbound(orderId);
//
//    }

    @Test
    void pageCheckRecord() {
        StoreInventoryCheckRecordPageQueryDTO dto = new StoreInventoryCheckRecordPageQueryDTO();
//        dto.setRecordStatus();
//        dto.setStartTime();
//        dto.setEndTime();
        dto.setPageIndex(1);
        dto.setPageSize(5);
        storeInventoryService.pageCheckRecord(dto, 4260L, 2L);
    }

    @Test
    void checkSubmit1() {
        StoreInventoryCheckInput input = new StoreInventoryCheckInput();
        input.setInventoryCheckNo("");
        input.setCheckName("");
        List<MarketItemStoreInventoryCheckDTO> itemList = new ArrayList<>();
        MarketItemStoreInventoryCheckDTO inventoryCheckDTO = new MarketItemStoreInventoryCheckDTO();
        inventoryCheckDTO.setItemId(47530L);
        inventoryCheckDTO.setTitle("金典测试");
        inventoryCheckDTO.setSpecification("300mL*10瓶");
        inventoryCheckDTO.setExpectedQuantity(BigDecimal.ZERO);
        inventoryCheckDTO.setActualQuantity("18");
        inventoryCheckDTO.setStoreInventoryUnit("瓶");
        itemList.add(inventoryCheckDTO);
        input.setItemList(itemList);

        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4260L);
        loginContextInfoDTO.setAccountId(3530L);
        loginContextInfoDTO.setAccountName("xwk");

        StoreInventoryCheckOrderVO vo = storeInventoryService.checkSubmit(input, loginContextInfoDTO);
        System.err.println(JSON.toJSONString(vo));
    }


    @Test
    void checkStaging() {
        StoreInventoryCheckInput input = JSON.parseObject("{\"inventoryCheckNo\":\"*********************\",\"itemList\":[{\"actualQuantity\":1,\"expectedQuantity\":1,\"id\":243,\"itemId\":47390,\"mainPicture\":\"https://azure.cosfo.cn/test/o3zsu6ke7cruubn2u.png\",\"specification\":\"1箱*1斤\",\"storeInventoryUnit\":\"箱\",\"title\":\"总部预售aa\",\"autoFocus\":false},{\"actualQuantity\":2.111111,\"expectedQuantity\":101,\"id\":242,\"itemId\":47530,\"mainPicture\":\"https://azure.cosfo.cn/test/3f15tu8zkq7tg5t6s.jpg\",\"specification\":\"300mL*10瓶\",\"storeInventoryUnit\":\"瓶\",\"title\":\"金典测试\",\"autoFocus\":false}]}", StoreInventoryCheckInput.class);


//        StoreInventoryCheckInput input = new StoreInventoryCheckInput();
//        input.setInventoryCheckNo("PD1821060379359490048");
//        input.setCheckName("");
//        List<MarketItemStoreInventoryCheckDTO> itemList = new ArrayList<>();
//        MarketItemStoreInventoryCheckDTO inventoryCheckDTO = new MarketItemStoreInventoryCheckDTO();
//        inventoryCheckDTO.setItemId(47530L);
//        inventoryCheckDTO.setTitle("金典测试");
//        inventoryCheckDTO.setSpecification("300mL*10瓶");
//        inventoryCheckDTO.setExpectedQuantity(0);
//        inventoryCheckDTO.setActualQuantity(13);
//        inventoryCheckDTO.setStoreInventoryUnit("瓶");
//        itemList.add(inventoryCheckDTO);
//        input.setItemList(itemList);

        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4260L);
        loginContextInfoDTO.setAccountId(3530L);
        loginContextInfoDTO.setAccountName("xwk");

        StoreInventoryCheckOrderVO vo = storeInventoryService.checkStaging(input, loginContextInfoDTO);
        System.err.println(JSON.toJSONString(vo));
    }

    @Test
    void existStoreItemInventoryLog(){
        boolean flag = storeInventoryQueryService.existStoreItemInventoryLog("*****************");
        System.err.println(flag);
    }

}