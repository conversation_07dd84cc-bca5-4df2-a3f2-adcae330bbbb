package com.cosfo.mall.planorder.provider.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.mall.client.planorder.provider.PlanOrderCreateOrderProvider;
import com.cosfo.mall.client.planorder.req.PlanOrderCreateOrderReq;
import com.cosfo.manage.client.planorder.PlanOrderQueryProvider;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
class PlanOrderCreateOrderProviderImplTest {

    @DubboReference
    private PlanOrderCreateOrderProvider planOrderCreateOrderProvider;
    @DubboReference
    private PlanOrderQueryProvider planOrderQueryProvider;

    @Test
    void createOrder() {

        PlanOrderDetailResp detailResp = RpcResponseUtil.handler(planOrderQueryProvider.queryByPlanOrderNo("PO17097995594751946"));

        PlanOrderCreateOrderReq req = new PlanOrderCreateOrderReq();
        req.setStoreId(4260L);
        req.setTenantId(2L);
        req.setPlanOrderNo(detailResp.getPlanOrderNo());

        List<PlanOrderCreateOrderReq.PlanOrderItem> orderItemList = detailResp.getPlanOrderItems().stream().map(e -> {
            PlanOrderCreateOrderReq.PlanOrderItem planOrderItem = new PlanOrderCreateOrderReq.PlanOrderItem();
            planOrderItem.setItemId(e.getItemId());
            planOrderItem.setQuantity(e.getItemAmount());
            return planOrderItem;
        }).collect(Collectors.toList());
//        Map<Long, Integer> itemMap = new HashMap<>();
//        itemMap.put(559L, 3);
//        itemMap.put(21752L, 7);
//        itemMap.put(24572L, 2);
//        itemMap.put(24573L, 4);
//        req.setOrderItemList(build(itemMap));
        req.setOrderItemList(orderItemList);

        DubboResponse<Boolean> dubboResponse = planOrderCreateOrderProvider.createOrder(req);
        System.err.println(JSON.toJSONString(dubboResponse));
    }

    private List<PlanOrderCreateOrderReq.PlanOrderItem> build(Map<Long, Integer> itemMap){
        List<PlanOrderCreateOrderReq.PlanOrderItem> orderItemList = new ArrayList<>();

        for (Map.Entry<Long, Integer> entry : itemMap.entrySet()) {
            PlanOrderCreateOrderReq.PlanOrderItem planOrderItem = new PlanOrderCreateOrderReq.PlanOrderItem();
            planOrderItem.setItemId(entry.getKey());
            planOrderItem.setQuantity(entry.getValue());
            orderItemList.add(planOrderItem);
        }

        return orderItemList;
    }
}