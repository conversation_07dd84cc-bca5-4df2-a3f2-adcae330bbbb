package com.cosfo.mall.planorder.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.planorder.model.input.PlanOrderListQueryInput;
import com.cosfo.mall.planorder.model.input.PlanOrderQueryInput;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
class PlanOrderServiceTest {

    @Resource
    private PlanOrderService planOrderService;

    @Test
    void countWaitConfirmPlanOrderNum() {
        Integer count = planOrderService.countWaitConfirmPlanOrderNum(2L,4260L);
        System.err.println(count);
    }

    @Test
    void listPlanOrder() {
        PlanOrderListQueryInput input = new PlanOrderListQueryInput();
        input.setPlanOrderStatus(null);
        input.setPageIndex(1);
        input.setPageSize(6);
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4260L);
        System.err.println(planOrderService.listPlanOrder(input, loginContextInfoDTO));
    }

    @Test
    void getPlanOrderDetail() {
        PlanOrderQueryInput input = new PlanOrderQueryInput();
        input.setPlanOrderNo("PO17092886230480777");
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4260L);
        System.err.println(planOrderService.getPlanOrderDetail(input, loginContextInfoDTO));
    }
}