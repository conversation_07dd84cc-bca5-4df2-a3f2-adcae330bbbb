package com.cosfo.mall.facade.sap;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.mall.common.utils.CXMLUtil;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.facade.sap.dto.SapAtpCXML;
import com.cosfo.mall.facade.sap.dto.SapPushOrderDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import static java.math.BigDecimal.ZERO;

@SpringBootTest
public class SapApiFacadeTest {


    @Resource
    private SapApiFacade sapApiFacade;

    @Test
    void queryAtp() {
        BigDecimal atp = sapApiFacade.queryAtp("0893980   035   12");

        System.err.println("Current ATP: " + atp);
    }

    @Test
    void pushOrder() {
//        Global.createOrderNo(Global.NORMAL_ORDER_CODE)

        List<String> soldToList = Arrays.asList("129100044","129104588","129115372","129104588","129106022");
//        SapPushOrderDTO.OrderPO orderPO = new SapPushOrderDTO.OrderPO();
//        orderPO.setOrderNo("OR174555960414266");
//        orderPO.setSoldTo("129100044");
//        orderPO.setShipTo("129100044");
//        orderPO.setOrderDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
//        orderPO.setOrderTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));
//        List<SapPushOrderDTO.OrderItemPO> itemList = new ArrayList<>();
//        orderPO.setItemList(itemList);
//        SapPushOrderDTO.OrderItemPO orderItemPO = new SapPushOrderDTO.OrderItemPO();
//        itemList.add(orderItemPO);
//        orderItemPO.setItemNo(10);
//        orderItemPO.setMaterial("0046000306092 2000");
//        orderItemPO.setQuantity(2);
//        orderItemPO.setNetPrice(new BigDecimal("20"));
//        orderItemPO.setUnit(100);
//        orderItemPO.setNetValue(new BigDecimal("40"));

        SapPushOrderDTO sapPushOrderDTO = new SapPushOrderDTO();
        List<SapPushOrderDTO.OrderPO> orderList = new ArrayList<>();
        sapPushOrderDTO.setOrderList(orderList);

        for (String soldTo : soldToList) {
            SapPushOrderDTO.OrderPO orderPO = new SapPushOrderDTO.OrderPO();
            orderPO.setOrderNo(Global.createOrderNo(Global.NORMAL_ORDER_CODE));
            orderPO.setSoldTo(soldTo);
            orderPO.setShipTo(soldTo);
            orderPO.setOrderDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            orderPO.setOrderTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));

            List<SapPushOrderDTO.OrderItemPO> itemList = build();
            orderPO.setItemList(itemList);

            BigDecimal totalAmount = itemList.stream().map(SapPushOrderDTO.OrderItemPO::getNetValue).reduce(ZERO, BigDecimal::add);
            orderPO.setTaxAmount(NumberUtil.mul(totalAmount, new BigDecimal("0.05")));
            orderPO.setRemark("测试备注信息test");
            orderList.add(orderPO);
        }

        sapApiFacade.pushOrder(sapPushOrderDTO);
    }

    private List<SapPushOrderDTO.OrderItemPO> build() {
        List<SapPushOrderDTO.OrderItemPO> itemList = new ArrayList<>();
        Random random = new Random();

        // 物料数据
        String[] materials = {
                "0046000306092 2000",
                "005510 100961  100",
                "005512 35 090  100",
                "00576  30 005  100",
                "0810280   961   20"
        };

        // 运费物料
        String freightMaterial = "0996000110090    1";

        // 生成物料项
        for (int i = 0; i < materials.length; i++) {
            SapPushOrderDTO.OrderItemPO orderItemPO = new SapPushOrderDTO.OrderItemPO();
            itemList.add(orderItemPO);
            orderItemPO.setItemNo(10 + (i * 10)); // ItemNo 从10开始，每个物料加10
            orderItemPO.setMaterial(materials[i]);
            int quantity = random.nextInt(10) + 1; // 随机数量 1-10
            orderItemPO.setQuantity(quantity);
            BigDecimal netPrice = BigDecimal.valueOf(10 + random.nextInt(91)); // 随机单价 10-100
            orderItemPO.setNetPrice(netPrice);
            orderItemPO.setUnit(1);
            orderItemPO.setNetValue(netPrice.multiply(BigDecimal.valueOf(quantity))); // NetValue = NetPrice * Quantity
        }

        // 生成运费项
        SapPushOrderDTO.OrderItemPO freightItemPO = new SapPushOrderDTO.OrderItemPO();
//        itemList.add(freightItemPO);
        freightItemPO.setItemNo(60); // 运费物料的 ItemNo 为60
        freightItemPO.setMaterial(freightMaterial);
        freightItemPO.setQuantity(1); // 运费数量为1
        BigDecimal freightNetPrice = BigDecimal.valueOf(10 + random.nextInt(91)); // 随机单价 10-100
        freightItemPO.setNetPrice(freightNetPrice);
        freightItemPO.setUnit(1);
        freightItemPO.setNetValue(freightNetPrice); // NetValue = NetPrice * Quantity

        // 输出结果
        for (SapPushOrderDTO.OrderItemPO item : itemList) {
            System.out.println("ItemNo: " + item.getItemNo() +
                    ", Material: " + item.getMaterial() +
                    ", Quantity: " + item.getQuantity() +
                    ", NetPrice: " + item.getNetPrice() +
                    ", Unit: " + item.getUnit() +
                    ", NetValue: " + item.getNetValue());
        }

        return itemList;
    }


    @Test
    void parseXml(){
        String xmlStr = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><!DOCTYPE cXML SYSTEM \"http://xml.cxml.org/schemas/cXML/1.1.010/cXML.dtd\">\n" +
                "<cXML version=\"1.2.011\" Material=\"0893980   035   12\" timestamp=\"20250422045600.468\">\n" +
                "    <Response>\n" +
                "        <Status code=\"200\" />\n" +
                "        <curr_atp date=\"\">\n" +
                "          109.000\n" +
                "        </curr_atp>\n" +
                "        <next_atp date=\"2025-09-25\">\n" +
                "          \n" +
                "        </next_atp>\n" +
                "    </Response>\n" +
                "</cXML>";

        SapAtpCXML sapAtpCXML = null;
        try {
            sapAtpCXML = CXMLUtil.fromXML(xmlStr, SapAtpCXML.class);
        } catch (JAXBException e) {
            e.printStackTrace();
        }

        System.err.println("Response Status Code: " + sapAtpCXML.getResponse().getStatus().getCode());
        System.err.println("Current ATP: " + sapAtpCXML.getResponse().getCurrAtp());
    }
}