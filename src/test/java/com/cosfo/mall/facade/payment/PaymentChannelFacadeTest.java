package com.cosfo.mall.facade.payment;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.facade.dto.payment.PaymentChannelQueryByIdDTO;
import com.cosfo.mall.facade.dto.payment.PaymentRoutingDTO;
import com.cosfo.mall.facade.dto.payment.PaymentRoutingQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-08-22
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles("dev")
public class PaymentChannelFacadeTest {

    @Resource
    private PaymentChannelFacade paymentChannelFacade;

    @Test
    public void testQueryChannelById() {
        Long id = 40L;
        PaymentChannelQueryByIdDTO dto = paymentChannelFacade.queryPaymentChannelById(id);
        log.info("query channel by id req:{}, resp:{}", id, JSON.toJSON(dto));
    }

    @Test
    public void testGetRoutingInfo() {
        PaymentRoutingQueryDTO dto = new PaymentRoutingQueryDTO();
        dto.setTenantId(2L);
        dto.setBusinessLine("saas");
        dto.setPlatform("h5");
        dto.setRouteKey("tenantId");
        dto.setRouteValue("2");
        dto.setPaymentMethod("wechat");
        PaymentRoutingDTO routingDTO = paymentChannelFacade.getRoutingInfo(dto);
        log.info("get routing info req:{}, resp:{}", JSON.toJSON(dto), JSON.toJSON(routingDTO));
    }
}
