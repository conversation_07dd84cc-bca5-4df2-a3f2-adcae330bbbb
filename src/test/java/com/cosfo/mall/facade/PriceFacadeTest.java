package com.cosfo.mall.facade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.xianmu.inventory.client.saleinventory.dto.req.QueryAddressSkuInventoryReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryResDTO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class PriceFacadeTest {
    @Autowired
    private PriceFacade priceFacade;
    @Autowired
    private SaleInventoryCenterQueryFacade saleInventoryCenterQueryFacade;

    @Test
    void queryAddressSkuInventoryInfo() {
        QueryAddressSkuInventoryReqDTO queryAddressSkuInventoryReqDTO = new QueryAddressSkuInventoryReqDTO();
        queryAddressSkuInventoryReqDTO.setTenantId(2L);
        queryAddressSkuInventoryReqDTO.setProvince("江西省");
        queryAddressSkuInventoryReqDTO.setCity("南昌市");
        queryAddressSkuInventoryReqDTO.setArea("红谷滩区");
        queryAddressSkuInventoryReqDTO.setAddress("西站大街1号(南昌西站地铁站出口步行110米)");
        queryAddressSkuInventoryReqDTO.setPoi("115.79297,28.622865");
        queryAddressSkuInventoryReqDTO.setContactId(143025L);
        queryAddressSkuInventoryReqDTO.setSkuCodeList(Lists.newArrayList("561346337371","561346337482"));
        WarehouseSkuInventoryResDTO warehouseSkuInventoryResDTO = saleInventoryCenterQueryFacade.queryAddressSkuInventoryInfo(queryAddressSkuInventoryReqDTO);
        System.err.println(JSON.toJSONString(warehouseSkuInventoryResDTO));
    }

    @Test
    void listItemPriceDetailByItemIds() {
    }

    @Test
    void listItemPriceDetailByItemIds4CombineItem() {
    }

    @Test
    void listItemPriceDetailByItemIds4CombineItemMock() {
//        priceFacade.listItemPriceDetailByItemIds4CombineItem(2L, 4065L, 1, Arrays.asList(1920L));
    }
}