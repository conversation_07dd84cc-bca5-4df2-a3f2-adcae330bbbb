package com.cosfo.mall.facade.usercenter;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class UserCenterMerchantStoreAccountFacadeTest {

    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;

    @Test
    void getMerchantStorePage() {
        MerchantStoreAccountPageReq pageReq = new MerchantStoreAccountPageReq();
        pageReq.setPhone("***********");
        pageReq.setTenantId(2L);
        pageReq.setStoreName("香菜好吃");
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageSize(10);
        pageQueryReq.setPageIndex(1);
        PageInfo<MerchantStoreAccountPageResp> merchantStorePage = userCenterMerchantStoreAccountFacade.getMerchantStorePage(pageReq, pageQueryReq);
        assertNotNull(merchantStorePage);
        assertEquals(merchantStorePage.getSize(), 1);
        assertEquals(merchantStorePage.getList().get(0).getAccountName(), "周嘿嘿");
        System.out.println(merchantStorePage);
    }
}