package com.cosfo.mall.facade.ordercenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.base.Objects;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.OrderStatusBatchUpdateReq;
import com.cosfo.ordercenter.client.req.OrderUpdateReq;
import com.cosfo.ordercenter.client.req.ProfitSharingFinishTimeReq;
import com.cosfo.ordercenter.client.req.RefreshOrderAmountReq;
import com.cosfo.ordercenter.client.req.event.*;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.client.service.OrderMutateService;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/12/19 下午1:57
 */
@SpringBootTest
public class OrderCommandProviderTest {

    @DubboReference
    private OrderMutateService orderMutateService;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;


    @Test
    void batchUpdateProfitSharingFinishTime() {
        Long tenantId = 2L;
        ProfitSharingFinishTimeReq req = new ProfitSharingFinishTimeReq();
        List<ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder> list = new ArrayList<>();
        req.setProfitSharingFinishTimeOrderList(list);
        ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder order = new ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder();
        order.setOrderId(122394L);
        order.setProfitSharingFinishTime(LocalDateTime.parse("2025-01-08 17:00:07", DateTimeFormatter.ofPattern(TimeUtils.FORMAT)));
        list.add(order);


        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.batchUpdateProfitSharingFinishTime(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.batchUpdateProfitSharingFinishTime(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void batchUpdateStatus() {
        OrderStatusBatchUpdateReq updateReq = new OrderStatusBatchUpdateReq();
        updateReq.setOrderNos(Lists.newArrayList("OR173632719941173"));
        updateReq.setOriginStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
        updateReq.setUpdateStatus(OrderStatusEnum.DELIVERING.getCode());

        Integer oldFlag = RpcResultUtil.handle(orderMutateService.batchUpdateStatus(updateReq));
        Integer newFlag = RpcResultUtil.handle(orderCommandProvider.batchUpdateStatus(updateReq));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void cancel() {
        OrderCancelReq req = new OrderCancelReq();
        req.setOrderIds(Lists.newArrayList(122394L));
        req.setTenantId(2L);
        req.setTimeoutCancel(false);

        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.cancel(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.cancel(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void confirm() {
        OrderConfirmReq req = new OrderConfirmReq();
        req.setOrderId(122394L);
        req.setTenantId(2L);

//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.confirm(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.confirm(req));

//        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
//        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void paySuccess() {
        OrderPaySuccessReq req = new OrderPaySuccessReq();
        req.setOrderId(122394L);

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.paySuccess(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.paySuccess(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void lockStockSuccess() {
        LockStockSuccessReq req = new LockStockSuccessReq();
        req.setTenantId(2L);
        req.setOrderId(122394L);

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.lockStockSuccess(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.lockStockSuccess(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void updatePayType() {
        OrderUpdateReq req = new OrderUpdateReq();
        req.setId(122394L);
        req.setPayType(3);
        req.setOnlinePayChannel(null);

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.updatePayType(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.updatePayType(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void refreshOrderAmount() {
        RefreshOrderAmountReq req = new RefreshOrderAmountReq();

        OrderResp oldFlag = null;
//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.refreshOrderAmount(req));
        OrderResp newFlag = RpcResultUtil.handle(orderCommandProvider.refreshOrderAmount(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void selfLiftingFinish() {
        OrderSelfLiftingFinishReq req = new OrderSelfLiftingFinishReq();
        req.setOrderId(122394L);

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.selfLiftingFinish(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.selfLiftingFinish(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void close() {
        OrderCloseReq orderCloseReq = new OrderCloseReq();
        orderCloseReq.setTenantId(2L);
        orderCloseReq.setOrderId(122403L);

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderMutateService.close(req));
        Boolean newFlag = RpcResultUtil.handle(orderCommandProvider.close(orderCloseReq));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }


}
