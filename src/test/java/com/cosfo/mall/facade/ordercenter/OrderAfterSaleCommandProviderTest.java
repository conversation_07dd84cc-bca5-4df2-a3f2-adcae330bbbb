package com.cosfo.mall.facade.ordercenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.base.Objects;
import com.cosfo.mall.common.context.DeliveryStateEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateReq;
import com.cosfo.ordercenter.client.service.OrderAfterSaleMutateService;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;

/**
 * @author: xiaowk
 * @time: 2025/1/9 上午11:05
 */
@SpringBootTest
public class OrderAfterSaleCommandProviderTest {

    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;
    @DubboReference
    private OrderAfterSaleMutateService orderAfterSaleMutateService;


    @Test
    void cancel() {
        Long id = 25006L;

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderAfterSaleMutateService.cancel(id));
        Boolean newFlag = RpcResultUtil.handle(orderAfterSaleCommandProvider.cancel(id));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void autoFinished() {

        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderAfterSaleMutateService.autoFinished());
        Boolean newFlag = RpcResultUtil.handle(orderAfterSaleCommandProvider.autoFinished());

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void updateStatus() {
        OrderAfterSaleStatusUpdateReq req = new OrderAfterSaleStatusUpdateReq();
        req.setAfterSaleId(25006L);
        req.setSourceStatus(null);
        req.setTargetStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
        req.setHandleRemark(null);
        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderAfterSaleMutateService.updateStatus(req));
        Boolean newFlag = RpcResultUtil.handle(orderAfterSaleCommandProvider.updateStatus(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }



    @Test
    void updateById() {
        OrderAfterSaleUpdateReq req = new OrderAfterSaleUpdateReq();
        req.setId(25006L);
        req.setRecycleTime(LocalDate.now().atStartOfDay());
        req.setStoreNo(2);
        Boolean oldFlag = true;
//        Boolean oldFlag = RpcResultUtil.handle(orderAfterSaleMutateService.updateById(req));
        Boolean newFlag = RpcResultUtil.handle(orderAfterSaleCommandProvider.updateById(req));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }

    @Test
    void processFinish() {
        OrderAfterSaleProcessFinishReq req = new OrderAfterSaleProcessFinishReq();
        req.setShortCount(1);
        req.setShouldCount(1);
        req.setState(DeliveryStateEnum.ABNORMAL.getState());
        req.setDeliveryType(0);
        req.setOrderAfterSaleNo("AS1857377733730066432");

        Boolean oldFlag = true;

//        Boolean oldFlag = RpcResultUtil.handle(orderAfterSaleMutateService.processFinish(Lists.newArrayList(req)));
        Boolean newFlag = RpcResultUtil.handle(orderAfterSaleCommandProvider.processFinish(Lists.newArrayList(req)));

        System.out.println(Objects.equal(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag)));
        Assert.assertEquals(JSON.toJSONString(oldFlag), JSON.toJSONString(newFlag));
    }




}
