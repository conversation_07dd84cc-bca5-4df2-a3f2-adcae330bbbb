package com.cosfo.mall.facade.ordercenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.client.service.OrderItemSnapshotQueryService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2025/1/10 上午11:48
 */
@SpringBootTest
public class OrderItemSnapshotQueryCompareTest {
    
    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;
    @DubboReference
    private OrderItemSnapshotQueryService orderItemSnapshotQueryService;


    List<Long> ids = Arrays.asList(300101L,300100L,300099L,300098L,300097L,300096L,300095L,300094L,300093L,300092L,300091L,300090L,300089L,300088L,300087L,300086L,300085L,300084L,300083L,300082L,300081L,300080L,300079L,300078L,300077L,300076L,300075L,300074L,300073L,300072L,300071L,300070L,300069L,300068L,300067L,300066L,300065L,300064L,300063L,300062L,300061L,300060L,300059L,300058L,300057L,300056L,300055L,300054L,300053L,300052L,300051L,300050L,300049L,300048L,300047L,300046L,300045L,300044L,300043L,300042L,300041L,300040L,300039L,300038L,300037L,300036L,300035L,300034L,300033L,300032L,300031L,300030L,300029L,300028L,300027L,300026L,300025L,300024L,300023L,300022L,300021L,300020L,300019L,300018L,300017L,300016L,300015L,300014L,300013L,300012L,300011L,300010L,300009L,300008L,300007L,300006L,300005L,300004L,300003L,300002L,300001L,300000L,299999L,299998L,299997L,299996L,299995L,299994L,299993L,299992L,299991L,299990L,299989L,299988L,299987L,299986L,299985L,299984L,299983L,299982L,299981L,299980L,299979L,299978L,299977L,299976L,299975L,299974L,299973L,299972L,299971L,299970L,299969L,299968L,299967L,299966L,299965L,299964L,299963L,299962L,299961L,299960L,299959L,299958L,299957L,299956L,299955L,299954L,299953L,299952L,299951L,299950L,299949L,299948L,299947L,299946L,299945L,299944L,299943L,299942L,299941L,299940L,299939L,299938L,299937L,299936L,299935L,299934L,299933L,299932L,299931L,299930L,299929L,299928L,299927L,299926L,299925L,299924L,299923L,299922L,299921L,299920L,299919L,299918L,299917L,299916L,299915L,299914L,299913L,299912L,299911L,299910L,299909L,299908L,299907L,299906L,299905L,299904L,299903L,299902L);


    @Test
    void queryByOrderItemIdCompareTest() {
        for (Long id : ids) {
            DubboResponse<OrderItemSnapshotResp> orderItemSnapshotRespDubboResponse = orderItemSnapshotQueryProvider.queryByOrderItemId(id);
            DubboResponse<OrderItemSnapshotDTO> orderItemSnapshotDTODubboResponse = orderItemSnapshotQueryService.queryByOrderItemId(id);
            Assertions.assertEquals(JSON.toJSONString(orderItemSnapshotRespDubboResponse.getData()), JSON.toJSONString(orderItemSnapshotDTODubboResponse.getData()));
        }
    }

    @Test
    void queryByOrderItemIdsCompareTest() {
//        List<Long> ids = Lists.newArrayList(289312L, 289294L);
//        List<Long> ids = Arrays.asList(291282L, 291281L, 291280L, 291279L, 291278L, 291277L, 291276L, 291275L, 291274L, 291273L);

        DubboResponse<List<OrderItemSnapshotDTO>> listDubboResponse = orderItemSnapshotQueryService.queryByOrderItemIds(ids);
        DubboResponse<List<OrderItemSnapshotResp>> listDubboResponse1 = orderItemSnapshotQueryProvider.queryByOrderItemIds(ids);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryListCompareTest() {
        OrderItemSnapshotQueryReq orderItemSnapshotQueryReq = new OrderItemSnapshotQueryReq();
        orderItemSnapshotQueryReq.setOrderId(114002L);
        orderItemSnapshotQueryReq.setTenantId(2L);
        DubboResponse<List<OrderItemSnapshotDTO>> listDubboResponse = orderItemSnapshotQueryService.queryList(orderItemSnapshotQueryReq);
        DubboResponse<List<OrderItemSnapshotResp>> listDubboResponse1 = orderItemSnapshotQueryProvider.queryList(orderItemSnapshotQueryReq);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}
