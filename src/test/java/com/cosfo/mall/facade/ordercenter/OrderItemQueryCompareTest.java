package com.cosfo.mall.facade.ordercenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.OrderItemDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2025/1/9 下午5:48
 */
@SpringBootTest
public class OrderItemQueryCompareTest {
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    
    @DubboReference
    private OrderItemQueryService orderItemQueryService;

    List<Long> ids = Arrays.asList(300101L,300100L,300099L,300098L,300097L,300096L,300095L,300094L,300093L,300092L,300091L,300090L,300089L,300088L,300087L,300086L,300085L,300084L,300083L,300082L,300081L,300080L,300079L,300078L,300077L,300076L,300075L,300074L,300073L,300072L,300071L,300070L,300069L,300068L,300067L,300066L,300065L,300064L,300063L,300062L,300061L,300060L,300059L,300058L,300057L,300056L,300055L,300054L,300053L,300052L,300051L,300050L,300049L,300048L,300047L,300046L,300045L,300044L,300043L,300042L,300041L,300040L,300039L,300038L,300037L,300036L,300035L,300034L,300033L,300032L,300031L,300030L,300029L,300028L,300027L,300026L,300025L,300024L,300023L,300022L,300021L,300020L,300019L,300018L,300017L,300016L,300015L,300014L,300013L,300012L,300011L,300010L,300009L,300008L,300007L,300006L,300005L,300004L,300003L,300002L,300001L,300000L,299999L,299998L,299997L,299996L,299995L,299994L,299993L,299992L,299991L,299990L,299989L,299988L,299987L,299986L,299985L,299984L,299983L,299982L,299981L,299980L,299979L,299978L,299977L,299976L,299975L,299974L,299973L,299972L,299971L,299970L,299969L,299968L,299967L,299966L,299965L,299964L,299963L,299962L,299961L,299960L,299959L,299958L,299957L,299956L,299955L,299954L,299953L,299952L,299951L,299950L,299949L,299948L,299947L,299946L,299945L,299944L,299943L,299942L,299941L,299940L,299939L,299938L,299937L,299936L,299935L,299934L,299933L,299932L,299931L,299930L,299929L,299928L,299927L,299926L,299925L,299924L,299923L,299922L,299921L,299920L,299919L,299918L,299917L,299916L,299915L,299914L,299913L,299912L,299911L,299910L,299909L,299908L,299907L,299906L,299905L,299904L,299903L,299902L);

    List<Long> orderIds = Arrays.asList(122409L,122406L,122403L,122402L,122401L,122400L,122399L,122398L,122397L,122394L,122393L,122392L,122391L,122390L,122389L,122388L,122291L,122290L,122288L,122287L,122286L,122285L,122031L,122030L,122027L,122026L,122025L,122024L,122023L,121936L,121935L,121934L,121690L,121375L,121278L,121277L,121276L,121092L,121091L,121090L,121089L,121088L,121083L,121082L,121016L,121013L,121012L,121008L,121007L,121006L,121005L,121003L,121002L,121001L,120998L,120997L,120996L,120995L,120994L,120993L,120980L,120978L,120976L,120975L,120974L,120973L,120968L,120936L,120935L,120929L,120927L,120159L,120158L,120030L,120028L,120026L,120025L,120024L,120018L,120017L,120016L,120015L,120005L,120004L,120003L,120002L,120001L,119987L,119904L,119536L,119534L,119533L,119509L,119508L,119131L,119114L,119113L,119082L,119079L,119048L,118978L,118977L,118976L,118974L,118953L,118952L,118951L,118950L,118949L,118948L,118946L,118944L,118943L,118902L,118901L,118900L,118899L,118897L,118868L,118867L,118805L,118804L,118803L,118802L,118715L,118714L,118713L,118712L,118682L,118681L,118501L,117852L,117723L,117719L,117711L,117707L,117706L,117705L,117618L,117581L,117580L,117579L,117578L,117574L,117252L,117140L,117108L,117107L,117106L,117104L,117103L,117102L,117024L,116841L,116840L,116839L,116838L,116837L,116836L,116835L,116834L,116761L,116299L,116298L,116297L,116295L,116286L,116283L,116282L,116281L,116280L,116279L,116278L,116277L,116276L,116275L,116268L,116267L,116266L,116257L,116256L,116255L,116254L,116253L,116252L,116250L,116224L,116223L,116222L,116220L,116219L,116207L,116206L,116205L,116204L,116200L,116198L,116197L,116196L,116194L);


    @Test
    void queryByIdCompareTest() {
        for (Long id : ids) {
            DubboResponse<OrderItemResp> orderItemRespDubboResponse = orderItemQueryProvider.queryById(id);
            DubboResponse<OrderItemDTO> orderItemDTODubboResponse = orderItemQueryService.queryById(id);
            Assertions.assertEquals(JSON.toJSONString(orderItemRespDubboResponse.getData()), JSON.toJSONString(orderItemDTODubboResponse.getData()));
        }
    }

    @Test
    void queryByIdsCompareTest() {
        DubboResponse<List<OrderItemDTO>> listDubboResponse = orderItemQueryService.queryByIds(Lists.newArrayList(ids));
        DubboResponse<List<OrderItemResp>> listDubboResponse1 = orderItemQueryProvider.queryByIds(Lists.newArrayList(ids));
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryDetailByIdCompareTest() {
        for (Long id : ids) {
            DubboResponse<OrderItemAndSnapshotDTO> orderItemDTODubboResponse = orderItemQueryService.queryDetailById(id);
            DubboResponse<OrderItemAndSnapshotResp> orderItemRespDubboResponse = orderItemQueryProvider.queryDetailById(id);
            Assertions.assertEquals(JSON.toJSONString(orderItemDTODubboResponse.getData()), JSON.toJSONString(orderItemRespDubboResponse.getData()));
        }
    }

    @Test
    void queryByOrderIdCompareTest() {
        for (Long orderId : orderIds) {
            DubboResponse<List<OrderItemAndSnapshotDTO>> listDubboResponse = orderItemQueryService.queryByOrderId(orderId);
            DubboResponse<List<OrderItemAndSnapshotResp>> listDubboResponse1 = orderItemQueryProvider.queryByOrderId(orderId);
            Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
        }
    }

    @Test
    void queryOrderItemListCompareTest() {
        for (Long orderId : orderIds) {
            DubboResponse<List<OrderItemDTO>> listDubboResponse = orderItemQueryService.queryOrderItemList(orderId);
            DubboResponse<List<OrderItemResp>> listDubboResponse1 = orderItemQueryProvider.queryOrderItemList(orderId);
            Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
        }
    }

    @Test
    void queryOrderItemListByReqCompareTest() {
        OrderItemQueryReq req = new OrderItemQueryReq();
        req.setTenantId(2L);
        req.setOrderIds(Lists.newArrayList(122464L));
        DubboResponse<List<OrderItemAndSnapshotDTO>> listDubboResponse = orderItemQueryService.queryOrderItemList(req);
        DubboResponse<List<OrderItemAndSnapshotResp>> listDubboResponse1 = orderItemQueryProvider.queryOrderItemList(req);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}
