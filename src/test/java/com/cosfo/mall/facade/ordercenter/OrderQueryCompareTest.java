package com.cosfo.mall.facade.ordercenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderNeedDeliveryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2025/1/9 下午5:50
 */
@SpringBootTest
public class OrderQueryCompareTest {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderQueryService orderQueryService;

    @Test
    void queryByIdCompareTest() {

        // 普通订单
//        Long orderId = 114174L;

        // 计划单订单
//        Long orderId = 114042L;

        // 组合订单
//        Long orderId = 114223L;

        List<Long> orderIds = Arrays.asList(122409L,122406L,122403L,122402L,122401L,122400L,122399L,122398L,122397L,122394L,122393L,122392L,122391L,122390L,122389L,122388L,122291L,122290L,122288L,122287L,122286L,122285L,122031L,122030L,122027L,122026L,122025L,122024L,122023L,121936L,121935L,121934L,121690L,121375L,121278L,121277L,121276L,121092L,121091L,121090L,121089L,121088L,121083L,121082L,121016L,121013L,121012L,121008L,121007L,121006L,121005L,121003L,121002L,121001L,120998L,120997L,120996L,120995L,120994L,120993L,120980L,120978L,120976L,120975L,120974L,120973L,120968L,120936L,120935L,120929L,120927L,120159L,120158L,120030L,120028L,120026L,120025L,120024L,120018L,120017L,120016L,120015L,120005L,120004L,120003L,120002L,120001L,119987L,119904L,119536L,119534L,119533L,119509L,119508L,119131L,119114L,119113L,119082L,119079L,119048L,118978L,118977L,118976L,118974L,118953L,118952L,118951L,118950L,118949L,118948L,118946L,118944L,118943L,118902L,118901L,118900L,118899L,118897L,118868L,118867L,118805L,118804L,118803L,118802L,118715L,118714L,118713L,118712L,118682L,118681L,118501L,117852L,117723L,117719L,117711L,117707L,117706L,117705L,117618L,117581L,117580L,117579L,117578L,117574L,117252L,117140L,117108L,117107L,117106L,117104L,117103L,117102L,117024L,116841L,116840L,116839L,116838L,116837L,116836L,116835L,116834L,116761L,116299L,116298L,116297L,116295L,116286L,116283L,116282L,116281L,116280L,116279L,116278L,116277L,116276L,116275L,116268L,116267L,116266L,116257L,116256L,116255L,116254L,116253L,116252L,116250L,116224L,116223L,116222L,116220L,116219L,116207L,116206L,116205L,116204L,116200L,116198L,116197L,116196L,116194L);

        for(Long orderId : orderIds) {
            DubboResponse<OrderResp> orderRespDubboResponse = orderQueryProvider.queryById(orderId);
            DubboResponse<OrderDTO> orderDTODubboResponse = orderQueryService.queryById(orderId);
            Assertions.assertEquals(JSON.toJSONString(orderRespDubboResponse.getData()), JSON.toJSONString(orderDTODubboResponse.getData()));
        }

    }

    @Test
    void queryByNoCompareTest() {

        // 普通订单
//        String orderNo = "OR171505258038143";

        // 计划单订单
//        String orderNo = "OR171447048133795";

        // 组合订单
//        String orderNo = "OR171506884151396";

        List<String> orderNos = Arrays.asList("OR171505258038143", "OR171447048133795", "OR171506884151396");

        for(String orderNo : orderNos) {
            DubboResponse<OrderResp> orderRespDubboResponse = orderQueryProvider.queryByNo(orderNo);
            DubboResponse<OrderDTO> orderDTODubboResponse = orderQueryService.queryByNo(orderNo);
            Assertions.assertEquals(JSON.toJSONString(orderRespDubboResponse.getData()), JSON.toJSONString(orderDTODubboResponse.getData()));
        }
    }

    @Test
    void queryByIdsCompareTest() {
        List<Long> orderIds = Arrays.asList(122409L,122406L,122403L,122402L,122401L,122400L,122399L,122398L,122397L,122394L,122393L,122392L,122391L,122390L,122389L,122388L,122291L,122290L,122288L,122287L,122286L,122285L,122031L,122030L,122027L,122026L,122025L,122024L,122023L,121936L,121935L,121934L,121690L,121375L,121278L,121277L,121276L,121092L,121091L,121090L,121089L,121088L,121083L,121082L,121016L,121013L,121012L,121008L,121007L,121006L,121005L,121003L,121002L,121001L,120998L,120997L,120996L,120995L,120994L,120993L,120980L,120978L,120976L,120975L,120974L,120973L,120968L,120936L,120935L,120929L,120927L,120159L,120158L,120030L,120028L,120026L,120025L,120024L,120018L,120017L,120016L,120015L,120005L,120004L,120003L,120002L,120001L,119987L,119904L,119536L,119534L,119533L,119509L,119508L,119131L,119114L,119113L,119082L,119079L,119048L,118978L,118977L,118976L,118974L,118953L,118952L,118951L,118950L,118949L,118948L,118946L,118944L,118943L,118902L,118901L,118900L,118899L,118897L,118868L,118867L,118805L,118804L,118803L,118802L,118715L,118714L,118713L,118712L,118682L,118681L,118501L,117852L,117723L,117719L,117711L,117707L,117706L,117705L,117618L,117581L,117580L,117579L,117578L,117574L,117252L,117140L,117108L,117107L,117106L,117104L,117103L,117102L,117024L,116841L,116840L,116839L,116838L,116837L,116836L,116835L,116834L,116761L,116299L,116298L,116297L,116295L,116286L,116283L,116282L,116281L,116280L,116279L,116278L,116277L,116276L,116275L,116268L,116267L,116266L,116257L,116256L,116255L,116254L,116253L,116252L,116250L,116224L,116223L,116222L,116220L,116219L,116207L,116206L,116205L,116204L,116200L,116198L,116197L,116196L,116194L);


        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryByIds(orderIds);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryByIds(orderIds);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryByNosCompareTest() {
        List<String> orderNos = Arrays.asList("OR173641787574733","OR173640508961876","OR173633290344491","OR173633244400414","OR173633199476650","OR173633188553515","OR173633133484373","OR173633128327808","OR173633123697092","OR173632719941173","OR173632719933522","OR173632719922627","OR173632416061838","OR173632416046307","OR173632416038060","OR173632416016021","OR173587250559678","OR173587241142227","OR173587101166092","OR173581218143094","OR173581209716925","OR173581125817812","OR173468637958662","OR173468635388767","OR173468478503901","OR173468430787343","OR173468358069519","OR173468358062811","OR173468333249073","OR173441441856433","OR173440753251042","OR173440745920906","OR173381889317220","OR173329817647689","OR173285923867040","OR173284919027141","OR173284822899844","OR173217229994781","OR173217214909145","OR173209342623411","OR173209202703578","OR173208188353939","OR173200289483481","OR173200245162065","OR173166824059357","OR173165050488943","OR173165050476976","OR173163780942102","OR173163780935909","OR173163718685692","OR173163718651307","OR173156728549598","OR173156728527895","OR173156728513727","OR173149498581834","OR173149498572220","OR173149181467440","OR173149181460582","OR173149181453160","OR173149181414872","OR173139759423123","OR173139025929769","OR173131314266269","OR173131214815651","OR173131204376870","OR173131198121391","OR173129107520596","OR173089079665553","OR173088948110647","OR173087721685281","OR173087680641034","OR172968095159071","OR172968088512588","OR172959192170417","OR172959192163065","OR172959126090251","OR172959126087587","OR172959126080977","OR172956768165946","OR172956768160851","OR172956768152952","OR172956609936506","OR172956532793360","OR172956525059747","OR172956499581301","OR172956463832542","OR172956463827643","OR172948290094166","OR172922120314484","OR172889250895517","OR172889104385606","OR172889104379130","OR172888491249053","OR172888490541751","OR172740393822760","OR172714816505932","OR172714816498093","OR172707394769132","OR172707318801036","OR172681596444450","OR172665326467793","OR172665237622770","OR172665227185669","OR172662940175047","OR172630210760078","OR172630210752293","OR172630210745682","OR172630170477613","OR172630170467974","OR172630170440215","OR172621572542426","OR172619887695987","OR172619736619813","OR172610451049901","OR172610451045393","OR172605306664794","OR172605306657519","OR172595755319082","OR172552223261873","OR172552223250292","OR172535890941866","OR172535890936144","OR172535890926491","OR172535890920606","OR172534425126229","OR172534425120789","OR172534425106606","OR172534425098529","OR172500897395226","OR172500786309188","OR172466951602269","OR172361688203224","OR172346004943114","OR172344149075789","OR172325907558683","OR172319635823136","OR172319391385210","OR172319376513635","OR172310344645460","OR172302694442408","OR172302694435771","OR172302470145847","OR172302381127849","OR172300906940610","OR172190331950091","OR172171983059537","OR172171308488197","OR172171308481176","OR172171308473880","OR172171005299567","OR172171005294610","OR172171005284316","OR172161891440119","OR172128494441246","OR172128494435847","OR172128494426736","OR172128494419716","OR172128494414350","OR172128494409808","OR172128494405574","OR172128494400849","OR172121190458626","OR172120525793493","OR172120523597462","OR172120522398130","OR172120509397535","OR172120351978208","OR172120254625312","OR172120199504121","OR172120199490950","OR172120199485748","OR172120199479482","OR172120199473358","OR172120088971707","OR172120088965967","OR172120088959663","OR172113598655180","OR172113598648471","OR172113598642095","OR172111311937926","OR172111311926705","OR172111311919759","OR172111292962693","OR172111292955523","OR172111292947747","OR172111128072586","OR172103620015495","OR172103617973677","OR172103509317187","OR172103327248876","OR172103220164200","OR172102419294748","OR172101140348895","OR172077982337608","OR172077982309718","OR172077874205520","OR172077819975541","OR172077819969615","OR172077048205057","OR172076966395580");

        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryByNos(orderNos);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryByNos(orderNos);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryOrderListCompareTest() {
        OrderQueryReq req = new OrderQueryReq();
        req.setTenantId(2L);
        req.setBatchSize(23);
        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryOrderList(req);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryOrderList(req);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryOrderPageCompareTest() {
        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setPageNum(4);
        queryReq.setPageSize(13);
        queryReq.setTenantId(2L);
        queryReq.setStatus(5);
        queryReq.setStoreIds(Lists.newArrayList(4260L));
        DubboResponse<PageInfo<OrderDTO>> listDubboResponse = orderQueryService.queryOrderPage(queryReq);
        DubboResponse<PageInfo<OrderResp>> response = orderQueryProvider.queryOrderPage(queryReq);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }

    @Test
    void queryNeedDeliveryOrderCompareTest() {
        OrderNeedDeliveryReq queryReq = new OrderNeedDeliveryReq();
        queryReq.setTenantId(2L);
        queryReq.setCreateTime(LocalDateTime.now().minusDays(3));
        DubboResponse<List<String>> listDubboResponse = orderQueryService.queryNeedDeliveryOrder(queryReq);
        DubboResponse<List<String>> response = orderQueryProvider.queryNeedDeliveryOrder(queryReq);
        Assertions.assertEquals(JSON.toJSONString(response.getData()), JSON.toJSONString(listDubboResponse.getData()));
    }
}
