package com.cosfo.mall.facade.ordercenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.base.Objects;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.CombineOrderQueryProvider;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.client.service.CombineOrderQueryService;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/12/19 下午1:57
 */
@SpringBootTest
public class CombineOrderQueryProviderTest {

    @DubboReference
    private CombineOrderQueryService combineOrderQueryService;
    @DubboReference
    private CombineOrderQueryProvider combineOrderQueryProvider;


    @Test
    void queryByCombineId() {
        Long tenantId = 2L;
        Long combineItemId = 1663454814895161855L;
        List<OrderDTO> combineOrderList = RpcResultUtil.handle(combineOrderQueryService.queryByCombineId(combineItemId, tenantId));
        List<OrderResp> newcombineOrderList = RpcResultUtil.handle(combineOrderQueryProvider.queryByCombineId(combineItemId, tenantId));

        System.out.println(Objects.equal(JSON.toJSONString(combineOrderList), JSON.toJSONString(newcombineOrderList)));
    }

    @Test
    void queryByCombineIds() {
        Long tenantId = 2L;
        List<Long> combineItemIds = Lists.newArrayList(1659098695264432129L,1659130951341297665L,1659137305053339649L,1659148574061862913L,1659149054427111426L,1659159036926283778L,1659392856361664514L,1659394278553681922L,1659395288680497154L,1659398420147863554L,1659405614893367298L,1659432110148157442L,1659465456356630529L,1659467008374939650L,1659467344267386881L,1659487590835195906L,1659489675400089602L,1659508240316153857L,1660478734270455809L,1660479035027218434L,1660485638799482881L,1660487663419383809L,1660568442861047809L,1660568550872764418L,1660568811938828289L,1660569465809850370L,1660571074866499586L,1660572884280856577L,1660576026447892482L,1660577437550170113L,1660577634686652418L,1660577692878426114L,1660840382039400449L,1660857586702811138L,1660891788458524674L,1660918420136312834L,1660918695169409026L,1660926586265845761L,1660940236745654273L,1661290624994164738L,1661313496031113217L,1661325997816885249L,1661326828997914625L,1661329093263568898L,1661329187568300033L,1661329463217958913L,1661330060658814978L,1661331650002235393L,1661563855251099L);
        List<OrderDTO> combineOrderList = RpcResultUtil.handle(combineOrderQueryService.queryByCombineIds(combineItemIds, tenantId));
        List<OrderResp> newcombineOrderList = RpcResultUtil.handle(combineOrderQueryProvider.queryByCombineIds(combineItemIds, tenantId));

        System.out.println(Objects.equal(JSON.toJSONString(combineOrderList), JSON.toJSONString(newcombineOrderList)));
        Assert.assertEquals(JSON.toJSONString(combineOrderList), JSON.toJSONString(newcombineOrderList));
    }

}
