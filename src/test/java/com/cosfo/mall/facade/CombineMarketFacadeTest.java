package com.cosfo.mall.facade;

import com.cosfo.mall.facade.dto.CombineMarketDetailDTO;
import com.cosfo.mall.facade.dto.CombineMarketQueryInputDTO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class CombineMarketFacadeTest {
    @Autowired
    private CombineMarketFacade combineMarketFacade;

    @Test
    void queryMarketItemById() {
    }

    @Test
    void combineDetail() {
        CombineMarketQueryInputDTO combineMarketQueryInputDTO = new CombineMarketQueryInputDTO();
        combineMarketQueryInputDTO.setItemId(1920L);
        combineMarketQueryInputDTO.setTenantId(2L);
        CombineMarketDetailDTO combineMarketDetailDTO = combineMarketFacade.combineDetail(combineMarketQueryInputDTO);
    }
}