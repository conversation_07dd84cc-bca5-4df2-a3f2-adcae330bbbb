package com.cosfo.mall.facade.auth;

import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class AuthClientLoginFacadeTest {

    @Resource
    private AuthClientLoginFacade authClientLoginFacade;

    @Test
    void queryWeChatInfo() {

        AuthQueryWechatInfoDTO authQueryWechatInfoDTO = authClientLoginFacade.queryWeChatInfo("0c1LdMml2PKh0c4zymol202i251LdMmA", "wxf6672acab7524893", "wx85a9fed1e711916e", "72_33P3roiMy3KoahpG8i5c-ZV3v4CeE-t6c_NQaLc2oDRnFo98zzEb9Y9z8dTxu6jlG-kGAW6WmVGoIRVjKOppXoYCGafXP5Ukm34x1IGLeIs_c9yrX3bQsm1L2XSS8mHhfxQKxfAT3SQKZmLlKPThADDBGK");
        System.out.println(authQueryWechatInfoDTO);
    }

    @Test
    void authClientLogin() {
        //AuthLoginDto authLoginDto = authClientLoginFacade.authClientLogin(2L, "ooWYY4zntJBzqqx4w2E6RC1i6PXY", "18334345453", 11440L);
        //System.out.println(authLoginDto);
    }

    @Test
    void logout() {
        Boolean logout = authClientLoginFacade.logout(11440L, "");
        System.out.println(logout);
    }
}