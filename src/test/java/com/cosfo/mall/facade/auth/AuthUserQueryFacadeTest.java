package com.cosfo.mall.facade.auth;

import net.xianmu.authentication.client.dto.user.AuthUserResp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class AuthUserQueryFacadeTest {

    @Resource
    private AuthUserQueryFacade authUserQueryFacade;
    @Test
    void queryAuthUserList() {

        List<AuthUserResp> authUserResps = authUserQueryFacade.queryAuthUserList(2L, "18334345453");
        System.out.println(authUserResps);
    }
}