package com.cosfo.mall.common.constants;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * 智付退款状态枚举测试
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
public class DinPayRefundStatusEnumTest {

    @Test
    public void testStatusMapping() {
        log.info("测试智付退款状态枚举映射");

        // 测试成功状态
        DinPayRefundStatusEnum successStatus = DinPayRefundStatusEnum.getByCode("SUCCESS");
        assert successStatus == DinPayRefundStatusEnum.SUCCESS;
        assert successStatus.isSuccess();
        assert successStatus.isFinalStatus();
        assert !successStatus.isProcessing();
        log.info("SUCCESS状态测试通过：{}", successStatus.getDesc());

        // 测试失败状态
        DinPayRefundStatusEnum failStatus = DinPayRefundStatusEnum.getByCode("FAIL");
        assert failStatus == DinPayRefundStatusEnum.FAIL;
        assert failStatus.isFailure();
        assert failStatus.isFinalStatus();
        assert !failStatus.isProcessing();
        log.info("FAIL状态测试通过：{}", failStatus.getDesc());

        // 测试关闭状态
        DinPayRefundStatusEnum closeStatus = DinPayRefundStatusEnum.getByCode("CLOSE");
        assert closeStatus == DinPayRefundStatusEnum.CLOSE;
        assert closeStatus.isFailure();
        assert closeStatus.isFinalStatus();
        assert !closeStatus.isProcessing();
        log.info("CLOSE状态测试通过：{}", closeStatus.getDesc());

        // 测试处理中状态
        DinPayRefundStatusEnum doingStatus = DinPayRefundStatusEnum.getByCode("DOING");
        assert doingStatus == DinPayRefundStatusEnum.DOING;
        assert doingStatus.isProcessing();
        assert !doingStatus.isFinalStatus();
        assert !doingStatus.isSuccess();
        assert !doingStatus.isFailure();
        log.info("DOING状态测试通过：{}", doingStatus.getDesc());

        // 测试等待处理状态
        DinPayRefundStatusEnum beforeReceiveStatus = DinPayRefundStatusEnum.getByCode("BEFORERECEIVE");
        assert beforeReceiveStatus == DinPayRefundStatusEnum.BEFORE_RECEIVE;
        assert beforeReceiveStatus.isProcessing();
        assert !beforeReceiveStatus.isFinalStatus();
        log.info("BEFORERECEIVE状态测试通过：{}", beforeReceiveStatus.getDesc());

        // 测试未知状态
        DinPayRefundStatusEnum unknownStatus = DinPayRefundStatusEnum.getByCode("UNKNOWN_STATUS");
        assert unknownStatus == DinPayRefundStatusEnum.UNKNOWN;
        assert !unknownStatus.isSuccess();
        assert !unknownStatus.isFailure();
        assert !unknownStatus.isProcessing();
        assert !unknownStatus.isFinalStatus();
        log.info("未知状态测试通过：{}", unknownStatus.getDesc());

        // 测试null状态
        DinPayRefundStatusEnum nullStatus = DinPayRefundStatusEnum.getByCode(null);
        assert nullStatus == DinPayRefundStatusEnum.UNKNOWN;
        log.info("null状态测试通过：{}", nullStatus.getDesc());
    }

    @Test
    public void testAllStatusCodes() {
        log.info("测试所有智付退款状态码");

        String[] expectedCodes = {
                "BEFORERECEIVE", "RECEIVE", "INIT", "DOING",
                "SUCCESS", "FAIL", "CLOSE"
        };

        for (String code : expectedCodes) {
            DinPayRefundStatusEnum status = DinPayRefundStatusEnum.getByCode(code);
            assert status != DinPayRefundStatusEnum.UNKNOWN;
            assert status.getCode().equals(code);
            log.info("状态码 {} -> {}", code, status.getDesc());
        }

        log.info("所有状态码测试通过");
    }

    @Test
    public void testStatusCategories() {
        log.info("测试智付退款状态分类");

        // 最终状态
        DinPayRefundStatusEnum[] finalStatuses = {
                DinPayRefundStatusEnum.SUCCESS,
                DinPayRefundStatusEnum.FAIL,
                DinPayRefundStatusEnum.CLOSE
        };

        for (DinPayRefundStatusEnum status : finalStatuses) {
            assert status.isFinalStatus();
            log.info("最终状态：{}", status.getDesc());
        }

        // 处理中状态
        DinPayRefundStatusEnum[] processingStatuses = {
                DinPayRefundStatusEnum.BEFORE_RECEIVE,
                DinPayRefundStatusEnum.RECEIVE,
                DinPayRefundStatusEnum.INIT,
                DinPayRefundStatusEnum.DOING
        };

        for (DinPayRefundStatusEnum status : processingStatuses) {
            assert status.isProcessing();
            assert !status.isFinalStatus();
            log.info("处理中状态：{}", status.getDesc());
        }

        log.info("状态分类测试通过");
    }
}
