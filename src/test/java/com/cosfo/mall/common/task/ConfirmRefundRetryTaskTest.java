package com.cosfo.mall.common.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/26
 */
@SpringBootTest
class ConfirmRefundRetryTaskTest {
    @Resource
    private ConfirmRefundRetryTask confirmRefundRetryTask;

    @Test
    void processResult() {

        confirmRefundRetryTask.processResult(new XmJobInput());
    }
}