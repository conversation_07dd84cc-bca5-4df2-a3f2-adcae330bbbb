package com.cosfo.mall.common.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class OrderCancelStatusMonitorTaskTest {

    @Resource
    private OrderCancelStatusMonitorTask orderCancelStatusMonitorTask;

    @Test
    void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        orderCancelStatusMonitorTask.processResult(xmJobInput);
    }
}