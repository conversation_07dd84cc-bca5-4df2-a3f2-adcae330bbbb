package com.cosfo.mall.common.task;

import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: xiaowk
 * @time: 2025/1/16 上午10:41
 */
@SpringBootTest
public class OrderProfitSharingTaskTest {


    @Resource
    private OrderProfitSharingTask orderProfitSharingTask;

    @Resource
    private ProfitSharingConfirmTask profitSharingConfirmTask;
    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;

    @Test
    void processResultProfitSharingConfirmTask() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        // 更新分账订单状态由4->0
        profitSharingConfirmTask.processResult(xmJobInput);
    }


    @Test
    void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setJobParameters("{\"maxRetryNum\": 9}");
        orderProfitSharingTask.processResult(xmJobInput);
    }

    @Test
    void handleOrderProfitSharing() throws Exception {
        List<BillProfitSharingOrder> list = billProfitSharingOrderService.queryByTenantAndOrderId(2L, 122843L);
        List<BillProfitSharingOrderDTO> billProfitSharingOrderDtos = list.stream().map(billProfitSharingOrder -> {
            BillProfitSharingOrderDTO billProfitSharingOrderDto = new BillProfitSharingOrderDTO();
            BeanUtils.copyProperties(billProfitSharingOrder, billProfitSharingOrderDto);
            return billProfitSharingOrderDto;
        }).collect(Collectors.toList());
        for (BillProfitSharingOrderDTO dto : billProfitSharingOrderDtos) {
            profitSharingBusinessService.handleOrderProfitSharing(dto);

        }
    }
}
