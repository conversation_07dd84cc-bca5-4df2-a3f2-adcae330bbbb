package com.cosfo.mall.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest
class TenantBillBuilderTaskTest {

    @Resource
    private TenantBillBuilderTask tenantBillBuilderTask;

    @Test
    public void testBuilder() throws Exception {

        XmJobInput content = new XmJobInput();
        ProcessResult result = tenantBillBuilderTask.processResult(content);
    }

}