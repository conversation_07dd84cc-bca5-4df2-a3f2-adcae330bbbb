package com.cosfo.mall.common.listener;

import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class OFCDelivery4AfterSaleListenerTest {

    @Resource
    private OFCDelivery4AfterSaleListener ofcDelivery4AfterSaleListener;

    @Test
    void process() {
        CommonFulfillmentFinishMessage msg = new CommonFulfillmentFinishMessage();
        msg.setSourceOrderNo("AS1727155265859616768");
        ofcDelivery4AfterSaleListener.process(msg);
    }
}