package com.cosfo.mall.common.utils;

import com.cosfo.mall.common.constant.QiNiuConstant;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class QiNiuUtilsTest {

    @Test
    void uploadFile() {

    }

    @Test
    void downloadFile() throws IOException{
       //  QiNiuUtils.uploadPhoto("https://cdn.summerfarm.net/", "test/1ll4m7gwq87uymbrf.jpg");
//        File file = new File("D:\\xianmu\\cosfo-manage\\1660209298593.xlsx");
//        MultipartFile multipartFile = FileUtil.fileToMultipartFile(file);
//        QiNiuUtils.uploadFile(multipartFile,"bill/1660209298593.xlsx");

        QiNiuUtils.uploadPhoto(QiNiuConstant.XIANMU_IMAGE_URL, "test/dtnefbc4ydmk5fgjz.jpeg");
    }
}