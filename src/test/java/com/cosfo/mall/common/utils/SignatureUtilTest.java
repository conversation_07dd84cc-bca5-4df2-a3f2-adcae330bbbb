package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.constants.NotifyTypeEnum;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/7
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
class SignatureUtilTest {
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;

    @Test
    public void Test(){
        String sign = "Tl9MbhJawc7+KNiHU5ruvZX1RB6jFIVttSqsz3MX2y8YlfFqVq7uEJpkLVNr/3onV/31MzTfCP8OHFsRGWSMhWQQZZCkSqL6TdxYJOXVSy+7X9Wr2YyeomjPg4x5afl6477RjHJBiZuBcQY85RO77vP4pvrFYDQ4wPv8urisdYsWR7KNmhpXm+n6eEdqbPXBvwbSgHq2PzLy3IpIlgXe9B/zm6OXYbXObKxYo28+xdAJKNj6MOsapUyBZS5czQ4vMSWjF6PVPtC/K3m1FzCtBIIboeByy49XSfkSkqG0JQ4w+aJ48CwPnEOgNpDfdl5bI16maSJNkO3Kr+J601I9gw==";
        String data = "{\"huifu_id\":\"6666000124877554\",\"org_hf_seq_id\":\"002900TOP3B230710105450P363ac139c1300000\",\"org_req_date\":\"20230710\",\"org_trans_stat\":\"P\",\"req_date\":\"20230710\",\"req_seq_id\":\"HP_CLOSE168895847777474\",\"resp_code\":\"00000100\",\"resp_desc\":\"交易处理中\",\"trans_stat\":\"P\"}";
        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(data, HuiFuiPaymentReceive.class);
        String huifuId = huiFuPaymentReceive.getHuifu_id();
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsuCt13POFDjglGT9618YMLotmomhbWdbDH2+yYkGJ+2JlLoNnLWHF8IgeldMFgUR4FKOkmA/ojQCB8DVE9/To6MD/4bqLzzM7kqqeiQzeUBJoRh3b/VdA0ByI0Lf5Gh2UXkinovDcczOcbZlruRoFjVSYu1Kn6VoyJm+/1vjKOwIDAQAB";
        boolean verify = SignatureUtil.verify(data, publicKey, sign);
        System.out.println("验签结果：" + verify);
    }
}