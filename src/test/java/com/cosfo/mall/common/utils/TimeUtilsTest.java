package com.cosfo.mall.common.utils;

import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/26
 */
class TimeUtilsTest {

    @Test
    void getThreeMonthBeforeTime() {
        String threeMonthBeforeTime = TimeUtils.getThreeMonthBeforeTime();
        System.out.println(threeMonthBeforeTime);
        String monthStartDate = TimeUtils.getMonthStartDate(threeMonthBeforeTime);
        System.out.println(monthStartDate);
    }

    @Test
    void BeforeTime() {
        Date beforeTime = TimeUtils.getBeforeTime(new Date(), 3);
        String[] dayStartAndEndTimeStr = TimeUtils.getDayStartAndEndTimeStr(TimeUtils.changeDate2String(beforeTime, TimeUtils.FORMAT_DATE));
        System.out.println(dayStartAndEndTimeStr);
    }
}