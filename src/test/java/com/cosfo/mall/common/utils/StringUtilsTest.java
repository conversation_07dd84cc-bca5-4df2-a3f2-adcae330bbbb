package com.cosfo.mall.common.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 字符串工具类测试
 * 用于演示JaCoCo测试覆盖率功能
 * 这是一个纯JUnit测试，不依赖Spring上下文
 */
class StringUtilsTest {

    @Test
    void testIsEmpty() {
        // 测试空字符串
        assertTrue(isEmpty(null));
        assertTrue(isEmpty(""));
        assertTrue(isEmpty("   "));
        
        // 测试非空字符串
        assertFalse(isEmpty("test"));
        assertFalse(isEmpty(" test "));
    }

    @Test
    void testIsNotEmpty() {
        // 测试非空字符串
        assertTrue(isNotEmpty("test"));
        assertTrue(isNotEmpty(" test "));
        
        // 测试空字符串
        assertFalse(isNotEmpty(null));
        assertFalse(isNotEmpty(""));
        assertFalse(isNotEmpty("   "));
    }

    @Test
    void testTrim() {
        // 测试去除空格
        assertEquals("test", trim("  test  "));
        assertEquals("", trim("   "));
        assertNull(trim(null));
        assertEquals("test", trim("test"));
    }

    // 简单的工具方法实现，用于测试覆盖率
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    private boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    private String trim(String str) {
        return str == null ? null : str.trim();
    }
}
