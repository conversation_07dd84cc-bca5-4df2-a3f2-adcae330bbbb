package com.cosfo.mall.common.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 字符串工具类测试
 * 用于演示JaCoCo测试覆盖率功能
 * 这是一个纯JUnit测试，不依赖Spring上下文
 */
class StringUtilsTest {

    @Test
    void testIsEmpty() {
        // 测试空字符串
        assertTrue(isEmpty(null));
        assertTrue(isEmpty(""));
        assertTrue(isEmpty("   "));
        
        // 测试非空字符串
        assertFalse(isEmpty("test"));
        assertFalse(isEmpty(" test "));
    }

    @Test
    void testIsNotEmpty() {
        // 测试非空字符串
        assertTrue(isNotEmpty("test"));
        assertTrue(isNotEmpty(" test "));
        
        // 测试空字符串
        assertFalse(isNotEmpty(null));
        assertFalse(isNotEmpty(""));
        assertFalse(isNotEmpty("   "));
    }

    @Test
    void testTrim() {
        // 测试去除空格
        assertEquals("test", trim("  test  "));
        assertEquals("", trim("   "));
        assertNull(trim(null));
        assertEquals("test", trim("test"));
    }

    @Test
    void testSubstring() {
        // 测试字符串截取
        assertEquals("ell", substring("hello", 1, 4));
        assertEquals("", substring("hello", 0, 0));
        assertEquals("hello", substring("hello", 0, 10));
        assertNull(substring(null, 0, 5));
        assertEquals("lo", substring("hello", 3));
    }

    @Test
    void testContains() {
        // 测试字符串包含
        assertTrue(contains("hello world", "world"));
        assertTrue(contains("hello", "hello"));
        assertFalse(contains("hello", "world"));
        assertFalse(contains(null, "test"));
        assertFalse(contains("test", null));
    }

    @Test
    void testStartsWith() {
        // 测试字符串开头
        assertTrue(startsWith("hello world", "hello"));
        assertFalse(startsWith("hello world", "world"));
        assertFalse(startsWith(null, "test"));
        assertFalse(startsWith("test", null));
    }

    @Test
    void testEndsWith() {
        // 测试字符串结尾
        assertTrue(endsWith("hello world", "world"));
        assertFalse(endsWith("hello world", "hello"));
        assertFalse(endsWith(null, "test"));
        assertFalse(endsWith("test", null));
    }

    // 简单的工具方法实现，用于测试覆盖率
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    private boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    private String trim(String str) {
        return str == null ? null : str.trim();
    }

    private String substring(String str, int start, int end) {
        if (str == null) return null;
        if (start < 0) start = 0;
        if (end > str.length()) end = str.length();
        if (start >= end) return "";
        return str.substring(start, end);
    }

    private String substring(String str, int start) {
        if (str == null) return null;
        if (start < 0) start = 0;
        if (start >= str.length()) return "";
        return str.substring(start);
    }

    private boolean contains(String str, String searchStr) {
        if (str == null || searchStr == null) return false;
        return str.contains(searchStr);
    }

    private boolean startsWith(String str, String prefix) {
        if (str == null || prefix == null) return false;
        return str.startsWith(prefix);
    }

    private boolean endsWith(String str, String suffix) {
        if (str == null || suffix == null) return false;
        return str.endsWith(suffix);
    }
}
