package com.cosfo.mall.marketing.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.CosfoMallApplication;
import com.cosfo.mall.marketing.model.dto.ActivityInfoDetailDTO;
import com.cosfo.mall.marketing.model.vo.ActivityInfoVO;
import com.cosfo.mall.marketing.model.vo.ActivityItemConfigVO;
import com.cosfo.mall.marketing.model.vo.ActivitySkuDetailVO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.validation.ConstraintViolationException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * ActivityService单元测试类
 *
 * <AUTHOR>
 * @date 2025-10-29
 */
@ActiveProfiles("wurth")
@SpringBootTest(classes = CosfoMallApplication.class)
@ExtendWith(SpringExtension.class)
public class ActivityServiceTest {

    @Autowired
    private ActivityService activityService;

    private LoginContextInfoDTO loginContextInfoDTO;
    private ActivityInfoDetailDTO activityInfoDetailDTO;

    @BeforeEach
    void setUp() {
        // 初始化登录上下文信息
        loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(10000L);
        loginContextInfoDTO.setStoreId(10001L);
        loginContextInfoDTO.setAccountId(10002L);
        loginContextInfoDTO.setStoreName("测试店铺");
        loginContextInfoDTO.setJwtToken("test-jwt-token");
        loginContextInfoDTO.setOpenId("test-open-id");
        loginContextInfoDTO.setPhone("***********");
        loginContextInfoDTO.setAccountName("测试用户");

        // 初始化活动详情查询DTO
        activityInfoDetailDTO = new ActivityInfoDetailDTO();
        activityInfoDetailDTO.setType(6); // 满减活动
    }

    @Test
    @DisplayName("测试方法调用不抛出异常 - 满减活动")
    public void testShould_NotThrowException_When_QueryFullReductionActivity() {
        // given
        activityInfoDetailDTO.setType(6); // 满减活动

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("满减活动查询结果: " + (result != null ? "有数据" : "无数据"));
            if (result != null) {
                System.out.println("满减活动详情: " + JSON.toJSONString(result));
            }
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试方法调用不抛出异常 - 满返活动")
    public void testShould_NotThrowException_When_QueryFullReturnActivity() {
        // given
        activityInfoDetailDTO.setType(7); // 满返活动

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("满返活动查询结果: " + (result != null ? "有数据" : "无数据"));
            if (result != null) {
                System.out.println("满返活动详情: " + JSON.toJSONString(result));
            }
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试活动类型边界值 - 最小值")
    public void testShould_NotThrowException_When_ActivityTypeMinValue() {
        // given
        activityInfoDetailDTO.setType(0); // 最小活动类型值

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("最小活动类型结果: " + (result != null ? JSON.toJSONString(result) : "null"));
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试活动类型边界值 - 最大值")
    public void testShould_NotThrowException_When_ActivityTypeMaxValue() {
        // given
        activityInfoDetailDTO.setType(9); // 最大活动类型值（根据注释）

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("最大活动类型结果: " + (result != null ? JSON.toJSONString(result) : "null"));
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试空租户ID处理")
    public void testShould_NotThrowException_When_NullTenantId() {
        // given
        loginContextInfoDTO.setTenantId(null);
        activityInfoDetailDTO.setType(6);

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("空租户ID处理结果: " + result);
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试空店铺ID处理")
    public void testShould_NotThrowException_When_NullStoreId() {
        // given
        loginContextInfoDTO.setStoreId(null);
        activityInfoDetailDTO.setType(6);

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("空店铺ID处理结果: " + result);
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试活动类型为空 - 应该正常处理")
    public void testShould_HandleNullActivityType() {
        // given
        activityInfoDetailDTO.setType(null);

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("空活动类型处理结果: " + (result != null ? "有数据" : "无数据"));
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试参数验证 - 正常参数")
    public void testShould_PassValidation_When_ValidParameters() {
        // given
        activityInfoDetailDTO.setType(6);

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("参数验证通过，查询结果: " + (result != null ? "有数据" : "无数据"));
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试不同租户ID的活动获取")
    public void testShould_NotThrowException_ForDifferentTenantIds() {
        // given
        Long[] tenantIds = {10000L, 10001L, 10002L};
        activityInfoDetailDTO.setType(6);

        for (Long tenantId : tenantIds) {
            loginContextInfoDTO.setTenantId(tenantId);

            // when & then
            assertThatCode(() -> {
                ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
                System.out.println("租户ID " + tenantId + " 的查询结果: " + (result != null ? "有数据" : "无数据"));
            }).doesNotThrowAnyException();
        }
    }

    @Test
    @DisplayName("测试活动配置信息查询")
    public void testShould_NotThrowException_When_QueryActivityItemConfig() {
        // given
        activityInfoDetailDTO.setType(6);

        // when & then
        assertThatCode(() -> {
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("活动配置信息查询结果: " + (result != null && result.getActivityItemConfigVO() != null ? "有配置数据" : "无配置数据"));
            if (result != null && result.getActivityItemConfigVO() != null) {
                System.out.println("活动配置信息: " + JSON.toJSONString(result.getActivityItemConfigVO()));
            }
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试方法参数传递正确性")
    public void testShould_PassCorrectParameters() {
        // given
        activityInfoDetailDTO.setType(6);
        loginContextInfoDTO.setTenantId(10000L);
        loginContextInfoDTO.setStoreId(10001L);

        // when & then
        assertThatCode(() -> {
            // 验证参数传递过程中不会抛出异常
            ActivityInfoVO result = activityService.getActivityInfoDetail(loginContextInfoDTO, activityInfoDetailDTO);
            System.out.println("参数传递测试通过，租户ID: " + loginContextInfoDTO.getTenantId() + ", 店铺ID: " + loginContextInfoDTO.getStoreId());
        }).doesNotThrowAnyException();
    }
}