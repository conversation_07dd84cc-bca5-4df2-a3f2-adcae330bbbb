package com.cosfo.mall.market.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.market.service.MarketClassificationService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/20
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class MarketClassificationServiceImplTest {

    @Resource
    private MarketClassificationService marketClassificationService;

    @Test
    void selectClassificationTree() {
        marketClassificationService.selectClassificationTree(1L);
    }

    @Test
    void testUnlogin() {
        ResultDTO resultDTO = marketClassificationService.noLoginSelectClassificationTree(1003L);
        String s = JSON.toJSONString(resultDTO);
        System.out.println(s);
    }
}
