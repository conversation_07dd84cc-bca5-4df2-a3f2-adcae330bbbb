package com.cosfo.mall.market.facade;

import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.provider.Market4StoreProvider;
import com.cofso.item.client.req.MarketItemDetailQueryReq;
import com.cofso.item.client.req.MarketItemPageQuery4StoreReq;
import com.cofso.item.client.resp.MarketItem4StoreResp;
import com.cofso.item.client.resp.MarketItemDetail4StoreResp;
import com.cofso.page.PageResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * @author: xiaowk
 * @time: 2023/5/18 下午1:37
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MarketFacadeTest {

    @DubboReference
    private Market4StoreProvider market4StoreProvider;

    @Test
    public void testQuery(){
        MarketItemPageQuery4StoreReq req = MarketItemPageQuery4StoreReq.builder()
                .tenantId(2L)
//                .storeId(4260L)
                .title(null)
                .classificationId(855L)
                .pageNum(1)
                .pageSize(1)
                .build();
        DubboResponse<PageResp<MarketItem4StoreResp>> dubboResponse = market4StoreProvider.listAllMarketItem(req);
        if (!dubboResponse.isSuccess()) {
//            log.error("查询首页商品信息错误。req={}, dubboResponse={}", req, JSONObject.toJSONString(dubboResponse));
            throw new ProviderException(dubboResponse.getMsg());
        }

        PageResp<MarketItem4StoreResp> pageData = dubboResponse.getData();
        System.err.println(JSONObject.toJSONString(pageData));
    }

    @Test
    public void testDetail(){
        MarketItemDetailQueryReq req = MarketItemDetailQueryReq
                .builder()
                .tenantId(null)
                .storeId(null)
                .itemId(1914L)
                .build();
        DubboResponse<MarketItemDetail4StoreResp> dubboResponse = market4StoreProvider.getMarketItemDetail(req);
        if (!dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse.getMsg());
        }

        MarketItemDetail4StoreResp resp = dubboResponse.getData();
        System.err.println(JSONObject.toJSONString(resp));
    }
}
