package com.cosfo.mall.payment.strategy.impl;

import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.order.model.dto.HuiFuPaymentDTO;
import com.cosfo.mall.wechat.api.PayMchAPI;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.Test;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/27
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
@Slf4j
class HuiFuPaymentStrategyTest {

    @Test
    void pay() {
        String payinfo = "{\n" +
                "\t\t\"channel_no\": \"00005016\",\n" +
                "\t\t\"delay_acct_flag\": \"Y\",\n" +
                "\t\t\"goods_desc\": \"冷冻油柑汁，东方饮力纯牛奶，奶酥牛角包等，共3个商品\",\n" +
                "\t\t\"huifu_id\": \"6666000124877554\",\n" +
                "\t\t\"notify_url\": \"https://devmall.cosfo.cn/pay-notify/huifu-pay\",\n" +
                "\t\t\"pay_scene\": \"02\",\n" +
                "\t\t\"payment_id\": 24244,\n" +
                "\t\t\"req_date\": \"20230706\",\n" +
                "\t\t\"req_seq_id\": \"P1673641254718853121111\",\n" +
                "\t\t\"time_expire\": \"20230706190444\",\n" +
                "\t\t\"trade_type\": \"T_JSAPI\",\n" +
                "\t\t\"trans_amt\": \"565.00\",\n" +
                "\t\t\"wx_data\": \"{\\\"openid\\\":\\\"o3geR6ixZL-4_KbHcvMGTP7UZGvI\\\",\\\"sub_appid\\\":\\\"wxda8819c87e1e69c8\\\"}\"\n" +
                "\t}";
        HuiFuPaymentDTO jsapiPayInfo = JSONObject.parseObject(payinfo, HuiFuPaymentDTO.class);
        String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKy4K3Xc84UOOCUZP3rXxgwui2aiaFtZ1sMfb7JiQYn7YmUug2ctYcXwiB6V0wWBRHgUo6SYD+iNAIHwNUT39OjowP/huovPMzuSqp6JDN5QEmhGHdv9V0DQHIjQt/kaHZReSKei8NxzM5xtmWu5GgWNVJi7UqfpWjImb7/W+Mo7AgMBAAECgYAZStrqZpGukVd9b0YRehmBXSuCuxOnFO/TIP5dU/AfAZX2FSqe6FFiCAgW2n/NVZGuN++CwdXKiyNg48kZMWpGl1Bn9fVfzSpwKKv1hkEJc58nxQ4Dn6HASP188z4DytPzq8T3R5ceSHLpTT0EPenIiRkeKklDb9YkAgDFcZ0WiQJBAON5dvBQE3ewevRsu4yszx4mdGt3TVBNZRwZGrzk4bEH6KlVeBioz7uN4Ii8Bhp8oRt99OUrl/rmZEHwjSVA4lcCQQDCYOwVlAlAsn30UZ71emU7IpKQlHQd+HEQPCSD1XbwF/pn0MYNZ7decOW+Ox6++I44jL5CvC7RhXDLAF+GTdC9AkEAlwmgkqHouzEgAslrolVf1Jod9PkrCaXJ++UjXsbuoDgrILxSWLVF8TecHc4Sk2WrJ3DzuXK/n+V4LlxFq7WwUwJAUKHIDUN1eyMP4LOjDw2QxLEYv2T1riELNcLdGtsIFZy8wSf3oEPv6vtGMl1v6aRNyuOHYUOS4FNMcMlc1uecuQJBAK4p2AQ0fMm9G1ljaTh81UcpGDw+EZsjitwi4GnGFwzzsjp+K/5BjcqiqdoJdFhHZbBS31nDGOSAp3y7vkNkskQ=";

        //参数加签
        String signRes = SignatureUtil.bodySign(jsapiPayInfo, privateKey);

        //请求汇付支付
        HuiFuDTO huiFuDTO = new HuiFuDTO(jsapiPayInfo);
        huiFuDTO.setSys_id(jsapiPayInfo.getHuiFuId());
        huiFuDTO.setProduct_id("PAYUN");
        huiFuDTO.setSign(signRes);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(PayMchAPI.huiFuBaseURI() + "/v2/trade/payment/jspay")
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(JSON.toJSONString(huiFuDTO)).execute();

        log.info("请求汇付支付，request：{}，response：{}", JSON.toJSONString(huiFuDTO), response.body());
        String body = response.body();
        if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatus())) {
            throw new DefaultServiceException(500, "/v2/trade/payment/jspay请求失败：" + body);
        }

    }
}