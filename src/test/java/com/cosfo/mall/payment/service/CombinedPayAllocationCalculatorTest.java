package com.cosfo.mall.payment.service;

import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.order.utils.CombinedPayAllocationCalculator;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import jdk.nashorn.internal.ir.annotations.Reference;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-07-02
 **/
@SpringBootTest
@ActiveProfiles("dev")
@Slf4j
public class CombinedPayAllocationCalculatorTest {

    @Resource
    private CombinedPayAllocationCalculator combinedPayAllocationCalculator;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;

    @Test
    public void testCalculateAllocation() {
        Long orderId = 142669L;
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        CombinedPayAllocationCalculator.CombinedPayAllocationResult combinedPayAllocationResult = combinedPayAllocationCalculator.calculateAllocation(orderDTO);
        log.info("combinedPayAllocationResult: {}", combinedPayAllocationResult);
    }
}
