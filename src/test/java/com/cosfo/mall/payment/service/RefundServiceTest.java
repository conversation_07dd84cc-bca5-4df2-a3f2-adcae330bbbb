package com.cosfo.mall.payment.service;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class RefundServiceTest {

    @Resource
    private RefundService refundService;

    @Test
    void refundSuccessTest() {

        RefundDTO refundDTO = JSONObject.parseObject("{\"afterSaleId\":8108,\"orderId\":87413,\"refundPrice\":1,\"tenantId\":2}", RefundDTO.class);
        ResultDTO resultDTO = refundService.refundRequest(refundDTO);
    }
}