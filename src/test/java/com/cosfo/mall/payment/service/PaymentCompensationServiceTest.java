package com.cosfo.mall.payment.service;

import com.cosfo.mall.common.task.PaymentCompensationTask;
import com.cosfo.mall.payment.service.impl.PaymentOrderNotifyServiceImpl;
import com.cosfo.mall.payment.service.impl.PaymentSettlementNotifyServiceImpl;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-08-23
 **/
@SpringBootTest
@ActiveProfiles("dev")
public class PaymentCompensationServiceTest {

    @Resource
    private PaymentOrderNotifyServiceImpl paymentOrderNotifyService;
    @Resource
    private PaymentSettlementNotifyServiceImpl paymentSettlementNotifyService;
    @Resource
    private PaymentCompensationTask paymentCompensationTask;

    @Test
    void compensationOrderTest() {
        String paymentNo = "P1826823540857069568";
        paymentOrderNotifyService.notify(paymentNo);
    }

    @Test
    void compensationSettlementTest() {
        String paymentNo = "P1826823540857069568";
        paymentSettlementNotifyService.notify(paymentNo);
    }

    @Test
    void compensationTaskTest() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setJobParameters("{\"startTime\": \"2024-08-23 11:50:00\", \"endTime\": \"2024-08-23 12:00:00\"}\n");
        paymentCompensationTask.processResult(xmJobInput);
    }
}
