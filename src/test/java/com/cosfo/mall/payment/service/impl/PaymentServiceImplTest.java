package com.cosfo.mall.payment.service.impl;

import cn.hutool.http.server.HttpServerRequest;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.DingTalkRobotConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.utils.AesUtil;
import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.common.utils.DingTalkRobotUtil;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.tenant.service.TenantPrepaymentService;
import com.cosfo.mall.wechat.bean.notify.DirectNotify;
import com.cosfo.mall.wechat.bean.refund.RefundNotify;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class PaymentServiceImplTest {
    @Resource
    private PaymentService paymentService;
    @Resource
    private TenantPrepaymentService tenantPrepaymentService;
    @Resource
    private OrderService orderService;
    @Resource
    private DingTalkRobotConfig dingTalkRobotConfig;
    @Resource
    private PaymentMapper paymentMapper;

    @Test
    void wxDirectPayNotify() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String jsonString = "{\"createTime\":\"2022-05-27T16:39:16\",\"eventType\":\"REFUND.SUCCESS\",\"id\":\"7e84f558-0397-591a-991e-6cc7c4acd641\",\"resource\":{\"algorithm\":\"AEAD_AES_256_GCM\",\"associatedData\":\"refund\",\"ciphertext\":\"lLOhBDxDAmmoplecQ/gTIXzQCvR85/l1RS4PcmbG32y418phDF0WsBFFtC1C1Xxb+EzgX7NCY91lCfmPQl+h6mUJyTPyF9ms3mshwAz3Gkikm7mk+kNUO382VrhjziCs7GC2wWF+bNfesHkCCwn2yo9xM6ClkxkmiKSRg2JqPDSMsqPfvHAqDJXrSzJBmZYJrGmLRAosfZmvTM2sT0doAETxRnNPPU0mpj4atPGPn0mcIaXY2IjDkXvXKllQpwbnca4E5mpSgASuxMHrsV18vHOCV2CHxDaieDn01ezACkhQSVQXZR/xPpPgeDyWdsyD2rkcUwymbEh9YH7cocB09Ju1EuejeWLEmDUiN/zqyu5ekqVzMRlZF5FewgmdormiHHAz0aYoB3SfUkugi5cs8MAnXmqIEU1AF1eFmN64uEcCcI4TkftYygZ/EdiuVGRM75g/mHWgky9aGAtAPL30/iKguynz7SpTBlCKeFfXAMR3jWWUjriscJSW82CpRkhxOz4=\",\"nonce\":\"upZ3MhCjZmXX\",\"originalType\":\"refund\"},\"resourceType\":\"encrypt-resource\",\"summary\":\"退款成功\"}";
        DirectNotify directNotify = JSONObject.parseObject(jsonString, DirectNotify.class);
        paymentService.wxDirectPayNotify(directNotify, null);
    }

    @Test
    void profitSharingTest() {
        StringBuffer title = new StringBuffer();
        title.append("订单分账结果同步");
        StringBuffer text = new StringBuffer();
        text.append("> 订单号: " + 123 + " \n\n");
        text.append("> 分账结果: " + "test" + " \n\n ");
        text.append("订单分账结果同步");
        HashMap<String, String> msgMap = new HashMap<>(NumberConstant.TWO);
        msgMap.put(Constants.TITLE, title.toString());
        msgMap.put(Constants.TEXT, text.toString());
        long timestamp = System.currentTimeMillis();
        try {
            String sign = DingTalkRobotUtil.sign(timestamp, dingTalkRobotConfig.getSecret());
            DingTalkRobotUtil.sendMarkDownMsg(dingTalkRobotConfig.getUrl() + "&timestamp=" + timestamp + "&sign=" + sign, () -> msgMap, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testDealThreePartiesOrderPrepaymentRefund() {
        OrderAfterSaleResultDTO orderAfterSaleResultDTO = new OrderAfterSaleResultDTO();
        orderAfterSaleResultDTO.setTenantId(2L);
        orderAfterSaleResultDTO.setOrderId(7603L);
        orderAfterSaleResultDTO.setAfterSaleOrderNo("AS168066432403108");
        orderAfterSaleResultDTO.setTotalPrice(BigDecimal.valueOf(100));
        orderAfterSaleResultDTO.setAmount(1);
        orderAfterSaleResultDTO.setOrderItemId(67807L);
        orderAfterSaleResultDTO.setResponsibilityType(0);
        //orderAfterSaleResultDTO.setDeliveryFee(BigDecimal.ZERO);
        orderAfterSaleResultDTO.setAfterSaleType(0);
        orderAfterSaleResultDTO.setServiceType(4);
//        tenantPrepaymentService.dealThreePartiesOrderPrepaymentRefund(orderAfterSaleResultDTO);
    }

    @Test
    void testDealThreePartiesOrderPrepayment() {
//        Order order = orderService.selectById(7486L);
        // tenantPrepaymentService.dealThreePartiesOrderPrepayment(order);
    }

}
