package com.cosfo.mall.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.constants.NotifyTypeEnum;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Lazy;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-09-19
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class NotifyServiceTest {

    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Lazy
    @Autowired
    private PaymentService paymentService;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;

    @Test
    void huifuPaySuccess() {
        String data = "{\"pay_scene\":\"02\",\"wx_response\":{\"bank_type\":\"CMB\",\"openid\":\"o8jhotzO9WLGyL6-z_iB3Xu8SCFU\",\"coupon_fee\":\"0.00\",\"sub_appid\":\"wx11361ccf7f47b948\",\"sub_openid\":\"ohrxz5UzB0SX-HoXKxZUQ2FxjK9I\"},\"bank_seq_id\":\"376266\",\"trans_amt\":\"0.01\",\"subsidy_stat\":\"I\",\"trans_fee_allowance_info\":{\"allowance_type\":\"0\",\"actual_fee_amt\":\"0.00\",\"receivable_fee_amt\":\"0.00\",\"allowance_fee_amt\":\"0.00\"},\"acct_id\":\"A18822546\",\"bank_order_no\":\"4200001996********5484843101\",\"trans_order_info\":{\"maze_resp_code\":\"\",\"fee_real_acct_id\":\"A25055786\",\"agent_id\":\"\",\"bank_seq_id\":\"376266\",\"subsidy_stat\":\"I\",\"acct_id\":\"A25055786\",\"ref_cnt\":0,\"trans_notify_url\":\"archer://C_TOP_TOPSCENARIO_TRANS_NOTIFY\",\"product_id\":\"PAYUN\",\"bank_mer_id\":\"W1035134718845623798\",\"id\":**********,\"icc_data\":\"\",\"atu_sub_mer_id\":\"*********\",\"bank_type\":\"CMB\",\"fee_allowance_flag\":0,\"trans_stat\":\"S\",\"region_id\":\"TOP2_A\",\"credit_type\":\"\",\"version\":3,\"org_auth_no\":\"\",\"cash_resp_desc\":\"成功\",\"bagent_id\":\"****************\",\"real_cust_id\":\"****************\",\"req_seq_id\":\"P1724739973047291904\",\"term_div_coupon_type\":3,\"iss_inst_id\":\"\",\"channel_finish_time\":*************,\"channel_stat\":\"S\",\"unconfirm_fee_amt\":0.00,\"real_gate_id\":\"VT\",\"db_unit\":\"2\",\"trans_date\":\"********\",\"sys_trace_audit_num\":\"\",\"batch_id\":\"231115\",\"credit_fee_amt\":0.00,\"bank_resp_code\":\"SUCCESS\",\"real_acct_id\":\"A25055786\",\"channel_message\":\"交易成功\",\"settle_trans_stat\":\"\",\"mypaytsf_discount\":0.00,\"unconfirm_amt\":0.01,\"org_huifu_seq_id\":\"\",\"cash_resp_code\":\"000\",\"double_limit_amt\":0.00,\"fee_split_type\":\"\",\"maze_resp_desc\":\"\",\"fee_real_cust_id\":\"****************\",\"fee_amt\":0.00,\"creator\":\"\",\"ord_amt\":0.01,\"acct_stat\":\"I\",\"debit_fee_amt\":0.00,\"ref_num\":\"************\",\"cash_req_date\":\"********184352\",\"out_trans_id\":\"4200001996********5484843101\",\"fee_acct_id\":\"A25055786\",\"subsidy_ref_amt\":0.00,\"is_acct_div_param\":0,\"mer_name\":\"客思服(杭州)科技有限公司\",\"pay_scene\":\"02\",\"time_expire\":\"********191248\",\"modifier\":\"\",\"channel_code\":\"00\",\"mcc\":\"\",\"close_trans_stat\":\"\",\"ref_fee_amt\":0.00,\"trans_finish_time\":*************,\"org_trans_date\":\"\",\"bank_resp_desc\":\"交易成功\",\"pay_channel_id\":\"********\",\"pay_amt\":0.01,\"create_time\":*************,\"org_acct_id\":\"A18822546\",\"dc_type\":\"1\",\"hf_seq_id\":\"0056default231115184352P490ac1362b700000\",\"is_route\":\"\",\"goods_desc\":\"微信测试商品等，共1个商品\",\"is_delay_acct\":1,\"settle_amt\":0.01,\"ref_amt\":0.01,\"gate_id\":\"SPIN022\",\"pay_channel\":\"T\",\"subsidy_amt\":0.00,\"huifu_id\":\"****************\",\"fee_huifu_id\":\"****************\",\"maze_bg_seq_id\":\"\",\"maze_bg_date\":\"\",\"modify_time\":*************,\"check_cash_flag\":\"I\",\"is_acct_div\":0,\"sys_id\":\"****************\",\"card_channel_type\":\"\",\"is_deleted\":0,\"fee_flag\":2,\"cash_trans_id\":\"********2320og0t\",\"pa_mer_id\":\"SSP001\",\"sn_code\":\"\",\"pay_type\":\"JSAPI\",\"channel_type\":\"U\",\"source_region_id\":\"TOP2_A\",\"trans_type\":\"1000\",\"mer_ord_id\":\"MER********36376266\",\"ord_id\":\"********1843520TOP2_Ait017565364\",\"card_sign\":\"\",\"fee_formula\":\"\",\"fee_source\":\"'SERVER'\",\"party_order_id\":\"03232311156743276211760\",\"bank_mer_name\":\"客思服(杭州)科技有限公司\",\"un_scene_info\":\"\",\"maze_pnr_dev_id\":\"\",\"req_date\":\"********\",\"route_region_id\":\"C23_A\",\"fee_rec_type\":1},\"product_id\":\"PAYUN\",\"combinedpay_fee_amt\":\"0.00\",\"atu_sub_mer_id\":\"*********\",\"org_trans_date\":\"\",\"sub_resp_code\":\"********\",\"pay_amt\":\"0.01\",\"bank_type\":\"CMB\",\"trans_stat\":\"S\",\"ref_no\":\"************\",\"hf_seq_id\":\"0056default231115184352P490ac1362b700000\",\"trans_time\":\"184352\",\"org_auth_no\":\"\",\"avoid_sms_flag\":\"\",\"bagent_id\":\"****************\",\"is_delay_acct\":\"1\",\"req_seq_id\":\"P1724739973047291904\",\"gate_id\":\"VT\",\"resp_code\":\"********\",\"huifu_id\":\"****************\",\"out_ord_id\":\"4200001996********5484843101\",\"trans_date\":\"********\",\"batch_id\":\"231115\",\"bank_message\":\"交易成功\",\"risk_check_info\":{},\"sys_id\":\"****************\",\"notify_type\":1,\"is_div\":\"0\",\"mypaytsf_discount\":\"0.00\",\"base_acct_id\":\"A18822546\",\"fee_flag\":2,\"org_huifu_seq_id\":\"\",\"settlement_amt\":\"0.01\",\"channel_type\":\"U\",\"acct_split_bunch\":{\"acct_infos\":[{\"acct_id\":\"A25055786\",\"huifu_id\":\"****************\",\"div_amt\":\"0.01\"}],\"fee_acct_id\":\"A25055786\",\"fee_huifu_id\":\"****************\",\"fee_amt\":\"0.00\"},\"fee_amt\":\"0.00\",\"trans_type\":\"T_JSAPI\",\"bank_code\":\"SUCCESS\",\"mer_ord_id\":\"MER********36376266\",\"resp_desc\":\"交易成功\",\"posp_seq_id\":\"03232311156743276211760\",\"end_time\":\"********184402\",\"acct_stat\":\"I\",\"debit_flag\":\"1\",\"fee_amount\":\"0.00\",\"out_trans_id\":\"4200001996********5484843101\",\"party_order_id\":\"03232311156743276211760\",\"req_date\":\"********\",\"fee_formula_infos\":[{\"fee_formula\":\"AMT*0.0023\",\"fee_type\":\"TRANS_FEE\"}],\"trade_type\":\"T_JSAPI\",\"combinedpay_data\":[],\"mer_name\":\"客思服(杭州)科技有限公司\",\"sub_resp_desc\":\"交易成功\",\"fee_rec_type\":\"1\",\"charge_flags\":\"758_0\"}";

        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(data, HuiFuiPaymentReceive.class);
        String huifuId = huiFuPaymentReceive.getHuifu_id();
        HuiFuPayment huiFuPaymentQuery = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getHfSeqId, huiFuPaymentReceive.getHf_seq_id()));
        if (Objects.isNull(huiFuPaymentQuery)) {
            huiFuPaymentQuery = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>()
                    .eq(HuiFuPayment::getReqSeqId, huiFuPaymentReceive.getReq_seq_id())
                    .eq(HuiFuPayment::getHuifuId, huiFuPaymentReceive.getHuifu_id()));
        }
        if (NotifyTypeEnum.ALREADY.getCode().equals(huiFuPaymentQuery.getNotifyFlag())) {
            System.err.println("1");
        }
        String publicKey = tenantAuthConnectionService.selectPublicKey(huifuId);

        String subRespCode = huiFuPaymentReceive.getResp_code();
        if ("********".equals(subRespCode)) {
            log.info("汇付支付异步返回消息:{}", JSONObject.toJSONString(huiFuPaymentReceive));
            paymentService.huifuPaySuccess(huiFuPaymentReceive, huiFuPaymentQuery.getPaymentId());
            log.info("RECV_ORD_ID_{}", huiFuPaymentReceive.getReq_seq_id());
            System.err.println( "RECV_ORD_ID_" + huiFuPaymentReceive.getReq_seq_id());
        } else {
            log.warn("汇付支付异步返回状态码异常:{}", JSONObject.toJSONString(huiFuPaymentReceive));

        }
    }
}
