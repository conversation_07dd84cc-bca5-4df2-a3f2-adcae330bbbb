package com.cosfo.mall.openapi.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.client.openapi.provider.OrderOpenProvider;
import com.cosfo.mall.client.openapi.req.PlaceOrderReq;
import com.cosfo.mall.client.openapi.resp.PlaceOrderResp;
import com.cosfo.mall.common.config.OpenApiConfig;
import com.cosfo.mall.common.listener.OfcOrderNewListener;
import com.cosfo.mall.openapi.service.OrderNotifyBizService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.xianmu.common.account.IsvInfo;
import net.xianmu.common.account.IsvInfoHolder;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/9/26 下午11:47
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderOpenProviderTest {

    @Resource
    private OrderOpenProvider orderOpenProvider;

    @Resource
    private OrderNotifyBizService orderNotifyBizService;

    @Resource
    private OfcOrderNewListener ofcOrderNewListener;
    @Resource
    private OpenApiConfig openApiConfig;

    @Test
    public void placeOrder() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("2368");
        req.setCustomerOrderId("nj333333334455");
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        // 运费鲜沐直营
        placeOrderItem1.setSkuCode("**********");
        placeOrderItem1.setQuantity(2);
        placeOrderItem1.setCustomerOrderItemId("NJ1001");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("NJskucode-001");
        placeOrderItem1.setCustomerSkuTitle("NJskucode-001商品标题");
        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格");
        orderItemList.add(placeOrderItem1);
        PlaceOrderReq.PlaceOrderItem placeOrderItem2 = new PlaceOrderReq.PlaceOrderItem();
        // 蓝莓
        placeOrderItem2.setSkuCode("*************");
        placeOrderItem2.setQuantity(1);
        placeOrderItem2.setCustomerOrderItemId("NJ1001");
        placeOrderItem2.setItemType(0);
        placeOrderItem2.setCustomerSkuCode("NJskucode-002");
        placeOrderItem2.setCustomerSkuTitle("NJskucode-002商品标题");
        placeOrderItem2.setCustomerSkuSpecification("NJskucode-002规格");
        orderItemList.add(placeOrderItem2);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrder(req);
        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void placeOrderV2() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("2368");
        req.setCustomerOrderId("nj33333333445591");
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        // 0920saas鲜沐直供11
        placeOrderItem1.setSkuCode("************");
        placeOrderItem1.setQuantity(1);
        placeOrderItem1.setCustomerOrderItemId("NJ100001");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("NJskucode-001");
        placeOrderItem1.setCustomerSkuTitle("乔治测试商品-鲜沐直配");
        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格");
        placeOrderItem1.setCustomerSkuSpecificationUnit("箱");
        orderItemList.add(placeOrderItem1);
        PlaceOrderReq.PlaceOrderItem placeOrderItem2 = new PlaceOrderReq.PlaceOrderItem();
        // zjc-代仓B
        placeOrderItem2.setSkuCode("183484035586");
        placeOrderItem2.setQuantity(1);
        placeOrderItem2.setCustomerOrderItemId("NJ10002");
        placeOrderItem2.setItemType(0);
        placeOrderItem2.setCustomerSkuCode("NJskucode-002");
        placeOrderItem2.setCustomerSkuTitle("zjc-代仓B");
        placeOrderItem2.setCustomerSkuSpecification("NJskucode-002规格");
        placeOrderItem2.setCustomerSkuSpecificationUnit("瓶");
        orderItemList.add(placeOrderItem2);

//        placeOrderItem2.setSkuCode("183448623126");
//        placeOrderItem2.setQuantity(1);
//        placeOrderItem2.setCustomerOrderItemId("NJ10002");
//        placeOrderItem2.setItemType(0);
//        placeOrderItem2.setCustomerSkuCode("NJskucode-002");
//        placeOrderItem2.setCustomerSkuTitle("自营仓商品");
//        placeOrderItem2.setCustomerSkuSpecification("NJskucode-002规格");
//        placeOrderItem2.setCustomerSkuSpecificationUnit("瓶");
//        orderItemList.add(placeOrderItem2);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrder(req);
        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void placeOrderV3() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("0927");
        req.setCustomerOrderId("nj20231221000113112");
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        // 回归货品
        placeOrderItem1.setSkuCode("*************");
        placeOrderItem1.setQuantity(1);
        placeOrderItem1.setCustomerOrderItemId("NJ100001");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("NJskucode-001");
        placeOrderItem1.setCustomerSkuTitle("乔治测试商品-鲜沐直配");
        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格");
        placeOrderItem1.setCustomerSkuSpecificationUnit("箱");
        orderItemList.add(placeOrderItem1);
//        // 0920saas鲜沐直供11
//        placeOrderItem1.setSkuCode("************6");
//        placeOrderItem1.setQuantity(********);
//        placeOrderItem1.setCustomerOrderItemId("NJ100001");
//        placeOrderItem1.setItemType(0);
//        placeOrderItem1.setCustomerSkuCode("NJskucode-001");
//        placeOrderItem1.setCustomerSkuTitle("乔治测试商品-鲜沐直配");
//        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格");
//        placeOrderItem1.setCustomerSkuSpecificationUnit("箱");
//        orderItemList.add(placeOrderItem1);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrder(req);
        System.err.println(JSON.toJSONString(response));

    }


    @Test
    public void placeOrderByItemCode() {
        SimpleDateFormat yearFormat= new SimpleDateFormat("yyyyMMdd");
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("0927");
        req.setCustomerOrderId(String.format("nj%s%s", yearFormat.format(new Date()), RandomStringUtils.randomNumeric(8)));
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        // 回归货品
        placeOrderItem1.setSkuCode("NJ0001");
        placeOrderItem1.setQuantity(2);
        placeOrderItem1.setCustomerOrderItemId("NJ100001");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("NJskucode-001XXX");
        placeOrderItem1.setCustomerSkuTitle("XXX");
        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格XXX");
        placeOrderItem1.setCustomerSkuSpecificationUnit("箱X");
        orderItemList.add(placeOrderItem1);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrderByItemCode(req);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void placeOrderByManyItemCode() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("2704");
        req.setCustomerOrderId("nj20231220001131131297");
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);

        // 贝塔测试数据勿动
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        placeOrderItem1.setSkuCode("NJ0001");
        placeOrderItem1.setQuantity(1);
        placeOrderItem1.setCustomerOrderItemId("NJ100000");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("贝塔测试数据勿动-001XXX");
        placeOrderItem1.setCustomerSkuTitle("贝塔测试数据勿动XXX");
        placeOrderItem1.setCustomerSkuSpecification("贝塔测试数据勿动-001规格XXX");
        placeOrderItem1.setCustomerSkuSpecificationUnit("箱X");
        orderItemList.add(placeOrderItem1);

//        // 桃子测试（栗子）
//        PlaceOrderReq.PlaceOrderItem placeOrderItem2 = new PlaceOrderReq.PlaceOrderItem();
//        placeOrderItem2.setSkuCode("TZ001");
//        placeOrderItem2.setQuantity(1);
//        placeOrderItem2.setCustomerOrderItemId("NJ100001");
//        placeOrderItem2.setItemType(0);
//        placeOrderItem2.setCustomerSkuCode("桃子测试-001XXX");
//        placeOrderItem2.setCustomerSkuTitle("桃子测试XXX");
//        placeOrderItem2.setCustomerSkuSpecification("桃子测试-001规格XXX");
//        placeOrderItem2.setCustomerSkuSpecificationUnit("箱");
//        orderItemList.add(placeOrderItem2);

        // 回归货品(验证人工绑定城配仓)
        PlaceOrderReq.PlaceOrderItem placeOrderItem3 = new PlaceOrderReq.PlaceOrderItem();
        placeOrderItem3.setSkuCode("12345678");
        placeOrderItem3.setQuantity(1);
        placeOrderItem3.setCustomerOrderItemId("NJ100002");
        placeOrderItem3.setItemType(0);
        placeOrderItem3.setCustomerSkuCode("回归货品-001XXX");
        placeOrderItem3.setCustomerSkuTitle("回归货品XXX");
        placeOrderItem3.setCustomerSkuSpecification("回归货品-001规格XXX");
        placeOrderItem3.setCustomerSkuSpecificationUnit("箱");
        orderItemList.add(placeOrderItem3);

//        // 回归货品(验证人工绑定城配仓)
//        PlaceOrderReq.PlaceOrderItem placeOrderItem4 = new PlaceOrderReq.PlaceOrderItem();
//        placeOrderItem4.setSkuCode("G1001001");
//        placeOrderItem4.setQuantity(1);
//        placeOrderItem4.setCustomerOrderItemId("NJ100003");
//        placeOrderItem4.setItemType(0);
//        placeOrderItem4.setCustomerSkuCode("自营仓品-001XXX");
//        placeOrderItem4.setCustomerSkuTitle("搭售测试自营仓AXXX");
//        placeOrderItem4.setCustomerSkuSpecification("自营仓品-001规格XXX");
//        placeOrderItem4.setCustomerSkuSpecificationUnit("箱");
//        orderItemList.add(placeOrderItem4);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrderByItemCode(req);
        System.err.println(JSON.toJSONString(response));
    }


    @Test
    public void placeOrderMaxStock() {
        try {
            placeOrderV3MaxStock();
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            placeOrderByItemCodeMaxStock();
        }
    }

    private void placeOrderV3MaxStock() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("0927");
        req.setCustomerOrderId("nj202312210001131121");
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        // 回归货品
        placeOrderItem1.setSkuCode("*************");
        placeOrderItem1.setQuantity(********);
        placeOrderItem1.setCustomerOrderItemId("NJ100001");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("NJskucode-001");
        placeOrderItem1.setCustomerSkuTitle("乔治测试商品-鲜沐直配");
        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格");
        placeOrderItem1.setCustomerSkuSpecificationUnit("箱");
        orderItemList.add(placeOrderItem1);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrder(req);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void placeOrderByItemCodeTest2() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = JSON.parseObject("{\n" +
                "    \"customerOrderId\": \"nj202312220000227\",\n" +
                "    \"orderItemList\": [\n" +
                "        {\n" +
                "            \"customerOrderItemId\": \"NJ10000001\",\n" +
                "            \"customerSkuCode\": \"贝塔测试-001\",\n" +
                "            \"customerSkuSpecification\": \"贝塔测试-001规格\",\n" +
                "            \"customerSkuTitle\": \"NJskucode-贝塔测试\",\n" +
                "            \"itemType\": 0,\n" +
                "            \"quantity\": 1,\n" +
                "            \"skuCode\": \"NJ0001\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"storeNo\": \"0927\"\n" +
                "}", PlaceOrderReq.class);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrderByItemCode(req);
        System.err.println(JSON.toJSONString(response));
    }

    private void placeOrderByItemCodeMaxStock() {
        Long tenantId = 2L;
        IsvInfo isvInfo = new IsvInfo();
        isvInfo.setAccountId(tenantId);
        IsvInfoHolder.setAccount(isvInfo);

        PlaceOrderReq req = new PlaceOrderReq();
        req.setStoreNo("0927");
        req.setCustomerOrderId("nj202312210001131122");
        List<PlaceOrderReq.PlaceOrderItem> orderItemList = Lists.newArrayList();
        req.setOrderItemList(orderItemList);
        PlaceOrderReq.PlaceOrderItem placeOrderItem1 = new PlaceOrderReq.PlaceOrderItem();
        // 回归货品
        placeOrderItem1.setSkuCode("NJ0001");
        placeOrderItem1.setQuantity(9999999);
        placeOrderItem1.setCustomerOrderItemId("NJ100001");
        placeOrderItem1.setItemType(0);
        placeOrderItem1.setCustomerSkuCode("NJskucode-001XXX");
        placeOrderItem1.setCustomerSkuTitle("XXX");
        placeOrderItem1.setCustomerSkuSpecification("NJskucode-001规格XXX");
        placeOrderItem1.setCustomerSkuSpecificationUnit("箱X");
        orderItemList.add(placeOrderItem1);

        System.err.println(JSON.toJSONString(req));

        DubboResponse<PlaceOrderResp> response = orderOpenProvider.placeOrderByItemCode(req);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void notifyOrderDelivering(){
        orderNotifyBizService.notifyOrderDelivering(103749L);

    }

    /**
     * 司机配送完成
     */
    @Test
    public void finishedNotifyThirdPart(){
        String json = "{\"deliveryType\":0,\"itemList\":[{\"actualQuantity\":0,\"itemId\":\"26263\",\"quantity\":1,\"shortQuantity\":1,\"skuCode\":\"183484035586\",\"status\":1},{\"actualQuantity\":0,\"itemId\":\"1454\",\"quantity\":1,\"shortQuantity\":1,\"skuCode\":\"************\",\"status\":1}],\"message\":\"\",\"sourceOrderNo\":\"OR169900598165855\"}";

        CommonFulfillmentFinishMessage commonFulfillmentFinishMessage = JSON.parseObject(json, CommonFulfillmentFinishMessage.class);
        orderNotifyBizService.deliveryFinishedNotifyThirdPart(commonFulfillmentFinishMessage);
    }

    /**
     * 模拟消息回调
     */
    @Test
    public void ofcProcess(){
        String json = "{\"deliveryType\":0,\"itemList\":[{\"actualQuantity\":0,\"itemId\":\"26263\",\"quantity\":1,\"shortQuantity\":1,\"skuCode\":\"183484035586\",\"status\":1},{\"actualQuantity\":0,\"itemId\":\"1454\",\"quantity\":1,\"shortQuantity\":1,\"skuCode\":\"************\",\"status\":1}],\"message\":\"\",\"sourceOrderNo\":\"OR169900598165855\"}";

        CommonFulfillmentFinishMessage commonFulfillmentFinishMessage = JSON.parseObject(json, CommonFulfillmentFinishMessage.class);
        ofcOrderNewListener.process(commonFulfillmentFinishMessage);
    }

    /**
     * 自提配送完成
     */
    @Test
    public void selfLiftFinishedNotifyThirdPart(){
        orderNotifyBizService.selfLiftFinishedNotifyThirdPart(94105L);
    }

}