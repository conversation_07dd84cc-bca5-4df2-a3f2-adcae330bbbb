package com.cosfo.mall.stock;

import static java.math.BigDecimal.ROUND_HALF_UP;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.summerfarm.model.input.SummerfarmTmsInput;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/20  11:31
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class StockTest {
    @Resource
    private StockService stockService;

    @Test
    public void testQueryDeliveryTime() {
//        浙江	嘉兴市	南湖区	甜品好吃的店
//        MerchantAddressDTO dto = new MerchantAddressDTO();
//        dto.setProvince("浙江");
//        dto.setCity("嘉兴市");
//        dto.setArea("南湖区");
//        dto.setAddress("甜品好吃的店");
//        LocalDate localDate = ofcDeliveryInfoFacade.queryDeliveryDate(dto, null);
//        System.out.println(localDate);
//        assert localDate != null;

        String s = "{\"deliveryPathId\":9145919,\"deliveryPathShortSkuList\":[],\"orderNos\":[\"OR165778681831971\"]}";
        SummerfarmTmsInput summerfarmTmsInput = JSONObject.parseObject(s, SummerfarmTmsInput.class);
        System.out.println(summerfarmTmsInput);
    }

    @Test
    public void test(){
        List<String> list = Arrays.asList("1","2","3","4","5","6","7","8","9","10");
        System.out.println(list.subList(0,9));
        BigDecimal serviceFee = NumberUtil.mul(new BigDecimal(475), NumberUtil.div(new BigDecimal(0.3), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
        System.out.println(serviceFee);
    }
}
