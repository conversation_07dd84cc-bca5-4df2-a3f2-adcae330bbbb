package com.cosfo.mall.stock.service.impl;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.StockRecordType;
import com.cosfo.mall.facade.SaleInventoryCommandFacade;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.stock.service.StockService;
import net.xianmu.inventory.client.saleinventory.dto.req.PreDistributionOrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.PreDistributionSkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.PreDistributionOrderOccupyResDTO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class StockServiceImplTest {
    @Resource
    private StockService stockService;
    @Resource
    private SaleInventoryCommandFacade saleInventoryCommandFacade;

    @Test
    void increaseSelfStock() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);

        stockService.increaseSelfStock(loginContextInfoDTO.getTenantId(), StockRecordType.ORDER, 1452L, -20, "2412412");
    }

    @Test
    void preDistributionOrderOccupy() {
        String json = "{\"address\":\"天目山路518号\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactId\":143025,\"poi\":\"120.061159,30.255079\",\"preDistributionSkuDetailReqDTOS\":[{\"occupyQuantity\":99999999,\"skuCode\":\"6315027007\"},{\"occupyQuantity\":99999999,\"skuCode\":\"6762453354\"}],\"province\":\"浙江省\",\"tenantId\":2}";

        PreDistributionOrderOccupyReqDTO preDistributionOrderOccupyReqDTO = JSON.parseObject(json, PreDistributionOrderOccupyReqDTO.class);
        PreDistributionOrderOccupyResDTO preDistributionOrderOccupyResDTO = saleInventoryCommandFacade.preDistributionOrderOccupy(preDistributionOrderOccupyReqDTO);
        System.err.println("第一次"+preDistributionOrderOccupyResDTO);

        List<PreDistributionSkuDetailReqDTO> preDistributionSkuDetailReqDTOS = preDistributionOrderOccupyReqDTO.getPreDistributionSkuDetailReqDTOS();
        preDistributionSkuDetailReqDTOS.stream().forEach(preDistributionSkuDetailReqDTO -> preDistributionSkuDetailReqDTO.setOccupyQuantity(1));
        preDistributionOrderOccupyResDTO = saleInventoryCommandFacade.preDistributionOrderOccupy(preDistributionOrderOccupyReqDTO);
        System.err.println("第二次"+preDistributionOrderOccupyResDTO);

        preDistributionSkuDetailReqDTOS.stream().forEach(preDistributionSkuDetailReqDTO -> preDistributionSkuDetailReqDTO.setOccupyQuantity(0));
        preDistributionOrderOccupyResDTO = saleInventoryCommandFacade.preDistributionOrderOccupy(preDistributionOrderOccupyReqDTO);
        System.err.println("第三次"+preDistributionOrderOccupyResDTO);
    }

    @Test
    void queryStockAmount(){
//        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//        loginContextInfoDTO.setTenantId(2L);
////        loginContextInfoDTO.setStoreId(4260L);
//        loginContextInfoDTO.setStoreId(162463L);
////        MerchantAddressDTO orderAddress = JSON.parseObject("{\"address\":\"新杭商务中心\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"cityId\":330100,\"houseNumber\":\"梅林路9\",\"id\":4256,\"poiNote\":\"120.058595,30.279946\",\"province\":\"浙江省\",\"storeId\":4260,\"tenantId\":2,\"updateTime\":\"2023-06-05T14:22:09\"}", MerchantAddressDTO.class);
//        MerchantAddressDTO orderAddress = JSON.parseObject("{\"address\":\"盐城市\",\"area\":\"盐都区\",\"city\":\"盐城市\",\"cityId\":320900,\"createTime\":\"2024-04-15T13:55:21\",\"houseNumber\":\"哈哈哈哈哈\",\"id\":41982,\"poiNote\":\"120.16263,33.348176\",\"province\":\"江苏省\",\"storeId\":162463,\"tenantId\":2,\"updateTime\":\"2024-04-18T18:54:03\"}", MerchantAddressDTO.class);
////        List<Long> itemIds = Lists.newArrayList(562L, 23562L, 13300L, 13160L, 632L);
//        List<Long> itemIds = Lists.newArrayList(46667L, 46670L);
//        Map<Long, StockDTO> stockDTOMap = stockService.queryStockAmount(loginContextInfoDTO, orderAddress, itemIds, null);
//        Map<Long, StockDTO> stockDTOMapOld = stockService.queryStockAmountOld(loginContextInfoDTO, orderAddress, itemIds, null);
//        System.err.println("新接口结果：" + JSON.toJSONString(stockDTOMap));
//        System.err.println("新接口结果：" + JSON.toJSONString(stockDTOMapOld));
    }
}