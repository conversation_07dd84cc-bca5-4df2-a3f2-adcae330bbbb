package com.cosfo.mall.order;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.order.mapper.OrderMapper;
import com.cosfo.mall.order.model.dto.HuiFuPaymentQueryRequestDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundQueryResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuQueryRefundRequestDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.api.PayMchAPI;
import com.cosfo.mall.wechat.bean.notify.DirectNotify;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/25  06:54
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class OrderPayTest {
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private RefundService refundService;
    @Resource
    private RefundMapper payStrategyRefundMapper;

    @Test
    public void payTest() {
//        Order order = orderMapper.selectByPrimaryKey(340L);
//        LoginContextInfoDTO dto = new LoginContextInfoDTO();
//        dto.setTenantId(order.getTenantId());
//        dto.setAccountId(order.getAccountId());
//        dto.setStoreId(order.getStoreId());
//        ResultDTO pay = orderService.pay(dto, Collections.singletonList(order.getOrderNo()));
//        System.out.println(pay);

    }

    @Test
    public void queryHuiFuPaymentRefundResult() {

        HuiFuQueryRefundRequestDTO huiFuQueryRefundRequestDTO = new HuiFuQueryRefundRequestDTO();
        huiFuQueryRefundRequestDTO.setHuiFuId("****************");
        huiFuQueryRefundRequestDTO.setOrgReqDate("********");
        huiFuQueryRefundRequestDTO.setOrgReqSeqId("HP_REFUND168802031203269");
//        huiFuQueryRefundRequestDTO.setMer_ord_id(org.apache.commons.lang3.StringUtils.EMPTY);
//        huiFuQueryRefundRequestDTO.setOrg_hf_seq_id(org.apache.commons.lang3.StringUtils.EMPTY);

        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setSecretKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJHDoRgk1kwRzwkp/CsFtMyEZ43jxQkYhv7+Q5FnFrDdwZBYkV+sDWJIQcJiac00sknMJx+2mkaZKd5S5c8Rry2q5CwJOzve64tWm53V5nClImPJVhasEDPZrIFRblaNHZzYqLI+eooKEzUqyDtUu6f1GfQfEX7OAQSt8uLLpOB5AgMBAAECgYAKIPfuv5CMlKg4UMyPCMR5ebHgcDVe3m9zBKQZVHqFK8Opjh378e60J6U6JrCHa+P/sMn/MCckRLxeEJCbhPURMnfexffiehLGQsunt5G7/Fo0aF5iZ2IbPal4nxQiV3sCkkzedAMWbCFdicrBD6OwaApgyKiDF/STedhio796AQJBAMKyDLZHKgQZRhGMxjAQ4nTMLT8TkYS/iCMISzMyGVzEVEyqRCVgL2TNGlPQVkZbN1hrYXDEU7xwBF9ktVLfERkCQQC/qVbfc8N+jz6MV6rVFcb13PjlnqsOGjhf60AX5ZbQ7uxIHlFETSW8VWnspo+WmRnx29Xa3N2xAj+xY1CniVZhAkEApgNAR6UCtKAAWTKaQ54CGumGo0RCTtkqcgS+apcLfUiDQSPmG6CaiO9Cxbjd9/wLgz98xLUDWaiCcfzjiBhg8QJAU1X+QArF5jHCsKMt1JkzeZQrZSwolGkyOgLAS6O4gABjHLiR/XDOB6Xb+NozdPf9H/zGb/O5MU4ZA7h7SV2eAQJAdwJSeiTcu0V/VJFrez/mmmoHpzFb5EWEk2VZVvWPXapcwt88R4Lju5dluL7OzNiwtpLn8pZrHmm5waH+/DpIPw==");

        HuiFuConfig huiFuConfig = new HuiFuConfig();
        huiFuConfig.setProductId("PAYUN");

        HuiFuPaymentRefundQueryResponseDTO huiFuPaymentRefundQueryResponseDTO = PayMchAPI.queryHuiFuPaymentRefundResult(huiFuQueryRefundRequestDTO, tenantAuthConnection, huiFuConfig);

        System.err.println(JSON.toJSONString(huiFuPaymentRefundQueryResponseDTO));
    }

    @Test
    public void queryHuiFuPaymentStat() {
        HuiFuPayment huiFuPayment = new HuiFuPayment();
        huiFuPayment.setReqDate("20230630");
        huiFuPayment.setHfSeqId("002900TOP3A230630192531P135ac139c0b00000");
        huiFuPayment.setHuifuId("6666000133141030");
        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setSecretKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMaP5btKV/y24yqpY83hwcVTanpCsPeG5GsrR6fl0gvAkJbopeVkWpIFmmvIU6UZi4nntyG1VCYJFlDenluns5wq1QMMRKZPBOSd2Mh6ozYYlAmYQ7tSVQNfaWmbtOClAhyCY0cSBr8AN0zPGPZlkkQayu/IQhXEHsaCennVLDcVAgMBAAECgYBkgCLkKB3ewGa8ih4E7/ZWvQDQvv65WmI0yCQtnAozfpTwfPKy55db/1AIq82CbCqPc2PiwNfFpKXR6su2Umx6gsrpsGP7BCrKfgAyb0YBR9dD/WNSk07LjOETI3Ba7kxhIw8Hy3oW6Zkgk1YSKfZGKr5WvJUIH/vYWqeFZYUoQQJBAPSMgZKTlcFb8c9VuH8CrtTrfSTOHU+XymF73o1Xh74R+R1eCPsF7fUXOev+56xHzjF47jgCgVMyvfLE4DqDbjECQQDP3CG4j8iGJP7uwC6uCe5NjktyzwqJyUtFCGxj4xkOpIKiSgHhjVXZnd57K/tKaLB64rER1gtMrDjK25/JHWolAkAlVVHuo5NIKpY21dpNoYY5QR4gwUqePQkLatKl90fkz2+deR6b2+Lg+MPB8G9D0M58pP6RFGjT37o51q54oFnxAkAUTtqQkKOzz3uE0gv9fwTkWOmwLuBFtAABn6APZkm1b4c/2eEzKz7fAxiQiiyECZRsfSd1qaby98SYtTp4SMJtAkEA8Y8k6ANqxZ6o0tZjLmvT1nOZ4J6Zlyssx3K6VvODml/c292/Y8Gl7bC+fYiD906t1RC9r5W5fZIUYDeWPeW8Iw==");
        HuiFuConfig huiFuConfig = new HuiFuConfig();
        huiFuConfig.setProductId("PAYUN");
        HuiFuPaymentQueryRequestDTO huiFuPaymentQueryRequestDTO = new HuiFuPaymentQueryRequestDTO();
        huiFuPaymentQueryRequestDTO.setOrgReqDate(huiFuPayment.getReqDate());
        huiFuPaymentQueryRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
        huiFuPaymentQueryRequestDTO.setOrgHfSeqId(huiFuPayment.getHfSeqId());
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPaymentQueryRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());

        PayMchAPI.queryHuiFuPaymentStat(huiFuPayment, tenantAuthConnection, huiFuConfig);
    }

    @Test
    public void insertRefund() {

        RefundDTO refundDTO = new RefundDTO();
        String refundNo = Global.generateRefundNo();
        refundDTO.setReqSeqId(Global.createHuiFuNo(Global.HUIFU_REFUND));
        Refund refund = new Refund();
        BeanUtil.copyProperties(refundDTO, refund);
        refund.setRefundNo(refundNo);
        refund.setCreateTime(LocalDateTime.now());
        refund.setRefundStatus(RefundEnum.Status.IN_REFUND.getStatus());
        int i = payStrategyRefundMapper.insertSelective(refund);
        System.err.println(i);
    }

    @Test
    public void notifyTest() {
        String ss = "{\"createTime\":\"2022-05-25T20:27:03\",\"eventType\":\"TRANSACTION.SUCCESS\",\"id\":\"593ec052-ad81-5390-ba03-78656a3f1097\",\"resource\":{\"algorithm\":\"AEAD_AES_256_GCM\",\"associatedData\":\"transaction\",\"ciphertext\":\"DDvoZ2tUS0DiIvBN3kQvfP+vQBjIEQqnxkTFV1f5Jq58N32XM/ZIiOq8UskmP8sgb6t4o/EuOJ9Djfn06AaNqKtGRAvxKZZRET9uMumuX6R21KYyAof2+0x9kBh+/ZXfUON5pP/oh3zJWHw+m3fOMJnL3egQvFLBToPL7OZAlwqT0eNRI/ym3LrYwir0XyO8z+F8w+GzTNdV3Ohx9jJqRgKhwaGY1jPmgKd9p5a3VH6QSopcTW0WMY4fTIp9I1ZmIAAWxs/7wHBPfBJLozliIP9kqBZKRM2UQzb9SCaVv0KKoOZHupYfCp++AbuQx/ZawEvcIxsLcYHsHWq/MHANoKOVVS2xTfTmWM29g9g1eQQ6ViQGS2qFRMocahjATfhiCIjZxi2wlYHx9Qj2Ol95F9tuehDSNGwoVSdjVVO4qhi1B7k7+MQhu3Fu6tQI+uvtY0u1bmtpMSsVk4n9wlkAwcOEq13NG9m/3Kpdi5wBZEYt6WdFlOpA5s33ylW1UlmKyDB1gYZtO65nFRdpOWpeLwcNvOTSYH9Yh3c44m29vHihi2nuV6dkgSCIFhLko51r1yW35kvuUw==\",\"nonce\":\"MiTOjQ62eczw\",\"originalType\":\"transaction\"},\"resourceType\":\"encrypt-resource\",\"summary\":\"支付成功\"}";
        paymentService.wxDirectPayNotify(JSONObject.parseObject(ss, DirectNotify.class), null);
    }

    @Test
    public void testRefund() {
        RefundDTO refundDTO = new RefundDTO();
        refundDTO.setOrderId(8387L);
        refundDTO.setTenantId(1003L);
        refundDTO.setAfterSaleId(3378L);
        refundDTO.setRefundPrice(BigDecimal.valueOf(2));
        refundService.refundRequest(refundDTO);
    }

}
