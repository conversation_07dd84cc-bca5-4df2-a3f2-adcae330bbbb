package com.cosfo.mall.order.controller;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.payment.model.vo.PaymentVO;
import com.cosfo.ordercenter.client.common.PayTypeEnum;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class OrderControllerTest {

    @Resource
    private OrderController orderController;

    @Test
    void pay() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setAccountId(3530L);
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4260L);
        ThreadTokenHolder.setToken(loginContextInfoDTO);
        PaymentVO paymentVO = new PaymentVO();
        paymentVO.setOrderNos(Lists.newArrayList("*****************"));
        paymentVO.setPayType(PayTypeEnum.BILL.getCode());
        paymentVO.setH5Request(true);
        ResultDTO resultDTO = orderController.pay(paymentVO);
        System.err.println(JSON.toJSONString(resultDTO));
    }

    @Test
    void getOrderInfo() {
        String orderNo = "*****************";
        Long tenantId = 2L;
        ResultDTO orderInfo = orderController.getOrderInfo(orderNo, tenantId);
        System.err.println(JSON.toJSONString(orderInfo));
    }

}
