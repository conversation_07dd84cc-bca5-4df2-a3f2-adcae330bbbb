package com.cosfo.mall.order.mapper;

import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.order.service.impl.OrderServiceImpl;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class OrderMapperTest {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderServiceImpl orderServiceImpl;
    @Resource
    private OrderService orderService;

//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;

    @Test
    void updateOrderStatusById() {
//        Integer count = orderMapper.updateOrderStatusById(6L, OrderStatusEnum.PAID.getCode(), OrderStatusEnum.DELIVERING.getCode(), null);
//        Assert.assertTrue(count.equals(NumberConstant.ONE));
    }

    @Test
    void get() {
//        MerchantStore merchantStore = merchantStoreMapper.selectByPrimaryKey(4053L);
//        System.err.println(merchantStore);
    }

    @Test
    void testFinish(){
//        orderMapper.finishOrderWithDeliveryTime(null, OrderStatusEnum.FINISHED.getCode(),
//                LocalDateTime.now());
    }

    @Test
    void testUpdate(){
        LocalDate localDate = LocalDate.now().plusDays(1);
        Integer count = orderMapper.updateOrderDeliveryTime(72714L, OrderStatusEnum.WAITING_DELIVERY.getCode(), localDate.atStartOfDay());

    }

    @Test
    void testReleaseStock(){
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        Order order = orderMapper.selectByPrimaryKeyForUpdate(72595L);
//        orderServiceImpl.releaseStock(Lists.newArrayList(order), loginContextInfoDTO);
    }

    @Test
    void testCancelOrder(){
        Long orderId = 92339L;
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        orderService.cancel(orderId, loginContextInfoDTO, OrderCancelTypeEnum.MANUALLY_CANCEL.getType());
    }
}