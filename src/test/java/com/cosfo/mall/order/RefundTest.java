package com.cosfo.mall.order;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundQueryResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuQueryRefundRequestDTO;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.service.impl.RefundServiceImpl;
import com.cosfo.mall.payment.strategy.impl.HuiFuPayStrategy;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.wechat.api.PayMchAPI;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @Date: 2023-06-30
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RefundTest {

    @Autowired
    private HuiFuPayStrategy huiFuPayStrategy;
    @Resource
    private RefundServiceImpl refundService;
    @Resource
    private RefundMapper refundMapper;

    @Test
    public void sendSuccessNotifyMessage() {
        Refund refund = refundMapper.selectByPrimaryKey(163513L);
        refundService.sendSuccessNotifyMessage(refund,true);
    }

    @Test
    public void queryHuiFuPaymentRefundResult() {

        HuiFuQueryRefundRequestDTO huiFuQueryRefundRequestDTO = new HuiFuQueryRefundRequestDTO();
        huiFuQueryRefundRequestDTO.setHuiFuId("6666000133119042");
        huiFuQueryRefundRequestDTO.setOrgReqDate("20230629");
        huiFuQueryRefundRequestDTO.setOrgReqSeqId("HP_REFUND168802031203269");
//        huiFuQueryRefundRequestDTO.setMer_ord_id(org.apache.commons.lang3.StringUtils.EMPTY);
//        huiFuQueryRefundRequestDTO.setOrg_hf_seq_id(org.apache.commons.lang3.StringUtils.EMPTY);

        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setSecretKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJHDoRgk1kwRzwkp/CsFtMyEZ43jxQkYhv7+Q5FnFrDdwZBYkV+sDWJIQcJiac00sknMJx+2mkaZKd5S5c8Rry2q5CwJOzve64tWm53V5nClImPJVhasEDPZrIFRblaNHZzYqLI+eooKEzUqyDtUu6f1GfQfEX7OAQSt8uLLpOB5AgMBAAECgYAKIPfuv5CMlKg4UMyPCMR5ebHgcDVe3m9zBKQZVHqFK8Opjh378e60J6U6JrCHa+P/sMn/MCckRLxeEJCbhPURMnfexffiehLGQsunt5G7/Fo0aF5iZ2IbPal4nxQiV3sCkkzedAMWbCFdicrBD6OwaApgyKiDF/STedhio796AQJBAMKyDLZHKgQZRhGMxjAQ4nTMLT8TkYS/iCMISzMyGVzEVEyqRCVgL2TNGlPQVkZbN1hrYXDEU7xwBF9ktVLfERkCQQC/qVbfc8N+jz6MV6rVFcb13PjlnqsOGjhf60AX5ZbQ7uxIHlFETSW8VWnspo+WmRnx29Xa3N2xAj+xY1CniVZhAkEApgNAR6UCtKAAWTKaQ54CGumGo0RCTtkqcgS+apcLfUiDQSPmG6CaiO9Cxbjd9/wLgz98xLUDWaiCcfzjiBhg8QJAU1X+QArF5jHCsKMt1JkzeZQrZSwolGkyOgLAS6O4gABjHLiR/XDOB6Xb+NozdPf9H/zGb/O5MU4ZA7h7SV2eAQJAdwJSeiTcu0V/VJFrez/mmmoHpzFb5EWEk2VZVvWPXapcwt88R4Lju5dluL7OzNiwtpLn8pZrHmm5waH+/DpIPw==");

        HuiFuConfig huiFuConfig = new HuiFuConfig();
        huiFuConfig.setProductId("PAYUN");

        HuiFuPaymentRefundQueryResponseDTO huiFuPaymentRefundQueryResponseDTO = PayMchAPI.queryHuiFuPaymentRefundResult(huiFuQueryRefundRequestDTO, tenantAuthConnection, huiFuConfig);

        System.err.println(JSON.toJSONString(huiFuPaymentRefundQueryResponseDTO));
    }

    @Test
    public void refundRetry() {
//        HuiFuRefund huiFuRefund = new HuiFuRefund();
//        huiFuRefund.setUpdateTime(LocalDateTime.now());
//        Refund updateRefund = new Refund();
//        updateRefund.setId(163266L);
//        huiFuRefundMapper.update(huiFuRefund, new LambdaUpdateWrapper<HuiFuRefund>().eq(HuiFuRefund::getRefundId, updateRefund.getId()).eq(HuiFuRefund::getReqSeqId, "HP_REFUND2023041310264577"));

        Refund refund = new Refund();
        refund.setCreateTime(LocalDateTime.now());
        refund.setTenantId(4L);
//        refund.setReqSeqId("HP_REFUND168809214625549");
        refund.setReqSeqId("HP_REFUND168861270009720");
        refund.setReqSeqId("HP_REFUND168861270009720123");
        refund.setRefundPrice(BigDecimal.valueOf(30.00));
        Payment payment = new Payment();
        payment.setId(55284L);


        // 退款成功不处理
//        OutRefundResultEnum outRefundResultEnum = huiFuPayStrategy.doLastRefundResult(refund, payment);
//        if (!refundFail) {
//            return;
//        }
//        // 更新失败不处理
//        String reqSeqId = Global.createHuiFuNo(Global.HUIFU_REFUND);
//        boolean success = huiFuPayStrategy.updateRefundReqIdCas(refund, reqSeqId);
//        if (!success) {
//            return;
//        }
//        refund.setReqSeqId(reqSeqId);
//        huiFuPayStrategy.executeRefundRequest(refund, payment);
    }

}
