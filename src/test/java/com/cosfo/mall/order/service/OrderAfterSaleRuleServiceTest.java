package com.cosfo.mall.order.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleQueryProvider;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleRuleDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.client.service.OrderAfterSaleRuleService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/11/14 下午2:06
 */
@SpringBootTest
public class OrderAfterSaleRuleServiceTest {

    @DubboReference
    private OrderAfterSaleRuleQueryProvider orderAfterSaleRuleQueryProvider;
    @DubboReference
    private OrderAfterSaleRuleService orderAfterSaleRuleService;


    @Test
    public void testqueryByTenantId(){
        Long tenantId = 2L;
        List<OrderAfterSaleRuleResp> resps = RpcResultUtil.handle(orderAfterSaleRuleQueryProvider.queryByTenantId(tenantId));
        List<OrderAfterSaleRuleDTO> oldDtos = RpcResultUtil.handle(orderAfterSaleRuleService.queryByTenantId(tenantId));

        System.err.println(JSON.toJSONString(resps));
        System.err.println(JSON.toJSONString(oldDtos));
        System.err.println(JSON.toJSONString(resps).equals(JSON.toJSONString(oldDtos)));
    }

}
