package com.cosfo.mall.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constants.ItemSaleModeEnum;
import com.cosfo.mall.common.context.PageSourceEnum;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.OrderQuantityRuleDetailDTO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleCheckVO;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleVO;
import com.cosfo.mall.order.model.dto.OrderRuleCheckDTO;
import com.cosfo.mall.order.model.dto.OrderRuleCheckItemDTO;
import com.cosfo.mall.order.service.OrderQuantityRuleService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderQuantityRuleServiceImplIntegrationTest {

    @Resource
    private OrderQuantityRuleService orderQuantityRuleService;
//
//    @Test
//    void ruleTest() {
//        OrderRuleCheckDTO dto = new OrderRuleCheckDTO();
//        OrderRuleCheckItemDTO checkItemDTO =  new OrderRuleCheckItemDTO();
//        checkItemDTO.setQuantity(1);
//        checkItemDTO.setItemId(1583L);
//        dto.setItemList(Lists.newArrayList(checkItemDTO));
//        LoginContextInfoDTO contextInfoDTO = new LoginContextInfoDTO();
//        contextInfoDTO.setTenantId(24457L);
//        contextInfoDTO.setStoreId(4265L);
//        List<OrderQuantityRuleCheckVO> checkVOList = orderQuantityRuleService.orderQuantityRuleCheck(dto, contextInfoDTO);
//        System.out.println(checkVOList);
//
//    }

    @Test
    void onlyAllMallRuleMatchTest() {

        OrderRuleCheckDTO dto = new OrderRuleCheckDTO();
        OrderRuleCheckItemDTO checkItemDTO = new OrderRuleCheckItemDTO();
        checkItemDTO.setQuantity(1);
        checkItemDTO.setItemId(1583L);
        checkItemDTO.setAmount(new BigDecimal("1"));
        dto.setItemList(Lists.newArrayList(checkItemDTO));
        dto.setCheckSource(PageSourceEnum.GOODS_DETAIL.getCode());

        LoginContextInfoDTO contextInfoDTO = new LoginContextInfoDTO();
        contextInfoDTO.setTenantId(24457L);
        contextInfoDTO.setStoreId(4265L);

        List<OrderQuantityRuleCheckVO> orderQuantityRuleCheckVOS = orderQuantityRuleService.orderQuantityRuleCheck(dto, contextInfoDTO);
        System.out.println(JSON.toJSONString(orderQuantityRuleCheckVOS));
    }

    @Test
    void otherRuleHitTest() {
        // 组装受检商品
        OrderRuleCheckDTO  checkDTO = new OrderRuleCheckDTO();
        OrderRuleCheckItemDTO checkItemDTO = new OrderRuleCheckItemDTO();
        checkItemDTO.setItemId(1L);
        checkItemDTO.setQuantity(1);
        checkItemDTO.setWarehouseNo(1L);
        checkItemDTO.setWarehouseType(0);
        checkItemDTO.setAmount(new BigDecimal("10"));
        checkItemDTO.setGoodsType(1);
        OrderRuleCheckItemDTO checkItemDTO2 = new OrderRuleCheckItemDTO();
        checkItemDTO2.setItemId(2L);
        checkItemDTO2.setQuantity(2);
        checkItemDTO2.setWarehouseNo(1L);
        checkItemDTO2.setWarehouseType(0);
        checkItemDTO2.setAmount(new BigDecimal("1"));
        checkItemDTO2.setGoodsType(1);

        OrderRuleCheckItemDTO checkItemDTO3 = new OrderRuleCheckItemDTO();
        checkItemDTO3.setItemId(3L);
        checkItemDTO3.setQuantity(2);
        checkItemDTO3.setWarehouseNo(2L);
        checkItemDTO3.setWarehouseType(1);
        checkItemDTO3.setAmount(new BigDecimal("1"));
        checkItemDTO3.setGoodsType(1);

        checkDTO.setItemList(Lists.newArrayList(checkItemDTO, checkItemDTO2, checkItemDTO3));
        checkDTO.setCheckSource(1);

        // 组装其他规则
        OrderQuantityRuleVO ruleVO = new OrderQuantityRuleVO();
        ruleVO.setId(1L);
        ruleVO.setTenantId(1L);
        ruleVO.setRuleTarget(1L);
        ruleVO.setWarehouseType(0);

        OrderQuantityRuleDetailDTO ruleDetailDTO = new OrderQuantityRuleDetailDTO();
        ruleDetailDTO.setRuleDetailType(0);
        ruleDetailDTO.setAmount(new BigDecimal("10"));


        ruleVO.setRuleDetailList(Lists.newArrayList(ruleDetailDTO));
        ruleVO.setOperator("or");
        ruleVO.setHitItemIds(null);


        // 销售模式
        Map<Long, Integer> itemSaleModeMap = new HashMap<>();
        itemSaleModeMap.put(1L, ItemSaleModeEnum.NORMAL_SALE.ordinal());
        itemSaleModeMap.put(2L, ItemSaleModeEnum.NORMAL_SALE.ordinal());

        OrderQuantityRuleServiceImpl.PartCheckResult partCheckResult = orderQuantityRuleService.handleOtherRule(checkDTO, Lists.newArrayList(ruleVO), itemSaleModeMap);
        System.out.println(JSON.toJSONString(partCheckResult));
    }

    @Test
    void ruleHitResultTest() {
        // 满足10件且20元
        OrderQuantityRuleVO ruleVO = new OrderQuantityRuleVO();
        OrderQuantityRuleDetailDTO quantityRuleDetailDTO = new OrderQuantityRuleDetailDTO();
        quantityRuleDetailDTO.setRuleDetailType(1);
        quantityRuleDetailDTO.setQuantity(10);

        OrderQuantityRuleDetailDTO priceDetail = new OrderQuantityRuleDetailDTO();
        priceDetail.setRuleDetailType(0);
        priceDetail.setAmount(new BigDecimal(20));

        ruleVO.setRuleDetailList(Lists.newArrayList(quantityRuleDetailDTO, priceDetail));
        ruleVO.setOperator("and");

        OrderQuantityRuleCheckVO orderQuantityRuleCheckVO = orderQuantityRuleService.ruleHitResult(ruleVO, 10, new BigDecimal(20));
        Assertions.assertTrue(orderQuantityRuleCheckVO.getRulePass());

        OrderQuantityRuleCheckVO resultWithQuantityNotHit = orderQuantityRuleService.ruleHitResult(ruleVO, 8, new BigDecimal(20));
        Assertions.assertFalse(resultWithQuantityNotHit.getRulePass());
        Assertions.assertEquals(resultWithQuantityNotHit.getQuantity(), 2);

        OrderQuantityRuleCheckVO resultWithPriceNotHit = orderQuantityRuleService.ruleHitResult(ruleVO, 10, new BigDecimal(10));
        Assertions.assertFalse(resultWithPriceNotHit.getRulePass());
        Assertions.assertEquals(resultWithPriceNotHit.getAmount(), new BigDecimal(10));
    }


    @Test
    void ruleHitResultWithOrOpTest() {
        // 满足10件或20元
        OrderQuantityRuleVO ruleVO = new OrderQuantityRuleVO();
        OrderQuantityRuleDetailDTO quantityRuleDetailDTO = new OrderQuantityRuleDetailDTO();
        quantityRuleDetailDTO.setRuleDetailType(1);
        quantityRuleDetailDTO.setQuantity(10);

        OrderQuantityRuleDetailDTO priceDetail = new OrderQuantityRuleDetailDTO();
        priceDetail.setRuleDetailType(0);
        priceDetail.setAmount(new BigDecimal(20));

        ruleVO.setRuleDetailList(Lists.newArrayList(quantityRuleDetailDTO, priceDetail));
        ruleVO.setOperator("or");

        OrderQuantityRuleCheckVO orderQuantityRuleCheckVO = orderQuantityRuleService.ruleHitResult(ruleVO, 10, new BigDecimal(20));
        Assertions.assertTrue(orderQuantityRuleCheckVO.getRulePass());

        OrderQuantityRuleCheckVO resultWithQuantityNotHit = orderQuantityRuleService.ruleHitResult(ruleVO, 8, new BigDecimal(20));
        Assertions.assertTrue(resultWithQuantityNotHit.getRulePass());

        OrderQuantityRuleCheckVO resultWithPriceNotHit = orderQuantityRuleService.ruleHitResult(ruleVO, 10, new BigDecimal(10));
        Assertions.assertTrue(resultWithPriceNotHit.getRulePass());

        OrderQuantityRuleCheckVO resultWithALlNotHit = orderQuantityRuleService.ruleHitResult(ruleVO, 8, new BigDecimal(10));
        Assertions.assertFalse(resultWithALlNotHit.getRulePass());
        Assertions.assertEquals(resultWithALlNotHit.getAmount(), new BigDecimal(10));
        Assertions.assertEquals(resultWithALlNotHit.getQuantity(), 2);

    }

    @Test
    void orderQuantityLimitResultForShowTest() {
        List<OrderQuantityRuleCheckVO> checkVOList = JSON.parseArray("[{\"amount\":10,\"op\":\"或\",\"quantity\":10,\"rulePass\":false,\"target\":1,\"warehouseType\":0}]", OrderQuantityRuleCheckVO.class);

        List<OrderQuantityRuleCheckVO> result = orderQuantityRuleService.orderQuantityLimitResultForShow(checkVOList);
        System.out.println(JSON.toJSONString(result));
    }
}