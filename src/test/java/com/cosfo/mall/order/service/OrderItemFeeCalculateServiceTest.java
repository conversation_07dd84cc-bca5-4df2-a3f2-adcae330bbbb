package com.cosfo.mall.order.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemFeeCalculateServiceTest {

    @Resource
    private OrderItemFeeCalculateService orderItemFeeCalculateService;
    @Resource
    private OrderItemFeeTransactionService orderItemFeeTransactionService;
    @Resource
    private OrderItemSnapshotService snapshotService;
    @Resource
    private OrderItemService orderItemService;

    @Test
    void calFeeTest() {


//        List<OrderItemFeeTransaction> orderItemFeeTransactionList = orderItemFeeCalculateService.calculateAgentFee(itemSnapshotList, orderItemList, OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode());
//
//        orderItemFeeTransactionService.saveOrderItemFeeTransaction(orderItemFeeTransactionList);
    }
}