package com.cosfo.mall.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constants.OrderCancelTypeEnum;
import com.cosfo.mall.common.result.PageResultDTO;
import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.order.mapper.OrderMapper;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.order.model.dto.OrderItemDTO;
import com.cosfo.mall.order.model.dto.OrderQueryDTO;
import com.cosfo.mall.order.model.dto.PlaceOrderDTO;
import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.order.model.vo.OrderDeliveryVO;
import com.cosfo.mall.order.model.vo.OrderVO;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.stock.service.impl.StockServiceImpl;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.summerfarm.model.dto.SummerfarmOrderDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessageDetail;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/6 15:32
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class OrderServiceTest {

    @Resource
    private OrderService orderService;
    @Resource
    private StockServiceImpl stockService;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderServiceImpl orderServiceImpl;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private MarketItemService marketItemService;

    @Test
    public void runFetchDeliveredDetails() {
        List<OrderDeliveryVO> orderDeliveryVOList = orderService.deliveredDetails(67765L);
        log.info("OrderDeliveryVO List:{}", JSON.toJSONString(orderDeliveryVOList));
        Assert.assertTrue(CollectionUtils.isEmpty(orderDeliveryVOList));
    }

    @Test
    public void testSaveSupplierDeliveryFee() {
        //String msg = "{\"msgData\":{\"code\":\"200\",\"deliveryTime\":\"2023-04-07\",\"orderNo\":\"OR168076606643067\",\"storeId\":4053,\"storeNo\":1,\"tenantId\":1003},\"msgType\":\"STOCK_LOCK\"}";
        //SummerfarmOrderDTO summerfarmOrderDTO = JSONObject.parseObject(msg, SummerfarmOrderDTO.class);
        // 处理库存冻结结果消息
        SummerfarmOrderDTO summerfarmOrderDTO = new SummerfarmOrderDTO();
        summerfarmOrderDTO.setCode("200");
        LocalDate deliveryTime = LocalDate.of(2023, 4, 7);
        summerfarmOrderDTO.setDeliveryTime(deliveryTime);
        summerfarmOrderDTO.setOrderNo("OR168076606643067");
        summerfarmOrderDTO.setStoreId(4053L);
        summerfarmOrderDTO.setStoreNo(1);
        summerfarmOrderDTO.setTenantId(1003L);
//        orderService.dealOrderStockLockInventoryResult(summerfarmOrderDTO);
    }

    @Test
    public void selfWarehouseOrderOutOfStorage() {
        CommonFulfillmentFinishMessage commonFulfillmentFinishMessage = new CommonFulfillmentFinishMessage();
        commonFulfillmentFinishMessage.setSourceOrderNo("OR170917401008619");
        orderService.orderOutOfStorage(commonFulfillmentFinishMessage);
    }

    @Test
    public void placeOrderTest() {
        String orderJson = "[{\"merchantAddressId\":4256,\"merchantContactId\":4313,\"payType\":2,\"orderItemDTOS\":[{\"itemId\":21427,\"amount\":1,\"itemType\":null,\"combineItemId\":null}],\"warehouseNo\":null,\"warehouseType\":1,\"remark\":\"\",\"orderType\":2,\"combineMarketId\":null,\"combineItemId\":null}]";
        List<PlaceOrderDTO> placeOrderDTOList = JSON.parseArray(orderJson, PlaceOrderDTO.class);
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setStoreId(4260L);
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setAccountId(3530L);
        orderService.newPlaceOrder(placeOrderDTOList, loginContextInfoDTO);
    }

    @Test
    public void updatePaySuccessTest() {
        Order order = orderMapper.selectByOrderNO("*****************", 2L);

        if (!OrderStatusEnum.CREATING_ORDER.getCode().equals(order.getStatus()) && !OrderStatusEnum.NO_PAYMENT.getCode().equals(order.getStatus())) {
            System.out.println("*****************订单不能取消");
        }

        Order order1 = orderMapper.selectByOrderNO("*****************", 2L);

        if (!OrderStatusEnum.CREATING_ORDER.getCode().equals(order1.getStatus()) && !OrderStatusEnum.NO_PAYMENT.getCode().equals(order1.getStatus())) {
            System.out.println("*****************订单不能取消");
        }
    }

    @Test
    public void timeCloseTest() {
        orderService.cancel(71871L, null, OrderCancelTypeEnum.MANUALLY_CANCEL.getType());
    }

    @Test
    public void testOrderTx() {
        OrderDTO orderDto = new OrderDTO();
        orderDto.setId(8L);
        orderDto.setOrderNo("01165294208211522");
        orderDto.setTenantId(3L);
        stockService.cancelOrderAfterLockStockFail(orderDto);
    }


    @Test
    public void orderList() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setStoreId(4252L);
        loginContextInfoDTO.setTenantId(1003L);
        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setPageNum(1);
        orderQueryDTO.setPageSize(6);
        PageResultDTO<List<OrderVO>> list = orderService.list(orderQueryDTO, loginContextInfoDTO);
        System.out.println(JSON.toJSONString(list));

        orderQueryDTO.setPageNum(list.getPages());
        PageResultDTO<List<OrderVO>> list1 = orderService.list(orderQueryDTO, loginContextInfoDTO);
        System.out.println(JSON.toJSONString(list1));
        Assert.assertTrue(list1.getData().size() == (list.getTotal() - 6 * (list.getPages() - 1)));

        orderQueryDTO.setPageNum(list.getPages() + 1);
        PageResultDTO<List<OrderVO>> list2 = orderService.list(orderQueryDTO, loginContextInfoDTO);
        System.out.println(JSON.toJSONString(list2));
        Assert.assertTrue(list2.getData().isEmpty());
    }


    @Test
    public void merchantStoreItemSaleLimitCheckTest() {
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(Lists.newArrayList(22248L), 2L);
        Map<Long, OrderItemDTO> orderItemDTOMap = new HashMap<>();

        OrderItemDTO itemDTO = new OrderItemDTO();
        itemDTO.setItemId(22248L);
        itemDTO.setAmount(20);

        orderItemDTOMap.put(22248L, itemDTO);
        orderService.merchantStoreItemSaleLimitCheck(marketItemVOList, orderItemDTOMap, 2L, 142087L);
    }

    @Test
    public void orderCancelTest() {
        orderService.cancel(93923L, null, OrderCancelTypeEnum.TIME_OUT_CANCEL.getType());

    }

    @Test
    public void stringtest() {
        MerchantAddressDTO merchantAddressDTO = new MerchantAddressDTO();
        merchantAddressDTO.setCityId(0L);
        merchantAddressDTO.setId(0L);
        merchantAddressDTO.setTenantId(0L);
        merchantAddressDTO.setStoreId(0L);
        merchantAddressDTO.setProvince("");
        merchantAddressDTO.setCity("ddd");
        merchantAddressDTO.setArea("ddd");
        merchantAddressDTO.setAddress("ddd");
        merchantAddressDTO.setHouseNumber("ddd");
        merchantAddressDTO.setCreateTime(LocalDateTime.now());
        merchantAddressDTO.setUpdateTime(LocalDateTime.now());
        merchantAddressDTO.setPoiNote("ddd");

        System.err.println(String.valueOf(merchantAddressDTO));
    }

    @Test
    public void checkRule(){
        CommonFulfillmentFinishMessage commonFulfillmentFinishMessage = new CommonFulfillmentFinishMessage();
        commonFulfillmentFinishMessage.setSourceOrderNo("OR169830995831402");
        List<CommonFulfillmentFinishMessageDetail> itemList = Lists.newArrayList();

        CommonFulfillmentFinishMessageDetail commonFulfillmentFinishMessageDetail = new CommonFulfillmentFinishMessageDetail();
        commonFulfillmentFinishMessageDetail.setItemId("26474");
        commonFulfillmentFinishMessageDetail.setShortQuantity(1);
        itemList.add(commonFulfillmentFinishMessageDetail);

        CommonFulfillmentFinishMessageDetail commonFulfillmentFinishMessageDetail1 = new CommonFulfillmentFinishMessageDetail();
        commonFulfillmentFinishMessageDetail1.setItemId("27258");
        commonFulfillmentFinishMessageDetail1.setShortQuantity(1);
        itemList.add(commonFulfillmentFinishMessageDetail1);

        CommonFulfillmentFinishMessageDetail commonFulfillmentFinishMessageDetail2 = new CommonFulfillmentFinishMessageDetail();
        commonFulfillmentFinishMessageDetail2.setItemId("27259");
        commonFulfillmentFinishMessageDetail2.setShortQuantity(1);
        itemList.add(commonFulfillmentFinishMessageDetail2);

        commonFulfillmentFinishMessage.setItemList(itemList);
        List<String> orderNos = Lists.newArrayList(commonFulfillmentFinishMessage.getSourceOrderNo());
        List<OrderResp> orderList = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        // 校验商城自动补发配置，过滤无需自动售后的订单子项
        List<CommonFulfillmentFinishMessageDetail> commonFulfillmentFinishMessageDetails = orderServiceImpl.filterByTenantAutoCreateResendAfterSaleRule(commonFulfillmentFinishMessage, orderList);

        commonFulfillmentFinishMessage.setItemList(commonFulfillmentFinishMessageDetails);
        System.err.println(JSON.toJSONString(commonFulfillmentFinishMessage));

    }
}
