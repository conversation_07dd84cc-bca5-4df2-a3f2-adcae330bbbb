package com.cosfo.mall.order.service.impl;

import com.cosfo.mall.market.model.vo.MarketItemVO;
import com.cosfo.mall.market.service.MarketItemService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
class OrderItemCheckServiceTest {

    @Resource
    private OrderItemCheckService orderItemCheckService;
    @Resource
    private MarketItemService marketItemService;

    @Test
    void merchantStoreItemSaleLimitCheck() {
        List<MarketItemVO> marketItemVOList = marketItemService.batchByItemIds(Lists.newArrayList(37485L), 2L);

        Map<Long, Integer> itemQuantityMap = new HashMap<>();
        itemQuantityMap.put(37485L, 5);
        Long tenantId = 2L;
        Long storeId = 4260L;
        orderItemCheckService.merchantStoreItemSaleLimitCheck(marketItemVOList, itemQuantityMap, tenantId, storeId);
    }
}