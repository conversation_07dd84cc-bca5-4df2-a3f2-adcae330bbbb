package com.cosfo.mall.order.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.merchant.model.vo.OrderQuantityRuleCheckVO;
import com.cosfo.mall.order.service.impl.OrderQuantityRuleServiceImpl;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class OrderQuantityRuleServiceTest {

    private OrderQuantityRuleService orderQuantityRuleService;



    @Test
    void orderQuantityLimitResultForShow() {
        orderQuantityRuleService = new OrderQuantityRuleServiceImpl();
        List<OrderQuantityRuleCheckVO> checkVOList = JSON.parseArray("[{\"amount\":10,\"op\":\"或\",\"quantity\":10,\"rulePass\":false,\"target\":1,\"warehouseType\":0}]", OrderQuantityRuleCheckVO.class);

        List<OrderQuantityRuleCheckVO> result = orderQuantityRuleService.orderQuantityLimitResultForShow(checkVOList);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void orderQuantityLimitResultOneForShow() {
        orderQuantityRuleService = new OrderQuantityRuleServiceImpl();
        List<OrderQuantityRuleCheckVO> checkVOList = JSON.parseArray("[{\"amount\":20,\"op\":\"或\",\"quantity\":0,\"rulePass\":false,\"target\":1,\"warehouseType\":0}, {\"amount\":10,\"op\":\"或\",\"quantity\":10,\"rulePass\":false,\"target\":1,\"warehouseType\":0}, {\"amount\":0,\"op\":\"或\",\"quantity\":10,\"rulePass\":false,\"target\":1,\"warehouseType\":0}]", OrderQuantityRuleCheckVO.class);

        List<OrderQuantityRuleCheckVO> result = orderQuantityRuleService.orderQuantityLimitResultForShow(checkVOList);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    void getMaxPriceTest() {
        List<OrderQuantityRuleCheckVO> ruleCheckVOS = new ArrayList<>();
        OrderQuantityRuleCheckVO vo1=  new OrderQuantityRuleCheckVO();
//        vo1.setAmount(new BigDecimal(20));
        vo1.setQuantity(1);
        OrderQuantityRuleCheckVO vo2=  new OrderQuantityRuleCheckVO();
        vo2.setAmount(new BigDecimal(22));

//        vo2.setQuantity(22);
        ruleCheckVOS.add(vo1);
        ruleCheckVOS.add(vo2);
        Optional<OrderQuantityRuleCheckVO> maxByAmount = ruleCheckVOS.stream()
                .max(Comparator.comparing(OrderQuantityRuleCheckVO::getAmount, Comparator.nullsLast(BigDecimal::compareTo))
                        .thenComparing(OrderQuantityRuleCheckVO::getQuantity, Comparator.nullsLast(Integer::compareTo)));

        System.out.println(maxByAmount.get());
    }
}