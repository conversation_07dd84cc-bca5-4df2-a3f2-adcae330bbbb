package com.cosfo.mall.order.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.order.model.po.OrderItemSnapshot;
import com.google.common.collect.Sets;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class OrderItemSnapshotServiceTest {

    @Resource
    private OrderItemSnapshotService orderItemSnapshotService;

    @Test
    public void test() {
        List<OrderItemSnapshot> list = orderItemSnapshotService.selectByOrderIds(2L, Sets.newHashSet(14025L));
        log.info("List<OrderItemSnapshot>:{}", JSON.toJSONString(list));
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }
}
