package com.cosfo.mall.order.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.context.DeliveryStateEnum;
import com.cosfo.mall.common.context.SummerfarmDeliveryTypeEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.facade.dto.OrderAfterSaleResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.model.dto.AfterSaleFinishDeliveryDTO;
import com.cosfo.mall.order.service.impl.OrderAfterSaleServiceImpl;
import com.cosfo.mall.warehouse.model.vo.WarehouseStorageVO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class OrderAfterSaleServiceTest {
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
//    @Resource
//    private RefundAfterSaleExecutor refundAfterSaleExecutor;
//    @Resource
//    private com.cosfo.aftersale.service.OrderAfterSaleService afterSaleService;
//    @Resource
//    private AsyncService asyncService;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderAfterSaleServiceImpl orderAfterSaleServiceImpl;

    @Test
    void payRefundSuccessDeal() {
        orderAfterSaleService.payRefundSuccessDeal(1583L);
    }

    @Test
    void afterSaleProcessFinish() {
        List<AfterSaleFinishDeliveryDTO> summerfarmFinishDeliveryDTOList = new ArrayList<>();
        AfterSaleFinishDeliveryDTO summerfarmFinishDeliveryDTO = new AfterSaleFinishDeliveryDTO();
        summerfarmFinishDeliveryDTO.setOrderNo("AS167507534920286");
        summerfarmFinishDeliveryDTO.setDeliveryType(SummerfarmDeliveryTypeEnum.DELIVERY.getType());
        summerfarmFinishDeliveryDTO.setState(DeliveryStateEnum.NORMAL.getState());
        summerfarmFinishDeliveryDTO.setRemark("配送正常");

        AfterSaleFinishDeliveryDTO summerfarmFinishDeliveryDTO2 = new AfterSaleFinishDeliveryDTO();
        summerfarmFinishDeliveryDTO2.setOrderNo("AS167507534920286");
        summerfarmFinishDeliveryDTO2.setDeliveryType(SummerfarmDeliveryTypeEnum.RECYCLE.getType());
        summerfarmFinishDeliveryDTO2.setState(DeliveryStateEnum.ABNORMAL.getState());
        summerfarmFinishDeliveryDTO2.setRemark("回收少了一件");
        summerfarmFinishDeliveryDTOList.add(summerfarmFinishDeliveryDTO);
        summerfarmFinishDeliveryDTOList.add(summerfarmFinishDeliveryDTO2);
        orderAfterSaleService.afterSaleProcessFinish(summerfarmFinishDeliveryDTOList);
    }

    @Test
    public void Test() {
//        orderService.orderProfitSharingTask();
    }


    @Test
    public void testAddress() {
        WarehouseStorageVO vo = orderAfterSaleService.queryOneWarehouseStorage("AS168207884823618");
        System.out.println(vo);
    }

    @Test
    public void test() {
//        OrderAfterSaleQueryDTO orderAfterSaleDTO = new OrderAfterSaleQueryDTO();
//        orderAfterSaleDTO.setPageIndex(1);
//        orderAfterSaleDTO.setPageNum(1);
//        orderAfterSaleDTO.setPageSize(100);
//        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//        loginContextInfoDTO.setTenantId(2L);
//        loginContextInfoDTO.setStoreId(4299L);
//        ResultDTO<PageDTO<OrderAfterSaleResultDTO>> resultDTO = orderAfterSaleService.list(orderAfterSaleDTO, loginContextInfoDTO);
//        System.err.println(JSON.toJSONString(resultDTO.getData()));
    }

//    @Test
//    public void test1() {
//        String msg = "{\"deliveryType\":0,\"itemList\":[{\"actualQuantity\":0,\"itemId\":\"1157\",\"quantity\":2,\"shortQuantity\":2,\"skuCode\":\"911238617708\",\"status\":1}],\"message\":\"\",\"sourceOrderNo\":\"AS1656218106483822592\"}";
//        CommonFulfillmentFinishMessage commonFulfillmentFinishMessage = JSON.parseObject(msg, CommonFulfillmentFinishMessage.class);
//        List<CosfoAfterSaleFinishDeliveryDTO> cosfoAfterSaleFinishDeliveryDTOS = orderAfterSaleServiceImpl.transferCosfoAfterSaleFinishDeliveryDTOList(commonFulfillmentFinishMessage);
//        orderAfterSaleServiceImpl.createAfterSaleOrderConsumer.accept(cosfoAfterSaleFinishDeliveryDTOS);
//    }

    @Test
    public void detailTest() {
        LoginContextInfoDTO dto = new LoginContextInfoDTO();
        dto.setTenantId(2L);
        dto.setStoreId(142068L);
        ResultDTO<List<OrderAfterSaleResultDTO>> detail = orderAfterSaleService.detail(238275L, dto);
        System.out.println(JSON.toJSONString(detail));
    }
}
