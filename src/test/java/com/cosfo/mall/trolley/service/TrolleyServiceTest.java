package com.cosfo.mall.trolley.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.utils.RedisUtils;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.order.model.vo.TrolleyItemVO;
import com.cosfo.mall.order.service.TrolleyService;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class TrolleyServiceTest {
    @Resource
    private TrolleyService trolleyService;
    @Resource
    private RedisUtils redisUtils;

    private LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();

    @BeforeEach
    public void init(){
        loginContextInfoDTO.setTenantId(1L);
        loginContextInfoDTO.setStoreId(1L);
        loginContextInfoDTO.setAccountId(1L);
    }

    @Test
    void get() {
        ResultDTO resultDTO = trolleyService.get(loginContextInfoDTO);
        Assert.assertNotNull("购物车为空", resultDTO);
    }

    @Test
    void clear() {
        trolleyService.clear(loginContextInfoDTO);
    }

    @Test
    void add() {
        trolleyService.add(2L,1, 2, loginContextInfoDTO);
    }

    @Test
    void remove() {
        List<Long> list = Arrays.asList(2L);
        trolleyService.remove(list, loginContextInfoDTO);
    }

    @Test
    void getByItemIds(){
        List<Long> itemIds = Arrays.asList(1615L, 24573L, 24559L);
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4260L);
        loginContextInfoDTO.setAccountId(3530L);
        List<TrolleyItemVO> list = trolleyService.getByItemIds(itemIds, loginContextInfoDTO);
        System.err.println(JSON.toJSONString(list));
    }

    @Test
    void redisTest(){
        String key = "eyJhbGciOiJIUzUxMiJ9.****************************************************************************.Wku35jaA-u6_Qaf4WObo9Z985DzWWW3zkknHX4t6ijtdQ1gAkUnUmxHgE-Ki64uKkbAwM8gzEMS5oG_u-mYbQg";
        Object o = redisUtils.get(key);
        System.out.println(o);
    }
}
