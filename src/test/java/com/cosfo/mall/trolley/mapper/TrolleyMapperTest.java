package com.cosfo.mall.trolley.mapper;

import com.cosfo.mall.order.mapper.TrolleyMapper;
import com.cosfo.mall.order.model.po.Trolley;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 购物车测试类
 *
 * <AUTHOR>
 * @date 2022/5/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class TrolleyMapperTest {
    @Resource
    private TrolleyMapper trolleyMapper;

    @Test
    public void merge() {
        // 添加商品
        Trolley trolley = new Trolley();
        trolley.setTenantId(1L);
        trolley.setStoreId(1L);
        trolley.setAccountId(1L);
        trolley.setItemId(1L);
        trolley.setAmount(1);
        trolleyMapper.merge(trolley);
    }
}