package com.cosfo.mall.bill.mapper;

import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.common.constants.ProfitSharingBusinessType;
import com.cosfo.mall.common.constants.ProfitSharingResultEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/25
 */
@SpringBootTest
class BillProfitSharingMapperTest {
    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;

    @Test
    void saveBatch() {
        List<BillProfitSharing> billProfitSharingList = new ArrayList<>();
        BillProfitSharing billProfitSharing = new BillProfitSharing();
        billProfitSharing.setTenantId(2L);
        billProfitSharing.setReceiverTenantId(2L);
        billProfitSharing.setOrderId(4301L);
        billProfitSharing.setType("MERCHANT_ID");
        billProfitSharing.setAccount("***********");
        billProfitSharing.setBusinessType(ProfitSharingBusinessType.REVERSE.getCode());
        // 请求流水号
        billProfitSharing.setOutTradeNo("***************");
        // 原交易请求流水号
        billProfitSharing.setTransactionId("**********");
        // 交易确认退款金额
        billProfitSharing.setPrice(new BigDecimal(10));
        billProfitSharing.setStatus(ProfitSharingResultEnum.WAITING.getStatus());
        billProfitSharingList.add(billProfitSharing);
        billProfitSharingMapper.saveBatch(billProfitSharingList);
        System.out.println(111);
    }
}