//package com.cosfo.mall.bill.service.impl;
//
//import com.cosfo.mall.tenant.service.TenantBillOfflineBuilder;
//import com.cosfo.mall.tenant.service.TenantBillService;
//import com.google.common.collect.Sets;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-11-03
 **/
//@SpringBootTest
//public class TenantBillTestService {
//
//    @Resource
//    private TenantBillOfflineBuilder tenantBillOfflineBuilder;
//
//    @Test
//    public void test() {
//        tenantBillOfflineBuilder.initTenantBill(Sets.newHashSet(2L), "2023-10-12", "2023-10-15");
//    }
//}
