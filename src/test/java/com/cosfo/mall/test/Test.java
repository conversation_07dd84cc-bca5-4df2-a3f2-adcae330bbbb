package com.cosfo.mall.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.HttpUtil;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.order.model.dto.HuiFuHostingPaymentDTO;
import com.cosfo.mall.order.model.po.HuiFuiHostingPaymentReceive;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.google.common.collect.Maps;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-03-31
 * @Description:
 */
public class Test {

    @org.junit.Test
    public void npeTest(){
        Map<String, Object> paramMap = Maps.newHashMap();
        String s = HttpUtil.formatUrlParam(paramMap);
        System.err.println(s);
        paramMap.put("1","123");
         s = HttpUtil.formatUrlParam(paramMap);
        System.err.println(s);
    }

    @org.junit.Test
    public void test(){

        String huifuId = "6666000124877554";
        String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKy4K3Xc84UOOCUZP3rXxgwui2aiaFtZ1sMfb7JiQYn7YmUug2ctYcXwiB6V0wWBRHgUo6SYD+iNAIHwNUT39OjowP/huovPMzuSqp6JDN5QEmhGHdv9V0DQHIjQt/kaHZReSKei8NxzM5xtmWu5GgWNVJi7UqfpWjImb7/W+Mo7AgMBAAECgYAZStrqZpGukVd9b0YRehmBXSuCuxOnFO/TIP5dU/AfAZX2FSqe6FFiCAgW2n/NVZGuN++CwdXKiyNg48kZMWpGl1Bn9fVfzSpwKKv1hkEJc58nxQ4Dn6HASP188z4DytPzq8T3R5ceSHLpTT0EPenIiRkeKklDb9YkAgDFcZ0WiQJBAON5dvBQE3ewevRsu4yszx4mdGt3TVBNZRwZGrzk4bEH6KlVeBioz7uN4Ii8Bhp8oRt99OUrl/rmZEHwjSVA4lcCQQDCYOwVlAlAsn30UZ71emU7IpKQlHQd+HEQPCSD1XbwF/pn0MYNZ7decOW+Ox6++I44jL5CvC7RhXDLAF+GTdC9AkEAlwmgkqHouzEgAslrolVf1Jod9PkrCaXJ++UjXsbuoDgrILxSWLVF8TecHc4Sk2WrJ3DzuXK/n+V4LlxFq7WwUwJAUKHIDUN1eyMP4LOjDw2QxLEYv2T1riELNcLdGtsIFZy8wSf3oEPv6vtGMl1v6aRNyuOHYUOS4FNMcMlc1uecuQJBAK4p2AQ0fMm9G1ljaTh81UcpGDw+EZsjitwi4GnGFwzzsjp+K/5BjcqiqdoJdFhHZbBS31nDGOSAp3y7vkNkskQ=";
        HuiFuHostingPaymentDTO huiFuHostingPaymentDTO = HuiFuHostingPaymentDTO.builder()
                .preOrderType("3")
                .reqDate("20231111")
                .reqSeqId("P1254264222222211")
                .huifuId(huifuId)
                .transAmt("0.01")
                .miniappData("{}")
                .goodsDesc("测试")
                .delayAcctFlag("Y")
                .timeExpire(LocalDateTime.now().plusMinutes(10).format(DateTimeFormatter.ofPattern(Constants.HUIFU_EXPIRE_DATE)))
                .notifyUrl("https://devmall.cosfo.cn/pay-notify/huifu-pay").build();

        String signRes = SignatureUtil.bodySign(huiFuHostingPaymentDTO, privateKey);

        //请求汇付支付
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuHostingPaymentDTO);
        huiFuDTO.setSys_id(huifuId);
        huiFuDTO.setProduct_id("PAYUN");
        huiFuDTO.setSign(signRes);
        String response = HuiFuApi.huiFuHostingPay(huiFuDTO);
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(response, HuiFuDTO.class);
        HuiFuiHostingPaymentReceive huiFuiHostingPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuiHostingPaymentReceive.class);
        System.err.println(response);
        System.err.println(JSON.toJSONString(huiFuiHostingPaymentReceive));
    }
}
