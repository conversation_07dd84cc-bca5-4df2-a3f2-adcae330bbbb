package com.cosfo.mall.task;

import com.cosfo.mall.common.constants.RefundSourceEnum;
import com.cosfo.mall.common.task.AutoCreateRefundTask;
import com.cosfo.mall.common.task.RefundRetryTask;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.service.RefundService;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.*;

/**
 * @Author: fansongsong
 * @Date: 2023-04-24
 * @Description:
 */
@SpringBootTest
public class TaskTest {

    @Resource
    private RefundRetryTask refundRetryTask;

    @Resource
    private AutoCreateRefundTask autoCreateRefundTask;
    @Resource
    private RefundService refundService;

    @Test
    public void refundRetryTaskProcessResult() {
        XmJobInput jobContext = new XmJobInput();
        jobContext.setJobParameters("{\"configTenantId\":-1}");
        refundRetryTask.processResult(jobContext);
    }

    @Test
    public void autoCreateProcessResult() {
        XmJobInput jobContext = new XmJobInput();
        jobContext.setJobParameters("{\"endBeforeDay\": 0}");
        autoCreateRefundTask.processResult(jobContext);
    }


    private static final Integer DEFAULT_NUM = 10;
    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(DEFAULT_NUM, DEFAULT_NUM, 10, TimeUnit.MINUTES, new LinkedBlockingQueue(100), new NamedThreadFactory("StoreBalance-thread"));

    @Test
    public void concurrenceExecute() throws InterruptedException {
        // 单订单并发执行
        CyclicBarrier cyclicBarrier = new CyclicBarrier(DEFAULT_NUM);

//        // 汇付、微信是一样的
//        Long orderId = 89106L;
//        Long tenantId = 2L;
//        Long orderAfterSaleId = 8910L;
//        BigDecimal totalPrice = new BigDecimal("0.01");
//        // 余额退款
//        Long orderId = 89121L;
//        Long tenantId = 2L;
//        Long orderAfterSaleId = 8928L;
        // 账期退款
        Long orderId = 89125L;
        Long tenantId = 2L;
        Long orderAfterSaleId = 8932L;
        BigDecimal totalPrice = new BigDecimal("0.10");
        for (int i = 0; i < DEFAULT_NUM; i++) {
            threadPoolExecutor.execute(() -> {
                        try {
                            RefundDTO refundDTO = new RefundDTO();
                            refundDTO.setOrderId(orderId);
                            refundDTO.setTenantId(tenantId);
                            refundDTO.setAfterSaleId(orderAfterSaleId);
                            refundDTO.setRefundPrice(totalPrice);
                            refundDTO.setSource(RefundSourceEnum.TASK);
                            cyclicBarrier.await();
                            refundService.refundRequest(refundDTO);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        } catch (BrokenBarrierException e) {
                            e.printStackTrace();
                        }

                    }

            );
        }
        Thread.sleep(30000);
    }

}
