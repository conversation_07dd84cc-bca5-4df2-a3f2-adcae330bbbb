//package com.cosfo.mall.task;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.schedulerx.worker.processor.ProcessResult;
//import com.cosfo.mall.common.task.TenantBillOfflineBuilderTask;
//import com.google.common.collect.Sets;
//import net.xianmu.task.vo.input.XmJobInput;
//import org.junit.Assert;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;

//@SpringBootTest
//public class TenantBillOfflineBuilderTaskTest {
//
//    @Autowired
//    private TenantBillOfflineBuilderTask tenantBillOfflineBuilderTask;
//
//    @Test
//    public void testTenantBillOfflineBuilderTask() throws Exception {
//        XmJobInput content = new XmJobInput();
//        content.setJobParameters(JSON.toJSONString(new TenantBillOfflineBuilderTask.Param()
//            .setTenantIds(Sets.newHashSet(24593L))
//            .setStartDate("2023-11-01")
//            .setEndDate("2023-11-03")));
//        ProcessResult result = tenantBillOfflineBuilderTask.processResult(content);
//        Assert.assertTrue(result != null && result.getStatus().isFinish());
//    }
//}
