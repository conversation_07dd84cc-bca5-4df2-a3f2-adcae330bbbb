package com.cosfo.mall.merchant;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.po.MerchantStore;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-05-06
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantStoreServiceTest {

    @Resource
    private MerchantStoreService merchantStoreService;

    @Resource
    private TenantService tenantService;

    @Test
    public void selectTenantInfo() {
        ResultDTO<TenantDTO> tenantDTOResultDTO = tenantService.selectTenantInfo(2L);
        System.err.println(JSON.toJSONString(tenantDTOResultDTO));
    }

    @Test
    public void sendCode() {
        ResultDTO resultDTO = merchantStoreService.sendCode("13588438646", 2L);
        resultDTO = merchantStoreService.sendCode("13588438646", 2L);
        resultDTO = merchantStoreService.sendCode("1111111111", 2L);
        resultDTO = merchantStoreService.sendCode("11111111111", 2L);
        resultDTO = merchantStoreService.sendCode("13588436547", 2L);
        System.err.println(JSON.toJSONString(resultDTO));
    }

    @Test
    public void queryStoreInfoMapTest() {
        Map<Long, MerchantStore> storeMap = merchantStoreService.queryStoreInfoMap(Sets.newHashSet(1L, 2L));
        Assert.assertEquals(storeMap.get(1L).getStoreName(), "11111哈哈哈哈11111哈哈哈哈11");
        Assert.assertEquals(storeMap.get(2L).getStoreName(), "阿斯达");
        System.out.println(storeMap.get(1L));
        System.out.println(storeMap.get(2L));
    }
}
