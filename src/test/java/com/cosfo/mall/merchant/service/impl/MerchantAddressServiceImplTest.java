package com.cosfo.mall.merchant.service.impl;

import com.cosfo.mall.merchant.model.po.MerchantAddress;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantAddressServiceImplTest {

    @Resource
    private MerchantAddressService merchantAddressService;

    @Test
    void batchQueryByStoreIds() {
        Map<Long, MerchantAddress> addressMap = merchantAddressService.batchQueryByStoreIds(Sets.newHashSet(142992L, 142993L), 2L);
        System.out.println(addressMap);
        assertEquals(addressMap.get(142993L).getProvince(), "浙江省");
        assertEquals(addressMap.get(142993L).getAddress(), "融创金成江南府");
        assertEquals(addressMap.get(142992L).getProvince(), "浙江省");
        assertEquals(addressMap.get(142992L).getAddress(), "融创金成江南府");
    }
}