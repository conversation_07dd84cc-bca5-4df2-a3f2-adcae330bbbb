package com.cosfo.mall.merchant.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.merchant.model.dto.*;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantStoreAccountServiceTest {

    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;

    @Resource
    private MerchantStoreService merchantStoreService;

    @Test
    void newListBelongStoreAccounts() {
        MerchantStoreAccountQueryDTO merchantStoreAccountQueryDTO = new MerchantStoreAccountQueryDTO();
        merchantStoreAccountQueryDTO.setTenantId(2L);
        merchantStoreAccountQueryDTO.setPhone("***********");
        merchantStoreAccountService.newListBelongStoreAccounts(merchantStoreAccountQueryDTO);
    }

    @Test
    void listStoreAccount() {
        MerchantStoreAccountPageQueryDTO pageQueryDTO = new MerchantStoreAccountPageQueryDTO();
        pageQueryDTO.setTenantId(2L);
        pageQueryDTO.setPhone("***********");
        pageQueryDTO.setCurrentLoginAccountId(4139L);
        pageQueryDTO.setPageIndex(1);
        pageQueryDTO.setPageSize(10);
        PageInfo<MerchantStoreAccountDTO> pageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(pageInfo.getSize(), 6);
        assertEquals(pageInfo.getList().size(), 6);
        assertFalse(pageInfo.isHasNextPage());

        pageQueryDTO.setPageIndex(2);
        PageInfo<MerchantStoreAccountDTO> emptyPageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(emptyPageInfo.getList().size(), 0);
        assertFalse(emptyPageInfo.isHasNextPage());
        System.out.println(pageInfo);
    }

    @Test
    void queryLoginAccountInfo() {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        loginContextInfoDTO.setStoreId(4486L);
        loginContextInfoDTO.setAccountId(3775L);
        LoginSuccessReturnDTO data = merchantStoreAccountService.queryLoginAccountInfo(loginContextInfoDTO).getData();
        System.err.println(JSON.toJSONString(data));
    }

    @Test
    void listStoreAccount2() {
        MerchantStoreAccountPageQueryDTO pageQueryDTO = new MerchantStoreAccountPageQueryDTO();
        pageQueryDTO.setTenantId(2L);
        pageQueryDTO.setPhone("***********");
        pageQueryDTO.setCurrentLoginAccountId(3771L);
        pageQueryDTO.setPageIndex(1);
        pageQueryDTO.setPageSize(10);
        PageInfo<MerchantStoreAccountDTO> pageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(pageInfo.getTotal(), 13);
        assertEquals(pageInfo.getList().size(), 10);
        assertTrue(pageInfo.isHasNextPage());

        pageQueryDTO.setPageIndex(2);
        PageInfo<MerchantStoreAccountDTO> nextPageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(nextPageInfo.getList().size(), 3);
        assertEquals(nextPageInfo.getSize(), 3);
        assertFalse(nextPageInfo.isHasNextPage());
        System.out.println(pageInfo);

        pageQueryDTO.setPageIndex(3);
        PageInfo<MerchantStoreAccountDTO> emptyPageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(emptyPageInfo.getList().size(), 0);
        assertEquals(emptyPageInfo.getSize(), 0);
        assertFalse(emptyPageInfo.isHasNextPage());
        System.out.println(pageInfo);
    }

    @Test
    void listStoreAccountByName() {
        MerchantStoreAccountPageQueryDTO pageQueryDTO = new MerchantStoreAccountPageQueryDTO();
        pageQueryDTO.setTenantId(2L);
        pageQueryDTO.setPhone("***********");
        pageQueryDTO.setStoreName("香菜");
        pageQueryDTO.setCurrentLoginAccountId(4064L);
        pageQueryDTO.setPageIndex(1);
        pageQueryDTO.setPageSize(10);
        PageInfo<MerchantStoreAccountDTO> pageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(pageInfo.getTotal(), 2);
        assertEquals(pageInfo.getList().size(), 2);
        assertFalse(pageInfo.isHasNextPage());
        System.out.println(pageInfo);

        pageQueryDTO.setPageIndex(2);
        PageInfo<MerchantStoreAccountDTO> nextPageInfo = merchantStoreAccountService.listStoreAccount(pageQueryDTO);
        assertEquals(nextPageInfo.getList().size(), 0);
        assertEquals(nextPageInfo.getSize(), 0);
        assertFalse(nextPageInfo.isHasNextPage());
        System.out.println(pageInfo);

    }
}