package com.cosfo.mall.merchant.store;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constants.MerchantStoreAccountStatusEnum;
import com.cosfo.mall.common.context.MerchantStoreAccountDeleteFlagEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreAccount;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-04-27
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantStoreServiceTest {

//    @Resource
//    private MerchantStoreServiceImpl merchantStoreService;
//    @Resource
//    private MerchantStoreAccountMapper merchantStoreAccountMapper;
//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;


    @Test
    public void testUnlogin() {
        Long storeId = 4306L;
        // 查询账户信息
        MerchantStoreAccount query = new MerchantStoreAccount();
        query.setPhone("***********");
        query.setStoreId(storeId);
        query.setStatus(MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus());
        query.setDeleteFlag(MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
//        MerchantStoreAccount account = merchantStoreAccountMapper.selectOne(query);
//        if (Objects.isNull(account)) {
//            return;
//        }

        // 处理对应门店信息
//        MerchantStore store = merchantStoreMapper.selectByPrimaryKey(storeId);
//        LoginSuccessReturnDTO loginSuccessReturnDTO = merchantStoreService.assemblyStoreInfo(null, store, account);
//        System.out.println(JSON.toJSONString(loginSuccessReturnDTO));
    }

    @Test
    public void consultationDetail() {
        Long orderItemId = 70492L;
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);
        ResultDTO resultDTO = orderAfterSaleService.consultationDetail(orderItemId, loginContextInfoDTO);
        System.err.println(JSON.toJSONString(resultDTO));
    }

}
