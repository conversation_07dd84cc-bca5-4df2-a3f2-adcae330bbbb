package com.cosfo.mall.product.service.impl;

import com.cosfo.mall.product.service.ProductSkuService;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class ProductSkuServiceImplTest {
    @Resource
    private ProductSkuService productSkuService;

//    @Test
//    public void test(){
//        SummerfarmSkuInput summerfarmSkuInput = new SummerfarmSkuInput();
//        List<Long> list = Arrays.asList(19199L);
//        summerfarmSkuInput.setSkuIds(list);
//        productSkuService.synchronizedSupplySku(summerfarmSkuInput);
//    }
}
