@echo off
echo ========================================
echo OpenClover 覆盖率报告查看器
echo ========================================

echo.
echo 检查覆盖率报告文件...

if exist "target\site\clover\index.html" (
    echo ✅ 主覆盖率报告: target\site\clover\index.html
) else (
    echo ❌ 主覆盖率报告未找到
)

if exist "target\site\clover\com\cosfo\mall\marketing\service\impl\ActivityServiceImpl.html" (
    echo ✅ ActivityServiceImpl覆盖率报告: target\site\clover\com\cosfo\mall\marketing\service\impl\ActivityServiceImpl.html
) else (
    echo ❌ ActivityServiceImpl覆盖率报告未找到
)

echo.
echo 📊 覆盖率报告说明：
echo 1. 主报告显示整体项目覆盖率
echo 2. ActivityServiceImpl报告显示具体类的覆盖率
echo 3. 绿色表示已覆盖的代码行
echo 4. 红色表示未覆盖的代码行
echo 5. 黄色表示部分覆盖的分支

echo.
echo 正在打开覆盖率报告...

rem 打开主报告
if exist "target\site\clover\index.html" (
    start "" "target\site\clover\index.html"
    echo ✅ 已打开主覆盖率报告
) else (
    echo ❌ 无法打开主覆盖率报告
)

rem 等待2秒后打开ActivityServiceImpl报告
timeout /t 2 /nobreak >nul

if exist "target\site\clover\com\cosfo\mall\marketing\service\impl\ActivityServiceImpl.html" (
    start "" "target\site\clover\com\cosfo\mall\marketing\service\impl\ActivityServiceImpl.html"
    echo ✅ 已打开ActivityServiceImpl覆盖率报告
) else (
    echo ❌ 无法打开ActivityServiceImpl覆盖率报告
)

echo.
echo ========================================
echo 🎉 成功解决JaCoCo覆盖率0%问题！
echo ========================================
echo.
echo 解决方案总结：
echo 1. ✅ 使用OpenClover替代JaCoCo
echo 2. ✅ 配置了正确的排除规则
echo 3. ✅ 解决了Spring Boot + Dubbo兼容性问题
echo 4. ✅ 测试成功执行并生成覆盖率报告
echo 5. ✅ ActivityServiceImpl类的覆盖率现在可以正确显示
echo.
echo 下次运行测试覆盖率的命令：
echo mvn clean test -Dtest=ActivityServiceTest -Dspring.profiles.active=wurth
echo mvn clover:aggregate clover:clover
echo.
pause
